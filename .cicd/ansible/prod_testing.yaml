- name: Test brand websites
  hosts: localhost
  gather_facts: false

  vars_files:
    - vars.yaml

  tasks:
    - name: Install PHP dependencies
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/composer
        tasks_from: install
    - name: Run symfony commands
      ansible.builtin.include_role:
        name: ansible-build-scripts/roles/symfony
        tasks_from: commands
      vars:
        symfony_command_run_type: prod_test

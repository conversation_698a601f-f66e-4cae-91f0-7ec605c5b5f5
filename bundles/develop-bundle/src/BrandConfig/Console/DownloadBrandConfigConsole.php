<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\BrandConfig\Console;

use App\Config\Helper\ConfigFileHelperInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\ArtemisApiClient\Action\GetBrandAssets\GetBrandAssetsRequest;
use Visymo\ArtemisApiClient\Action\GetBrandConfig\GetBrandConfigRequest;
use Visymo\ArtemisApiClient\Action\GetBrands\GetBrandsRequest;
use Visymo\ArtemisApiClient\ArtemisApiClientInterface;
use Visymo\ArtemisApiClient\Model\Brand\Brand;
use Visymo\DevelopBundle\BrandConfig\ArtemisApiClient\ArtemisApiClientFactory;
use Visymo\Filesystem\SerializedFile\Type\NativePrettyJsonFileFactory;

#[AsCommand(
    name       : 'develop:brand-config:download',
    description: 'Download config and assets of all brands from Artemis'
)]
final class DownloadBrandConfigConsole extends Command
{
    private const string ARGUMENT_DEVELOP_ARTEMIS       = 'develop_artemis';
    private const string ARGUMENT_EXECUTE_COMMAND_CHAIN = 'execute_command_chain';

    private const string OPTION_BRAND = 'brand';

    private ArtemisApiClientInterface $artemisApiClient;

    public function __construct(
        private readonly ArtemisApiClientFactory $artemisApiClientFactory,
        private readonly NativePrettyJsonFileFactory $nativePrettyJsonFileFactory,
        private readonly ConfigFileHelperInterface $configFileHelper
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_DEVELOP_ARTEMIS,
                InputArgument::OPTIONAL,
                'Download from Artemis in develop',
            )
            ->addArgument(
                self::ARGUMENT_EXECUTE_COMMAND_CHAIN,
                InputArgument::OPTIONAL,
                'Execute command chain after push (import config, yarn dev, etc.)',
            )
            ->addOption(
                self::OPTION_BRAND,
                'b',
                InputOption::VALUE_REQUIRED,
                'This brand only',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $useDevelopArtemis = (bool)$input->getArgument(self::ARGUMENT_DEVELOP_ARTEMIS);
        $executeCommandChain = (bool)$input->getArgument(self::ARGUMENT_EXECUTE_COMMAND_CHAIN);
        $filterBrand = $input->getOption(self::OPTION_BRAND);

        $this->artemisApiClient = $useDevelopArtemis
            ? $this->artemisApiClientFactory->createForDevelop()
            : $this->artemisApiClientFactory->createForProduction();

        $getBrandsResponse = $this->artemisApiClient->getBrands(new GetBrandsRequest());

        foreach ($getBrandsResponse->getBrands() as $brand) {
            if ($filterBrand !== null && $filterBrand !== $brand->slug) {
                continue;
            }

            $this->importBrandConfig($brand, $output, $useDevelopArtemis);
            $this->importBrandAssets($brand, $output, $useDevelopArtemis);
        }

        if ($executeCommandChain) {
            $this->executeCommandChain($output);
        }

        return Command::SUCCESS;
    }

    private function importBrandConfig(
        Brand $brand,
        OutputInterface $output,
        bool $useDevelopArtemis
    ): void
    {
        $output->writeln(
            sprintf(
                'Download brand config <info>%s</info> from Artemis%s',
                $brand->slug,
                $useDevelopArtemis ? ' in develop' : '',
            ),
        );

        $filePath = $this->configFileHelper->getBrandConfigJsonFilePath($brand->slug);
        $file = $this->nativePrettyJsonFileFactory->create($filePath);

        $response = $this->artemisApiClient->getBrandConfig(
            new GetBrandConfigRequest($brand->slug),
        );
        $file->writeContent($response->response->getData());
    }

    private function importBrandAssets(
        Brand $brand,
        OutputInterface $output,
        bool $useDevelopArtemis
    ): void
    {
        $output->writeln(
            sprintf(
                'Download brand assets <info>%s</info> from Artemis%s',
                $brand->slug,
                $useDevelopArtemis ? ' in develop' : '',
            ),
        );

        $filePath = $this->configFileHelper->getBrandAssetsJsonFilePath($brand->slug);
        $file = $this->nativePrettyJsonFileFactory->create($filePath);

        $response = $this->artemisApiClient->getBrandAssets(
            new GetBrandAssetsRequest($brand->slug),
        );
        $file->writeContent($response->response->getData());
    }

    private function executeCommandChain(OutputInterface $output): void
    {
        $commands = [
            'sudo composer import-config',
            'yarn dev',
        ];

        foreach ($commands as $command) {
            $output->writeln(sprintf('<info>Executing %s</info>', $command));

            $resultCode = null;
            passthru($command, $resultCode);

            if ($resultCode !== Command::SUCCESS) {
                throw new \RuntimeException(sprintf('Command failed: %s', $command));
            }
        }
    }
}

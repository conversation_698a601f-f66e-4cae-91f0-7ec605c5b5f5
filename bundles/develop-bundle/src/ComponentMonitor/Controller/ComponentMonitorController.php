<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\ComponentMonitor\Controller;

use App\Component\Generic\Group\GroupLayout;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\DevelopBundle\ComponentMonitor\Repository\ComponentStateRepository;

class ComponentMonitorController extends AbstractController
{
    public function __construct(
        private readonly ComponentStateRepository $componentStateRepository
    )
    {
    }

    #[Route(path: '/dev/component-monitor', name: 'route_develop_component_monitor', methods: ['GET'])]
    public function index(): Response
    {
        return $this->render(
            '@develop/component_monitor/component_monitor.html.twig',
            [
                'component_states'        => $this->componentStateRepository->findAll(),
                'group_component_layouts' => GroupLayout::getSupportedLayoutVariants(),
            ],
        );
    }
}

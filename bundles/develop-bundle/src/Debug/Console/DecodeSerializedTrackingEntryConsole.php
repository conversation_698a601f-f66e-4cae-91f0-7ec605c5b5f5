<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\Console;

use App\Tracking\Entry\Serializer\TrackingEntryStringSerializer;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name       : 'develop:decode-ste',
    description: 'Decode serialized tracking entry value'
)]
class DecodeSerializedTrackingEntryConsole extends Command
{
    private const string ARG_SERIALIZED_TRACKING_ENTRY = 'ste';

    public function __construct(private readonly TrackingEntryStringSerializer $trackingEntryStringSerializer)
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(self::ARG_SERIALIZED_TRACKING_ENTRY, InputArgument::REQUIRED, 'STE payload');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        /** @var string $serializedTrackingEntry */
        $serializedTrackingEntry = $input->getArgument(self::ARG_SERIALIZED_TRACKING_ENTRY);

        $trackingEntry = $this->trackingEntryStringSerializer->deserialize($serializedTrackingEntry);

        $output->writeln(
            print_r($trackingEntry, true),
        );

        return Command::SUCCESS;
    }
}

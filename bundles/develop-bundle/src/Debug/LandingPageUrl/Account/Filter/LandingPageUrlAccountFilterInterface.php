<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Account\Filter;

use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRoute;

interface LandingPageUrlAccountFilterInterface
{
    /**
     * @param mixed[] $brandConfig
     * @param mixed[] $domainConfig
     * @param mixed[] $accountIds
     *
     * @return mixed[]
     */
    public function filter(
        array $brandConfig,
        array $domainConfig,
        array $accountIds,
        LandingPageRoute $landingPageRoute,
        LandingPageUrlGeneratorOptions $options
    ): array;
}

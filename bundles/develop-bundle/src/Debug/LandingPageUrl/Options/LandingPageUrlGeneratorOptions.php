<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Options;

use App\Account\Service\AccountService;
use App\Generic\Device\Device;
use App\Tracking\Model\TrafficSource;

readonly class LandingPageUrlGeneratorOptions
{
    public function __construct(
        public ?string $devVmName,
        public string $brand,
        public string $domain,
        public ?string $locale,
        public ?int $accountId,
        public ?AccountService $accountService,
        public ?TrafficSource $trafficSource,
        public ?Device $device,
        public ?string $query,
        public ?string $splitTestVariant,
        public ?string $templateVariant,
        public bool $redirectUrl,
        public ?bool $offlineConversionTracking
    )
    {
    }
}

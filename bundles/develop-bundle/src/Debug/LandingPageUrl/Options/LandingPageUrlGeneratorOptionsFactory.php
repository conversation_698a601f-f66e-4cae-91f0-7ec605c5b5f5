<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Options;

use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettingsFactory;
use App\Generic\Device\Device;
use App\Tracking\Model\TrafficSource;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;

class LandingPageUrlGeneratorOptionsFactory
{
    private const array EXAMPLE_QUERIES = [
        'Apple iPhone',
        'Audi A4',
        'Hotel Amsterdam',
        'Hotel New York',
        'iPad',
        'pizza',
    ];

    public function __construct(
        private readonly DomainToBrandMapReader $domainToBrandMapReader,
        private readonly WebsiteConfigurationRepository $websiteConfigurationRepository,
        private readonly WebsiteConfigurationFileRepository $websiteConfigurationFileRepository,
        private readonly AccountSettingsFactory $accountSettingsFactory
    )
    {
    }

    public function create(
        ?string $devVmName,
        ?string $brand,
        ?string $domain,
        ?string $locale,
        ?int $accountId,
        ?TrafficSource $trafficSource,
        ?Device $device,
        ?string $query,
        ?string $splitTestVariant,
        ?string $templateVariant,
        bool $redirectUrl,
        ?bool $offlineConversionTracking
    ): LandingPageUrlGeneratorOptions
    {
        if ($brand === null) {
            $brand = $domain !== null
                ? $this->domainToBrandMapReader->getBrandForDomain($domain)
                : $this->getRandomBrand();
        }

        $websiteConfiguration = $this->websiteConfigurationRepository->getForBrand($brand);

        $domain ??= $this->getDomainFromConfig($websiteConfiguration, $locale);
        $query ??= array_rand(array_flip(self::EXAMPLE_QUERIES));
        $accountService = $this->getAccountService($accountId, $websiteConfiguration);

        return new LandingPageUrlGeneratorOptions(
            devVmName                : $devVmName,
            brand                    : $brand,
            domain                   : $domain,
            locale                   : $locale,
            accountId                : $accountId,
            accountService           : $accountService,
            trafficSource            : $trafficSource,
            device                   : $device,
            query                    : $query,
            splitTestVariant         : $splitTestVariant,
            templateVariant          : $templateVariant,
            redirectUrl              : $redirectUrl,
            offlineConversionTracking: $offlineConversionTracking,
        );
    }

    private function getRandomBrand(): string
    {
        $availableBrands = $this->websiteConfigurationFileRepository->getAvailableBrands();

        return $availableBrands[array_rand($availableBrands)];
    }

    private function getDomainFromConfig(
        WebsiteConfiguration $websiteConfiguration,
        ?string $locale
    ): string
    {
        if ($locale !== null) {
            return $websiteConfiguration->getDomainForLocale($locale);
        }

        // Select random domain
        $availableDomains = $websiteConfiguration->getAvailableDomains();

        /** @var string $domain */
        $domain = array_rand(array_flip($availableDomains));

        return $domain;
    }

    private function getAccountService(?int $accountId, WebsiteConfiguration $websiteConfiguration): ?AccountService
    {
        if ($accountId === null) {
            return null;
        }

        $accountConfig = $websiteConfiguration->getAccountConfig($accountId);

        if ($accountConfig === null) {
            return null;
        }

        return $this->accountSettingsFactory->create($accountConfig)->service;
    }
}

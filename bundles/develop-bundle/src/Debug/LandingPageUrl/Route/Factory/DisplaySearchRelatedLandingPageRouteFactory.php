<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Route\Factory;

use App\Account\Service\AccountService;
use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\TrafficSource;
use App\Tracking\Request\SeaRequestInterface;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Options\LandingPageUrlGeneratorOptions;
use Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRoute;

readonly class DisplaySearchRelatedLandingPageRouteFactory implements LandingPageRouteFactoryInterface
{
    public function __construct(
        private DisplaySearchRelatedSettings $displaySearchRelatedSettings
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function create(LandingPageUrlGeneratorOptions $options): array
    {
        if (!$this->displaySearchRelatedSettings->enabled) {
            return [];
        }

        return [
            new LandingPageRoute(
                'Display Search Related LandingPage Google',
                'route_display_search_related',
                null,
                AccountService::GOOGLE_ADS,
                TrafficSource::GOOGLE,
                [
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => uniqid('campaign-', false),
                    ClickIdSource::GOOGLE_CLICK_ID->value        => uniqid('gclid-', false),
                ],
            ),
            new LandingPageRoute(
                'Display Search Related LandingPage Microsoft',
                'route_display_search_related',
                null,
                AccountService::MICROSOFT_ADVERTISING,
                TrafficSource::MICROSOFT,
                [
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => uniqid('campaign-', false),
                    ClickIdSource::MICROSOFT_CLICK_ID->value     => uniqid('msclkid-', false),
                ],
            ),
            new LandingPageRoute(
                'Display Search Related LandingPage Partners',
                'route_display_search_related',
                null,
                AccountService::PARTNERS,
                TrafficSource::GOOGLE,
                [
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => uniqid('campaign-', false),
                    ClickIdSource::GOOGLE_CLICK_ID->value        => uniqid('gclid-', false),
                ],
            ),
            new LandingPageRoute(
                'Display Search Related LandingPage Partners - Facebook',
                'route_display_search_related',
                null,
                AccountService::PARTNERS,
                TrafficSource::FACEBOOK,
                [
                    SeaRequestInterface::PARAMETER_CAMPAIGN_NAME => uniqid('campaign-', false),
                    ClickIdSource::FACEBOOK_CLICK_ID->value      => uniqid('fbclid-', false),
                ],
            ),
        ];
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Route;

use App\Account\Service\AccountService;
use App\Tracking\Model\TrafficSource;

readonly class LandingPageRoute
{
    /**
     * @param mixed[] $urlParameters
     */
    public function __construct(
        public string $label,
        public string $route,
        public ?string $redirectRoute,
        public ?AccountService $accountService,
        public ?TrafficSource $trafficSource,
        public array $urlParameters
    )
    {
    }
}

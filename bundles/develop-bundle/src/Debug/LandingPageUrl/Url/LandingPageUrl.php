<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Debug\LandingPageUrl\Url;

final class LandingPageUrl
{
    /** @var array<string, string> */
    private array $info = [];

    /** @var array<string, string> */
    private array $urls = [];

    private ?int $accountId = null;

    private ?string $campaign = null;

    private string $endpoint;

    public function __construct(
        private readonly string $label
    )
    {
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function addInfo(string $label, string $value): self
    {
        $this->info[$label] = $value;

        return $this;
    }

    /**
     * @return array<string, string>
     */
    public function getInfo(): array
    {
        $info = $this->info;

        if ($this->accountId !== null) {
            $info['Account ID'] = (string)$this->accountId;
        }

        if ($this->campaign !== null) {
            $info['Campaign'] = $this->campaign;
        }

        return $info;
    }

    public function addUrl(string $label, string $url): self
    {
        $this->urls[$label] = $url;

        return $this;
    }

    /**
     * @return array<string, string>
     */
    public function getUrls(): array
    {
        return $this->urls;
    }

    public function getEndpoint(): string
    {
        if (!isset($this->endpoint)) {
            $urlPath = null;
            $firstUrl = reset($this->urls);

            if (is_string($firstUrl)) {
                $urlPath = parse_url($firstUrl, PHP_URL_PATH);
            }

            $this->endpoint = is_string($urlPath) ? $urlPath : '/';
        }

        return $this->endpoint;
    }

    public function setAccountId(?int $accountId): self
    {
        $this->accountId = $accountId;

        return $this;
    }

    public function setCampaign(?string $campaign): self
    {
        $this->campaign = $campaign;

        return $this;
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\FrontendTest\Console;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\DevelopBundle\FrontendTest\Enum\FrontendFilterTypeEnum;
use Visymo\DevelopBundle\FrontendTest\Helper\FrontendTestFilterHelper;

#[AsCommand(
    name       : 'develop:test:frontend',
    description: 'Run frontend tests'
)]
class TestFrontendConsole extends Command
{
    public function __construct(
        private readonly FrontendTestFilterHelper $frontendTestFilterHelper,
        private readonly string $projectDir,
        ?string $name = null
    )
    {
        parent::__construct($name);
    }

    protected function configure(): void
    {
        foreach ($this->frontendTestFilterHelper->getFilters() as $filter) {
            $availableValues = null;

            if ($filter->availableOptions() !== []) {
                $availableValues = sprintf(' ("%s")', implode('", "', $filter->availableOptions()));
            }

            $this->addOption(
                $filter->value,
                null,
                InputOption::VALUE_OPTIONAL,
                sprintf(
                    'Filter frontend tests by %s%s',
                    $filter->value,
                    $availableValues,
                ),
            );
        }
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $options = $input->getOptions();
        $availableFilters = array_column($this->frontendTestFilterHelper->getFilters(), 'value');

        foreach ($availableFilters as $filter) {
            $value = $options[$filter] ?? null;

            if (!is_string($value)) {
                continue;
            }

            $validatedEnum = FrontendFilterTypeEnum::from($filter);
            $validatedEnum->validateValue($value);

            $this->frontendTestFilterHelper->setFilter($validatedEnum, $value);
        }

        passthru(sprintf('%s/bin/phpunit-app --testsuite=frontend', $this->projectDir));

        return Command::SUCCESS;
    }
}

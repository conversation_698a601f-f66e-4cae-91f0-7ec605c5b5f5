<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\FrontendTest\Enum;

use App\Generic\Device\Device;
use Tests\Frontend\Framework\WebDriver\Browser\Browser;
use Tests\Frontend\Framework\WebDriver\LambdaTest\LambdaTestDesktopCapabilities;
use Tests\Frontend\TestCase\DisplaySearchRelated\DisplaySearchRelatedTestCase;
use Tests\Frontend\TestCase\DisplaySearchRelatedWeb\DisplaySearchRelatedWebTestCase;
use Tests\Frontend\TestCase\MicrosoftDisplaySearch\MicrosoftDisplaySearchTestCase;
use Tests\Frontend\TestCase\MicrosoftDisplaySearchAdvertised\MicrosoftDisplaySearchAdvertisedTestCase;
use Tests\Frontend\TestCase\MicrosoftSearchRelated\MicrosoftSearchRelatedTestCase;
use Tests\Frontend\TestCase\MicrosoftSearchRelatedWeb\MicrosoftSearchRelatedWebTestCase;
use Tests\Frontend\TestCase\Page\PageTestCase;
use Tests\Frontend\TestCase\WebSearch\WebSearchTestCase;
use Tests\Frontend\TestCase\WebSearchAdvertised\WebSearchAdvertisedTestCase;

enum FrontendFilterTypeEnum: string
{
    case FILTER_BRAND                 = 'brand';
    case FILTER_PAGE                  = 'page';
    case FILTER_DEVICE                = 'device';
    case FILTER_OS                    = 'os';
    case FILTER_BROWSER               = 'browser';
    case FILTER_RESPONSIVE_BREAKPOINT = 'breakpoint';

    public const array FILTER_PAGE_OPTIONS = [
        'page' => PageTestCase::class,
        'ws'   => WebSearchTestCase::class,
        'wsa'  => WebSearchAdvertisedTestCase::class,
        'ds'   => MicrosoftDisplaySearchTestCase::class,
        'dsa'  => MicrosoftDisplaySearchAdvertisedTestCase::class,
        'dsr'  => DisplaySearchRelatedTestCase::class,
        'dsrw' => DisplaySearchRelatedWebTestCase::class,
        'msr'  => MicrosoftSearchRelatedTestCase::class,
        'msrw' => MicrosoftSearchRelatedWebTestCase::class,
    ];

    public const array FILTER_OS_OPTIONS = [
        'windows' => LambdaTestDesktopCapabilities::OS_WINDOWS_11,
        'macos'   => LambdaTestDesktopCapabilities::OS_MAC_SEQUOIA,
    ];

    public const array FILTER_BROWSER_OPTIONS = [
        'chrome'  => Browser::CHROME,
        'firefox' => Browser::FIREFOX,
        'safari'  => Browser::SAFARI,
    ];

    /** @return string[] */
    public function availableOptions(): array
    {
        return match ($this) {
            self::FILTER_PAGE    => array_keys(self::FILTER_PAGE_OPTIONS),
            self::FILTER_OS      => array_keys(self::FILTER_OS_OPTIONS),
            self::FILTER_BROWSER => array_keys(self::FILTER_BROWSER_OPTIONS),
            self::FILTER_DEVICE  => [
                ...Device::getSupportedShortValues(),
                ...array_column(Device::cases(), 'value'),
            ],
            default              => [],
        };
    }

    public function validateValue(string $value): void
    {
        $availableOptions = $this->availableOptions();
        $separatedValues = explode(',', $value);
        $invalidOptions = array_diff($separatedValues, $availableOptions);

        if ($availableOptions !== [] && $invalidOptions !== []) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Invalid frontend test filter value(s) "%s" for filter %s',
                    implode('", "', $invalidOptions),
                    $this->value,
                ),
            );
        }
    }
}

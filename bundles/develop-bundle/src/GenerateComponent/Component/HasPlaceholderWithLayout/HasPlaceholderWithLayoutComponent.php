<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithLayout;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionComponent;
use App\JsonTemplate\Component\ComponentInterface;

final class HasPlaceholderWithLayoutComponent extends AbstractSearchApiConditionComponent
{
    /**
     * @param ComponentInterface[] $matchingChildren
     * @param ComponentInterface[] $nonMatchingChildren
     */
    public function __construct(
        public readonly HasPlaceholderWithLayoutLayout $layout,
        array $matchingChildren,
        array $nonMatchingChildren
    )
    {
        parent::__construct($matchingChildren, $nonMatchingChildren);
    }

    public static function getType(): string
    {
        return 'has_placeholder_with_layout';
    }

    public function getRenderer(): string
    {
        return HasPlaceholderWithLayoutRenderer::class;
    }
}

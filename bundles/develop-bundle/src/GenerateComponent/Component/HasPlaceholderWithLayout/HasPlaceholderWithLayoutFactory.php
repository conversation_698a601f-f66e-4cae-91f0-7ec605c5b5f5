<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithLayout;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class HasPlaceholderWithLayoutFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return HasPlaceholderWithLayoutComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $matchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_YES],
        );
        $nonMatchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_NO],
        );

        return new HasPlaceholderWithLayoutComponent(
            layout             : HasPlaceholderWithLayoutLayout::from($options[LayoutInterface::KEY]),
            matchingChildren   : $matchingChildren,
            nonMatchingChildren: $nonMatchingChildren,
        );
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithoutLayout;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionResolver;

final class HasPlaceholderWithoutLayoutResolver extends AbstractSearchApiConditionResolver
{
    public static function getSupportedComponent(): string
    {
        return HasPlaceholderWithoutLayoutComponent::class;
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\Placeholder;

use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;

final readonly class PlaceholderResolver implements ComponentResolverInterface
{
    public function __construct(
        private ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return PlaceholderComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(PlaceholderLayout::class);

        return $this->optionsResolver->resolve($options);
    }
}

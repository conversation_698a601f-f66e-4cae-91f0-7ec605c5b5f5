<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\PlaceholderWithSpaceModifier;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class PlaceholderWithSpaceModifierComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly PlaceholderWithSpaceModifierLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'placeholder_with_space_modifier';
    }

    public function getRenderer(): string
    {
        return PlaceholderWithSpaceModifierRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\PlaceholderWithSpaceModifier;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class PlaceholderWithSpaceModifierFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return PlaceholderWithSpaceModifierComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new PlaceholderWithSpaceModifierComponent(
            layout                 : PlaceholderWithSpaceModifierLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

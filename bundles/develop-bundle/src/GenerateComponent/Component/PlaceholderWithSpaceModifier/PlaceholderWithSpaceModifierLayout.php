<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\PlaceholderWithSpaceModifier;

use App\JsonTemplate\Component\Layout\LayoutInterface;

// phpcs:disable Generic.Files.LineLength.MaxExceeded
enum PlaceholderWithSpaceModifierLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Section/PlaceholderWithSpaceModifier/layout/default/placeholder_with_space_modifier-default.html.twig',
        };
    }
}

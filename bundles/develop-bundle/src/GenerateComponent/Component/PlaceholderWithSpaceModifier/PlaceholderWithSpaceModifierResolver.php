<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\PlaceholderWithSpaceModifier;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;

final class PlaceholderWithSpaceModifierResolver extends AbstractSpaceResolver
{
    public static function getSupportedComponent(): string
    {
        return PlaceholderWithSpaceModifierComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(PlaceholderWithSpaceModifierLayout::class);
    }
}

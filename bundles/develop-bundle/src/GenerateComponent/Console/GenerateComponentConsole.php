<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Console;

use App\JsonTemplate\Component\ComponentNamespaceMapper;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ChoiceQuestion;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Visymo\DevelopBundle\GenerateComponent\Generator\ComponentGenerator;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentName;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentNameFactory;
use Visymo\Filesystem\File\Iterator\FileIteratorInterface;

#[AsCommand(
    name       : 'develop:generate-component',
    description: 'Generate component with a default layout'
)]
final class GenerateComponentConsole extends Command
{
    private const string ARGUMENT_SECTION_NAME   = 'section_name';
    private const string ARGUMENT_COMPONENT_NAME = 'component_name';

    public function __construct(
        private readonly FileIteratorInterface $placeholderComponentFileIterator,
        private readonly ComponentNamespaceMapper $componentNamespaceMapper,
        private readonly ComponentNameFactory $componentNameFactory,
        private readonly ComponentGenerator $componentGenerator
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_SECTION_NAME,
                InputArgument::REQUIRED,
                'Component section name in PascalCase (like Ads, Generic)',
            )
            ->addArgument(
                self::ARGUMENT_COMPONENT_NAME,
                InputArgument::REQUIRED,
                'Component name in PascalCase',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        /** @var QuestionHelper $questionHelper */
        $questionHelper = $this->getHelper('question');

        try {
            $destinationComponentName = $this->getDestinationComponentName(
                sectionName  : $input->getArgument(self::ARGUMENT_SECTION_NAME),
                componentName: $input->getArgument(self::ARGUMENT_COMPONENT_NAME),
            );
        } catch (\Throwable $exception) {
            $output->writeln(
                sprintf('<error>%s</error>', $exception->getMessage()),
            );

            return Command::FAILURE;
        }
        $sourceComponentName = $this->getSourceComponentName($questionHelper, $input, $output);

        $question = new ConfirmationQuestion(
            sprintf(
                '<info>Allow to create %s with type "%s"?</info> (y/n): ',
                $destinationComponentName->namespace,
                $destinationComponentName->getType(),
            ),
            false,
        );

        if (!(bool)$questionHelper->ask($input, $output, $question)) {
            return Command::FAILURE;
        }

        $this->componentGenerator->buildFiles(
            sourceComponentName     : $sourceComponentName,
            destinationComponentName: $destinationComponentName,
        );

        return Command::SUCCESS;
    }

    private function getDestinationComponentName(string $sectionName, string $componentName): ComponentName
    {
        $fullyQualifiedNamespace = sprintf(
            'App\Component\%s\%2$s\%2$sComponent',
            $sectionName,
            $componentName,
        );
        $destinationComponentName = $this->componentNameFactory->create(
            fullyQualifiedNamespace: $fullyQualifiedNamespace,
        );

        if ($this->componentNamespaceMapper->hasType($destinationComponentName->getType())) {
            throw new \InvalidArgumentException(
                sprintf('Component type "%s" already exists', $destinationComponentName->getType()),
            );
        }

        return $destinationComponentName;
    }

    private function getSourceComponentName(
        QuestionHelper $questionHelper,
        InputInterface $input,
        OutputInterface $output
    ): ComponentName
    {
        $placeholderComponentNamespaces = $this->getPlaceholderComponentNamespaces();
        $question = new ChoiceQuestion(
            'Which placeholder component would you copy from?',
            array_keys($placeholderComponentNamespaces),
        );
        $question->setErrorMessage('Choice %s is invalid.');

        $answer = $questionHelper->ask($input, $output, $question);
        $fullyQualifiedNamespace = $placeholderComponentNamespaces[$answer];

        return $this->componentNameFactory->create(
            fullyQualifiedNamespace: $fullyQualifiedNamespace,
        );
    }

    /**
     * @return string[]
     */
    private function getPlaceholderComponentNamespaces(): array
    {
        $namespacePrefix = (string)preg_replace('~^(.+Bundle\\\).+$~', '$1', __NAMESPACE__);
        $placeholderComponents = [];

        foreach ($this->placeholderComponentFileIterator->iterate() as $componentFile) {
            $namespace = (string)preg_replace('~.+src/(.+)\.php$~', '$1', $componentFile->getRealFilePath());
            $namespace = str_replace('/', '\\', $namespace);
            $namespace = sprintf(
                '%s%s',
                $namespacePrefix,
                $namespace,
            );

            $componentName = explode('\\', $namespace);
            $componentName = end($componentName);

            $placeholderComponents[$componentName] = $namespace;
        }

        return $placeholderComponents;
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Console;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\DevelopBundle\GenerateComponent\Generator\ComponentLayoutGenerator;

#[AsCommand(
    name       : 'develop:generate-component-layout',
    description: 'Generate a component layout'
)]
final class GenerateComponentLayoutConsole extends Command
{
    private const string ARGUMENT_SECTION_NAME   = 'section_name';
    private const string ARGUMENT_COMPONENT_NAME = 'component_name';
    private const string ARGUMENT_LAYOUT_NAME    = 'layout_name';

    public function __construct(
        private readonly ComponentLayoutGenerator $componentLayoutGenerator
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_SECTION_NAME,
                InputArgument::REQUIRED,
                'Component section name in PascalCase (like Ads, Generic)',
            )
            ->addArgument(
                self::ARGUMENT_COMPONENT_NAME,
                InputArgument::REQUIRED,
                'Component name in PascalCase',
            )
            ->addArgument(
                self::ARGUMENT_LAYOUT_NAME,
                InputArgument::REQUIRED,
                'Layout name in PascalCase or titleCase',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $layoutName = $input->getArgument(self::ARGUMENT_LAYOUT_NAME);

            if (preg_match('~^[a-z][a-z\d]+$~i', $layoutName) !== 1) {
                throw new \InvalidArgumentException(
                    sprintf(
                        'Invalid layout name "%s" given. It must start with a letter and should only contain letters and numbers.',
                        $layoutName,
                    ),
                );
            }

            $this->componentLayoutGenerator->generate(
                section  : $input->getArgument(self::ARGUMENT_SECTION_NAME),
                component: $input->getArgument(self::ARGUMENT_COMPONENT_NAME),
                layout   : $layoutName,
            );
        } catch (\Throwable $exception) {
            $output->writeln(
                sprintf('<error>%s</error>', $exception->getMessage()),
            );

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}

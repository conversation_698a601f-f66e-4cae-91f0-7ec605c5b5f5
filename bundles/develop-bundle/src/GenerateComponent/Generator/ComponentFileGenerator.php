<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Generator;

use Visymo\DevelopBundle\GenerateComponent\Generator\FileReplacer\ComponentFileReplacerInterface;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentLayout;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentName;
use Visymo\Filesystem\Directory\DirectoryFactory;
use Visymo\Filesystem\Directory\DirectoryInterface;
use Visymo\Filesystem\File\FileFactory;
use Visymo\Filesystem\File\FileInterface;
use Visymo\Filesystem\File\Iterator\FileIterator;

final class ComponentFileGenerator
{
    /** @var array<string, ComponentFileReplacerInterface> */
    private array $fileReplacers = [];

    /**
     * @param iterable<ComponentFileReplacerInterface> $fileReplacers
     */
    public function __construct(
        private readonly FileFactory $fileFactory,
        private readonly DirectoryFactory $directoryFactory,
        private readonly string $componentPath,
        private readonly string $developBundleComponentPath,
        iterable $fileReplacers
    )
    {
        foreach ($fileReplacers as $fileReplacer) {
            $this->fileReplacers[$fileReplacer->supportsFileExtension()] = $fileReplacer;
        }
    }

    public function buildLayoutFiles(
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout,
        DirectoryInterface $sourceLayoutDirectory,
        DirectoryInterface $destinationComponentDirectory
    ): void
    {
        $sourceDirectory = $sourceLayoutDirectory->getChild($sourceLayout->titleCase);
        $destinationLayoutDirectory = $destinationComponentDirectory->getChild('layout');

        if (!$destinationLayoutDirectory->exists()) {
            $destinationLayoutDirectory->create();
        }

        $destinationDirectory = $destinationLayoutDirectory->getChild($destinationLayout->titleCase)->create();

        $this->copyFiles(
            sourceComponentName     : $sourceComponentName,
            sourceLayout            : $sourceLayout,
            destinationComponentName: $destinationComponentName,
            destinationLayout       : $destinationLayout,
            sourceDirectory         : $sourceDirectory,
            destinationDirectory    : $destinationDirectory,
        );
    }

    public function copyFiles(
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout,
        DirectoryInterface $sourceDirectory,
        DirectoryInterface $destinationDirectory
    ): void
    {
        $sourceFileIterator = new FileIterator(
            sprintf('%s/*', $sourceDirectory->getDirectoryPath()),
            $this->fileFactory,
        );

        foreach ($sourceFileIterator->iterate() as $sourceFile) {
            $destinationFile = $this->getDestinationFile(
                sourceComponentName     : $sourceComponentName,
                sourceLayout            : $sourceLayout,
                destinationComponentName: $destinationComponentName,
                destinationLayout       : $destinationLayout,
                sourceFile              : $sourceFile,
                destinationDirectory    : $destinationDirectory,
            );

            $this->copyFileContent(
                sourceComponentName     : $sourceComponentName,
                sourceLayout            : $sourceLayout,
                destinationComponentName: $destinationComponentName,
                destinationLayout       : $destinationLayout,
                sourceFile              : $sourceFile,
                destinationFile         : $destinationFile,
            );
        }
    }

    public function getComponentSourceDirectory(ComponentName $sourceComponentName): DirectoryInterface
    {
        $sourcePath = sprintf(
            '%s/%s',
            $this->developBundleComponentPath,
            $sourceComponentName->pascalCase,
        );

        return $this->directoryFactory->create($sourcePath);
    }

    public function getComponentDestinationDirectory(ComponentName $destinationComponentName): DirectoryInterface
    {
        $destinationPath = sprintf(
            '%s/%s/%s',
            $this->componentPath,
            $destinationComponentName->section,
            $destinationComponentName->pascalCase,
        );

        return $this->directoryFactory->create($destinationPath);
    }

    private function copyFileContent(
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout,
        FileInterface $sourceFile,
        FileInterface $destinationFile
    ): void
    {
        $content = $sourceFile->readContent();
        $fileReplacer = $this->fileReplacers[$destinationFile->getExtension()] ?? null;

        if ($fileReplacer !== null) {
            $content = $fileReplacer->replaceContent(
                content                 : $content,
                sourceComponentName     : $sourceComponentName,
                sourceLayout            : $sourceLayout,
                destinationComponentName: $destinationComponentName,
                destinationLayout       : $destinationLayout,
            );
        }

        $destinationFile->writeContent($content);
    }

    private function getDestinationFile(
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout,
        FileInterface $sourceFile,
        DirectoryInterface $destinationDirectory
    ): FileInterface
    {
        $fileReplacer = $this->fileReplacers[$sourceFile->getExtension()] ?? null;

        if ($fileReplacer === null) {
            throw new \InvalidArgumentException(
                sprintf('File extension %s is not supported', $sourceFile->getExtension()),
            );
        }

        $componentFileName = $fileReplacer->replaceFileName(
            fileName                : $sourceFile->getFileName(),
            sourceComponentName     : $sourceComponentName,
            sourceLayout            : $sourceLayout,
            destinationComponentName: $destinationComponentName,
            destinationLayout       : $destinationLayout,
        );

        $filePath = sprintf(
            '%s/%s',
            $destinationDirectory->getDirectoryPath(),
            $componentFileName,
        );

        return $this->fileFactory->create($filePath);
    }
}

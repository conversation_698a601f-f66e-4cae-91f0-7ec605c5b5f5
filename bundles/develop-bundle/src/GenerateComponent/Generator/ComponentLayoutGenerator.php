<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Generator;

use App\JsonTemplate\Component\ComponentNamespaceMapper;
use Visymo\DevelopBundle\GenerateComponent\Component\Placeholder\PlaceholderComponent;
use Visymo\DevelopBundle\GenerateComponent\Component\PlaceholderWithSpaceModifier\PlaceholderWithSpaceModifierComponent;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentLayout;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentLayoutFactory;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentName;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentNameFactory;
use Visymo\Filesystem\File\FileFactory;

final readonly class ComponentLayoutGenerator
{
    public function __construct(
        private ComponentNameFactory $componentNameFactory,
        private ComponentNamespaceMapper $componentNamespaceMapper,
        private FileFactory $fileFactory,
        private ComponentFileGenerator $componentFileGenerator,
        private ComponentLayoutFactory $componentLayoutFactory,
        private string $componentPath
    )
    {
    }

    public function generate(string $section, string $component, string $layout): void
    {
        $fullyQualifiedNamespace = sprintf(
            'App\Component\%s\%2$s\%2$sComponent',
            $section,
            $component,
        );
        $destinationComponentName = $this->componentNameFactory->create(
            fullyQualifiedNamespace: $fullyQualifiedNamespace,
        );
        $destinationLayout = $this->componentLayoutFactory->create($layout);
        $destinationComponentDirectory = $this->componentFileGenerator
            ->getComponentDestinationDirectory($destinationComponentName);

        $this->validateComponentLayout(
            fullyQualifiedNamespace : $fullyQualifiedNamespace,
            destinationComponentName: $destinationComponentName,
            destinationLayout       : $destinationLayout,
        );

        $sourceComponentName = $this->componentNameFactory->create(
            fullyQualifiedNamespace: $this->getPlaceholderComponent($fullyQualifiedNamespace),
        );
        $sourceComponentDirectory = $this->componentFileGenerator
            ->getComponentSourceDirectory($sourceComponentName);

        $this->componentFileGenerator->buildLayoutFiles(
            sourceComponentName          : $sourceComponentName,
            sourceLayout                 : $this->componentLayoutFactory->create('default'),
            destinationComponentName     : $destinationComponentName,
            destinationLayout            : $destinationLayout,
            sourceLayoutDirectory        : $sourceComponentDirectory->getChild('layout'),
            destinationComponentDirectory: $destinationComponentDirectory,
        );
        $this->addLayoutToComponent($destinationComponentName, $destinationLayout);
    }

    private function validateComponentLayout(
        string $fullyQualifiedNamespace,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): void
    {
        if (!$this->componentNamespaceMapper->hasType($destinationComponentName->getType())) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Component "%s" with type "%s" was not found',
                    $fullyQualifiedNamespace,
                    $destinationComponentName->getType(),
                ),
            );
        }

        $layoutCases = $this->getLayoutCases($destinationComponentName);

        if (array_key_exists(mb_strtoupper($destinationLayout->snakeCase), $layoutCases)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Layout "%s" already exists in component layout of "%s"',
                    $destinationLayout->snakeCase,
                    $fullyQualifiedNamespace,
                ),
            );
        }
    }

    private function getPlaceholderComponent(string $fullyQualifiedNamespace): string
    {
        $reflection = new \ReflectionClass($fullyQualifiedNamespace);
        $withSpaceModifiers = $reflection->hasProperty('componentSpaceModifiers');

        return $withSpaceModifiers
            ? PlaceholderWithSpaceModifierComponent::class
            : PlaceholderComponent::class;
    }

    private function addLayoutToComponent(
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): void
    {
        $layoutCases = $this->getLayoutCases($destinationComponentName);
        $layoutCases[mb_strtoupper($destinationLayout->snakeCase)] = $destinationLayout;
        ksort($layoutCases);

        // Find case with the longest name
        $longestCaseNameLength = array_map(
            static fn (string $caseName) => strlen($caseName),
            array_keys($layoutCases),
        );
        $longestCaseNameLength = max($longestCaseNameLength);

        $twigTemplatePrefix = sprintf(
            '@component/%s/%s/layout',
            $destinationComponentName->section,
            $destinationComponentName->pascalCase,
        );
        $fileLayoutCases = [];
        $fileLayoutTwigTemplates = [];

        foreach ($layoutCases as $caseName => $layoutCase) {
            $fileLayoutCases[] = sprintf(
                '%scase %s = \'%s\';',
                str_repeat(' ', 4),
                str_pad($caseName, $longestCaseNameLength),
                $layoutCase->kebabCase,
            );
            $fileLayoutTwigTemplates[] = sprintf(
                '%sself::%s => \'%s/%s/%s-%s.html.twig\',',
                str_repeat(' ', 12),
                str_pad($caseName, $longestCaseNameLength),
                $twigTemplatePrefix,
                $layoutCase->titleCase,
                $destinationComponentName->snakeCase,
                $layoutCase->snakeCase,
            );
        }

        $componentLayoutFilePath = sprintf(
            '%s/%s/%s/%sLayout.php',
            $this->componentPath,
            $destinationComponentName->section,
            $destinationComponentName->pascalCase,
            $destinationComponentName->pascalCase,
        );
        $layoutFile = $this->fileFactory->create($componentLayoutFilePath);
        $layoutFileContents = $layoutFile->readContent();

        // Replace cases
        $layoutFileContents = (string)preg_replace(
            '~\h+case [A-Za-z0-9-_\s=\';]+?;~mUi',
            implode("\n", $fileLayoutCases),
            $layoutFileContents,
        );

        // Replace Twig templates
        $layoutFileContents = (string)preg_replace(
            '~(getTwigTemplate.+match\h?\(\$this\)\h?\{\s).+(\s+})~sU',
            sprintf('\\1%s\\2', implode("\n", $fileLayoutTwigTemplates)),
            $layoutFileContents,
        );

        $layoutFile->writeContent($layoutFileContents);
    }

    /**
     * @return array<string, ComponentLayout>
     */
    private function getLayoutCases(ComponentName $destinationComponentName): array
    {
        $fullyQualifiedNamespace = sprintf(
            'App\Component\%s\%2$s\%2$sLayout',
            $destinationComponentName->section,
            $destinationComponentName->pascalCase,
        );

        $reflection = new \ReflectionClass($fullyQualifiedNamespace);
        $layoutCases = [];

        /** @var \BackedEnum|string $layoutCase */
        foreach ($reflection->getConstants() as $layoutCase) {
            if (!is_object($layoutCase) || $layoutCase::class !== $fullyQualifiedNamespace) {
                continue;
            }

            if (preg_match('~^[A-Z0-9_]+$~', $layoutCase->name) !== 1) {
                continue;
            }

            $layoutCases[$layoutCase->name] = $this->componentLayoutFactory->create(
                (string)$layoutCase->value,
            );
        }

        return $layoutCases;
    }
}

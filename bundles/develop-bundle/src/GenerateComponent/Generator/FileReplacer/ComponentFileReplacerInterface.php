<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Generator\FileReplacer;

use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentLayout;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentName;

interface ComponentFileReplacerInterface
{
    public function supportsFileExtension(): string;

    public function replaceFileName(
        string $fileName,
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): string;

    public function replaceContent(
        string $content,
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): string;
}

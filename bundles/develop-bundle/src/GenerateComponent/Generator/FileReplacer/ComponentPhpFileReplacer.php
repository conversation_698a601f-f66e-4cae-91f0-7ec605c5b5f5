<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Generator\FileReplacer;

use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentLayout;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentName;

final class ComponentPhpFileReplacer implements ComponentFileReplacerInterface
{
    public function supportsFileExtension(): string
    {
        return 'php';
    }

    public function replaceFileName(
        string $fileName,
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): string
    {
        // With layout
        $name = str_replace(
            sprintf('%s%s', $sourceComponentName->pascalCase, $sourceLayout->pascalCase),
            sprintf('%s%s', $destinationComponentName->pascalCase, $destinationLayout->pascalCase),
            $fileName,
        );

        if ($name !== $fileName) {
            return $name;
        }

        // Without layout
        return str_replace(
            $sourceComponentName->pascalCase,
            $destinationComponentName->pascalCase,
            $fileName,
        );
    }

    public function replaceContent(
        string $content,
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): string
    {
        // Namespace
        $content = str_replace(
            sprintf('namespace %s;', $sourceComponentName->namespace),
            sprintf('namespace %s;', $destinationComponentName->namespace),
            $content,
        );

        // Use
        $content = str_replace(
            sprintf('use %s\\', $sourceComponentName->namespace),
            sprintf('use %s\\', $destinationComponentName->namespace),
            $content,
        );

        // Twig layout file
        $content = str_replace(
            sprintf(
                '@component/Section/%s/layout/%s/%s-%s.html.twig',
                $sourceComponentName->pascalCase,
                $sourceLayout->titleCase,
                $sourceComponentName->snakeCase,
                $sourceLayout->snakeCase,
            ),
            sprintf(
                '@component/%s/%s/layout/%s/%s-%s.html.twig',
                $destinationComponentName->section,
                $destinationComponentName->pascalCase,
                $destinationLayout->titleCase,
                $destinationComponentName->snakeCase,
                $destinationLayout->snakeCase,
            ),
            $content,
        );

        // Type
        $content = str_replace(
            sprintf('\'%s\'', $sourceComponentName->getType()),
            sprintf('\'%s\'', $destinationComponentName->getType()),
            $content,
        );

        // Component name prefix
        $content = str_replace(
            $sourceComponentName->pascalCase,
            $destinationComponentName->pascalCase,
            $content,
        );

        return $content;
    }
}

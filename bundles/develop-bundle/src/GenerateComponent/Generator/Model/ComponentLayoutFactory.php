<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Generator\Model;

use Symfony\Component\String\UnicodeString;

final class ComponentLayoutFactory
{
    public function create(string $name): ComponentLayout
    {
        $name = (new UnicodeString($name))->camel()->toString();
        $pascalCase = ucfirst($name);
        $titleCase = lcfirst($name);
        $snakeCase = (new UnicodeString($pascalCase))->snake()->toString();
        $snakeCase = (string)preg_replace('/([a-z])([\d])|([\d])([a-z])/i', '$1_$2', $snakeCase);
        $kebabCase = str_replace('_', '-', $snakeCase);

        return new ComponentLayout(
            pascalCase: $pascalCase,
            titleCase : $titleCase,
            snakeCase : $snakeCase,
            kebabCase : $kebabCase,
        );
    }
}

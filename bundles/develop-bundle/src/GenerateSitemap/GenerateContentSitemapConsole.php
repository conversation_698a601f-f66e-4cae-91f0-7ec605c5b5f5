<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateSitemap;

use App\Sitemap\Helper\SitemapHelper;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Visymo\Shared\Domain\Locale\Locale;

#[AsCommand(
    name       : 'develop:generate-content-sitemap',
    description: '[PoC] Generate content sitemap for a certain brand'
)]
class GenerateContentSitemapConsole extends Command
{
    private const string ARGUMENT_BRAND       = 'brand';
    private const string ARGUMENT_LOCALE      = 'locale';
    private const string ARGUMENT_SOURCE_FILE = 'source_file';

    public function __construct(
        private readonly DomainToBrandMapReader $domainToBrandMapReader,
        private readonly WebsiteConfigurationRepository $websiteConfigurationRepository,
        private readonly RouterInterface $router,
        private readonly SitemapHelper $sitemapHelper
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_BRAND,
                InputArgument::REQUIRED,
                'The brand which the sitemap is for',
            )
            ->addArgument(
                self::ARGUMENT_LOCALE,
                InputArgument::REQUIRED,
                'The locale the sitemap is generated for',
            )
            ->addArgument(
                self::ARGUMENT_SOURCE_FILE,
                InputArgument::REQUIRED,
                'The source file for the content pages',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $brandSlug = $input->getArgument(self::ARGUMENT_BRAND);
        $locale = $input->getArgument(self::ARGUMENT_LOCALE);
        $sourceFile = $input->getArgument(self::ARGUMENT_SOURCE_FILE);
        $brandSlugs = $this->domainToBrandMapReader->getBrands();

        if (!in_array($brandSlug, $brandSlugs, true)) {
            $output->writeln(sprintf('Brand %s not found in the list of brandSlugs', $brandSlug));

            return Command::FAILURE;
        }

        $websiteConfiguration = $this->websiteConfigurationRepository->getForBrand($brandSlug);
        $domain = $websiteConfiguration->getDomainForLocale($locale);
        Locale::validateCode($locale);
        $locale = new Locale($locale);
        $file = file_get_contents(sprintf('resource/%s', $sourceFile), true);

        if ($file === false) {
            $output->writeln('Failed to read content pages JSON file');

            return Command::FAILURE;
        }

        $jsonData = json_decode($file, true, 512, JSON_THROW_ON_ERROR);

        if (!is_array($jsonData)) {
            $output->writeln('Failed to decode content pages JSON file');

            return Command::FAILURE;
        }

        $urls = $this->generateContentPageUrls($jsonData, $domain);
        $this->sitemapHelper->createXMLDocument($urls, sprintf('content_pages_%s', strtolower($locale->getRegion()->getCode())));

        return Command::SUCCESS;
    }

    /**
     * @param array<string, mixed> $jsonData
     *
     * @return array<string>
     */
    private function generateContentPageUrls(
        array $jsonData,
        string $domain
    ): array
    {
        $contentPageUrls = [];

        foreach ($jsonData as $contentPage) {
            $url = $this->router->generate(
                'route_article',
                [
                    'content_page_slug'      => $contentPage['slug'],
                    'content_page_public_id' => $contentPage['public_id'],
                    'q'                      => $contentPage['query'],
                ],
                UrlGeneratorInterface::ABSOLUTE_URL,
            );

            $contentPageUrls[] = str_replace('http://localhost', sprintf('https://%s', $domain), $url);
        }

        return $contentPageUrls;
    }
}

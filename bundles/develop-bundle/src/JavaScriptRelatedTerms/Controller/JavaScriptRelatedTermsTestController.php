<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JavaScriptRelatedTerms\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class JavaScriptRelatedTermsTestController extends AbstractController
{
    #[Route(
        path     : '/dev/javascript-related-terms-content',
        name     : 'route_develop_javascript_related_terms_content',
        methods  : ['GET'],
        condition: 'service(\'route_checker_javascript_related_terms_content\').check()'
    )]
    public function javaScriptRelatedTermsContent(): Response
    {
        $requiredParameters = [
            'rac'  => 'best hotel in New York',
            'asid' => 'ip_vt_ch10',
        ];

        $optionalParameters = [
            //            'locale'=> 'fr_FR',
            //            'q'          => 'Hotel New York',
            //            'de'       => 'm',
            //            'gclid'    => 'gclid123',
            //            'msclkid'  => 'msclkid123',
            //            'fbclid'   => 'fbclid123',
            //            'clid'     => 'clid123',
            //            'sclid'    => 'sclid123',
            //            'style_id' => '1234',
            //            'terms' => [
            //                'hotel in New York',
            //                'New York state',
            //                'best hotels in New York',
            //                'hotel california',
            //            ],
        ];

        $endpointUrl = $this->generateUrl(
            'route_javascript_related_terms_content',
            [
                // All optional parameters are commented out
                ...$requiredParameters,
                ...$optionalParameters,
                'debug_info' => 1,
            ],
        );

        return $this->render(
            '@develop/javascript_related_terms/javascript_related_terms_test.html.twig',
            [
                'title'               => 'javascript-related-terms-content',
                'endpoint_url'        => $endpointUrl,
                'endpoint_url_pretty' => urldecode($endpointUrl),
            ],
        );
    }

    #[Route(
        path     : '/dev/javascript-related-terms-search',
        name     : 'route_develop_javascript_related_terms_search',
        methods  : ['GET'],
        condition: 'service(\'route_checker_javascript_related_terms_search\').check()'
    )]
    public function javaScriptRelatedTermsSearch(): Response
    {
        $requiredParameters = [
            'q'    => 'Hotel in Washington',
            'asid' => 'ip_vt_ch10',
        ];

        $optionalParameters = [
            //            'locale'=> 'fr_FR',
            //            'q'          => 'Hotel New York',
            //            'de'       => 'm',
            //            'gclid'    => 'gclid123',
            //            'msclkid'  => 'msclkid123',
            //            'fbclid'   => 'fbclid123',
            //            'clid'     => 'clid123',
            //            'sclid'    => 'sclid123',
            //            'style_id' => '1234',
        ];

        $endpointUrl = $this->generateUrl(
            'route_javascript_related_terms_search',
            [
                // All optional parameters are commented out
                ...$requiredParameters,
                ...$optionalParameters,
                'debug_info' => 1,
            ],
        );

        return $this->render(
            '@develop/javascript_related_terms/javascript_related_terms_test.html.twig',
            [
                'title'               => 'javascript-related-terms-search',
                'endpoint_url'        => $endpointUrl,
                'endpoint_url_pretty' => urldecode($endpointUrl),
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\Console;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\DevelopBundle\JsonSchema\Generator\GroupComponentLayoutJsonSchemaGenerator;
use Visymo\DevelopBundle\JsonSchema\Generator\JsonTemplateJsonSchemaGenerator;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

#[AsCommand(
    name       : 'develop:update-json-schema',
    description: 'Update JSON schema files for JSON templates and group component layouts'
)]
final class UpdateJsonSchemaConsole extends Command
{
    public function __construct(
        private readonly SerializedFileInterface $jsonTemplateJsonSchemaFile,
        private readonly JsonTemplateJsonSchemaGenerator $jsonTemplateJsonSchemaGenerator,
        private readonly SerializedFileInterface $groupComponentLayoutJsonSchemaFile,
        private readonly GroupComponentLayoutJsonSchemaGenerator $groupComponentLayoutJsonSchemaGenerator
    )
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // JSON Template
        $data = $this->jsonTemplateJsonSchemaGenerator->generate();
        $this->jsonTemplateJsonSchemaFile->writeContent($data);

        // Group Component Layout
        $data = $this->groupComponentLayoutJsonSchemaGenerator->generate();
        $this->groupComponentLayoutJsonSchemaFile->writeContent($data);

        $output->writeln('<info>JSON schema files updated</info>');

        return Command::SUCCESS;
    }
}

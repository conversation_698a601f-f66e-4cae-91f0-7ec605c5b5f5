<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\Generator;

use Visymo\DevelopBundle\JsonSchema\Component\ComponentSchema;

final class GroupComponentLayoutJsonSchemaGenerator extends AbstractJsonSchemaGenerator
{
    /**
     * @return mixed[]
     */
    public function generate(): array
    {
        $componentSchemas = $this->getComponentSchemas();
        $componentsData = $this->getComponentsData($componentSchemas);
        [$componentsData, $propertyDefinitions] = $this->optimizeComponentPropertyDefinitions(
            $componentsData,
        );

        return [
            '$id'                  => 'https://www.visymo.com/group_component_layout.schema.json',
            '$schema'              => 'http://json-schema.org/draft-07/schema#',
            'additionalProperties' => false,
            'type'                 => 'object',
            'definitions'          => [
                'property'   => $propertyDefinitions,
                'component'  => [
                    'anyOf' => array_map(
                        static fn (ComponentSchema $componentSchema) => [
                            '$ref' => $componentSchema->getDefinitionReference(),
                        ],
                        $componentSchemas,
                    ),
                ],
                'components' => $componentsData,
            ],
            'required'             => ['components'],
            'properties'           => [
                'components' => [
                    'additionalProperties' => false,
                    'default'              => [],
                    'items'                => [
                        '$ref' => '#/definitions/component',
                    ],
                    'type'                 => 'array',
                ],
            ],
        ];
    }
}

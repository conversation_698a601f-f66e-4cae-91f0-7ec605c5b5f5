<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\OptionsResolver;

use App\Http\Url\UrlValidationRouter;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionDefinitionInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class DebugComponentOptionDefinition extends DebugOptionDefinition implements ComponentOptionDefinitionInterface
{
    private ?string $rendersComponentWithInstanceOf = null;

    private bool $rendersMultipleComponents = false;

    private ?DebugComponentOptionsResolver $nestedResolver = null;

    public function __construct(
        string $field,
        private readonly ComponentResolverInterface $componentResolver,
        private readonly ComponentResolverLocator $componentResolverLocator,
        private readonly UrlValidationRouter $urlValidationRouter
    )
    {
        parent::__construct($field);
    }

    public function getRendersComponentWithInstanceOf(): ?string
    {
        return $this->rendersComponentWithInstanceOf;
    }

    public function setRendersComponentWithInstanceOf(?string $rendersComponentWithInstanceOf): self
    {
        $this->rendersComponentWithInstanceOf = $rendersComponentWithInstanceOf;

        return $this;
    }

    public function rendersMultipleComponents(): bool
    {
        return $this->rendersMultipleComponents;
    }

    public function setRendersMultipleComponents(): self
    {
        $this->rendersMultipleComponents = true;

        return $this;
    }

    public function setNestedResolver(callable $callback): ComponentOptionDefinitionInterface
    {
        $this->nestedResolver = new DebugComponentOptionsResolver(
            componentResolver       : $this->componentResolver,
            componentResolverLocator: $this->componentResolverLocator,
            urlValidationRouter     : $this->urlValidationRouter,
            isNestedResolver        : true,
        );
        $callback($this->nestedResolver);

        if (!$this->nestedResolver->isPrototype()) {
            $this->type = OptionType::TYPE_OBJECT;
        }

        return $this;
    }

    public function getNestedResolver(): ?DebugComponentOptionsResolver
    {
        return $this->nestedResolver;
    }
}

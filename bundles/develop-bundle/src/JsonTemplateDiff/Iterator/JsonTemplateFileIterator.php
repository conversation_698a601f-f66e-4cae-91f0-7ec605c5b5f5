<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Iterator;

use Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIteratorFactoryInterface;
use Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIteratorInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

readonly class JsonTemplateFileIterator implements SerializedFileIteratorInterface
{
    /**
     * @param string[] $globPatterns
     */
    public function __construct(
        private SerializedFileIteratorFactoryInterface $serializedFileIteratorFactory,
        private array $globPatterns
    )
    {
    }

    /**
     * @return iterable<SerializedFileInterface>
     */
    public function iterate(): iterable
    {
        foreach ($this->globPatterns as $globPattern) {
            $serializedFileIterator = $this->serializedFileIteratorFactory->create($globPattern);

            foreach ($serializedFileIterator->iterate() as $file) {
                yield $file;
            }
        }
    }
}

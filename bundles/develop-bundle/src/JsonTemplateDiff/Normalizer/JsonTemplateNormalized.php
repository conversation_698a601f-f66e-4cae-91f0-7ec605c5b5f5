<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Normalizer;

use App\Generic\Device\Device;
use App\GroupComponent\GroupComponentModule;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

final class JsonTemplateNormalized
{
    public readonly string $module;

    public readonly ?Device $device;

    /** @var mixed[] */
    private array $normalizedData = [];

    /** @var array<string, true> */
    private array $componentTypes = [];

    /** @var array<string, array<string, true>> */
    private array $componentLayouts = [];

    public function __construct(
        public readonly SerializedFileInterface $jsonTemplateFile
    )
    {
        $this->device = $this->getDevice();
        $this->module = $this->getModule();
    }

    public function isGroupComponentModule(): bool
    {
        return $this->module === GroupComponentModule::getModuleName();
    }

    /**
     * @return mixed[]
     */
    public function getNormalizedData(): array
    {
        return $this->normalizedData;
    }

    /**
     * @param mixed[] $normalizedData
     */
    public function setNormalizedData(array $normalizedData): self
    {
        $this->normalizedData = $normalizedData;

        return $this;
    }

    public function getNormalizedDataAsJson(): string
    {
        return json_encode(
            $this->normalizedData,
            JSON_UNESCAPED_SLASHES | JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT,
        );
    }

    public function addComponentType(string $componentType): self
    {
        $this->componentTypes[$componentType] = true;

        return $this;
    }

    /**
     * @return string[]
     */
    public function getComponentTypes(): array
    {
        return array_keys($this->componentTypes);
    }

    public function hasComponentType(string $componentType): bool
    {
        return $this->componentTypes[$componentType] ?? false;
    }

    public function addComponentLayout(string $componentType, string $layout): self
    {
        $this->componentLayouts[$componentType][$layout] = true;

        return $this;
    }

    /**
     * @return string[]
     */
    public function getComponentLayouts(string $componentType): array
    {
        return array_keys($this->componentLayouts[$componentType] ?? []);
    }

    public function hasComponentLayout(string $componentType, string $layout): bool
    {
        return $this->componentLayouts[$componentType][$layout] ?? false;
    }

    private function getDevice(): ?Device
    {
        $fileName = $this->jsonTemplateFile->getFileName();

        foreach (Device::cases() as $device) {
            if (str_contains($fileName, sprintf('_%s', $device->value))) {
                return $device;
            }
        }

        return null;
    }

    private function getModule(): string
    {
        if (str_starts_with($this->jsonTemplateFile->getFileName(), 'group-')) {
            return GroupComponentModule::getModuleName();
        }

        $module = explode('/', $this->jsonTemplateFile->getRealFilePath());

        return array_slice($module, -2, 1)[0];
    }
}

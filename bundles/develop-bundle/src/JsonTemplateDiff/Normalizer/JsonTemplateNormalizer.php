<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Normalizer;

use App\Component\Generic\Container\ContainerComponent;
use App\Component\Generic\Container\ContainerResolver;
use App\Component\Generic\Group\GroupResolver;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\Component\Layout\ComponentLayoutHelper;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Parent\ParentComponentResolverInterface;
use App\JsonTemplate\Template\JsonTemplateResolver;
use Visymo\DevelopBundle\JsonTemplateDiff\Request\JsonTemplateDiffRequestInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

final readonly class JsonTemplateNormalizer
{
    public function __construct(
        private ComponentResolverLocator $componentResolverLocator,
        private ComponentLayoutHelper $componentLayoutHelper,
        private ComponentResolverDecoratorInterface $componentResolverDecorator,
        private JsonTemplateDiffRequestInterface $jsonTemplateDiffRequest
    )
    {
    }

    public function normalize(SerializedFileInterface $jsonTemplateFile): JsonTemplateNormalized
    {
        $jsonTemplateNormalized = new JsonTemplateNormalized(
            jsonTemplateFile: $jsonTemplateFile,
        );

        if ($jsonTemplateNormalized->isGroupComponentModule()) {
            $this->normalizeGroupComponentJsonTemplate($jsonTemplateNormalized);
        } else {
            $this->normalizeJsonTemplate($jsonTemplateNormalized);
        }

        return $jsonTemplateNormalized;
    }

    private function normalizeGroupComponentJsonTemplate(JsonTemplateNormalized $jsonTemplateNormalized): void
    {
        $data = $jsonTemplateNormalized->jsonTemplateFile->getContents();
        $data[GroupResolver::KEY_COMPONENTS] = $this->sortComponents(
            componentsData        : $data[GroupResolver::KEY_COMPONENTS],
            jsonTemplateNormalized: $jsonTemplateNormalized,
        );

        $jsonTemplateNormalized->setNormalizedData($data);
    }

    private function normalizeJsonTemplate(JsonTemplateNormalized $jsonTemplateNormalized): void
    {
        $data = $jsonTemplateNormalized->jsonTemplateFile->getContents();

        // Options
        if (array_key_exists(JsonTemplateResolver::KEY_OPTIONS, $data)) {
            if ($data[JsonTemplateResolver::KEY_OPTIONS] === []) {
                unset($data[JsonTemplateResolver::KEY_OPTIONS]);
            } else {
                ksort($data[JsonTemplateResolver::KEY_OPTIONS]);
            }
        }

        $data[JsonTemplateResolver::KEY_CONTAINER] = $this->normalizeContainerComponentData(
            $data[JsonTemplateResolver::KEY_CONTAINER] ?? [],
            $jsonTemplateNormalized,
        );

        // Sort all components
        $componentsData = $this->sortComponents(
            componentsData        : $data[JsonTemplateResolver::KEY_COMPONENTS],
            jsonTemplateNormalized: $jsonTemplateNormalized,
        );
        unset($data[JsonTemplateResolver::KEY_COMPONENTS]);

        ksort($data);

        // Add components as last
        $data[JsonTemplateResolver::KEY_COMPONENTS] = $componentsData;

        $jsonTemplateNormalized->setNormalizedData($data);
    }

    /**
     * @param mixed[] $componentsData
     *
     * @return mixed[]
     */
    private function sortComponents(
        array $componentsData,
        JsonTemplateNormalized $jsonTemplateNormalized
    ): array
    {
        foreach ($componentsData as $key => $componentData) {
            if (!array_key_exists(ComponentInterface::KEY_TYPE, $componentData)) {
                continue;
            }

            $componentsData[$key] = $this->sortComponent(
                componentData         : $componentData,
                jsonTemplateNormalized: $jsonTemplateNormalized,
            );
        }

        return $componentsData;
    }

    /**
     * @param mixed[] $componentData
     *
     * @return mixed[]
     */
    private function sortComponent(
        array $componentData,
        JsonTemplateNormalized $jsonTemplateNormalized
    ): array
    {
        $componentType = $componentData[ComponentInterface::KEY_TYPE];
        unset($componentData[ComponentInterface::KEY_TYPE]);
        $componentResolver = $this->componentResolverLocator->getResolver($componentType);

        // Resolving all components can be very expensive while using the JSON Template Diff Tool
        $resolveComponentData = $this->jsonTemplateDiffRequest->getResolveComponentOptions() === true
                                && $this->jsonTemplateDiffRequest->getComponentType() === $componentType;

        if ($resolveComponentData) {
            $componentData = $componentResolver->resolve($componentData, $this->componentResolverDecorator);
        }

        $jsonTemplateNormalized->addComponentType($componentType);
        $componentData = $this->normalizeLayoutValue(
            componentType         : $componentType,
            componentData         : $componentData,
            jsonTemplateNormalized: $jsonTemplateNormalized,
        );

        ksort($componentData);

        // Place `type` as first property
        // Place `layout` as second property (if exists)
        $componentLayout = $componentData[LayoutInterface::KEY] ?? null;
        unset($componentData[LayoutInterface::KEY]);
        $priorityComponentData = [
            ComponentInterface::KEY_TYPE => $componentType,
        ];

        if ($componentLayout !== null) {
            $priorityComponentData[LayoutInterface::KEY] = $componentLayout;
        }

        $componentData = [
            ...$priorityComponentData,
            ...$componentData,
        ];

        return $this->normalizeChildComponents(
            componentData         : $componentData,
            componentResolver     : $componentResolver,
            jsonTemplateNormalized: $jsonTemplateNormalized,
        );
    }

    /**
     * @param mixed[] $componentData
     *
     * @return mixed[]
     */
    private function normalizeContainerComponentData(
        array $componentData,
        JsonTemplateNormalized $jsonTemplateNormalized
    ): array
    {
        /** @var ContainerResolver $componentResolver */
        $componentResolver = $this->componentResolverLocator->getResolver(ContainerComponent::getType());
        $componentData[ComponentInterface::KEY_TYPE] = ContainerComponent::getType();

        foreach ($componentResolver->getChildComponentProperties() as $childComponentProperty) {
            $componentData[$childComponentProperty->property] = [];
        }

        $componentData = $this->sortComponent(
            componentData         : $componentData,
            jsonTemplateNormalized: $jsonTemplateNormalized,
        );
        unset($componentData[ComponentInterface::KEY_TYPE]);

        foreach ($componentResolver->getChildComponentProperties() as $childComponentProperty) {
            unset($componentData[$childComponentProperty->property]);
        }

        return $componentData;
    }

    /**
     * @param mixed[] $componentData
     *
     * @return mixed[]
     */
    private function normalizeLayoutValue(
        string $componentType,
        array $componentData,
        JsonTemplateNormalized $jsonTemplateNormalized
    ): array
    {
        if (!isset($componentData[LayoutInterface::KEY])) {
            $layout = $this->componentLayoutHelper->getComponentLayout($componentType);

            // Component has no layout
            if ($layout === null) {
                return $componentData;
            }

            $componentData[LayoutInterface::KEY] ??= $layout::getDefault()->value;
        }

        $jsonTemplateNormalized->addComponentLayout(
            componentType: $componentType,
            layout       : $componentData[LayoutInterface::KEY],
        );

        return $componentData;
    }

    /**
     * @param mixed[] $componentData
     *
     * @return mixed[]
     */
    private function normalizeChildComponents(
        array $componentData,
        ComponentResolverInterface $componentResolver,
        JsonTemplateNormalized $jsonTemplateNormalized
    ): array
    {
        if (!$componentResolver instanceof ParentComponentResolverInterface) {
            return $componentData;
        }

        foreach ($componentResolver->getChildComponentProperties() as $property) {
            if (($componentData[$property->property] ?? null) === null) {
                continue;
            }

            if ($property->containsOneComponent()) {
                $componentData[$property->property] = $this->sortComponent(
                    componentData         : $componentData[$property->property],
                    jsonTemplateNormalized: $jsonTemplateNormalized,
                );
            }

            if ($property->containsMultipleComponents()) {
                $componentData[$property->property] = $this->sortComponents(
                    $componentData[$property->property],
                    $jsonTemplateNormalized,
                );
            }
        }

        return $componentData;
    }
}

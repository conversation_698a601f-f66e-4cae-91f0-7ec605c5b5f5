<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Request;

use App\Generic\Device\Device;
use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class JsonTemplateDiffRequest implements JsonTemplateDiffRequestInterface
{
    private string $device;

    private string $moduleContentVersionId;

    private string $module;

    private string $query;

    private string $componentType;

    private string $componentLayout;

    private ?bool $resolveComponentOptions = null;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getDevice(): ?Device
    {
        if (!isset($this->device)) {
            $this->device = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_DEVICE,
                array_column(Device::cases(), 'value'),
            );
        }

        return Device::tryFrom($this->device);
    }

    public function getModuleContentVersionId(): ?string
    {
        if (!isset($this->moduleContentVersionId)) {
            $this->moduleContentVersionId = $this->requestManager->queryBag()
                ->getString(self::PARAMETER_MODULE_CONTENT_VERSION_ID);
        }

        return $this->requestPropertyNormalizer->getString($this->moduleContentVersionId);
    }

    public function getModule(): ?string
    {
        if (!isset($this->module)) {
            $this->module = $this->requestManager->queryBag()->getString(self::PARAMETER_MODULE);
        }

        return $this->requestPropertyNormalizer->getString($this->module);
    }

    public function getQuery(): ?string
    {
        if (!isset($this->query)) {
            $this->query = $this->requestManager->queryBag()->getString(self::PARAMETER_QUERY);
        }

        return $this->requestPropertyNormalizer->getString($this->query);
    }

    public function getComponentType(): ?string
    {
        if (!isset($this->componentType)) {
            $this->componentType = $this->requestManager->queryBag()->getString(
                self::PARAMETER_COMPONENT_TYPE,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->componentType);
    }

    public function getComponentLayout(): ?string
    {
        if (!isset($this->componentLayout)) {
            $this->componentLayout = $this->requestManager->queryBag()->getString(
                self::PARAMETER_COMPONENT_LAYOUT,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->componentLayout);
    }

    public function getResolveComponentOptions(): ?bool
    {
        if ($this->getComponentType() === null) {
            return null;
        }

        if ($this->resolveComponentOptions === null) {
            $this->resolveComponentOptions = $this->requestManager->queryBag()->getBool(
                self::PARAMETER_RESOLVE_COMPONENT_OPTIONS,
            );
        }

        return $this->resolveComponentOptions;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_MODULE_CONTENT_VERSION_ID => $this->getModuleContentVersionId(),
            self::PARAMETER_MODULE                    => $this->getModule(),
            self::PARAMETER_QUERY                     => $this->getQuery(),
            self::PARAMETER_DEVICE                    => $this->getDevice()?->value,
            self::PARAMETER_COMPONENT_TYPE            => $this->getComponentType(),
            self::PARAMETER_COMPONENT_LAYOUT          => $this->getComponentLayout(),
            self::PARAMETER_RESOLVE_COMPONENT_OPTIONS => $this->getResolveComponentOptions(),
        ];
    }
}

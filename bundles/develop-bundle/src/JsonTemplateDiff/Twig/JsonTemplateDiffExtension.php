<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Twig;

use S<PERSON>fony\Component\Routing\RouterInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilter;
use Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilterHighlighter;
use Visymo\DevelopBundle\JsonTemplateDiff\Normalizer\JsonTemplateNormalizer;
use Visymo\DevelopBundle\JsonTemplateDiff\Request\JsonTemplateDiffRequestInterface;
use Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateDiff;
use Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResult;

final class JsonTemplateDiffExtension extends AbstractExtension
{
    public function __construct(
        private readonly RouterInterface $router,
        private readonly JsonTemplateNormalizer $jsonTemplateNormalizer,
        private readonly JsonTemplateDiff $jsonTemplateDiff,
        private readonly JsonTemplateDiffFilterHighlighter $jsonTemplateDiffFilterHighlighter
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'json_template_diff_path',
                $this->generateJsonTemplateDiffPath(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'render_json_template_diff_active_result',
                $this->getJsonTemplateDiffActiveResult(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'render_json_template_diff_diff_result',
                $this->jsonTemplateDiff->getJsonTemplateDiffDiffResult(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    private function generateJsonTemplateDiffPath(
        JsonTemplateResult $jsonTemplateResult,
        JsonTemplateDiffFilter $jsonTemplateDiffFilter
    ): string
    {
        return $this->router->generate(
            'route_develop_json_template_diff',
            [
                JsonTemplateDiffRequestInterface::PARAMETER_MODULE_CONTENT_VERSION_ID => $jsonTemplateResult->moduleContentVersionId,
                JsonTemplateDiffRequestInterface::PARAMETER_MODULE                    => $jsonTemplateDiffFilter->module,
                JsonTemplateDiffRequestInterface::PARAMETER_COMPONENT_TYPE            => $jsonTemplateDiffFilter->componentType,
                JsonTemplateDiffRequestInterface::PARAMETER_COMPONENT_LAYOUT          => $jsonTemplateDiffFilter->componentLayout,
                JsonTemplateDiffRequestInterface::PARAMETER_RESOLVE_COMPONENT_OPTIONS => $jsonTemplateDiffFilter->resolveComponentOptions,
                JsonTemplateDiffRequestInterface::PARAMETER_DEVICE                    => $jsonTemplateDiffFilter->device?->value,
                JsonTemplateDiffRequestInterface::PARAMETER_QUERY                     => $jsonTemplateDiffFilter->query,
            ],
        );
    }

    private function getJsonTemplateDiffActiveResult(
        JsonTemplateResult $jsonTemplateResult,
        JsonTemplateDiffFilter $jsonTemplateDiffFilter
    ): string
    {
        $json = $this->jsonTemplateNormalizer
            ->normalize($jsonTemplateResult->jsonTemplateFile)
            ->getNormalizedDataAsJson();

        return $this->jsonTemplateDiffFilterHighlighter->highlight(
            $json,
            $jsonTemplateDiffFilter,
        );
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\LandingPage\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class LandingPageRequest implements LandingPageRequestInterface
{
    private string $brand;

    private bool $production;

    private string $splitTestVariant;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getBrand(): ?string
    {
        if (!isset($this->brand)) {
            $this->brand = $this->requestManager->queryBag()->getString(self::PARAMETER_BRAND);
        }

        return $this->requestPropertyNormalizer->getString($this->brand);
    }

    public function getProduction(): bool
    {
        if (!isset($this->production)) {
            $this->production = $this->requestManager->queryBag()->getBool(self::PARAMETER_PRODUCTION);
        }

        return $this->production;
    }

    public function getSplitTestVariant(): ?string
    {
        if (!isset($this->splitTestVariant)) {
            $this->splitTestVariant = $this->requestManager->queryBag()->getString(self::PARAMETER_SPLIT_TEST_VARIANT);
        }

        return $this->requestPropertyNormalizer->getString($this->splitTestVariant);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_BRAND              => $this->getBrand(),
            self::PARAMETER_PRODUCTION         => $this->getProduction(),
            self::PARAMETER_SPLIT_TEST_VARIANT => $this->getSplitTestVariant(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\LandingPage\Request;

use App\Http\Request\RequestInterface;

interface LandingPageRequestInterface extends RequestInterface
{
    public const string PARAMETER_PRODUCTION         = 'production';
    public const string PARAMETER_BRAND              = 'brand';
    public const string PARAMETER_SPLIT_TEST_VARIANT = 'split_test_variant';

    public function getProduction(): bool;

    public function getBrand(): ?string;

    public function getSplitTestVariant(): ?string;
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Menu;

use App\JavaScriptRelatedTerms\Settings\JavaScriptRelatedTermsSettings;

final class DevelopMenuHelper
{
    /** @var DevelopMenuItem[] */
    private array $menuItems;

    public function __construct(
        private readonly DevelopMenuItemFactory $developMenuItemFactory,
        private readonly JavaScriptRelatedTermsSettings $javaScriptRelatedTermsSettings
    )
    {
    }

    /**
     * @return DevelopMenuItem[]
     */
    public function getMenuItems(): array
    {
        if (!isset($this->menuItems)) {
            $this->createMenuItems();
        }

        return $this->menuItems;
    }

    public function getActiveMenuTitle(): string
    {
        foreach ($this->getMenuItems() as $menuItem) {
            if ($menuItem->active) {
                return $menuItem->label;
            }
        }

        return 'Develop Tools';
    }

    private function createMenuItems(): void
    {
        $this->menuItems = [
            $this->developMenuItemFactory->create(
                route  : 'route_develop_landing_page',
                label  : 'Landing Pages',
                enabled: true,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_javascript_related_terms_content',
                label  : 'JS Related Terms Content Tester',
                enabled: $this->javaScriptRelatedTermsSettings->contentEnabled,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_javascript_related_terms_search',
                label  : 'JS Related Terms Search Tester',
                enabled: $this->javaScriptRelatedTermsSettings->searchEnabled,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_component_monitor',
                label  : 'Component Monitor',
                enabled: true,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_json_template_diff',
                label  : 'JSON Template Diff Tool',
                enabled: true,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_robots_txt_diff',
                label  : 'Robots.txt Diff Tool',
                enabled: true,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_screenshot_diff',
                label  : 'Frontend Test Screenshot Tool',
                enabled: true,
            ),
            $this->developMenuItemFactory->create(
                route  : 'route_develop_split_test_monitor',
                label  : 'Split Test Monitor',
                enabled: true,
            ),
        ];
    }
}

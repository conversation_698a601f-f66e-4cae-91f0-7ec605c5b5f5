<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Menu\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\DevelopBundle\Menu\DevelopMenuHelper;

final class DevelopMenuExtension extends AbstractExtension
{
    public function __construct(
        private readonly DevelopMenuHelper $developMenuHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'develop_get_menu_items',
                $this->developMenuHelper->getMenuItems(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
            new TwigFunction(
                'develop_get_active_menu_title',
                $this->developMenuHelper->getActiveMenuTitle(...),
                [
                    'is_safe' => ['html'],
                ],
            ),
        ];
    }
}

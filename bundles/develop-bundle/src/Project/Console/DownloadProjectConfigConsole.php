<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Project\Console;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\ArtemisApiClient\Action\GetProjectConfig\GetProjectConfigRequest;
use Visymo\DevelopBundle\BrandConfig\ArtemisApiClient\ArtemisApiClientFactory;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

#[AsCommand(
    name       : 'develop:project-config:download',
    description: 'Download project config from Artemis'
)]
final class DownloadProjectConfigConsole extends Command
{
    private const string ARGUMENT_DEVELOP_ARTEMIS = 'develop_artemis';

    public function __construct(
        private readonly ArtemisApiClientFactory $artemisApiClientFactory,
        private readonly SerializedFileInterface $projectConfigJsonFile
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_DEVELOP_ARTEMIS,
                InputArgument::OPTIONAL,
                'Download from Artemis in develop',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $useDevelopArtemis = (bool)$input->getArgument(self::ARGUMENT_DEVELOP_ARTEMIS);
        $artemisApiClient = $useDevelopArtemis
            ? $this->artemisApiClientFactory->createForDevelop()
            : $this->artemisApiClientFactory->createForProduction();

        $getProjectConfigResponse = $artemisApiClient->getProjectConfig(
            new GetProjectConfigRequest(),
        );

        $this->projectConfigJsonFile->writeContent(
            $getProjectConfigResponse->response->getData(),
        );

        return Command::SUCCESS;
    }
}

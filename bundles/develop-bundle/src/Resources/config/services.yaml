# Symfony best practices for a bundle dictates that all services are defined manually instead of using autowiring.
# However, this introduces a lot of extra work. Since this bundle is only used internally, we've chosen to use
# autowiring.
#
# See: https://symfony.com/doc/current/bundles/best_practices.html#services
services:
    _defaults:
        autowire: true
        autoconfigure: true
        bind:
            $projectDir: '%kernel.project_dir%'

    Visymo\DevelopBundle\:
        resource: '../../*'
        exclude: '../../{DependencyInjection,Resources}'

    Visymo\DevelopBundle\Generic\Routing\RoutesLoader:
        tags:
            - { name: routing.loader }

    _instanceof:
        Visymo\DevelopBundle\Debug\LandingPageUrl\Account\Filter\LandingPageUrlAccountFilterInterface:
            tags: [ 'develop.debug.landing_page_url.landing_page_url_account_filter' ]

        Visymo\DevelopBundle\Debug\LandingPageUrl\Route\Factory\LandingPageRouteFactoryInterface:
            tags: [ 'develop.debug.landing_page_url.landing_page_route_factory' ]

        Visymo\DevelopBundle\GenerateComponent\Generator\FileReplacer\ComponentFileReplacerInterface:
            tags: [ 'develop.generate_component.component_file_replacer' ]

    ### Artemis API client ###
    brand_website.develop_bundle.artemis_api.guzzle.http_sync_client:
        class: Psr\Http\Client\ClientInterface
        factory: [ 'Http\Adapter\Guzzle7\Client', 'createWithConfig' ]
        bind:
            $config:
                timeout: 5
                headers: { 'User-Agent': 'Brand-Website-Visymo (+https://www.visymo.com)' }

    Visymo\DevelopBundle\BrandConfig\ArtemisApiClient\ArtemisApiClientFactory:
        arguments:
            $client: '@brand_website.develop_bundle.artemis_api.guzzle.http_sync_client'
            $apiUrl: '%env(ARTEMIS_API_URL)%'

    # JSON
    brand_website.develop.native_pretty_json.array_serializer:
        class: Visymo\Serializer\NativePrettyJsonArraySerializer
        arguments:
            $unescapedSlashes: true

    brand_website.develop.native_pretty_json.file_factory:
        class: Visymo\Filesystem\SerializedFile\Type\NativePrettyJsonFileFactory
        arguments:
            $nativePrettyJsonArraySerializer: '@brand_website.develop.native_pretty_json.array_serializer'

    # JSON Schema
    Visymo\DevelopBundle\JsonSchema\Component\ComponentSchemaFactory:
        arguments:
            $container: '@service_container'

    Visymo\DevelopBundle\JsonSchema\Console\UpdateJsonSchemaConsole:
        arguments:
            $jsonTemplateJsonSchemaFile: '@brand_websites.json_schema.json_template'
            $groupComponentLayoutJsonSchemaFile: '@brand_websites.json_schema.group_component_layout'

    # Brand config
    Visymo\DevelopBundle\BrandConfig\Console\DownloadBrandConfigConsole:
        arguments:
            $nativePrettyJsonFileFactory: '@brand_website.develop.native_pretty_json.file_factory'

    # Debug
    Visymo\DevelopBundle\Debug\LandingPageUrl\Account\LandingPageUrlAccountHelper:
        arguments:
            $accountFilters: !tagged_iterator develop.debug.landing_page_url.landing_page_url_account_filter
            $adProviderSettingsFactories: !tagged_iterator brand_website.website_settings.ad_provider_settings_factory

    Visymo\DevelopBundle\Debug\LandingPageUrl\Route\LandingPageRouteGenerator:
        arguments:
            $landingPageRouteFactories: !tagged_iterator develop.debug.landing_page_url.landing_page_route_factory

    # Component generator
    brand_website.develop.component_generator.placeholder_component_file_iterator_factory:
        class: Visymo\Filesystem\File\Iterator\FileIteratorFactory

    brand_website.develop.component_generator.placeholder_component_file_iterator:
        class: Visymo\Filesystem\File\Iterator\FileIterator
        factory: [ '@brand_website.develop.component_generator.placeholder_component_file_iterator_factory', 'create' ]
        arguments:
            $globPattern: '%kernel.project_dir%/bundles/develop-bundle/src/GenerateComponent/Component/**/*Component.php'

    Visymo\DevelopBundle\GenerateComponent\Console\GenerateComponentConsole:
        arguments:
            $placeholderComponentFileIterator: '@brand_website.develop.component_generator.placeholder_component_file_iterator'

    Visymo\DevelopBundle\GenerateComponent\Generator\ComponentFileGenerator:
        arguments:
            $fileReplacers: !tagged_iterator develop.generate_component.component_file_replacer
            $componentPath: '%kernel.project_dir%/src/Component'
            $developBundleComponentPath: '%kernel.project_dir%/bundles/develop-bundle/src/GenerateComponent/Component'

    Visymo\DevelopBundle\GenerateComponent\Generator\ComponentLayoutGenerator:
        arguments:
            $componentPath: '%kernel.project_dir%/src/Component'

    # JSON template diff
    brand_website.develop.json_template_diff.serialized_file_iterator_factory:
        class: Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIteratorFactory
        arguments:
            $serializedFileFactory: '@brand_website.develop.native_pretty_json.file_factory'

    brand_website.develop.json_template_diff.file_iterator:
        class: Visymo\DevelopBundle\JsonTemplateDiff\Iterator\JsonTemplateFileIterator
        arguments:
            $serializedFileIteratorFactory: '@brand_website.develop.json_template_diff.serialized_file_iterator_factory'
            $globPatterns:
                - '%kernel.project_dir%/resources/json_template_overrides/*/*/*.json'
                - '%kernel.project_dir%/src/Component/Generic/Group/layout/*/*.json'
                - '%kernel.project_dir%/resources/prototype/templates_json/*/*.json'
                - '%kernel.project_dir%/resources/shared/templates_json/*/*.json'

    Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResultRegistry:
        arguments:
            $jsonTemplateFileIterator: '@brand_website.develop.json_template_diff.file_iterator'

    Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilterFactory: ~
    Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilter:
        factory: [ '@Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilterFactory', 'create' ]

    # Module
    Visymo\DevelopBundle\Module\ModuleSettingsManager:
        public: true
        arguments:
            $container: '@service_container'
            $moduleSettingsFactories: !tagged_iterator { tag: 'brand_website.generic.module_settings_factories', default_index_method: 'getModuleName' }

    # Project config
    Visymo\DevelopBundle\Project\Console\DownloadProjectConfigConsole:
        arguments:
            $projectConfigJsonFile: '@brand_website.project_config.json_file'

    # Robots.txt diff
    brand_website.develop.robots_txt_diff.robots_txt_file_iterator:
        class: Visymo\Filesystem\File\Iterator\FileIterator
        arguments:
            $globPattern: '%kernel.project_dir%/../**/config/robots/robots*.txt'

    brand_website.develop.robots_txt_diff.shared_robots_txt_file_iterator:
        class: Visymo\Filesystem\File\Iterator\FileIterator
        arguments:
            $globPattern: '%brand_website.resources_dir%/config/robots/robots*.txt'

    Visymo\DevelopBundle\RobotsTxtDiff\Result\RobotsTxtResultRegistry:
        arguments:
            $sharedRobotsTxtFileIterator: '@brand_website.develop.robots_txt_diff.shared_robots_txt_file_iterator'
            $robotsTxtFileIterator: '@brand_website.develop.robots_txt_diff.robots_txt_file_iterator'

    # Screenshot diff
    brand_website.develop.screenshot_diff.expected_screenshot_file_iterator:
        class: Visymo\Filesystem\File\Iterator\FileIterator
        arguments:
            $globPattern: '%kernel.project_dir%/tests/Frontend/Resources/expected/**/*.png'

    Visymo\DevelopBundle\ScreenshotDiff\Result\ScreenshotDiffResultsFactory:
        arguments:
            $expectedScreenshotFileIterator: '@brand_website.develop.screenshot_diff.expected_screenshot_file_iterator'

    # Shared style
    Visymo\DevelopBundle\SharedStyle\Generator\SharedStyleConfigGenerator:
        arguments:
            $sharedStyleFile: '@brand_websites.shared_style.file'
            $sharedStylePath: '%brand_website.resources_dir%/shared_style'

    brand_website.translations.bundles_iterator:
        class: Visymo\Filesystem\File\Iterator\FileIterator
        arguments:
            $globPattern: '%brand_website.resources_dir%/translations/messages.en.yaml'

    brand_website.translations.projects_iterator:
        class: Visymo\Filesystem\File\Iterator\FileIterator
        arguments:
            $globPattern: '%kernel.project_dir%/translations/messages.en.yaml'

    Visymo\DevelopBundle\Translation\Console\TranslateToLocaleConsole:
        arguments:
            $bundlesTranslationIterator: '@brand_website.translations.bundles_iterator'
            $projectTranslationIterator: '@brand_website.translations.projects_iterator'
            $googleProjectId: '%env(GOOGLE_API_PROJECT_ID)%'
            $googleCredentials: '%env(json:GOOGLE_API_CREDENTIALS_JSON)%'
            $fileReaderFactory: '@Visymo\Filesystem\File\FileFactory'

    brand_website.develop_bundle.split_test_mapping:
        class: Visymo\Filesystem\File\FileInterface
        factory: [ '@Visymo\Filesystem\File\FileFactory', 'create' ]
        arguments:
            $filePath: '%kernel.project_dir%/split-test-mapping.yaml'

    Visymo\DevelopBundle\SplitTest\Helper\SplitTestVariantHelper:
        arguments:
            $splitTestTicketMappingFile: '@brand_website.develop_bundle.split_test_mapping'

    brand_website.brand-config.client:
        class: Psr\Http\Client\ClientInterface
        factory: [ 'Http\Adapter\Guzzle7\Client', 'createWithConfig' ]
        arguments:
            $config:
                -   verify: false

    Visymo\DevelopBundle\BrandDomains\Controller\BrandDomainsController:
        arguments:
            $domainToBrandMapSerializedFile: '@brand_website.website_settings.domain_to_brand_map.serialized_file'

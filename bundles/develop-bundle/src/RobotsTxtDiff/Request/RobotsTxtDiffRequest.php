<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\RobotsTxtDiff\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class RobotsTxtDiffRequest implements RobotsTxtDiffRequestInterface
{
    private string $project;

    private string $type;

    private int $version;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getType(): ?string
    {
        if (!isset($this->type)) {
            $this->type = $this->requestManager->queryBag()->getString(self::PARAMETER_TYPE);
        }

        return $this->requestPropertyNormalizer->getString($this->type);
    }

    public function getVersion(): ?int
    {
        if (!isset($this->version)) {
            $this->version = $this->requestManager->queryBag()->getUnsignedInt(self::PARAMETER_VERSION);
        }

        return $this->requestPropertyNormalizer->getInt($this->version);
    }

    public function getProject(): ?string
    {
        if (!isset($this->project)) {
            $this->project = $this->requestManager->queryBag()->getString(self::PARAMETER_PROJECT);
        }

        return $this->requestPropertyNormalizer->getString($this->project);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_PROJECT => $this->getProject(),
            self::PARAMETER_TYPE    => $this->getType(),
            self::PARAMETER_VERSION => $this->getVersion(),
        ];
    }
}

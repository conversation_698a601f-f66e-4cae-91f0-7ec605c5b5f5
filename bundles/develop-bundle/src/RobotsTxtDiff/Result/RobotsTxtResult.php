<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\RobotsTxtDiff\Result;

use Visymo\Filesystem\File\FileInterface;

readonly class RobotsTxtResult
{
    public function __construct(
        public FileInterface $file,
        public string $project,
        public string $type,
        public string $contentHash,
        public int $version
    )
    {
    }

    public function isOfSameTypeAndVersion(self $result): bool
    {
        return $result->project !== $this->project
               && $result->type === $this->type
               && $result->version === $this->version;
    }
}

<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\RobotsTxtDiff\Result;

use Visymo\Filesystem\File\FileInterface;

class RobotsTxtResultFactory
{
    public function create(
        FileInterface $file,
        string $type,
        string $contentHash,
        int $version
    ): RobotsTxtResult
    {
        return new RobotsTxtResult(
            $file,
            $this->getProject($file->getRealFilePath()),
            $type,
            $contentHash,
            $version,
        );
    }

    private function getProject(string $filePath): string
    {
        if (preg_match('~/brand-websites/brand-websites/([^/]+)/~', $filePath, $matches) === 1) {
            return $matches[1];
        }

        // Shared robots*.txt also now exist and can be used by any project
        return 'shared';
    }
}

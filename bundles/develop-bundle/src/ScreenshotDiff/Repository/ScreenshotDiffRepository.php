<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\ScreenshotDiff\Repository;

use Visymo\DevelopBundle\ScreenshotDiff\Result\ScreenshotDiffResult;
use Visymo\DevelopBundle\ScreenshotDiff\Result\ScreenshotDiffResults;
use Visymo\DevelopBundle\ScreenshotDiff\Result\ScreenshotDiffResultsFactory;

class ScreenshotDiffRepository
{
    private ScreenshotDiffResults $screenshotDiffResults;

    public function __construct(
        private readonly ScreenshotDiffResultsFactory $screenshotDiffResultsFactory
    )
    {
    }

    public function getScreenshotDiffResults(): ScreenshotDiffResults
    {
        if (!isset($this->screenshotDiffResults)) {
            $this->screenshotDiffResults = $this->screenshotDiffResultsFactory->create();
        }

        return $this->screenshotDiffResults;
    }

    public function clearAll(): void
    {
        foreach ($this->getScreenshotDiffResults()->getResults() as $result) {
            $result->comparisonFile->delete();
        }
    }

    public function clear(ScreenshotDiffResult $result): void
    {
        $result->comparisonFile->delete();
    }

    public function accept(ScreenshotDiffResult $result): void
    {
        $result->actualFile->copyTo($result->expectedFile->getRealFilePath());
        $result->comparisonFile->delete();
    }

    public function acceptAll(): void
    {
        foreach ($this->getScreenshotDiffResults()->getResults() as $result) {
            $this->accept($result);
        }
    }
}

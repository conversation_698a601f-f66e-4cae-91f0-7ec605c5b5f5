<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\ScreenshotDiff\Result;

readonly class ScreenshotDiffResults
{
    /**
     * @param ScreenshotDiffResult[] $results
     */
    public function __construct(private array $results)
    {
    }

    /**
     * @return ScreenshotDiffResult[]
     */
    public function getResults(): array
    {
        return $this->results;
    }

    public function getFirstResult(): ?ScreenshotDiffResult
    {
        return $this->results !== [] ? current($this->results) : null;
    }

    public function getResultByFrontendTest(string $frontendTest): ?ScreenshotDiffResult
    {
        foreach ($this->getResults() as $result) {
            if ($result->getFrontendTest() === $frontendTest) {
                return $result;
            }
        }

        return null;
    }

    /**
     * @return string[]
     */
    public function getDevices(): array
    {
        $devices = [];

        foreach ($this->results as $result) {
            $devices[$result->device] = true;
        }

        return array_keys($devices);
    }

    /**
     * @return ScreenshotDiffResult[]
     */
    public function getDeviceResults(string $device): array
    {
        return array_filter(
            $this->results,
            static fn (ScreenshotDiffResult $result) => $result->device === $device,
        );
    }
}

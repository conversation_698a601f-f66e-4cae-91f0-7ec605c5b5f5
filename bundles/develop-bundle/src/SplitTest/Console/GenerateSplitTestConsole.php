<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\SplitTest\Console;

use App\WebsiteSettings\Configuration\Import\WebsiteConfigurationImporter;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;
use Symfony\Bundle\FrameworkBundle\Command\AbstractConfigCommand;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\Filesystem\SerializedFile\Type\NativePrettyJsonFileFactory;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

#[AsCommand(
    name       : 'develop:generate-split-test',
    description: 'Generate and add split test to brand config file'
)]
final class GenerateSplitTestConsole extends AbstractConfigCommand
{
    private const string ARGUMENT_BRAND_SLUG     = 'brand_slug';
    private const string ARGUMENT_VARIANTS       = 'variants';
    private const string OPTION_CONTAINER_SUFFIX = 'container_suffix';

    private const array AVAILABLE_SPLIT_TEST_CHANNELS = [
        'ab_ta',
        'ab_tb',
        'ab_tc',
        'ab_td',
        'ab_te',
        'ab_tf',
        'ab_tg',
        'ab_th',
        'ab_ti',
        'ab_tj',
    ];

    public function __construct(
        private readonly DateTimeFactory $dateTimeFactory,
        private readonly WebsiteConfigurationFileRepository $websiteConfigurationFileRepository,
        private readonly WebsiteConfigurationImporter $websiteConfigurationImporter,
        private readonly NativePrettyJsonFileFactory $nativePrettyJsonFileFactory
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument(
            self::ARGUMENT_BRAND_SLUG,
            InputArgument::REQUIRED,
            'Brand slug',
        );
        $this->addArgument(
            self::ARGUMENT_VARIANTS,
            InputArgument::REQUIRED,
            'Split test variant; add multiple with comma separated values',
        );
        $this->addOption(
            self::OPTION_CONTAINER_SUFFIX,
            null,
            InputArgument::OPTIONAL,
            'Split test container suffix',
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $brandSlug = $input->getArgument(self::ARGUMENT_BRAND_SLUG);
        $variants = explode(',', $input->getArgument(self::ARGUMENT_VARIANTS));
        $variants = array_map('trim', $variants);
        $variants = array_filter($variants, static fn ($value) => $value !== '');
        $containerSuffix = $input->getOption(self::OPTION_CONTAINER_SUFFIX);

        if ($variants === []) {
            throw new \InvalidArgumentException('No variants provided');
        }

        $brandConfigJsonFile = $this->getBrandConfigJsonFile($brandSlug);
        $brandConfig = $brandConfigJsonFile->getContents();
        $splitTestsConfig = $brandConfig[WebsiteConfiguration::KEY_SPLIT_TESTS] ?? [];

        foreach ($variants as $variant) {
            if ($this->hasSplitTestWithVariant($splitTestsConfig, $variant)) {
                throw new \InvalidArgumentException(
                    sprintf('Split test with variant "%s" already exists.', $variant),
                );
            }
        }

        $splitTestId = $this->generateSplitTestId($splitTestsConfig);
        $splitTestsConfig[$splitTestId] = $this->getSplitTestData($variants, $containerSuffix);
        $brandConfig[WebsiteConfiguration::KEY_SPLIT_TESTS] = $splitTestsConfig;

        $brandConfigJsonFile->writeContent($brandConfig);
        $this->websiteConfigurationImporter->import($brandConfigJsonFile);

        return self::SUCCESS;
    }

    private function getBrandConfigJsonFile(string $brandSlug): SerializedFileInterface
    {
        $brandConfigPhpFile = $this->websiteConfigurationFileRepository
            ->getForBrand($brandSlug);

        return $this->nativePrettyJsonFileFactory
            ->create(
                str_replace('.php', '.json', $brandConfigPhpFile->getRealFilePath()),
            );
    }

    /**
     * @param mixed[] $splitTestsConfig
     */
    private function generateSplitTestId(array $splitTestsConfig): int
    {
        do {
            $splitTestId = random_int(1234, 99999);
        } while (isset($splitTestsConfig[$splitTestId]));

        return $splitTestId;
    }

    /**
     * @param mixed[] $splitTestsConfig
     */
    private function hasSplitTestWithVariant(array $splitTestsConfig, string $variant): bool
    {
        foreach ($splitTestsConfig as $splitTest) {
            $splitTestVariants = $splitTest['variants'] ?? [];

            foreach ($splitTestVariants as $splitTestVariant) {
                if ($splitTestVariant['variant'] === $variant) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param string[] $variants
     *
     * @return mixed[]
     */
    private function getSplitTestData(array $variants, ?string $containerSuffix): array
    {
        $dateNow = $this->dateTimeFactory->createNow();
        $variantPercentage = (int)round(100 / (count($variants) + 1));
        $availableChannels = self::AVAILABLE_SPLIT_TEST_CHANNELS;

        $data = [
            'activation'       => [
                'device'     => null,
                'service'    => null,
                'date_start' => $dateNow->format('Y-m-d H:i:s'),
                'date_end'   => $dateNow->modify('+10 weeks')->format('Y-m-d H:i:s'),
                'domains'    => null,
                'routes'     => null,
            ],
            'control_channels' => [
                'landingpage' => $this->getSplitTestChannel($availableChannels),
                'advertised'  => $this->getSplitTestChannel($availableChannels),
            ],
            'variants'         => [],
        ];

        foreach ($variants as $variant) {
            $data['variants'][] = [
                'variant'          => $variant,
                'container_suffix' => $containerSuffix,
                'channels'         => [
                    'landingpage' => $this->getSplitTestChannel($availableChannels),
                    'advertised'  => $this->getSplitTestChannel($availableChannels),
                ],
                'percentage'       => $variantPercentage,
            ];
        }

        return $data;
    }

    /**
     * @param string[] $availableChannels
     *
     * phpcs:disable SlevomatCodingStandard.PHP.DisallowReference.DisallowedPassingByReference
     */
    private function getSplitTestChannel(array &$availableChannels): string
    {
        $channel = array_shift($availableChannels);

        if ($channel === null) {
            throw new \RuntimeException('Run out of split test channels');
        }

        return $channel;
    }
}

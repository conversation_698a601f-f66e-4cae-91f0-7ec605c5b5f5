<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\SplitTest\FileSplitTest;

class FileSplitTestCollection
{
    private const string JIRA_BROWSE_URL = 'https://visymo.atlassian.net/browse';

    /**  @var array<string, FileSplitTest> $fileSplitTests */
    private array $fileSplitTests = [];

    public function __construct(
        public readonly ?string $ticketNumber = null
    )
    {
    }

    public function add(FileSplitTest $fileSplitTest): self
    {
        $filePath = $fileSplitTest->file->getRealFilePath();

        $this->fileSplitTests[$filePath] = $fileSplitTest;

        return $this;
    }

    /**
     * @return array<string, FileSplitTest>
     */
    public function getFileSplitTests(): array
    {
        ksort($this->fileSplitTests);

        return $this->fileSplitTests;
    }

    public function getTicketUrl(): ?string
    {
        if ($this->ticketNumber !== null) {
            return sprintf('%s/%s', self::JIRA_BROWSE_URL, $this->ticketNumber);
        }

        return null;
    }
}

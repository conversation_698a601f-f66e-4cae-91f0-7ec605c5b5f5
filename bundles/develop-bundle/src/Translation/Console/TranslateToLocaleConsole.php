<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Translation\Console;

use Google\Cloud\Translate\V3\Client\TranslationServiceClient;
use Google\Cloud\Translate\V3\TranslateTextRequest;
use Google\Cloud\Translate\V3\Translation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Yaml\Yaml;
use Visymo\Filesystem\File\FileInterface;
use Visymo\Filesystem\File\Iterator\FileIteratorInterface;
use Visymo\Filesystem\File\Reader\FileReaderFactoryInterface;
use Visymo\Shared\Domain\Locale\Locale;

#[AsCommand(
    name       : 'develop:translate',
    description: 'Translate from EN translation files to other locales.'
)]
class TranslateToLocaleConsole extends Command
{
    private const string ARGUMENT_LOCALES                     = 'locales';
    private const string OPTION_FORCE                         = 'force';
    private const string OPTION_REMOVE_KEYS_MISSING_FROM_BASE = 'remove-missing-from-base';
    private const string DEFAULT_LOCALE                       = 'en';

    private TranslationServiceClient $translationClient;

    private bool $force;

    private bool $removeMissingFromBase;

    /**
     * @param array<string, string> $googleCredentials
     */
    public function __construct(
        private readonly FileIteratorInterface $bundlesTranslationIterator,
        private readonly FileIteratorInterface $projectTranslationIterator,
        private readonly string $googleProjectId,
        private readonly array $googleCredentials,
        private readonly Filesystem $fileSystem,
        private readonly FileReaderFactoryInterface $fileReaderFactory
    )
    {
        parent::__construct();
    }

    public function getTranslationClient(): TranslationServiceClient
    {
        if (isset($this->translationClient)) {
            return $this->translationClient;
        }

        $this->translationClient = new TranslationServiceClient(
            [
                'credentials' => $this->googleCredentials,
            ],
        );

        return $this->translationClient;
    }

    protected function configure(): void
    {
        $this->addArgument(
            self::ARGUMENT_LOCALES,
            InputArgument::OPTIONAL,
            'Comma separated list of locales. Leave empty to update all locales.',
        );

        $this->addOption(
            self::OPTION_FORCE,
            'f',
            null,
            'Override existing translations. Only translate new strings if not set.',
        );

        $this->addOption(
            self::OPTION_REMOVE_KEYS_MISSING_FROM_BASE,
            'r',
            null,
            sprintf('Remove keys in locales that do not exist in the base locale (%s).', self::DEFAULT_LOCALE),
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $locales = $input->getArgument(self::ARGUMENT_LOCALES);
        $this->force = $input->getOption(self::OPTION_FORCE);
        $this->removeMissingFromBase = $input->getOption(self::OPTION_REMOVE_KEYS_MISSING_FROM_BASE);
        $localesArray = $locales !== null ? explode(',', $locales) : [];

        if ($localesArray === []) {
            $localesArray = array_map(
                static fn ($case) => explode('_', $case)[0],
                Locale::SUPPORTED_LOCALES,
            );
            $localesArray = array_unique($localesArray);
        }

        foreach ($localesArray as $locale) {
            if ($locale === self::DEFAULT_LOCALE) {
                continue;
            }

            $output->writeln(sprintf('<info>Translating locale %s</info>', $locale));

            foreach ($this->bundlesTranslationIterator->iterate() as $file) {
                $output->writeln(sprintf('<info>Translating file %s</info>', $file->getFilePath()));
                $this->translateToLocale($locale, $file);
            }

            foreach ($this->projectTranslationIterator->iterate() as $file) {
                $output->writeln(sprintf('<info>Translating file %s</info>', $file->getFilePath()));
                $this->translateToLocale($locale, $file);
            }
        }

        $output->writeln('<info>Done generating translations</info>');

        return Command::SUCCESS;
    }

    private function translateToLocale(string $locale, FileInterface $file): void
    {
        $yamlContents = $file->readContent();
        $translations = Yaml::parse($yamlContents);
        $translatedToLocale = [];

        $fileName = str_replace(
            sprintf('.%s.', self::DEFAULT_LOCALE),
            sprintf('.%s.', $locale),
            $file->getFilePath(),
        );

        if ($this->force === false && $this->fileSystem->exists($fileName)) {
            $contents = $this->fileReaderFactory->create($fileName)
                ->readContent();

            $translatedToLocale = Yaml::parse($contents);

            if ($this->removeMissingFromBase) {
                $keysMissingInBase = array_keys(array_diff_key($translatedToLocale, $translations));

                foreach ($keysMissingInBase as $key) {
                    unset($translatedToLocale[$key]);
                }
            }

            $translations = array_diff_key($translations, $translatedToLocale);
        }

        if ($translations === []) {
            $this->saveTranslationsFile($fileName, $translatedToLocale);

            return;
        }

        $translationClient = $this->getTranslationClient();
        $response = $translationClient->translateText(
            TranslateTextRequest::build(
                sprintf('projects/%s', $this->googleProjectId),
                $locale,
                $this->getPreparedTranslations($translations),
            ),
        );

        $translationKeys = array_keys($translations);

        /** @var Translation $translation */
        foreach ($response->getTranslations() as $index => $translation) {
            $translationKey = $translationKeys[$index];
            $translatedResult = $this->prepareTranslation($translation->getTranslatedText());

            if (isset($translatedToLocale[$translationKey]) && $this->force) {
                $translatedToLocale[$translationKey] = $translatedResult;
            } elseif (!isset($translatedToLocale[$translationKey])) {
                $translatedToLocale[$translationKey] = $translatedResult;
            }
        }

        $this->saveTranslationsFile($fileName, $translatedToLocale);
    }

    /**
     * @param array<string, string> $translations
     *
     * @return array<string, string>
     */
    private function getPreparedTranslations(array $translations): array
    {
        $preparedTranslations = [];

        foreach ($translations as $key => $translation) {
            $preparedTranslations[$key] = (string)preg_replace(
                '/%(.*?)%/',
                '<span class="notranslate">$1</span>',
                $translation,
            );
        }

        return $preparedTranslations;
    }

    private function prepareTranslation(string $translation): string
    {
        $translation = (string)preg_replace('/<span class="notranslate">(.*?)<\/span>/', '%$1%', $translation);

        $translation = str_replace('% .', '%.', $translation);

        return $translation;
    }

    /**
     * @param array<string> $translatedToLocale
     */
    private function saveTranslationsFile(string $fileName, array $translatedToLocale): void
    {
        if ($this->fileSystem->exists($fileName)) {
            $this->fileSystem->remove($fileName);
        }

        ksort($translatedToLocale);

        $this->fileSystem->appendToFile(
            $fileName,
            Yaml::dump($translatedToLocale, 2, 4, Yaml::DUMP_MULTI_LINE_LITERAL_BLOCK),
        );
    }
}

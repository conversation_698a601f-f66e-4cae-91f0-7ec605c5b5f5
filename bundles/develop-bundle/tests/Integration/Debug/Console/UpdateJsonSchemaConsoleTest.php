<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\Tests\Integration\Debug\Console;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\DevelopBundle\JsonSchema\Console\UpdateJsonSchemaConsole;
use Visymo\DevelopBundle\JsonSchema\Generator\GroupComponentLayoutJsonSchemaGenerator;
use Visymo\DevelopBundle\JsonSchema\Generator\JsonTemplateJsonSchemaGenerator;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;

class UpdateJsonSchemaConsoleTest extends AbstractSymfonyIntegrationTest
{
    public function testRun(): void
    {
        $jsonTemplateJsonSchemaFileMock = $this->getJsonSchemaFileMock(
            'brand_websites.json_schema.json_template',
        );
        $groupComponentLayoutJsonSchemaFileMock = $this->getJsonSchemaFileMock(
            'brand_websites.json_schema.group_component_layout',
        );

        $inputMock = $this->createMock(InputInterface::class);
        $outputMock = $this->createMock(OutputInterface::class);

        /** @var JsonTemplateJsonSchemaGenerator $jsonTemplateJsonSchemaGenerator */
        $jsonTemplateJsonSchemaGenerator = self::getContainer()->get(JsonTemplateJsonSchemaGenerator::class);

        /** @var GroupComponentLayoutJsonSchemaGenerator $groupComponentLayoutJsonSchemaGenerator */
        $groupComponentLayoutJsonSchemaGenerator = self::getContainer()->get(GroupComponentLayoutJsonSchemaGenerator::class);

        $updateJsonSchemaConsole = new UpdateJsonSchemaConsole(
            jsonTemplateJsonSchemaFile             : $jsonTemplateJsonSchemaFileMock,
            jsonTemplateJsonSchemaGenerator        : $jsonTemplateJsonSchemaGenerator,
            groupComponentLayoutJsonSchemaFile     : $groupComponentLayoutJsonSchemaFileMock,
            groupComponentLayoutJsonSchemaGenerator: $groupComponentLayoutJsonSchemaGenerator,
        );
        $updateJsonSchemaConsole->run($inputMock, $outputMock);
    }

    private function getJsonSchemaFileMock(string $serviceId): SerializedFileInterface
    {
        /** @var SerializedFileInterface $jsonSchemaSerializedFile */
        $jsonSchemaSerializedFile = self::getContainer()->get($serviceId);
        $jsonSchema = $jsonSchemaSerializedFile->getContents();

        $jsonTemplateJsonSchemaFileMock = $this->createMock(SerializedFileInterface::class);
        $jsonTemplateJsonSchemaFileMock->expects($this->once())->method('writeContent')->with($jsonSchema);

        return $jsonTemplateJsonSchemaFileMock;
    }
}

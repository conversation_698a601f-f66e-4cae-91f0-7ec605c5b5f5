<?php

declare(strict_types=1);

namespace Visymo\PrototypeBundle\PillRelatedTerms\Model;

final readonly class PillRelatedTermCollection
{
    /**
     * @param PillRelatedTerm[] $pillRelatedTerms
     */
    public function __construct(
        public array $pillRelatedTerms
    )
    {
    }

    public function withTerm(PillRelatedTerm $pillRelatedTerm): self
    {
        $pillRelatedTerms = $this->pillRelatedTerms;
        $pillRelatedTerms[] = $pillRelatedTerm;

        return new self($pillRelatedTerms);
    }

    public function withoutTerm(PillRelatedTerm $pillRelatedTerm): self
    {
        $pillRelatedTerms = $this->pillRelatedTerms;
        $index = array_search($pillRelatedTerm, $pillRelatedTerms, true);

        if ($index === false) {
            return $this;
        }

        unset($pillRelatedTerms[$index]);

        return new self(array_values($pillRelatedTerms));
    }

    /**
     * @return string[]
     */
    public function getTerms(): array
    {
        $terms = array_map(
            static fn (PillRelatedTerm $pillRelatedTerm) => $pillRelatedTerm->term,
            $this->pillRelatedTerms,
        );

        return array_values($terms);
    }

    public function getAmount(): int
    {
        return count($this->pillRelatedTerms);
    }
}

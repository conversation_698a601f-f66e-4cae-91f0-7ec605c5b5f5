<?php

declare(strict_types=1);

namespace Visymo\PrototypeBundle\PillRelatedTerms\Twig;

use App\Http\Url\PersistentUrlParametersRouter;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\PrototypeBundle\PillRelatedTerms\Model\PillRelatedTerm;
use Visymo\PrototypeBundle\PillRelatedTerms\Registry\ActivePillRelatedTermCollectionRegistry;
use Visymo\PrototypeBundle\PillRelatedTerms\Request\PillRelatedTermsRequestInterface;
use Visymo\PrototypeBundle\PillRelatedTerms\Serializer\PillRelatedTermsStringSerializer;

final class PillRelatedTermsExtension extends AbstractExtension
{
    public function __construct(
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly PillRelatedTermsStringSerializer $pillRelatedTermsStringSerializer,
        private readonly ActivePillRelatedTermCollectionRegistry $activePillRelatedTermCollectionRegistry,
        private readonly RouteRegistry $routeRegistry
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'pill_related_term_path',
                $this->getPillRelatedTermPath(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    public function getPillRelatedTermPath(PillRelatedTerm $pillRelatedTerm, bool $add): string
    {
        $termCollection = $this->activePillRelatedTermCollectionRegistry->getActivePillRelatedTermCollection();

        if ($add) {
            $termCollection = $termCollection->withTerm($pillRelatedTerm);
        } else {
            $termCollection = $termCollection->withoutTerm($pillRelatedTerm);
        }

        $serializedPillRelatedTerms = $this->pillRelatedTermsStringSerializer->serialize(
            $termCollection->getTerms(),
        );

        return $this->persistentUrlParametersRouter->generate(
            $this->routeRegistry->getSearchRoute(),
            [
                SearchRequestInterface::PARAMETER_QUERY                                   => $pillRelatedTerm->query,
                PillRelatedTermsRequestInterface::PARAMETER_SERIALIZED_PILL_RELATED_TERMS => $serializedPillRelatedTerms,
            ],
        );
    }
}

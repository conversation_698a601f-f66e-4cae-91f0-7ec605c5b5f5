<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\BingAds;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Endpoint\BingAds\BingAdsRequestInterface;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\TrackingOrder\ClickCount\ClickCountRepository;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class PartnerBingAdsConversionFactory
{
    public function __construct(
        private BingAdsRequestInterface $bingAdsRequest,
        private TrackingOrderFactory $trackingOrderFactory,
        private ClickCountRepository $clickCountRepository
    )
    {
    }

    public function create(): Conversion
    {
        $adBlockNumber = $this->bingAdsRequest->getBlock();
        $adNumber = $this->bingAdsRequest->getRank();

        $trackingOrder = $this->trackingOrderFactory->onePerAdV2($adBlockNumber, $adNumber)
                         ?? $this->trackingOrderFactory->random();

        $clickCount = $this->clickCountRepository->increaseClickCount($trackingOrder);

        return new Conversion(
            trackingOrder: $trackingOrder,
            eventType    : ConversionEventType::CLICK_AD,
            extraLogData : [
                               ConversionLogExtra::AD_TYPE                  => 'bing',
                               ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => $clickCount,
                           ],
        );
    }
}

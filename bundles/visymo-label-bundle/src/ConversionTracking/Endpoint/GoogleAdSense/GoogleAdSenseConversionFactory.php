<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdSense;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseRequestInterface;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\Logging\ConversionVendor;
use App\ConversionTracking\TrackingOrder\ClickCount\ClickCountRepository;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class GoogleAdSenseConversionFactory
{
    public function __construct(
        private GoogleAdSenseRequestInterface $googleAdSenseRequest,
        private TrackingOrderFactory $trackingOrderFactory,
        private ClickCountRepository $clickCountRepository
    )
    {
    }

    public function create(): Conversion
    {
        $adBlockNumber = $this->googleAdSenseRequest->getBlock();
        $adNumber = $this->googleAdSenseRequest->getAd();

        $trackingOrder = $this->trackingOrderFactory->fromWebsiteSettings()
                         ?? $this->trackingOrderFactory->onePerAdV2($adBlockNumber, $adNumber)
                            ?? $this->trackingOrderFactory->random();

        $clickCount = $this->clickCountRepository->increaseClickCount($trackingOrder);

        return new Conversion(
            trackingOrder             : $trackingOrder,
            eventType                 : ConversionEventType::CLICK_AD,
            conversionVendor          : ConversionVendor::GOOGLE_ADSENSE,
            supportsConversionTracking: $trackingOrder->supportsOnlineConversion($clickCount),
            extraLogData              : [
                                            ConversionLogExtra::AD_TYPE                  => 'google',
                                            ConversionLogExtra::AD_BLOCK_NUMBER          => $adBlockNumber,
                                            ConversionLogExtra::AD_NUMBER                => $adNumber,
                                            ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => $clickCount,
                                        ],
        );
    }
}

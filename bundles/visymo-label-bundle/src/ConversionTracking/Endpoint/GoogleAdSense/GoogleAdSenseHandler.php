<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdSense;

use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Endpoint\EndpointResponse;
use App\ConversionTracking\Endpoint\EndpointResponseInterface;
use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseHandlerInterface;
use App\ConversionTracking\Logging\ConversionLogger;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTracker;

readonly class GoogleAdSenseHandler implements GoogleAdSenseHandlerInterface
{
    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private GoogleAdSenseConversionFactory $googleAdSenseConversionFactory,
        private VisymoConversionTracker $visymoConversionTracker,
        private ConversionLogger $conversionLogger,
        private PartnerGoogleAdSenseConversionFactory $partnerGoogleAdSenseConversionFactory,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function handle(): EndpointResponseInterface
    {
        $brandSettings = $this->brandSettingsHelper->getSettings();

        if ($brandSettings->getPartnerSlug() !== null) {
            $conversion = $this->partnerGoogleAdSenseConversionFactory->create();
            $this->conversionLogger->logConversion($conversion);

            return new EndpointResponse();
        }

        if (!$this->isSupported()) {
            return new EndpointResponse();
        }

        $conversion = $this->googleAdSenseConversionFactory->create();

        $conversionTrackingResult = $this->visymoConversionTracker->track($conversion);

        $this->conversionLogger->logConversion($conversion, $conversionTrackingResult);

        return new EndpointResponse(
            $conversionTrackingResult->getClientSideTrackingUrl(),
        );
    }

    private function isSupported(): bool
    {
        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        // Not valid when tracking entry is empty
        if ($trackingEntry->isEmpty) {
            return false;
        }

        return true;
    }
}

<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdSenseOnline;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\TrackingOrder\ClickCount\ClickCountRepository;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class PartnerGoogleAdSenseOnlineConversionFactory
{
    public function __construct(
        private TrackingOrderFactory $trackingOrderFactory,
        private ClickCountRepository $clickCountRepository
    )
    {
    }

    public function create(): Conversion
    {
        $trackingOrder = $this->trackingOrderFactory->onePerPageviewId()
                         ?? $this->trackingOrderFactory->random();

        $clickCount = $this->clickCountRepository->increaseClickCount($trackingOrder);

        return new Conversion(
            trackingOrder: $trackingOrder,
            eventType    : ConversionEventType::CLICK_AD,
            extraLogData : [
                               ConversionLogExtra::AD_TYPE                  => 'google',
                               ConversionLogExtra::AD_UNIQUE_AD_CLICK_COUNT => $clickCount,
                           ],
        );
    }
}

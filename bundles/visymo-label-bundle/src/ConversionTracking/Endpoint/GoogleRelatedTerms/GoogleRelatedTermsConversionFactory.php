<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleRelatedTerms;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Conversion\ConversionRelatedType;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\Logging\ConversionVendor;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class GoogleRelatedTermsConversionFactory
{
    public function __construct(
        private TrackingOrderFactory $trackingOrderFactory
    )
    {
    }

    public function create(): Conversion
    {
        $trackingOrder = $this->trackingOrderFactory->random();

        $extraLogData = [
            ConversionLogExtra::CONVERSION_RELATED_TYPE => ConversionRelatedType::GOOGLE_ADSENSE,
        ];

        return new Conversion(
            trackingOrder             : $trackingOrder,
            eventType                 : ConversionEventType::CLICK_RELATED,
            conversionVendor          : ConversionVendor::GOOGLE_ADSENSE_RELATED_TERMS,
            supportsConversionTracking: $trackingOrder->supportsOnlineConversion(1),
            extraLogData              : $extraLogData,
        );
    }
}

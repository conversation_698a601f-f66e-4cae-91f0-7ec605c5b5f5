<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\VisymoRelatedTerms;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Conversion\ConversionRelatedType;
use App\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsRequestInterface;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\Logging\ConversionVendor;
use App\ConversionTracking\TrackingOrder\TrackingOrderFactory;

readonly class VisymoRelatedTermsConversionFactory
{
    public function __construct(
        private TrackingOrderFactory $trackingOrderFactory,
        private VisymoRelatedTermsRequestInterface $visymoRelatedTermsConversionRequest
    )
    {
    }

    public function create(): Conversion
    {
        $trackingOrder = $this->trackingOrderFactory->random();

        $extraLogData = [
            ConversionLogExtra::PAGE_TERM               => $this->visymoRelatedTermsConversionRequest->getTerm(),
            ConversionLogExtra::CONVERSION_RELATED_TYPE => ConversionRelatedType::VISYMO,
        ];

        return new Conversion(
            trackingOrder             : $trackingOrder,
            eventType                 : ConversionEventType::CLICK_RELATED,
            conversionVendor          : ConversionVendor::VISYMO,
            supportsConversionTracking: false,
            extraLogData              : $extraLogData,
        );
    }
}

<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Blocker;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Helper\ConversionTrackingBlockingHelper;
use Psr\Log\LoggerInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerPluginInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerResult;

readonly class ConversionTrackingBlocker implements VisymoConversionTrackerPluginInterface
{
    public function __construct(
        private ConversionTrackingBlockingHelper $conversionTrackingBlockingHelper,
        private LoggerInterface $logger
    )
    {
    }

    public function handle(Conversion $conversion, VisymoConversionTrackerResult $result): ?bool
    {
        if ($this->conversionTrackingBlockingHelper->isBlocked()) {
            $this->logger->info(
                'VisymoLabel conversion tracking is prevented because tracking pixel is blocked',
                [
                    'conversion' => $conversion,
                ],
            );

            return $result->stopPropagation();
        }

        if (!$conversion->supportsConversionTracking) {
            $this->logger->info(
                'VisymoLabel conversion tracking is prevented because the conversion does not support tracking',
                [
                    'conversion' => $conversion,
                ],
            );

            return $result->stopPropagation();
        }

        return $result->continuePropagation();
    }
}

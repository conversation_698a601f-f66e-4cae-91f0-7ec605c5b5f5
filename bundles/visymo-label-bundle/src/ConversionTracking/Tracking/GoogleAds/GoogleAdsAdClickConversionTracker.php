<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Tracking\GoogleAds;

use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettingsHelper;
use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Logging\ConversionVendor;
use App\ConversionTracking\Tracking\GoogleAds\GoogleAdsConversionUrlFactory;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingHttpClient;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerPluginInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerResult;

readonly class GoogleAdsAdClickConversionTracker implements VisymoConversionTrackerPluginInterface
{
    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private GoogleAdsConversionUrlFactory $googleAdsConversionUrlFactory,
        private ConversionTrackingHttpClient $conversionTrackingHttpClient,
        private AccountSettingsHelper $accountSettingsHelper,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function handle(Conversion $conversion, VisymoConversionTrackerResult $result): ?bool
    {
        $accountService = $this->accountSettingsHelper->getSettings()?->service;
        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();
        $conversionTrackingSettings = $this->websiteSettingsHelper->getSettings()->getGoogleAdsConversionTracking();

        if ($accountService !== AccountService::GOOGLE_ADS
            || $conversion->eventType !== ConversionEventType::CLICK_AD
            || $conversion->conversionVendor === ConversionVendor::GOOGLE_AD_MANAGER
            || !$conversionTrackingSettings->isEnabled()
        ) {
            return $result->continuePropagation();
        }

        /**
         * For ProAdvisr we are running a test with pushing conversion using google tags.
         * So we don't want to track conversions for ProAdvisr via GoogleAdsClickConversionTracker.
         */
        if ($this->brandSettingsHelper->getSettings()->getSlug() === 'proadvisr') {
            // stopPropagation because otherwise double conversions.
            return $result->stopPropagation();
        }

        $conversionPixelUrl = $this->googleAdsConversionUrlFactory->create(
            conversionTrackingId   : $conversionTrackingSettings->getConversionTrackingId(),
            conversionTrackingLabel: $conversionTrackingSettings->getConversionTrackingLabel(),
            orderId                : $conversion->trackingOrder->getId(),
            clickId                : $trackingEntry->clickId,
        );

        $isTracked = $this->conversionTrackingHttpClient->sendRequest(
            $conversionPixelUrl,
        );

        return $result->finishTracking(
            $conversionPixelUrl,
            $isTracked,
            true,
        );
    }
}

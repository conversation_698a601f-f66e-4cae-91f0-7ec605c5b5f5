<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Tracking\MicrosoftAds;

use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettingsHelper;
use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingHttpClient;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerPluginInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerResult;

readonly class MicrosoftAdsConversionTracker implements VisymoConversionTrackerPluginInterface
{
    public function __construct(
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private MicrosoftAdsConversionUrlFactory $microsoftAdsConversionUrlFactory,
        private ConversionTrackingHttpClient $conversionTrackingHttpClient,
        private AccountSettingsHelper $accountSettingsHelper
    )
    {
    }

    public function handle(Conversion $conversion, VisymoConversionTrackerResult $result): ?bool
    {
        if ($conversion->eventType !== ConversionEventType::CLICK_AD) {
            return $result->continuePropagation();
        }

        $accountService = $this->accountSettingsHelper->getSettings()?->service;
        $websiteSettings = $this->websiteSettingsHelper->getSettings();
        $microsoftAdsConversionTracking = $websiteSettings->getMicrosoftAdsConversionTracking();

        if ($accountService !== AccountService::MICROSOFT_ADVERTISING
            || !$microsoftAdsConversionTracking->isEnabled()
        ) {
            return $result->continuePropagation();
        }

        $conversionPixelUrl = $this->microsoftAdsConversionUrlFactory->create(
            $microsoftAdsConversionTracking->getConversionTrackingId(),
            $microsoftAdsConversionTracking->getConversionTrackingLabel(),
            $conversion->trackingOrder->getId(),
        );

        $isTracked = $this->conversionTrackingHttpClient->sendRequest(
            $conversionPixelUrl,
        );

        return $result->finishTracking(
            $conversionPixelUrl,
            $isTracked,
            true,
        );
    }
}

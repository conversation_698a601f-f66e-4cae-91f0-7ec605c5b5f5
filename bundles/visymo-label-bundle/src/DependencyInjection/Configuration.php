<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\DependencyInjection;

use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

class Configuration implements ConfigurationInterface
{
    /**
     * @inheritDoc
     *
     * @noinspection NullPointerExceptionInspection
     */
    public function getConfigTreeBuilder(): TreeBuilder
    {
        $treeBuilder = new TreeBuilder('visymo_label');

        // @formatter:off
        $rootNodeChildren = $treeBuilder->getRootNode()->children();

        // Tracking
        $trackingNodeChildren = $rootNodeChildren
            ->arrayNode('tracking')
            ->addDefaultsIfNotSet()
            ->children();

        $trackingLoggingNodeChildren = $trackingNodeChildren->arrayNode('conversion_log')->addDefaultsIfNotSet()->children();
        $trackingLoggingNodeChildren
            ->booleanNode('file_enabled')->defaultTrue()->end()
            ->end();

        // @formatter:on

        return $treeBuilder;
    }
}

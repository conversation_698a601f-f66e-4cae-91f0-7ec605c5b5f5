<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\DependencyInjection;

use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Extension\Extension;
use Symfony\Component\DependencyInjection\Extension\PrependExtensionInterface;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

class VisymoLabelExtension extends Extension implements PrependExtensionInterface
{
    /**
     * @param mixed[] $configs
     */
    public function load(array $configs, ContainerBuilder $container): void
    {
        // load services
        $loader = new YamlFileLoader(
            $container,
            new FileLocator(__DIR__.'/../Resources/config'),
        );

        $loader->load('services.yaml');

        if ($container->getParameter('kernel.environment') === 'test') {
            $loader->load('services.test.yaml');
        }
    }

    public function prepend(ContainerBuilder $container): void
    {
        $bundles = (array)$container->getParameter('kernel.bundles');

        if (!array_key_exists('MonologBundle', $bundles)) {
            throw new \RuntimeException('Monolog bundle is required');
        }

        // process bundle configuration
        $configs = $container->getExtensionConfig($this->getAlias());
        $this->processConfiguration(new Configuration(), $configs);
    }
}

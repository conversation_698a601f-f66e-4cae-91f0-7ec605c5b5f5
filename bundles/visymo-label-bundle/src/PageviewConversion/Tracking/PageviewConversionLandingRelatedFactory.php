<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\PageviewConversion\Tracking;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\TrackingOrder\Type\RandomTrackingOrderFactory;

readonly class PageviewConversionLandingRelatedFactory
{
    public function __construct(
        private RandomTrackingOrderFactory $randomTrackingOrderFactory
    )
    {
    }

    public function create(): Conversion
    {
        $trackingOrder = $this->randomTrackingOrderFactory->create();

        return new Conversion(
            trackingOrder: $trackingOrder,
            eventType    : ConversionEventType::PAGEVIEW_LANDING_RELATED,
        );
    }
}

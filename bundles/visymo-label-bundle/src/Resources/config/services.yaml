# Symfony best practices for a bundle dictates that all services are defined manually instead of using autowiring.
# However, this introduces a lot of extra work. Since this bundle is only used internally, we've chosen to use
# autowiring.
#
# See: https://symfony.com/doc/current/bundles/best_practices.html#services
services:
    _defaults:
        autowire: true
        autoconfigure: true

    Visymo\VisymoLabelBundle\:
        resource: '../../*'
        exclude: '../../{DependencyInjection}'

    # Conversion Tracking
    App\ConversionTracking\Endpoint\BingAds\BingAdsHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\BingAds\BingAdsHandler'
    App\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerDisplayHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerDisplayHandler'
    App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseHandler'
    App\ConversionTracking\Endpoint\GoogleAdSenseOnline\GoogleAdSenseOnlineHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleAdSenseOnline\GoogleAdSenseOnlineHandler'
    App\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsHandler'
    App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchHandler'
    App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchConversionUrlGeneratorInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchConversionUrlGenerator'
    App\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsHandlerInterface: '@Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsHandler'
    App\PageviewConversion\Tracking\PageviewConversionLandingHandlerInterface: '@Visymo\VisymoLabelBundle\PageviewConversion\Tracking\PageviewConversionLandingHandler'
    App\PageviewConversion\Tracking\PageviewConversionLandingRelatedHandlerInterface: '@Visymo\VisymoLabelBundle\PageviewConversion\Tracking\PageviewConversionLandingRelatedHandler'

    Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Blocker\ConversionTrackingBlocker: ~
    Visymo\VisymoLabelBundle\ConversionTracking\Tracking\GoogleAds\GoogleAdsAdClickConversionTracker: ~
    Visymo\VisymoLabelBundle\ConversionTracking\Tracking\MicrosoftAds\MicrosoftAdsConversionTracker: ~
    Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Offline\OfflineConversionTracker: ~

    Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTracker:
        arguments:
            $visymoConversionTrackerPlugins:
                - '@Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Offline\OfflineConversionTracker'
                - '@Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Blocker\ConversionTrackingBlocker' # This should always be one of the first trackers
                - '@Visymo\VisymoLabelBundle\ConversionTracking\Tracking\GoogleAds\GoogleAdsAdClickConversionTracker'
                - '@Visymo\VisymoLabelBundle\ConversionTracking\Tracking\MicrosoftAds\MicrosoftAdsConversionTracker'


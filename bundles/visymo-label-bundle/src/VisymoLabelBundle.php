<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle;

use Symfony\Component\DependencyInjection\Extension\ExtensionInterface;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use Visymo\VisymoLabelBundle\DependencyInjection\VisymoLabelExtension;

class VisymoLabelBundle extends Bundle
{
    public function getContainerExtension(): ?ExtensionInterface
    {
        return new VisymoLabelExtension();
    }
}

<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\Tests\Unit\ConversionTracking\Tracking\Blocker;

use App\ConversionTracking\Helper\ConversionTrackingBlockingHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\Blocker\ConversionTrackingBlocker;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerResult;
use Visymo\VisymoLabelBundle\Tests\Stub\ConversionTracking\Conversion\VisymoConversionStubBuilder;

class ConversionTrackingBlockerTest extends PhpUnitTestCase
{
    private ConversionTrackingBlockingHelper & MockObject $conversionTrackingBlockingHelperMock;

    private LoggerInterface & MockObject $loggerMock;

    private ConversionTrackingBlocker $conversionTrackingBlocker;

    protected function setUp(): void
    {
        $this->conversionTrackingBlockingHelperMock = $this->createMock(ConversionTrackingBlockingHelper::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->conversionTrackingBlocker = new ConversionTrackingBlocker(
            $this->conversionTrackingBlockingHelperMock,
            $this->loggerMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function handleDataProvider(): array
    {
        return [
            'standard'                                  => [
                'trackingPixelBlocked'       => false,
                'conversionSupportsTracking' => true,
                'expectedResult'             => null,
            ],
            'with tracking pixel blocked'               => [
                'trackingPixelBlocked'       => true,
                'conversionSupportsTracking' => true,
                'expectedResult'             => false,
            ],
            'with conversion does not support tracking' => [
                'trackingPixelBlocked'       => false,
                'conversionSupportsTracking' => false,
                'expectedResult'             => false,
            ],
        ];
    }

    #[DataProvider('handleDataProvider')]
    public function testHandle(
        bool $trackingPixelBlocked,
        bool $conversionSupportsTracking,
        ?bool $expectedResult
    ): void
    {
        $this->conversionTrackingBlockingHelperMock
            ->method('isBlocked')
            ->willReturn($trackingPixelBlocked);

        $visymoConversion = (new VisymoConversionStubBuilder())
            ->withSupportsConversionTracking($conversionSupportsTracking)
            ->build();

        // Run
        $visymoConversionTrackerResult = new VisymoConversionTrackerResult();

        $actualResult = $this->conversionTrackingBlocker->handle(
            $visymoConversion,
            $visymoConversionTrackerResult,
        );

        self::assertSame($expectedResult, $actualResult);
    }
}

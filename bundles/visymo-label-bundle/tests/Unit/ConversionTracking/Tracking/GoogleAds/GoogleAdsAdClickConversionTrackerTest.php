<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\Tests\Unit\ConversionTracking\Tracking\GoogleAds;

use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettings;
use App\Account\Settings\AccountSettingsHelper;
use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Logging\ConversionVendor;
use App\ConversionTracking\Tracking\GoogleAds\GoogleAdsConversionUrlFactory;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingHttpClient;
use App\WebsiteSettings\Settings\WebsiteSettings;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\Stub\Account\Settings\AccountSettingsStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\GoogleAds\GoogleAdsAdClickConversionTracker;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTrackerResult;
use Visymo\VisymoLabelBundle\Tests\Stub\ConversionTracking\Conversion\VisymoConversionStubBuilder;

class GoogleAdsAdClickConversionTrackerTest extends PhpUnitTestCase
{
    private AccountSettingsHelper & MockObject $accountSettingsHelperMock;

    private WebsiteSettingsHelper & MockObject $websiteSettingsHelperMock;

    private GoogleAdsConversionUrlFactory & MockObject $googleAdsConversionUrlFactoryMock;

    private ConversionTrackingHttpClient & MockObject $conversionTrackingHttpClientMock;

    private GoogleAdsAdClickConversionTracker $googleAdsAdClickConversionTracker;

    private int $serverSideTrackingCallAmount = 0;

    protected function setUp(): void
    {
        $this->accountSettingsHelperMock = $this->createMock(AccountSettingsHelper::class);
        $this->websiteSettingsHelperMock = $this->createMock(WebsiteSettingsHelper::class);
        $brandSettingsHelperMock = $this->createMock(BrandSettingsHelper::class);
        $this->googleAdsConversionUrlFactoryMock = $this->createMock(GoogleAdsConversionUrlFactory::class);
        $this->conversionTrackingHttpClientMock = $this->createMock(ConversionTrackingHttpClient::class);

        $activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->clear()
            ->setQuery('query');

        $this->googleAdsAdClickConversionTracker = new GoogleAdsAdClickConversionTracker(
            $activeTrackingEntryHelperStub,
            $this->websiteSettingsHelperMock,
            $this->googleAdsConversionUrlFactoryMock,
            $this->conversionTrackingHttpClientMock,
            $this->accountSettingsHelperMock,
            $brandSettingsHelperMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function handleDataProvider(): array
    {
        return [
            'standard'                           => [
                'websiteSettings'          => (static function () {
                    $websiteSettingsStub = new WebsiteSettingsStub();
                    $websiteSettingsStub->getGoogleAdsConversionTracking()
                        ->setEnabled();

                    return $websiteSettingsStub;
                })(),
                'accountSettings'          => (static fn () => (new AccountSettingsStubBuilder())
                    ->setService(AccountService::GOOGLE_ADS)
                    ->create()
                )(),
                'visymoConversion'         => (new VisymoConversionStubBuilder())
                    ->withConversionVendor(ConversionVendor::GOOGLE_ADSENSE)
                    ->withEventType(ConversionEventType::CLICK_AD)
                    ->build(),
                'serverSideTrackingResult' => true,
            ],
            'with Microsoft account'             => [
                'websiteSettings'          => (static function () {
                    $websiteSettingsStub = new WebsiteSettingsStub();
                    $websiteSettingsStub->getGoogleAdsConversionTracking()
                        ->setEnabled();

                    return $websiteSettingsStub;
                })(),
                'accountSettings'          => (static fn () => (new AccountSettingsStubBuilder())
                    ->setService(AccountService::MICROSOFT_ADVERTISING)
                    ->create()
                )(),
                'visymoConversion'         => (new VisymoConversionStubBuilder())
                    ->withConversionVendor(ConversionVendor::GOOGLE_ADSENSE)
                    ->withEventType(ConversionEventType::CLICK_AD)
                    ->build(),
                'serverSideTrackingResult' => true,
            ],
            'with related click'                 => [
                'websiteSettings'          => (static function () {
                    $websiteSettingsStub = new WebsiteSettingsStub();
                    $websiteSettingsStub->getGoogleAdsConversionTracking()
                        ->setEnabled();

                    return $websiteSettingsStub;
                })(),
                'accountSettings'          => (static fn () => (new AccountSettingsStubBuilder())
                    ->setService(AccountService::GOOGLE_ADS)
                    ->create()
                )(),
                'visymoConversion'         => (new VisymoConversionStubBuilder())
                    ->withConversionVendor(ConversionVendor::GOOGLE_ADSENSE)
                    ->withEventType(ConversionEventType::CLICK_RELATED)
                    ->build(),
                'serverSideTrackingResult' => true,
            ],
            'with disabled settings'             => [
                'websiteSettings'          => (static function () {
                    $websiteSettingsStub = new WebsiteSettingsStub();
                    $websiteSettingsStub->getGoogleAdsConversionTracking()
                        ->setEnabled(false);

                    return $websiteSettingsStub;
                })(),
                'accountSettings'          => (static fn () => (new AccountSettingsStubBuilder())
                    ->setService(AccountService::GOOGLE_ADS)
                    ->create()
                )(),
                'visymoConversion'         => (new VisymoConversionStubBuilder())
                    ->withConversionVendor(ConversionVendor::GOOGLE_ADSENSE)
                    ->withEventType(ConversionEventType::CLICK_AD)
                    ->build(),
                'serverSideTrackingResult' => true,
            ],
            'with client side tracking fallback' => [
                'websiteSettings'          => (static function () {
                    $websiteSettingsStub = new WebsiteSettingsStub();
                    $websiteSettingsStub->getGoogleAdsConversionTracking()
                        ->setEnabled();

                    return $websiteSettingsStub;
                })(),
                'accountSettings'          => (static fn () => (new AccountSettingsStubBuilder())
                    ->setService(AccountService::GOOGLE_ADS)
                    ->create()
                )(),
                'visymoConversion'         => (new VisymoConversionStubBuilder())
                    ->withConversionVendor(ConversionVendor::GOOGLE_ADSENSE)
                    ->withEventType(ConversionEventType::CLICK_AD)
                    ->build(),
                'serverSideTrackingResult' => false,
            ],
        ];
    }

    #[DataProvider('handleDataProvider')]
    public function testHandle(
        WebsiteSettings $websiteSettings,
        AccountSettings $accountSettings,
        Conversion $visymoConversion,
        bool $serverSideTrackingResult
    ): void
    {
        $this->websiteSettingsHelperMock
            ->method('getSettings')
            ->willReturn($websiteSettings);

        $this->accountSettingsHelperMock
            ->method('getSettings')
            ->willReturn($accountSettings);

        $conversionUrlStub = TestStubRandomizer::createString('conversion-url');
        $this->googleAdsConversionUrlFactoryMock
            ->method('create')
            ->willReturn($conversionUrlStub);

        $this->conversionTrackingHttpClientMock
            ->method('sendRequest')
            ->willReturnCallback(
                function () use ($serverSideTrackingResult) {
                    $this->serverSideTrackingCallAmount++;

                    return $serverSideTrackingResult;
                },
            );

        // Run
        $visymoConversionTrackerResult = new VisymoConversionTrackerResult();

        $actualResult = $this->googleAdsAdClickConversionTracker->handle(
            $visymoConversion,
            $visymoConversionTrackerResult,
        );

        $assertionData = [
            'result'                     => $actualResult,
            'server_side_tracking_calls' => $this->serverSideTrackingCallAmount,
            'client_side_tracking'       => $visymoConversionTrackerResult->getClientSideTrackingUrl() !== null,
            'log_data'                   => $visymoConversionTrackerResult->getExtraLogData(),
        ];

        $assertionFile = $this->initJsonAssertionFile($assertionData);
        $assertionFile->assertSame();
    }
}

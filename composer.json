{"name": "serp/brand-websites", "description": "SERP Brand Websites", "type": "project", "license": "proprietary", "require": {"php": "^8.3", "ext-ctype": "*", "ext-curl": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-maxminddb": "*", "ext-redis": "*", "ext-simplexml": "*", "ext-zend-opcache": "*", "ext-zlib": "*", "geoip2/geoip2": "^2.13.0", "google/cloud-translate": "^1.21.0", "league/commonmark": "^2.6.1", "mobiledetect/mobiledetectlib": "^4.8.09", "monolog/monolog": "^3.9.0", "nyholm/psr7": "^1.8.2", "opis/json-schema": "^2.4.1", "php-http/guzzle7-adapter": "^1.1.0", "php-http/httplug": "^2.4.1", "sentry/sentry": "^4.11.0", "symfony/console": "^7.2.5", "symfony/dotenv": "^7.2.0", "symfony/expression-language": "^7.2.0", "symfony/form": "^7.2.5", "symfony/framework-bundle": "^7.2.5", "symfony/mailer": "^7.2.3", "symfony/monolog-bundle": "^v3.10.0", "symfony/polyfill-uuid": "^1.31.0", "symfony/runtime": "^7.2.3", "symfony/translation": "^7.2.4", "symfony/twig-bundle": "^7.2.0", "symfony/validator": "^7.2.5", "symfony/webpack-encore-bundle": "^2.2.0", "symfony/yaml": "^7.2.5", "twig/extra-bundle": "^3.20.0", "twig/string-extra": "^3.20.0", "twig/twig": "^3.20.0", "ua-parser/uap-php": "^3.9.14", "visymo/array-reader": "^1.2.0", "visymo/autosuggest-api-client": "^4.1.0", "visymo/bing-ads": "^3.1", "visymo/bing-api-client": "^3.4.0", "visymo/composite-search-api-client": "^18.0.0", "visymo/filesystem": "^1.0.0", "visymo/google-csa": "^8.1.0", "visymo/monolog-extensions-bundle": "^8.5.0", "visymo/sentry-bundle": "^8.0.0", "visymo/serializer": "^1.0.0", "visymo/shared": "^17.2.0"}, "require-dev": {"ext-dom": "*", "ext-gd": "*", "ext-imagick": "*", "ext-zip": "*", "escapestudios/symfony2-coding-standard": "^3.15.0", "php-webdriver/webdriver": "^1.15.2", "phpstan/phpstan": "^2.1.6", "phpstan/phpstan-deprecation-rules": "^2.0.1", "phpstan/phpstan-phpunit": "^2.0.4", "phpstan/phpstan-strict-rules": "^2.0.3", "phpstan/phpstan-symfony": "^2.0.2", "phpunit/phpunit": "^12.0.7", "roave/security-advisories": "dev-latest", "slevomat/coding-standard": "8.15.0", "squizlabs/php_codesniffer": "^3.12.2", "symfony/profiler-pack": "^1.0.6", "visymo/artemis-api-client": "^7.0.0", "visymo/coding-standards": "^10.1.0", "visymo/frontend-coding-standards": "^1.0.2", "visymo/log-tail": "^1.0.0", "visymo/phpunit-extensions": "^1.1.0"}, "config": {"gitlab-domains": ["git.visymo.com"], "bin-dir": "bin", "bin-compat": "full", "preferred-install": {"*": "dist"}, "sort-packages": true, "discard-changes": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true, "symfony/runtime": true}, "platform": {"php": "8.3"}}, "autoload": {"psr-4": {"App\\": "src/", "Visymo\\PrototypeBundle\\": "bundles/prototype-bundle/src", "Visymo\\VisymoLabelBundle\\": "bundles/visymo-label-bundle/src"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Visymo\\DevelopBundle\\": "bundles/develop-bundle/src", "Visymo\\DevelopBundle\\Tests\\": "bundles/develop-bundle/tests", "Visymo\\PrototypeBundle\\Tests\\": "bundles/prototype-bundle/tests", "Visymo\\VisymoLabelBundle\\Tests\\": "bundles/visymo-label-bundle/tests"}}, "scripts": {"auto-scripts": ["@clear-cache-and-log"], "clear-cache-and-log": ["rm -rf var/cache var/log"], "post-install-cmd": ["@auto-scripts", "@visymo-cs", "@visymo-fcs"], "post-update-cmd": ["@auto-scripts", "@visymo-cs", "@visymo-fcs"], "visymo-cs": ["visymo-cs self-update", "visymo-cs auto-scripts"], "visymo-fcs": ["visymo-fcs self-update"], "update-config-from-develop": ["console develop:brand-config:download 1", "@import-config"], "update-config": ["console develop:brand-config:download", "@import-config"], "import-config": ["console serp:config:import --no_invalidate_opcache", "console serp:assets:import --no_invalidate_opcache", "console serp:project-config:import"], "dump-env": ["dump-env"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://git.visymo.com/api/v4/group/6/-/packages/composer/packages.json", "canonical": false}]}
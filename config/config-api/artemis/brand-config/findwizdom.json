{"checksum": "84c2d4d4ef46ae7fd71dbf67e178b33c", "brand": {"name": "FindWizdom", "partner_slug": "sbbc_health", "slug": "<PERSON><PERSON><PERSON><PERSON>", "conversion_pixel_url": null, "google_adsense": {"approval": true, "contract_type": "online", "default_client": "pub-****************", "default_channel": null}, "bing_ads": {"approval": true, "default_ad_unit_id": "*********"}, "active": true, "article": {"enabled": true}, "cheq": {"enabled": false}, "content_page": {"enabled": true, "collection": "<PERSON><PERSON><PERSON><PERSON>", "author": {"slug": "ethan", "name": "<PERSON>"}, "use_brand_for_organic_results": false, "organic_result_route": null}, "content_page_home": {"enabled": true, "type": "home_4", "search_route": "route_display_search_related_web"}, "content_search": {"enabled": false}, "display_search": {"enabled": true}, "display_search_related": {"enabled": true, "related_fallback_enabled": false, "style_id_desktop": "**********", "style_id_mobile": "**********", "web_style_id_desktop": "**********", "web_style_id_mobile": "**********"}, "google_publisher_tag": {"enabled": false}, "google_tag_manager": {"enabled": false}, "image_search": {"enabled": false}, "info_pages": {"link_to_external_about_page": false, "link_to_visymo_publishing": false, "page_type": "search"}, "javascript_related_terms": {"enabled": false}, "json_template": {"template_variant": null, "template_overrides": null}, "microsoft_search": {"enabled": true}, "microsoft_search_related": {"enabled": false}, "monetization": {"ads_enabled": false, "related_terms_enabled": false, "display_banners_enabled": false}, "news_search": {"enabled": false}, "one_trust": {"enabled": false}, "pageview_conversion": {"enabled": false}, "search": {"enabled": true, "seo_enabled": false, "style_id_desktop": "**********", "style_id_mobile": "**********"}, "spam_click_detect": {"enabled": false}, "tracking": [], "web_search": {"enabled": true, "style_id_desktop": "**********", "style_id_mobile": "**********"}}, "domains": {"search.findwizdom.com": {"javascript_related_terms_enabled": true, "locales": [{"locale": "en_US", "is_default": true}], "bing_ads": {"enabled": true, "clarity_id": "io9k2ghav0"}, "google_adsense": {"enabled": true}}, "www.findwizdom.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "en_US", "is_default": true}], "bing_ads": {"enabled": true, "clarity_id": "io9k2ghav0"}, "google_adsense": {"enabled": true}}}, "redirect_domains": [], "accounts": {"2": {"name": "~<PERSON><PERSON><PERSON><PERSON>", "service": "Partners", "campaigns_v2": [], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": false}, "google_adsense": {"enabled": true, "sem_client": "pub-****************", "web_client": "pub-****************"}, "bing_ads": {"enabled": true, "web_ad_unit_id": "*********", "sem_ad_unit_id": "*********"}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}}, "split_tests": []}
{"checksum": "e2e406a33d1b830eed42be513b87ef45", "brand": {"name": "Gileq", "partner_slug": "explorads", "slug": "gileq", "conversion_pixel_url": null, "google_adsense": {"approval": true, "contract_type": "online", "default_client": "pub-****************", "default_channel": null}, "bing_ads": {"approval": true, "default_ad_unit_id": "*********"}, "active": true, "article": {"enabled": true}, "cheq": {"enabled": false}, "content_page": {"enabled": true, "collection": "gileq", "author": {"slug": "martha", "name": "<PERSON>"}, "use_brand_for_organic_results": false, "organic_result_route": null}, "content_page_home": {"enabled": true, "type": "home_2", "search_route": "route_display_search_related_web"}, "content_search": {"enabled": false}, "display_search": {"enabled": false}, "display_search_related": {"enabled": true, "related_fallback_enabled": true, "style_id_desktop": "5535694885", "style_id_mobile": "5535694885", "web_style_id_desktop": "5535694885", "web_style_id_mobile": "5535694885"}, "google_publisher_tag": {"enabled": true, "ad_unit_path": "gileq_lander"}, "google_tag_manager": {"enabled": true, "google_tag_manager_id": "GTM-NCKM37G", "routes": ["route_display_search_related", "route_display_search_related_web", "route_web_search", "route_web_search_advertised"]}, "image_search": {"enabled": false}, "info_pages": {"link_to_external_about_page": false, "link_to_visymo_publishing": false, "page_type": "content"}, "javascript_related_terms": {"enabled": false}, "json_template": {"template_variant": null, "template_overrides": ["dsr_ea_multipage"]}, "microsoft_search": {"enabled": false}, "microsoft_search_related": {"enabled": false}, "monetization": [], "news_search": {"enabled": false}, "one_trust": {"enabled": false, "domain_script_id": "048d29da-3ca6-4157-96a9-80416ff9c81d"}, "pageview_conversion": {"enabled": true, "routes": ["route_display_search_related", "route_display_search_related_web"]}, "search": {"enabled": true, "seo_enabled": false, "style_id_desktop": "**********", "style_id_mobile": "**********"}, "spam_click_detect": {"enabled": false}, "tracking": [], "web_search": {"enabled": true, "style_id_desktop": "**********", "style_id_mobile": "**********"}}, "domains": {"search.gileq.com": {"javascript_related_terms_enabled": true, "locales": [{"locale": "en_US", "is_default": true}], "bing_ads": {"enabled": true, "clarity_id": "lyiksv4xyw"}, "google_adsense": {"enabled": true}}, "www.gileq.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "en_US", "is_default": true}], "bing_ads": {"enabled": true, "clarity_id": "lyiksv4xyw"}, "google_adsense": {"enabled": true}}}, "redirect_domains": [], "accounts": {"1": {"name": "~gileq", "service": "Partners", "campaigns_v2": [], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": false}, "google_adsense": {"enabled": true, "sem_client": "pub-****************", "web_client": "pub-****************"}, "bing_ads": {"enabled": true, "web_ad_unit_id": "*********", "sem_ad_unit_id": "*********"}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}}, "split_tests": []}
# Search View

## Inhoud

- [Search View Manager werking](#search-view-manager-werking)
- [Search Api Manager werking](#search-api-manager-werking)
- [Events](#events)

## Search View Manager werking

* Er wordt een view aangemaakt met da<PERSON>an gelinkt een ViewDataRequest en ViewDataRegistry, het id van het container component wordt ingesteld, en het SearchViewCreatedEvent wordt afgevuurd.
* Het ViewDataRequest wordt opgebouwd.
* het SearchViewBuildCompletedEvent wordt afgevuurd.
* Als er search api requests geregistreerd zijn, dan worden deze gedaan. Hierbij wordt bij elk CSAPI request het SearchApiRequestEvent afgevuurd.
* De conditionele data wordt aan het ViewDataRequest toegevoegd.
* De view data requirements worden gecontroleerd.
* Het SearchViewHandledEvent wordt afgevuurd.
* De view wordt gerenderd.

## Search Api Manager werking

1. Vanuit een component wordt er een search request geregistreerd, hierbij word gecontroleerd of de condities afhankelijk zijn van het te registreren request en wordt waar mogelijk het request samengevoegd met eerdere geregistreerde requests.
2. Vanuit de Search View Manager wordt de search api manager aangeroepen om de requests te doen. (als er requests zijn geregistreerd)
3. Er wordt een nieuw ViewDataRequest gemaakt voor de search api requests die uitgevoerd kunnen worden op zodat het mogelijk is om meerdere requests te doen.
4. Het SearchApiRequestEvent wordt afgevuurd.
5. De CSAPI request wordt gedaan.
6. De response wordt toegevoegd aan de ViewDataRequest.
7. Indien nodig word stap 3 t/m 6 herhaald.

## Events

### SearchViewCreatedEvent

Het SearchViewCreatedEvent wordt afgevuurd zodra de view is aangemaakt, als er iets ingesteld moet worden bij de view voor het bouwen van de view data request / renderen van de view dan kan dit event gebruikt worden.

### SearchViewBuildCompletedEvent

Het SearchViewBuildCompletedEvent wordt afgevuurd zodra het view data request is opgebouwd en voordat de CSAPI request wordt gedaan.

### SearchApiRequestEvent

Het SearchApiRequestEvent wordt afgevuurd zodra er een CSAPI request wordt gedaan. Dit event kan gebruikt worden om de CSAPI request te manipuleren.

### SearchViewHandledEvent

Het SearchViewHandledEvent wordt afgevuurd zodra de view data request is opgebouwd, de CSAPI request(s) zijn gedaan en de view data requirements zijn gecontroleerd. Na dit event wordt de view gerenderd.

# Bing Ads Style ID

Bing Ads werkt zelf niet met style ID's zoals Google dat doet. In plaats daarvan moet je zelf alle properties meegeven aan de ad units. Omdat dit er nogal veel zijn hebben we een eigen Bing Ads style ID ontworpen.

## Nieuw style ID toevoegen

Een nieuwe style ID toevoegen is eenvoudig te doen door een nieuw PHP bestand toe te voegen aan /brand-websites/src/Resources/bing_ads_styles/
De bestandsnaam moet de opmaak `bing_ads_style_#.php` hebben waarbij de `#` vervangen moet worden door het ID. Bijvoorbeeld `bing_ads_style_3.php`.

## Inhoud

Het meest eenvoudige is het kopiëren van een bestaand style bestand. Hierin kun je op page niveau en/of unit niveau settings opgeven.
Op page niveau betekent niet dat dit op alle units toegepast wordt, maar dat dit settings zijn die je bij Bing Ads aan de `pageOptions` meegeeft.

Om er achter te komen welke settings er allemaal zijn kun je het beste kijken in de klasses:

* Page: [Visymo\BingAds\PageOptions\BingAdsPageOptions](../../vendor/visymo/bing-ads/src/PageOptions/BingAdsPageOptions.php)
* Unit: [Visymo\BingAds\Style\BingAdsAdStyle](../../vendor/visymo/bing-ads/src/Style/BingAdsAdStyle.php)

## Style testen

De nieuwe style ID kun je vervolgens testen op een pagina waar Bing Ads getoond worden door het ID mee te geven aan `&debug_force_style_id=`.

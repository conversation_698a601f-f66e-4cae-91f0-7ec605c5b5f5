# Split test overschrij<PERSON> [split tests](split_tests.md) voor meer algemene informatie over split tests.

## Standaard werking

De standaard werking van een split test bevat twee onderdelen:

1. Het wel/niet activeren van een split test. Dit is normaal op basis van instellingen uit Antelope zoals beschreven in [BrandWebsite Configuratie](../cookbook/using_brand_website_bundle.md).
2. Het kiezen van een split test variant. Dit is normaal op basis van ingestelde percentages per variant.

## Standaard werking overschrijven

Om deze standaard werking te overschrijven zijn twee interfaces geïntroduceerd:

1. De `SplitTestActivationMatcherInterface` [(code)](../../src/SplitTest/Activate/SplitTestActivationMatcherInterface.php) om te bepalen of een split test wel/niet geactiveerd moet worden.
2. De `SplitTestVariantSelectorInterface` [(code)](../../src/SplitTest/Activate/SplitTestVariantSelectorInterface.php) om te bepalen welke variant gekozen moet worden.

## Werking

### Het wel/niet activeren van een split test

De `SplitTestActivateHelper` krijgt een lijst van `SplitTestActivationMatcherInterface` door die op volgorde van `getDefaultPriority` worden afgehandeld.
Deze sortering wordt door Symfony gedaan en bij het aanpassen hiervan moet de cache ververst worden met `bin/console cache:clear`.

Met het implementeren van `splitTestMatchesActivationConditions` kan worden aangegeven of de split test geactiveerd moet worden:

* `null` - De matcher heeft geen oordeel over de split test, andere matchers mogen het bepalen.
* `false` - De split test moet niet geactiveerd worden.
* `true` - De split test moet wel geactiveerd worden.

Als de split test wel/niet moet worden geactiveerd gebeurt dit direct en andere eventuele activators worden niet meer gecontroleerd.

### Het kiezen van een split test variant

Na het activeren van de split test moet een variant gekozen worden. Als de matcher ook een `SplitTestVariantSelectorInterface` implementeert, dan wordt `selectSplitTestVariant` aangeroepen die de variant kiest.
De werking is verder ongewijzigd: `null` is de controle conditie en anders wordt een `SplitTestVariant` terug gegeven.

Als de matcher de interface niet implementeert, wordt de standaard manier gebruikt op basis van ingestelde percentages per variant.

## Voorbeeld

```php 
<?php

declare(strict_types=1);

namespace App\SplitTest\Activate;

use App\SplitTest\Settings\SplitTestSettings;
use App\SplitTest\Settings\SplitTestVariant;

class PhpVersionSplitTestActivator implements SplitTestActivationMatcherInterface, SplitTestVariantSelectorInterface
{
    private const SPLIT_TEST_PHP81_VARIANT = 'php81';

    private SplitTestActivator $splitTestActivator;

    public function __construct(SplitTestActivator $splitTestActivator)
    {
        $this->splitTestActivator = $splitTestActivator;
    }

    public static function getDefaultPriority(): int
    {
        return 100;
    }

    public function splitTestMatchesActivationConditions(SplitTestSettings $splitTest, bool $forActivatedTest): ?bool
    {
        // Only check for php81 split test
        if (!$splitTest->hasVariant(self::SPLIT_TEST_PHP81_VARIANT)) {
            return null;
        }

        // Check conditions of split test
        return $this->splitTestActivator->splitTestMatchesActivationConditions($splitTest);
    }

    public function selectSplitTestVariant(SplitTestSettings $splitTest): ?SplitTestVariant
    {
        // SERP-2798
        $php8Variant = $splitTest->findVariantByValue(self::SPLIT_TEST_PHP81_VARIANT);

        // Select variant based on PHP version
        return PHP_VERSION_ID < 80100 ? null : $php8Variant;
    }
}
```

Opmerkingen:

* Met `getDefaultPriority()` op `100` zal de activator als één van de eerste worden gecontroleerd.
* Door de `SplitTestActivator` als dependency te laden (zonder `extends`) houden we de constructor onafhankelijk van `SplitTestActivator`.
* Als de variant `php81` niet aanwezig is laten we het aan andere matchers over.
* Als de variant wel bestaat respecteren we de brand, domain, device, etc. instellingen van de split test door deze met de standaard `SplitTestActivator` te controleren.
  Als hier `true` zou worden teruggeven werd de test altijd geactiveerd onafhankelijk van deze instellingen.
* De variant wordt gekozen op basis van de PHP-versie en niet op basis van percentages.

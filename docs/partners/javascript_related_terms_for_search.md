# JavaScript Related Terms for Search

With Visymo JavaScript Related Terms Search you can use the Google related search without the need of Google configuration. This will be generated by the script automatically.

## Document versions

version 1.0.0

- Initial version

## Installation

To make use of the Visymo JavaScript Related Terms Search you need to include the JavaScript file. For performance reasons it's recommended to include it in the HTML `<head>` element.

```
<script async src="https://website.domain/p/related-terms-search/v1"></script>
<script type="text/javascript" charset="utf-8">
    (function(r,t){r[t]=r[t]||function(){(r[t]['q']=r[t]['q']||[]).push(arguments)}})(window, '_vrt');
</script>
```

Based on the website domain the language of the related terms is chosen. This will also be the domain where visitors will go to when they click on a related term.

### URL parameters

Add URL parameters to the JavaScript file to make the request complete.

| Parameter  | Description                                                          | Required | Example            | Restrictions                                                                                            |
|------------|----------------------------------------------------------------------|----------|--------------------|---------------------------------------------------------------------------------------------------------|
| `q`        | If set, use this query instead of `rac` to search for related terms. | yes      | `hotel washington` |                                                                                                         |
| `de`       | Device of the visitor, `c` (computer), `t` (tablet), `m` (mobile).   | no       | `c`                |                                                                                                         |
| `asid`     | Campaign name                                                        | yes      | `cmp_123_nm`       | This needs to hold an existing campaign name, otherwise there might be issues with conversion tracking. |
| `gclid`    | Google click ID                                                      | no       | `abcde-123`        |                                                                                                         |
| `msclkid`  | Microsoft click ID                                                   | no       | `abcde-123`        |                                                                                                         |
| `fbclid`   | Facebook click ID                                                    | no       | `abcde-123`        |                                                                                                         |
| `clid`     | Generic click ID                                                     | no       | `abcde-123`        |                                                                                                         |
| `sclid`    | Secondary generic click ID                                           | no       | `abcde-123`        |                                                                                                         |
| `style_id` | StyleId for related terms                                            | no       | `12345678`         |                                                                                                         |

### Async

The JavaScript code can be loaded async by adding `async` to the script element.

```
<script async src="https://website.domain/p/related-terms-search/v1"></script>
```

## Related terms containers

Display related terms on the page is done by calling the `_vrt` function. Add each container config as a new parameter. The maximum is 2 containers.

```
<script>
    _vrt(
        {
            container: 'related-1',
            amount: 6,
            columns: 1
        },
        {
            container: 'related-2',
            amount: 10,
            columns: 2,
            width: 600
        }
    );
</script>
```

### Options

| Property         | Description                                                                     | Required | Default    | Example                                                                    |
|------------------|---------------------------------------------------------------------------------|----------|------------|----------------------------------------------------------------------------|
| `container`      | ID of the container where the related terms will be displayed.                  | yes      |            | `related-1`                                                                |
| `amount`         | The maximum number of related terms to display.                                 | no       | `10`       | `6`                                                                        |
| `columns`        | The maximum number of columns the related terms will be split into.             | no       | `1`        | `2`                                                                        |
| `width`          | The maximum width of the container in pixels.                                   | no       | Full width | `500`                                                                      |
| `loadedCallback` | A callback function to check if the container received related terms by Google. | no       |            | ```function (containerId, loaded) { console.log(containerId, loaded); }``` |

### Containers

Add HTML `<div>` elements in the HTML `<body>` element with the ID of the container property value.

```
<body>
    ...
    <div id="related-1"></div>
    ...
    <div id="related-2"></div>
</body>
```

## Example code

```
<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, minimal-ui">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge">
        <title>A nice hotel in Washington</title>
        <script async src="https://domain/p/related-terms-search/v1?q=hotel%20washington&asid=cmp_123_nm&de=c&gclid=abcdef-1234"></script>
        <script type="text/javascript" charset="utf-8">
            (function(r,t){r[t]=r[t]||function(){(r[t]['q']=r[t]['q']||[]).push(arguments)}})(window, '_vrt');
        </script>
        <script>
            _vrt(
                {
                    container: 'related-1',
                    amount: 6,
                    columns: 1
                },
                {
                    container: 'related-2',
                    amount: 10,
                    columns: 2,
                    width: 600
                }
            );
        </script>
    </head>
    <body>
        <h1>Hotel Washington</h1>
        <div id="related-1"></div>
        <p>
            Luxe amenities and unparalleled sophistication come to life at Hotel Washington.
        </p>

        <h2>The Capital's premier address</h2>
        <p>
            With the best of D.C. at your fingertips, set out to explore with ease.
            Our prime location is just steps from The White House, the Washington Monument, and the National Mall.
        </p>
        <div id="related-2"></div>
    </body>
</html>
```

# C<PERSON>n van de homepage

## Redenen van cachen
Alle homepages worden gecached als er geen parameters aanwezig zijn. 
* De vinden.nl startpagina is erg uitgebreid en bevat het weerbericht en nieuws 
 waardoor dit te zwaar is om iedere keer op te halen.
* De afbeeldingen voor het weer en nieuws mogen niet constant extern worden opgehaald. 
* Daarnaast vinden er soms DDOS aanvallen plaats op de homepage zonder parameters.

## Varnish
De pagina wordt gecached door Varnish die is ingesteld om alleen de homepage zonder 
parameters te cachen. Varnish volgt hierbij de instellingen die de applicatie aangeeft. 
Het is dus erg belangrijk dat de applicatie alleen voor de juiste pagina's, zonder 
enige bezoekers specifieke informatie, aangeeft dat deze gecached mogen worden. 
De time-to-live (TTL) die Varnish hanteert wordt ook de applicatie aangegeven. Op dit 
moment is dat 5 minuten.

Samenvattend:
* Varnish moet dus goed ingesteld zijn, zodat bepaalde URL's gecached kunnen worden.
* Dit gebeurt alleen als de applicatie de juiste cache headers terugstuurt.

### Vinden startpagina
De instellingen voor vinden.nl zijn:

```
sub vcl_recv {
    # vinden.nl homepage
    if (req.http.host == "www.vinden.nl" && (req.url == "/" || req.url ~ "^/images/(weather|news)/.*$")) {
      unset req.http.cookie;

      return(hash);
    }
}

sub vcl_backend_response {
    # vinden.nl homepage, only to prevent the if from below to overwrite cache control headers and ttl
    if (beresp.status == 200 && bereq.method == "GET" && bereq.http.host == "www.vinden.nl" && (bereq.url == "/" || bereq.url ~ "^/images/(weather|news)/.*$")) {
        return(deliver);
    }
} 
```

Alleen https://www.vinden.nl/ en de paden voor afbeeldingen(`/images/weather` 
en `/images/news`) worden opgeslagen en uit de cache gereserveerd. 

### Brand websites homepage
De instellingen voor de brand website domeinen zullen ongeveer als volgt zijn:

```
sub vcl_recv {
    # Website homepage
    if (req.http.host == "www.brand.com" && (req.url == "/")) {
      unset req.http.cookie;

      return(hash);
    }
}

sub vcl_backend_response {
    # Brand homepage, only to prevent the if from below to overwrite cache control headers and ttl
    if (beresp.status == 200 && bereq.method == "GET" && bereq.http.host == "www.brand.com") {
        return(deliver);
    }
} 
```

## ResponseHelper
Deze helper kan de benodigde cache headers aan de response toevoegen. Daarnaast stuurt het 
een `StartResponseCachingEvent` uit, zodat andere modules zichzelf kunnen configureren, 
zodat er geen gebruikers specifieke gegevens op de pagina's wordt toegevoegd. Dat zijn op dit moment:

### Persistente URL-parameters
Op de cache versie mogen geen URL-parameters worden gebruikt, want die kunnen 
specifiek voor de bezoeker zijn (TrackingEntry of Visitor ID). Deze worden door middel van 
`PersistentUrlParametersHelper->disable()` uitgezet. 

### Cookie consent op cache pagina's
Cookie consent wordt alleen toegevoegd als er nog geen consent is. Voor een cache pagina 
moeten beide situaties mogelijk zijn. Daarom wordt de cookie consent geforceerd toegevoegd 
en in het geval van consent verwijderd met JavaScript.

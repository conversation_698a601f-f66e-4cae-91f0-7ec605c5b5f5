# Visuele browser tests

De visuele browser test maakt gebru<PERSON> van BrowserStack. Het draaien van een test kan zeer lang duren.

Emulators en real devices zijn doorgaans langzamer dan desktop. Vandaar dat alle standaard timeouts redelijk hoog staan.
Desktop tests zouden nooit last mogen hebben van de hoge timeouts.

## Tunnel

Om de frontend tests op lokale urls te kunnen draaien, moet er een Tunnel actief zijn. Ten tijde van schrijven houdt
Maurice deze tunnel actief in zijn RDS sessie.
Als we LambdaTest volledig gaan inzetten, zal er met OPS een alternatieve manier worden afgestemd om de Tunnel te draaien.
Je hoeft deze in principe nooit zelf te starten.

Mochten er SSL fouten voordoen in Safari, dan kan het helpen om de tunnel in MITM mode te draaien. Indien de UnderPass
applicatie gebruikt wordt, kan dat bij geavanceerde instellingen gedaan worden.

## Alle frontend tests

Draai alle tests met het volgende commando:

```
bin/phpunit-all --testsuite=frontend
```

## Filter op test

Gebruik de filter optie om op een test te filteren, bijvoorbeeld:

```
bin/phpunit-all --testsuite=frontend --filter=preferences
```

Hiermee worden alleen tests uitgevoerd waar in de naam `preferences` staat.

## Falende test

Als een test faalt kun je dit het beste in LambdaTest zelf bekijken. Zie https://automation.lambdatest.com/build

Is het verschil in de afbeelding bewust, dan kun je dit bijwerken door de testsuite met "UPDATE_ASSERTIONS=1" env var te
draaien. (is een functionaliteit van `visymo/shared` package)

```bash
UPDATE_ASSERTIONS=1 bin/phpunit-all --testsuite=frontend
```

De afbeeldingen staan per brand opgeslagen onder `/brand-websites/%brand%/tests/Frontend/Resources/`.

## Test instellen

Om een test in te stellen voeg je dit toe aan de `getFrontendTestCases` functie toe die je per brand in moet stellen.
Zie bijvoorbeeld [IzitoFrontendTest](../../brand-websites/izito/tests/Frontend/IzitoFrontendTest.php).

Voeg een `WebSearchTestCase` toe om een websearch landingpage na te bootsen en een `ContentPageTestCase` om andere pagina's, bijvoorbeeld de voorpagina te testen.

## Device tests

Wanneer je een testcase maakt die ook op mobiele devices moet werken, hou er dan rekening mee dat niet alle Selenium
functies op alle devices beschikbaar zijn.

WebDriver selectors

```
# Select by classname
WebDriverBy::classname('example');
# Kan vervangen worden door
WebDriverBy::cssSelector('.example');

# Select by ID
WebDriverBy::id('example');
# Kan vervangen worden door
WebDriverBy::cssSelector('#example');
```

DOM properties

```
RemoteWebElement::getDomProperty();
```

## Device tests

**LET OP** Op dit moment staan alle device tests standaard uitgeschakeld vanwege stabiliteitsproblemen. Als je devices
wilt testen, dien je de device capabilities aan te zetten in `\App\Tests\Frontend\FrontendTestSettings`

Wanneer je een testcase maakt die ook op mobiele devices moet werken, hou er dan rekening mee dat niet alle Selenium
functies op alle devices beschikbaar zijn.

WebDriver selectors

```
# Select by classname
WebDriverBy::classname('example');
# Kan vervangen worden door
WebDriverBy::cssSelector('.example');

# Select by ID
WebDriverBy::id('example');
# Kan vervangen worden door
WebDriverBy::cssSelector('#example');
```

DOM properties

```
RemoteWebElement::getDomProperty();
```

## Connection issues

Het blijft helaas een feit dat wij vanuit Visymo soms problemen hebben met de verbinding naar externe partijen. Dit
was ook het geval voor bijv browserstack en bingapis.com.

Helaas lijkt dit bij LambdaTest ook met enige regelmaat te gebeuren. En dan voornamelijk met hun device automation
platform. (die ook een andere url heeft).

Om toch gebruik te kunnen maken van de device tests, hebben we gekozen om de betreffende foutmeldingen als notice
te loggen en de huidige test te skippen. Bij een probleem met de verbinding zal de test dus niet falen. Dit is opzich
logisch, want een verbindingsprobleem zegt niets over de testcase die op dat moment wordt uitgevoerd. We hoeven daar
dan ook geen Sentry melding van te ontvangen waar we naar moeten kijken. Want we kunnen er toch niets aan doen.

In plaats daarvan wordt de test als "Skipped" gemarkeerd. En wanneer de testsuite de volgende keer draait, zal de
betreffende testcase waarschijnlijk weer mee draaien. Want verbindingsproblemen gebeuren vrijwel willekeurig, de ene
keer is het test 4 die mislukt, de andere keer test 10.

Op deze manier kunnen we de device tests in ieder geval gebruiken.

Maar hou dit dus in het achterhoofd. Dit kan namelijk ook gebeuren wanneer je de screenshots aan het bijwerken bent.
In de console output zie je dat er een test "Skipped" is. Voor die test zijn dan ook de screenshots niet bijgewerkt.
Die moet je dan dus opnieuw uitvoeren.

De foutmeldingen worden afgevangen in `\App\Tests\Frontend\Framework\Helper\ExceptionHandlerHelper::handle()`

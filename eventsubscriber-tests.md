# EventSubscriber Test Coverage

| EventSubscriber Class | Test Exists | Test File |
|----------------------|-------------|-----------|
| AbstractInjectEntriesEventSubscriber | (Abstract) |  |
| BuildSearchHistoryEventSubscriber | Yes | [tests/Unit/SearchHistory/EventSubscriber/BuildSearchHistoryEventSubscriberTest.php](tests/Unit/SearchHistory/EventSubscriber/BuildSearchHistoryEventSubscriberTest.php) |
| ContentPageEventSubscriber | Yes | [tests/Unit/ContentPage/EventSubscriber/ContentPageEventSubscriberTest.php](tests/Unit/ContentPage/EventSubscriber/ContentPageEventSubscriberTest.php) |
| ContentPagesEventSubscriber | Yes | [tests/Unit/ContentPages/EventSubscriber/ContentPagesEventSubscriberTest.php](tests/Unit/ContentPages/EventSubscriber/ContentPagesEventSubscriberTest.php) |
| CreateStatisticsLogEventSubscriber | Yes | [tests/Unit/Statistics/EventSubscriber/CreateStatisticsLogEventSubscriberTest.php](tests/Unit/Statistics/EventSubscriber/CreateStatisticsLogEventSubscriberTest.php) |
| ForwardMainDomainToLocaleEventSubscriber | Yes | [tests/Unit/Locale/EventSubscriber/ForwardMainDomainToLocaleEventSubscriberTest.php](tests/Unit/Locale/EventSubscriber/ForwardMainDomainToLocaleEventSubscriberTest.php) |
| InjectAdClickSpamDetectEventSubscriber | Yes | [tests/Unit/SpamClickDetect/EventSubscriber/InjectAdClickSpamDetectEventSubscriberTest.php](tests/Unit/SpamClickDetect/EventSubscriber/InjectAdClickSpamDetectEventSubscriberTest.php) |
| InjectBaseEntriesEventSubscriber | Yes | [tests/Unit/Template/EventSubscriber/InjectBaseEntriesEventSubscriberTest.php](tests/Unit/Template/EventSubscriber/InjectBaseEntriesEventSubscriberTest.php) |
| InjectDelayedContainerFallbackScriptsEventSubscriber | Yes | [tests/Unit/Template/EventSubscriber/InjectDelayedContainerFallbackScriptsEventSubscriberTest.php](tests/Unit/Template/EventSubscriber/InjectDelayedContainerFallbackScriptsEventSubscriberTest.php) |
| InjectOneTrustScriptsEventSubscriber | Yes | [tests/Unit/OneTrust/EventSubscriber/InjectOneTrustScriptsEventSubscriberTest.php](tests/Unit/OneTrust/EventSubscriber/InjectOneTrustScriptsEventSubscriberTest.php) |
| InjectPageviewConversionEventSubscriber | Yes | [tests/Unit/PageviewConversion/EventSubscriber/InjectPageviewConversionEventSubscriberTest.php](tests/Unit/PageviewConversion/EventSubscriber/InjectPageviewConversionEventSubscriberTest.php) |
| InjectSplitTestEntriesEventSubscriber | Yes | [tests/Unit/Template/EventSubscriber/InjectSplitTestEntriesEventSubscriberTest.php](tests/Unit/Template/EventSubscriber/InjectSplitTestEntriesEventSubscriberTest.php) |
| InjectTrackingEntriesEventSubscriber | Yes | [tests/Unit/Template/EventSubscriber/InjectTrackingEntriesEventSubscriberTest.php](tests/Unit/Template/EventSubscriber/InjectTrackingEntriesEventSubscriberTest.php) |
| InitLocaleEventSubscriber | Yes | [tests/Unit/WebsiteSettings/EventSubscriber/InitLocaleEventSubscriberTest.php](tests/Unit/WebsiteSettings/EventSubscriber/InitLocaleEventSubscriberTest.php) |
| InitSplitTestEventSubscriber | Yes | [tests/Unit/SplitTest/EventSubscriber/InitSplitTestEventSubscriberTest.php](tests/Unit/SplitTest/EventSubscriber/InitSplitTestEventSubscriberTest.php) |
| JsonTemplateEventSubscriber | Yes | [tests/Unit/JsonTemplate/EventSubscriber/JsonTemplateEventSubscriberTest.php](tests/Unit/JsonTemplate/EventSubscriber/JsonTemplateEventSubscriberTest.php) |
| MainStatisticsLogEventSubscriber | Yes | [tests/Unit/Statistics/EventSubscriber/MainStatisticsLogEventSubscriberTest.php](tests/Unit/Statistics/EventSubscriber/MainStatisticsLogEventSubscriberTest.php) |
| PageHeadTagsEventSubscriber | Yes | [tests/Unit/PageHeadTags/EventSubscriber/PageHeadTagsEventSubscriberTest.php](tests/Unit/PageHeadTags/EventSubscriber/PageHeadTagsEventSubscriberTest.php) |
| SeaResponseHeadersEventSubscriber | Yes | [tests/Unit/Tracking/EventSubscriber/SeaResponseHeadersEventSubscriberTest.php](tests/Unit/Tracking/EventSubscriber/SeaResponseHeadersEventSubscriberTest.php) |
| SearchEventSubscriber | Yes | [tests/Unit/Search/EventSubscriber/SearchEventSubscriberTest.php](tests/Unit/Search/EventSubscriber/SearchEventSubscriberTest.php) |

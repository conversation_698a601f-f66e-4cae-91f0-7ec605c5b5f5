import gulp from 'gulp';

const activeTasks = new Set();
const runningTasks = new Set();
let isShuttingDown = false;

const gracefulShutdown = () => {
    if (!isShuttingDown) {
        isShuttingDown = true;
        console.log('Gracefully shutting down all tasks...');

        activeTasks.forEach((taskDone) => {
            try {
                if (typeof taskDone === 'function') {
                    taskDone();
                }
            } catch (error) {
                // Do nothing
            }
        });

        activeTasks.clear();

        runningTasks.forEach((task) => {
            try {
                if (task && typeof task.emit === 'function') {
                    task.emit('end');
                }
            } catch (error) {
                // Do nothing
            }
        });

        runningTasks.clear();
    }
}

if (!process.listenerCount('SIGINT')) {
    process.on('SIGINT', gracefulShutdown);
}

if (!process.listenerCount('SIGTERM')) {
    process.on('SIGTERM', gracefulShutdown);
}

/**
 * Creates an enhanced version of a gulp task function that handles interruption gracefully
 * @param {Function} taskFn - The gulp task function (gulp.parallel or gulp.series)
 * @param {string} displayName - The display name for the task
 * @returns {Function} - A function that creates enhanced tasks
 */
export function createEnhancedTasksFn(taskFn, displayName) {
    return function (...tasks) {
        const enhancedTask = function (done) {
            if (isShuttingDown) {
                if (typeof done === 'function') {
                    done();
                }

                return;
            }

            activeTasks.add(done);

            const enhancedDone = function (error) {
                activeTasks.delete(done);

                if (isShuttingDown) {
                    return;
                }

                if (typeof done === 'function') {
                    done(error);
                }
            }

            const taskStream = taskFn(...tasks)(enhancedDone);

            if (taskStream && typeof taskStream.on === 'function') {
                runningTasks.add(taskStream);

                taskStream.on('end', () => {
                    runningTasks.delete(taskStream);
                });
            }

            return taskStream;
        }

        enhancedTask.displayName = displayName;

        return enhancedTask;
    }
}

/**
 * Creates an enhanced version of a gulp task function that handles interruption gracefully
 * @param {function} taskFn
 * @param {string} displayName
 * @returns {function(...[*]): function(*): void}
 */
export function createEnhancedTaskFn(taskFn, displayName) {
    const enhancedTask = function (done) {
        if (isShuttingDown) {
            if (typeof done === 'function') {
                done();
            }

            return;
        }

        activeTasks.add(done);

        const enhancedDone = function (error) {
            activeTasks.delete(done);

            if (isShuttingDown) {
                return;
            }

            if (typeof done === 'function') {
                done(error);
            }
        }

        const taskStream = taskFn(enhancedDone);

        runningTasks.add(taskStream);

        return taskStream;
    }

    enhancedTask.displayName = displayName;

    return enhancedTask;
}

/**
 * Enhanced version of gulp.parallel that handles interruption gracefully
 * @param {...Function} tasks - The tasks to run in parallel
 * @returns {Function} - A function that runs the tasks in parallel and handles interruption
 */
export const enhancedParallel = createEnhancedTasksFn(
    gulp.parallel,
    'enhancedParallel'
);

/**
 * Enhanced version of gulp.series that handles interruption gracefully
 * @param {...Function} tasks - The tasks to run in series
 * @returns {Function} - A function that runs the tasks in series and handles interruption
 */
export const enhancedSeries = createEnhancedTasksFn(
    gulp.series,
    'enhancedSeries'
);

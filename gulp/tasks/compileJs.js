import filesystem from 'fs';
import gulp from 'gulp';
import include from 'gulp-include';
import sourceMaps from 'gulp-sourcemaps';
import uglify from 'gulp-uglify';
import rename from 'gulp-rename';
import gulp_mode from 'gulp-mode';

import {createBaseConfig, mergedStreams, mkdirSafe} from '../helpers.js';
import renameDestinationComponents from '../plugins/renameDestinationComponents.js';
import {buildLocations} from '../buildConfiguration.js';
import { createEnhancedTaskFn } from '../enhancedTasks.js';

const mode = gulp_mode();

export default function compileJs(projectConfig) {
    return createEnhancedTaskFn(function (callback) {
        const events = [];
        const tempFolder = buildLocations.js.build;

        mkdirSafe(tempFolder);

        const baseConfig = createBaseConfig();
        const compileStream = gulp.src(baseConfig.js.sourceFiles)
            .pipe(include({
                separateInputs: true,
                hardFail: true
            }))
            .pipe(renameDestinationComponents())
            .pipe(mode.development(sourceMaps.init()))
            .pipe(uglify())
            .pipe(mode.development(sourceMaps.write()))
            .pipe(gulp.dest(tempFolder));

        events.push(compileStream);

        const required = JSON.parse(filesystem.readFileSync(projectConfig.js.require));
        // Required entries
        const sourceFiles = required.entries.map(function (entry) {
            const sharedEntry = projectConfig.js.sharedEntriesFolder + '/' + entry;
            const prototypeEntry = projectConfig.js.prototypeEntriesFolder + '/' + entry;

            if (filesystem.existsSync(sharedEntry)) {
                return sharedEntry;
            }

            // if shared is not found, try prototype
            return prototypeEntry;
        });

        events.push(
            gulp.src(sourceFiles)
                .pipe(include({
                    separateInputs: true,
                    hardFail: true
                }))
                .pipe(renameDestinationComponents())
                .pipe(mode.development(sourceMaps.init()))
                .pipe(uglify())
                .pipe(mode.development(sourceMaps.write()))
                .pipe(rename(function (path) {
                    path.basename = path.basename.replace(/Entry$/, '');
                }))
                .pipe(gulp.dest(projectConfig.sharedFolder + '/js'))
        );

        return mergedStreams(events, callback);
    }, 'compileJs');
}

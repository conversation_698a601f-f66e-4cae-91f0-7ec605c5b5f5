.landing-page {
    margin-top: 2rem;
}

.landing-page:first-child {
    margin-top: 0;
}

.landing-page__title {
    border-bottom: .1rem dashed #888888;
    color: #0E7744;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 2.4rem;
    margin-bottom: .4rem;
}

.landing-page__info {
    column-gap: 2rem;
    display: inline-grid;
    font-size: 1.2rem;
    grid-template-columns: auto 1fr;
    margin-bottom: 1rem;
    row-gap: .6rem;
}

.landing-page__info dt {
    color: #666666;
}

.landing-page__info dd {
    color: #000000;
}

.landing-page__urls {
    column-gap: 2rem;
    display: inline-grid;
    font-size: 1.2rem;
    grid-template-columns: auto 1fr;
    margin-bottom: 1rem;
    row-gap: .6rem;
}

.landing-page__urls dt {
    color: #000000;
}

.landing-page__url {
    color: #11486E;
    text-decoration: none;
}

.landing-page__url:hover {
    text-decoration: underline;
}

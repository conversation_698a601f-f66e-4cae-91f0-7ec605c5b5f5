.page {
    --_sidebar_padding-bottom: 2rem;
    --_sidebar_padding-top: 2rem;
}

.page {
    align-items: stretch;
    display: grid;
    grid-template-areas: "header header" "sidebar content";
    grid-template-columns: min-content auto;
    grid-template-rows: 4rem auto;
    min-height: 100vh;
}

.page__header + .page__content {
    grid-area: 2;
}

.page__sidebar {
    align-items: stretch;
    background: #F0F0F0;
    grid-area: sidebar;
    min-width: 30rem;
    padding: var(--_sidebar_padding-top) 2rem var(--_sidebar_padding-bottom) 2rem;
}

.page__sidebar--inner {
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header_height) - var(--_sidebar_padding-bottom) - var(--_sidebar_padding-top));
    overflow: hidden;
    row-gap: 2rem;
}

.page__content {
    grid-area: content;
    padding: 2rem;
}

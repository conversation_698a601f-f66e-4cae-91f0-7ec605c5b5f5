.toolbar {
    align-items: center;
    display: grid;
    font-size: 1.4rem;
    grid-template-areas: "left center right";
    grid-template-columns: repeat(3, 1fr);
    justify-content: space-between;
    margin-bottom: 1rem;
    text-align: center;
    white-space: nowrap;
    width: 100%;
}

.toolbar__button {
    background-color: #FFFFFF;
    border: .1rem solid #DDDDDD;
    color: #000000;
    cursor: pointer;
    display: inline-block;
    line-height: 3rem;
    padding: 0 2rem;
    text-align: center;
    text-decoration: none;
    width: max-content;
}

.toolbar__button--active {
    background-color: #DDDDDD;
}

.toolbar__button + .toolbar__button {
    border-left: 0;
}

.toolbar__button:hover {
    background-color: #EEEEEE;
}

.toolbar__group {
    display: flex;
    justify-content: center;
}

.toolbar__group--left {
    grid-area: left;
    justify-self: flex-start;
}

.toolbar__group--center {
    align-self: center;
    grid-area: center;
    margin: 0 auto;
}

.toolbar__group--right {
    grid-area: right;
    justify-self: flex-end;
}

.toolbar__control {
    flex-grow: 1;
    margin-inline: auto;
    text-align: center;
}

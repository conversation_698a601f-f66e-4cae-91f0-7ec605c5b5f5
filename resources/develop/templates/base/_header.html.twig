<style>
    {% include '@develop/assets/css/Components/Header.css' %}
</style>
{% set component_class = 'header' %}
<div class="page__header {{ component_class }}">
    <div class="{{ component_class }}__menu">
        <div class="{{ component_class }}__hamburger-menu">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <menu class="{{ component_class }}__menu-items">
            {% for item in develop_get_menu_items() %}
                <li class="{{ component_class }}__menu-item{% if item.active %} {{ component_class }}__menu-item--active{% endif %}">
                    {% if item.enabled %}
                        <a href="{{ item.url }}" class="{{ component_class }}__menu-item-link">{{ item.label }}</a>
                    {% else %}
                        <span class="{{ component_class }}__menu-item-label" title="Not available for this brand">{{ item.label }}</span>
                    {% endif %}
                </li>
            {% endfor %}
        </menu>
    </div>
    <div class="{{ component_class }}__title">{{ develop_get_active_menu_title() }}</div>
</div>

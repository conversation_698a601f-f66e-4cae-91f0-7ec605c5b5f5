{# @var json_template_result_registry Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResultRegistry #}
{# @var module_result Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateModuleResult #}
{# @var active_result Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResult|null #}
{# @var active_filter Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilter #}
{% set modules = json_template_result_registry.modules %}

{% if modules|length > 0 %}
    <ul class="files files--no-list">
        {% for module in modules %}
            {% if active_filter.module is null or module == active_filter.module %}
                {% set module_result = json_template_result_registry.moduleResult(module) %}
                {% if module_result.results()|length > 0 %}
                    <li class="files__item">
                        <strong class="files__label" id="module-{{ module }}">{{ module }}</strong>
                        {% include '@develop/json_template_diff/module/_results.html.twig' with {
                            module_result: module_result,
                            results: module_result.results(),
                            active_filter: active_filter,
                            active_result: active_result
                        } only %}
                    </li>
                {% endif %}
            {% endif %}
        {% endfor %}
    </ul>
{% else %}
    No JSON template files found.
{% endif %}

{# @var results Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResult[] #}
{# @var module_result Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateModuleResult #}
{# @var active_filter Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilter #}
<ul class="files">
    {% for name, json_template_results in results %}
        <li class="files__item">
            <strong class="files__label">{{ name }}</strong>
            <ul class="files">
                {% for result in json_template_results %}
                    {% set active = result.equals(active_result) %}
                    {% set url = json_template_diff_path(result, active_filter) %}
                    {% set has_remove_suggestions = module_result.hasRemoveResultSuggestions(result.moduleContentVersionId) %}
                    <li class="files__item files__item--end">
                        <a href="{{ url }}" class="files__link{{ has_remove_suggestions ? ' files__link--important ' }}{{ active ? ' files__link--active' }}"{{ has_remove_suggestions ? ' title="Has remove result suggestions"' }} data-module-version="{{ result.module ~ '-' ~ result.contentVersion }}">
                            {{ result.fileName }} (v{{ result.contentVersion }})
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </li>
    {% endfor %}
</ul>

{# @var landing_pages Visymo\DevelopBundle\Debug\LandingPageUrl\Url\LandingPageUrl[] #}
{% extends '@develop/develop_base.html.twig' %}

{% block head %}
    {{ parent() }}
    <style>
        {% include '@develop/assets/css/Components/Button.css' %}
        {% include '@develop/assets/css/Components/Form.css' %}
        {% include '@develop/assets/css/Components/LandingPage.css' %}
    </style>
{% endblock %}

{% block body %}
    <div class="page__sidebar">
        <form method="get" class="form">
            <div class="form__row">
                <label for="brand" class="form__label">Brand</label>
                <select id="brand" name="brand" onchange="this.form.submit();" class="form__select">
                    {% for key, brand in brand_options %}
                        <option value="{{ key }}"{% if brand.selected %} selected="selected"{% endif %}>{{ brand.label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form__row">
                <label for="locale" class="form__label">Locale</label>
                <select id="locale" name="locale" onchange="this.form.submit();" class="form__select">
                    {% for key, locale in locale_options %}
                        <option value="{{ key }}"{% if locale.selected %} selected="selected"{% endif %}>{{ locale.label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form__row">
                <label for="account_id" class="form__label">Account ID</label>
                <input id="account_id" name="ac" type="text" value="{{ account_id }}" class="form__input">
            </div>
            <div class="form__row">
                <label for="query" class="form__label">Query</label>
                <input id="query" name="q" type="text" value="{{ query }}" class="form__input">
            </div>
            <div class="form__row">
                <label for="split-test-variant" class="form__label">Split Test Variant</label>
                <input id="split-test-variant" name="split_test_variant" type="text" value="{{ split_test_variant }}" class="form__input">
            </div>
            <div class="form__row">
                <label for="template-variant" class="form__label">Template Variant</label>
                <input id="template-variant" name="tv" type="text" value="{{ template_variant }}" class="form__input">
            </div>
            <div class="form__row">
                <label for="production" class="form__label">Production</label>
                <input id="production" name="production" type="checkbox" value="y"{% if production %} checked{% endif %} class="form__checkbox">
            </div>
            <button type="submit" class="button">Submit</button>
        </form>
    </div>
    <div class="page__content">
        {% set component_class = 'landing-page' %}
        {% for landing_page in landing_pages %}
            <div class="{{ component_class }}">
                <div class="{{ component_class }}__title">{{ landing_page.label }} ({{ landing_page.endpoint }})</div>
                <dl class="{{ component_class }}__info">
                    {% for key, info in landing_page.info %}
                        <dt>{{ key }}</dt>
                        <dd>{{ info }}</dd>
                    {% endfor %}
                    {% for device, url in landing_page.urls %}
                        <dt>{{ device }}</dt>
                        <dd>
                            <a href="{{ url }}" class="{{ component_class }}__url">{{ url }}</a>
                        </dd>
                    {% endfor %}
                </dl>
            </div>
        {% endfor %}
    </div>
{% endblock %}

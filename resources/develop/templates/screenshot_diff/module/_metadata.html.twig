{# @var metadata array #}
<div class="metadata">
    <table class="metadata--table">
        <tbody>
            <tr>
                <th colspan="2">
                    Query
                </th>
            </tr>

            {% for param, value in metadata.query %}
                <tr>
                    <td>{{ param }}</td>
                    <td>{{ value }}</td>
                </tr>
            {% endfor %}

            <tr>
                <th colspan="2">
                    Viewport
                </th>
            </tr>
            <tr>
                <td>
                    Height
                </td>
                <td>
                    {{ metadata.viewport.height }}
                </td>
            </tr>
            <tr>
                <td>
                    Width
                </td>
                <td>
                    {{ metadata.viewport.width }}
                </td>
            </tr>
        </tbody>
    </table>
</div>

<script>
    document.getElementById('screenshot-metadata-button')
        .addEventListener('click', function () {
            document.querySelector('.metadata')
                .classList.toggle('metadata--visible');
        });
</script>

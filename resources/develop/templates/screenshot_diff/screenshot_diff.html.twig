{# @var screenshot_diff_results Visymo\DevelopBundle\ScreenshotDiff\Result\ScreenshotDiffResults #}
{# @var active_frontend_test string|null #}
{# @var active_image_type Visymo\DevelopBundle\ScreenshotDiff\Image\ImageType #}
{# @var image_types Visymo\DevelopBundle\ScreenshotDiff\Image\ImageType[] #}
{% extends '@develop/develop_base.html.twig' %}

{% block head %}
    {{ parent() }}
    <style>
        {% include '@develop/assets/css/Components/Button.css' %}
        {% include '@develop/assets/css/Components/Files.css' %}
        {% include '@develop/assets/css/Components/Screenshot.css' %}
        {% include '@develop/assets/css/Components/Toolbar.css' %}
        {% include '@develop/assets/css/Components/Metadata.css' %}
    </style>
{% endblock %}

{% block footer_scripts %}
    {{ parent() }}
    <script>
        function updateScreenshotDiffUrl(anchorElement) {
            const url = new URL(anchorElement.href);
            url.searchParams.set('image_type', activeImageType);

            anchorElement.href = url.href;
        }

        const screenshotSelectorElement = document.getElementById('screenshot-selector');

        let activeImageType = '{{ active_image_type.value }}';

        // Image type buttons
        screenshotSelectorElement?.querySelector(`.toolbar__button[data-value="${activeImageType}"]`)?.classList.add('toolbar__button--active');
        screenshotSelectorElement?.addEventListener('click', (event) => {
            const toolbarButtonElement = event.target.closest('.toolbar__button');

            if (toolbarButtonElement === null) {
                return;
            }

            activeImageType = toolbarButtonElement.dataset.value;

            // Screenshot ID
            const screenshotId = `screenshot-${toolbarButtonElement.dataset.value}`;

            // Toolbar button
            screenshotSelectorElement.querySelector('.toolbar__button--active')?.classList.remove('toolbar__button--active');
            toolbarButtonElement.classList.add('toolbar__button--active');

            // Screenshot image container
            document.querySelector('.screenshot__image-container--visible')?.classList.remove('screenshot__image-container--visible');
            document.getElementById(screenshotId)?.classList.add('screenshot__image-container--visible');
        });

        // Accept / clear diff buttons
        document.querySelectorAll('a.toolbar__button').forEach((toolbarButtonElement) => {
            toolbarButtonElement.addEventListener(
                'click', () => {
                    updateScreenshotDiffUrl(toolbarButtonElement);
                    toolbarButtonElement.style.pointerEvents = 'none';
                }, {once: true}
            );
        });

        // Image
        const screenshotImage = document.querySelector('.screenshot__image-container--visible .screenshot__image');

        if (screenshotImage) {
            const screenshotImageTop = screenshotImage.getBoundingClientRect().top;
            const screenshotImageMaxHeight = (window.innerHeight - screenshotImageTop - 30) + 'px';

            document.querySelectorAll('.screenshot__image-container').forEach((screenshotImageContainer) => {
                const screenshotImage = screenshotImageContainer.querySelector('.screenshot__image');
                const screenshotImageInfo = screenshotImageContainer.querySelector('.screenshot__image-info');

                screenshotImage.onload = function () {
                    screenshotImageInfo.innerHTML = this.naturalWidth + 'x' + this.naturalHeight;
                };
                screenshotImage.style.maxHeight = screenshotImageMaxHeight;

                screenshotImage.addEventListener(
                    'click', () => {
                        screenshotImage.style.maxHeight = screenshotImage.style.maxHeight !== screenshotImageMaxHeight
                            ? screenshotImageMaxHeight
                            : 'none';
                    }
                );
            });
        }

        // Frontend tests
        document.querySelectorAll('.frontendtest-result').forEach((anchorElement) => {
            anchorElement.addEventListener(
                'click', () => {
                    updateScreenshotDiffUrl(anchorElement);
                }
            );
        });
    </script>
{% endblock %}

{% block body %}
    <div class="page__sidebar">
        {% include '@develop/screenshot_diff/module/_devices.html.twig' with {
            screenshot_diff_results: screenshot_diff_results,
            active_frontend_test: active_frontend_test
        } only %}
    </div>
    <div class="page__content">
        {% if active_screenshot_diff is not null %}
            <div class="active-diff">
                <div class="toolbar">
                    {% if metadata is not null %}
                        <div class="toolbar__group toolbar__group--left">
                            <div>
                                <button class="toolbar__button" id="screenshot-metadata-button">Metadata</button>
                                {% include '@develop/screenshot_diff/module/_metadata.html.twig' with {
                                    metadata: metadata
                                } only %}
                            </div>
                            <a class="toolbar__button" target="_blank" href="{{ metadata.url.prod }}">Open prod</a>
                            <a class="toolbar__button" target="_blank" href="{{ metadata.url.dev }}">Open dev</a>
                        </div>
                    {% endif %}
                    <div id="screenshot-selector" class="toolbar__group toolbar__group--center" title="Use ALT-1, 2 and 3 to switch images">
                        <div accesskey="1" class="toolbar__button" data-value="expected">Expected</div>
                        <div accesskey="2" class="toolbar__button" data-value="comparison">Diff</div>
                        <div accesskey="3" class="toolbar__button" data-value="actual">Actual</div>
                    </div>
                    <div class="toolbar__group toolbar__group--right" title="Accept with ALT-A and clear with ALT-C">
                        <a accesskey="a" href="{{ path('route_develop_screenshot_diff_accept', {frontend_test: active_frontend_test}) }}" class="toolbar__button">Accept diff</a>
                        <a accesskey="c" href="{{ path('route_develop_screenshot_diff_clear', {frontend_test: active_frontend_test}) }}" class="toolbar__button">Clear diff</a>
                    </div>
                </div>
                <div class="screenshot" title="Click to toggle size">
                    {% for image_type in image_types %}
                        {% set image_url = path('route_develop_screenshot_diff_image', {frontend_test: active_frontend_test, image_type: image_type.value}) %}
                        <div class="screenshot__image-container{{ image_type == active_image_type ? ' screenshot__image-container--visible' }}" id="screenshot-{{ image_type.value }}">
                            <span class="screenshot__image-info"></span>
                            <img src="{{ image_url }}" alt="" class="screenshot__image"/>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

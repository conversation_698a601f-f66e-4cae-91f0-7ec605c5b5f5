'use strict';

//=require ../Library/Helper.js
//=require ../Library/CookieHelper.js
//=require ../Library/ClassList.js
//=require ../Library/Generic/Singleton.js
//=require ../Library/EventEmitter.js
//=require ../Library/HttpRequest.js
//=require ../Library/Statistics/StatisticsLogger.js
//=require ../Library/Statistics/StatisticsResult.js

function persistentPath(url) {
    return url + (url.indexOf('?') >= 0 ? '&' : '?') + app.path.value;
}

function persistentPathWithoutQuery(url) {
    return url + (url.indexOf('?') >= 0 ? '&' : '?') + app.path.valueNoQuery;
}

function logError(error) {
    var errorWithStackTrace = error.toString() + ', stacktrace: ' + (error.stack.toString() || '');
    (new Image).src = persistentPath(
        '/js-error?error=' + encodeURIComponent(errorWithStackTrace)
        + '&url=' + encodeURIComponent(window.location.href)
        + '&pvid=' + (pvid || '')
    );

    window.console && window.console.error && window.console.error(error);
}

function debounce(func, wait, immediate) {
    var timeout;

    return function () {
        var context = this;
        var args = arguments;
        var later = function () {
            timeout = null;

            if (!immediate) {
                func.apply(context, args);
            }
        };

        var callNow = immediate && !timeout;

        clearTimeout(timeout);
        timeout = setTimeout(later, wait);

        if (callNow) {
            func.apply(context, args);
        }
    };
}

'use strict';

//=require ../Library/BingAds/BingAds.js

function bAdsUnitLoaded(id, loaded, domainUrlList) {
    var callback;

    bingAds.registerAdsTimeToLoad();

    if (loaded) {
        bingAds.registerFirstShown();
        delayedContainer.showDelayedContent();

        callback = function (unitElement) {
            ClassList.add(unitElement, 'csa--loaded');
            delayedContainer.showFallbackContentElement(unitElement, false);
        };
    } else {
        callback = function (unitElement) {
            ClassList.add(unitElement, 'csa--empty');
            delayedContainer.showFallbackContentElement(unitElement, true);
        };
    }

    appReady.push(function () {
        var unitElement = document.getElementById(id);

        if (unitElement) {
            callback(unitElement);
            ClassList.remove(unitElement, 'csa--loading');
        }

        bingAds.increaseAdsAmount(domainUrlList.length);

        if (!bingAds.hasPendingContainers()) {
            bingAds.handleFallbackContent();
            bingAds.sendStatistics();
        }
    });
}

try {
    var bingAds = new BingAds();
} catch (error) {
    logError(error);
}

var acsd = { // AdClickSpamDetect
    _stopped: false,
    _tCC: 0, // Total click count
    _tCCLimit: 4, // Total click count limit
    create: function () {
        this._tCC = appACC;
        this._attachEvents();

        return this;
    },
    _attachEvents: function () {
        var self = this;

        window.addEventListener('message', (event) => {
            if (!event.data.match('FSXDC,.aCS')) {
                return;
            }

            if (event.origin !== 'https://www.google.com' && event.origin !== 'https://www.adsensecustomsearchads.com' && event.origin !== 'https://syndicatedsearch.goog') {
                return;
            }

            self._tCC++;

            if (self._tCC > self._tCCLimit) {
                self._stopI();
            }
        });
    },
    _stopI: function () { // Stop / hide iframe
        if (this._stopped) {
            return;
        }

        this._stopped = true;

        var elements = document.querySelectorAll('.csa iframe');
        var elementsLength = elements.length;

        for (var elementIndex = 0; elementIndex < elementsLength; elementIndex++) {
            var element = elements[elementIndex];

            element.parentElement.removeChild(element);
        }
    },
};



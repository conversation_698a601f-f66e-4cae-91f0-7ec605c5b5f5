function BingAdsStatisticsResult() {
    StatisticsResult.call(
        this,
        'ba',
        {
            hl: null,
            as: null,
            lt: null,
        }
    );
}

BingAdsStatisticsResult.prototype = Object.create(StatisticsResult.prototype);
BingAdsStatisticsResult.prototype.constructor = BingAdsStatisticsResult;

BingAdsStatisticsResult.prototype.setAdsLoaded = function setAdsLoaded(value) {
    this.setValueOnce('hl', value);
};

BingAdsStatisticsResult.prototype.setAdsAmount = function setAdsAmount(value) {
    this.setValueOnce('as', value);
};

BingAdsStatisticsResult.prototype.setAdsTime = function setAdsTime(value) {
    this.setValueOnce('lt', value);
};

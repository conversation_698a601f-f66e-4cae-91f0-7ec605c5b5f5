//=require ./GoogleAdsStatisticsResult.js
//=require ./GoogleAdsRelatedTermsStatisticsResult.js
//=require ./GoogleAdsScriptStatisticsResult.js

function GoogleAds() {
    this._topAdUnitId = 'csa-top';
    this._relatedUnitIdPrefix = 'csa-related-';
    this._adsAmount = null;
    this._adsTopRequestAmount = null;
    this._adsTopAmount = null;
    this._adsLoadTime = (new Date()).getTime();
    this._adsTimeToLoad = null;
    this._adsRequest = false;
    this._pendingAdsContainers = true;
    this._pendingRelatedTermsContainers = true;

    this._googleAdsStatisticsResult = new GoogleAdsStatisticsResult();
    this._googleAdsRelatedTermsStatisticsResult = new GoogleAdsRelatedTermsStatisticsResult();
    this._googleAdsScriptStatisticsResult = new GoogleAdsScriptStatisticsResult();
}

GoogleAds.prototype.hasPendingAdsContainers = function hasPendingAdsContainers() {
    if (!this._pendingAdsContainers) {
        return false;
    }

    this._pendingAdsContainers = this._hasPendingContainers(function (unitId) {
        return googleAds.isAdUnit(unitId);
    });

    return this._pendingAdsContainers;
};

GoogleAds.prototype.hasPendingRelatedTermsContainers = function hasPendingRelatedTermsContainers() {
    if (!this._pendingRelatedTermsContainers) {
        return false;
    }

    this._pendingRelatedTermsContainers = this._hasPendingContainers(function (unitId) {
        return googleAds.isRelatedUnit(unitId);
    });

    return this._pendingRelatedTermsContainers;
};

GoogleAds.prototype._hasPendingContainers = function hasPendingContainers(isUnit) {
    var pending = false;

    Helper.iterateHtmlElements('.csa', function (element) {
        if (!isUnit(element.id)) {
            return;
        }

        if (ClassList.has(element, 'csa--loaded')) {
            return;
        }

        if (ClassList.has(element, 'csa--empty')) {
            return;
        }

        pending = true;
    });

    return pending;
};

GoogleAds.prototype.setAdsRequest = function setAdsRequest(adsRequest) {
    this._adsRequest = adsRequest;
};

GoogleAds.prototype.hasAdsRequest = function hasAdsRequest() {
    return this._adsRequest;
};

GoogleAds.prototype.hasShoppingAds = function hasShoppingAds() {
    if (this._adsTopAmount === null) {
        return false;
    }

    if (this._adsTopRequestAmount === null) {
        return false;
    }

    return this._adsTopAmount > this._adsTopRequestAmount;
};

GoogleAds.prototype.isAdUnit = function isAdUnit(unitId) {
    return !this.isRelatedUnit(unitId);
};

GoogleAds.prototype.isTopAdUnit = function isTopAdUnit(unitId) {
    return unitId.indexOf(this._topAdUnitId) >= 0;
};

GoogleAds.prototype.isRelatedUnit = function isRelatedUnit(unitId) {
    return unitId.indexOf(this._relatedUnitIdPrefix) >= 0;
};

GoogleAds.prototype.isAdIframe = function isAdIframe(iframeId) {
    var unitId = this._getUnitIdFromIframeId(iframeId);

    return unitId !== null ? this.isAdUnit(unitId) : false;
};

GoogleAds.prototype.isRelatedIframe = function isAdIframe(iframeId) {
    var unitId = this._getUnitIdFromIframeId(iframeId);

    return unitId !== null ? this.isRelatedUnit(unitId) : false;
};

GoogleAds.prototype._getUnitIdFromIframeId = function getUnitIdFromIframeId(iframeId) {
    var iframe = document.getElementById(iframeId);

    if (iframe === null) {
        return false;
    }

    var unitElement = iframe.closest('.csa[id]');

    return unitElement !== null ? unitElement.id : null;
}

GoogleAds.prototype.setAdsAmount = function setAdsAmount(adsAmount) {
    if (this._adsAmount === null) {
        this._adsAmount = adsAmount;
    }
};

GoogleAds.prototype.setAdsTopRequestAmount = function setAdsTopRequestAmount(adsTopRequestAmount) {
    if (this._adsTopRequestAmount === null) {
        this._adsTopRequestAmount = adsTopRequestAmount;
    }
};

GoogleAds.prototype.setAdsTopAmount = function setAdsTopAmount(adsTopAmount) {
    if (this._adsTopAmount === null) {
        this._adsTopAmount = adsTopAmount;
    }
};

GoogleAds.prototype.setGoogleAdsScriptLoaded = function setGoogleAdsScriptLoaded(googleAdsScriptLoaded) {
    this._googleAdsScriptStatisticsResult.setGoogleAdsScriptLoaded(googleAdsScriptLoaded);
};

GoogleAds.prototype.setRequestedRelatedTerms = function setRequestedRelatedTerms(requestedRelatedTerms) {
    this._googleAdsRelatedTermsStatisticsResult.setTermsRequested(requestedRelatedTerms);
};

GoogleAds.prototype.registerAdsTimeToLoad = function registerAdsTimeToLoad() {
    if (this._adsTimeToLoad === null) {
        this._adsTimeToLoad = (new Date()).getTime() - this._adsLoadTime;

        if (this._adsTimeToLoad < 0) {
            this._adsTimeToLoad = 0;
        }
    }
};

GoogleAds.prototype.registerFirstShown = function registerFirstShown(value) {
    delayedContainerStatisticsResult.setFirstShown(value);
};

GoogleAds.prototype.registerRelatedOnlineResponse = function registerRelatedOnlineResponse(response) {
    this._googleAdsRelatedTermsStatisticsResult.handleGoogleAdsRelatedOnlineResponse(response);
};

GoogleAds.prototype.registerRelatedResponse = function registerRelatedResponse(map) {
    this._googleAdsRelatedTermsStatisticsResult.handleGoogleAdsRelatedResponse(map);
};

GoogleAds.prototype.registerRelatedUnitLoaded = function registerRelatedUnitLoaded(unitId, loaded, isExperimentVariant, callbackOptions) {
    this._googleAdsRelatedTermsStatisticsResult.handleGoogleAdsRelatedUnitLoaded(unitId, loaded, isExperimentVariant, callbackOptions);
};

GoogleAds.prototype.sendStatistics = function sendStatistics() {
    if (this._adsAmount === null) {
        // This is needed because the events are not always fired in the same order,
        // and only when the amount is set we can send the statistics
        return;
    }

    this._googleAdsStatisticsResult.setAdsLoaded(true);
    this._googleAdsStatisticsResult.setAdsAmount(this._adsAmount);
    this._googleAdsStatisticsResult.setAdsTime(this._adsTimeToLoad);
    this._googleAdsStatisticsResult.setAdsTopAmount(this._adsTopAmount);
    this._googleAdsStatisticsResult.setAdsTopRequestAmount(this._adsTopRequestAmount);

    this._googleAdsStatisticsResult.sendStatistics();
};

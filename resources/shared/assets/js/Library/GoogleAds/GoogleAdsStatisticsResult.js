/**
 * @constructor
 * @param googleAdsUnitCount {int}
 */
function GoogleAdsStatisticsResult() {
    StatisticsResult.call(
        this,
        'ga',
        {
            hl: null,
            as: null,
            lt: null,
            tas: null,
            tar: null
        }
    );
}

GoogleAdsStatisticsResult.prototype = Object.create(StatisticsResult.prototype);
GoogleAdsStatisticsResult.prototype.constructor = GoogleAdsStatisticsResult;

/**
 * @param value {boolean}
 */
GoogleAdsStatisticsResult.prototype.setAdsLoaded = function setAdsLoaded(value) {
    this.setValue('hl', value);
};

/**
 * @param value {int}
 */
GoogleAdsStatisticsResult.prototype.setAdsAmount = function setAdsAmount(value) {
    this.setValue('as', value);
};

/**
 * @param value {int}
 */
GoogleAdsStatisticsResult.prototype.setAdsTopAmount = function setAdsTopAmount(value) {
    this.setValue('tas', value);
};

/**
 * @param value {int}
 */
GoogleAdsStatisticsResult.prototype.setAdsTopRequestAmount = function setAdsTopRequestAmount(value) {
    this.setValue('tar', value);
};

/**
 * @param value {int}
 */
GoogleAdsStatisticsResult.prototype.setAdsTime = function setAdsTime(value) {
    this.setValue('lt', value);
};
/**
 * @param slot {googletag.Slot}
 *
 * @constructor
 */
function GoogleGptSlot(slot) {
    this._impression = false;
    this._impressionViewed = false;
    this._adUnitPath = slot.getAdUnitPath();
    this._slotId = slot.getSlotElementId();
}

GoogleGptSlot.prototype.setImpression = function () {
    this._impression = true;
};

GoogleGptSlot.prototype.hasImpression = function () {
    return this._impression;
};

GoogleGptSlot.prototype.setImpressionViewed = function () {
    this._impressionViewed = true;
};

GoogleGptSlot.prototype.hasImpressionViewed = function () {
    return this._impressionViewed;
};

GoogleGptSlot.prototype.getSlotId = function () {
    return this._slotId;
};

GoogleGptSlot.prototype.getAdUnitPath = function () {
    return this._adUnitPath;
};

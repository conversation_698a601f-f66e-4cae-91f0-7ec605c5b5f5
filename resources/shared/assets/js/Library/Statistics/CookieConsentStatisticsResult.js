function CookieConsentStatisticsResult() {
    StatisticsResult.call(
        this,
        'cc',
        {
            c: null,
        }
    );
}

CookieConsentStatisticsResult.prototype = Object.create(StatisticsResult.prototype);
CookieConsentStatisticsResult.prototype.constructor = CookieConsentStatisticsResult;

CookieConsentStatisticsResult.prototype.setConsent = function setConsent(value) {
    this.setValueOnce('c', value);

    this.sendStatistics();
};

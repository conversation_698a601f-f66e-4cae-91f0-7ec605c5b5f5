function GetNetworkInformation() {
    var networkInformation = {};

    if ('connection' in navigator) {
        // https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation
        var connection = navigator.connection;
        networkInformation.net = connection.effectiveType;
        networkInformation.nt = connection.type;
    }

    return networkInformation;
}

function JavaScriptStatisticsResult() {
    StatisticsResult.call(
        this,
        'j',
        {
            // javascript enabled
            e: null,
            nw: GetNetworkInformation(),
        },
    );
}

JavaScriptStatisticsResult.prototype = Object.create(StatisticsResult.prototype);
JavaScriptStatisticsResult.prototype.constructor = JavaScriptStatisticsResult;

JavaScriptStatisticsResult.prototype.setJavaScriptEnabled = function setJavaScriptEnabled() {
    this.setValueOnce('e', true);
    this.sendStatistics();
};

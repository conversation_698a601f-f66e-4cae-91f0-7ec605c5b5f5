@import "../defaultVariables";

/** @define media-query */
.media-query {
    background: transparent;
    bottom: 0;
    display: block;
    height: 0;
    left: 0;
    position: fixed;
    width: 0;

    // stylelint-disable plugin/selector-bem-pattern
    li {
        display: none;
    }

    @mixin media-query-item($child) {
        li:nth-child(#{$child}) {
            display: inline-block;
        }
    }
    // stylelint-enable plugin/selector-bem-pattern

    @media #{map-get($media-min, f)} {
        @include media-query-item(6);
    }

    @media #{map-get($media-max, e)} {
        @include media-query-item(5);
    }

    @media #{map-get($media-max, d)} {
        @include media-query-item(4);
    }

    @media #{map-get($media-max, c)} {
        @include media-query-item(3);
    }

    @media #{map-get($media-max, b)} {
        @include media-query-item(2);
    }

    @media #{map-get($media-max, a)} {
        @include media-query-item(1);
    }
}

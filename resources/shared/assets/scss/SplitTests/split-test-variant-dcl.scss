html.variant-dcl {
    --_component_background: #EEEEEE;
}

html.variant-dcl .csa--loading {
    min-height: 20rem;
}

html.variant-dcl .component[id^=delayed] {
    background: none !important;
    border-color: transparent !important;
    border-radius: 0.5rem;
    box-shadow: none !important;
    overflow: hidden;
    position: relative;
    visibility: visible !important;
}

html.variant-dcl .component[id^=delayed]::after {
    background: var(--_component_background);
    border-radius: 0.5rem;
    bottom: 0;
    content: "";
    display: block;
    left: 1.5rem;
    position: absolute;
    right: 1.5rem;
    top: 0;
    z-index: 2;
}

html.variant-dcl .component[id^=delayed] * {
    visibility: hidden;
}

html.variant-dcl.html--mode-dark {
    --_component_background: #010970;
}

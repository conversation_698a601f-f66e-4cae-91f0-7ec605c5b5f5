html:has(.pagination--default, .pagination--modern, .pagination--rounded) .pagination__list {
    display: flex;
}

html:has(.pagination--default, .pagination--modern, .pagination--rounded) .pagination__item--prev {
    justify-self: flex-start;
}

html:has(.pagination--default, .pagination--modern, .pagination--rounded) .pagination__item--next {
    justify-self: flex-end;
}

html:has(.pagination--default, .pagination--modern) .pagination__item--hidden {
    display: block;
    visibility: hidden;
}

html .pagination--rounded .pagination__item--hidden {
    display: inline-block;
    visibility: hidden;
}

@media (min-width: 768px) {
    html:has(.pagination--default, .pagination--modern) .pagination__list {
        justify-content: center;
    }
}

@media (max-width: 767px) {
    html:has(.pagination--default, .pagination--modern) .pagination__list {
        justify-content: space-between;
    }
}

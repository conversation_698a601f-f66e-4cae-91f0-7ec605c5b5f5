{# @see /vendor/symfony/twig-bridge/Resources/views/Form/form_div_layout.html.twig #}
{% use 'form_div_layout.html.twig' %}

{% block text_widget %}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form-text-widget')|trim}) %}
    <input type="text" {{ block('widget_attributes') }}
        {% if value is defined %} value="{{ value }}"{% endif %}
        {% if attr %}{{ block('attributes') }}{% endif %}>
{% endblock %}

{%- block switch_checkbox_widget -%}
    {# Add default class for attributes block #}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form-switch-widget__checkbox')|trim}) %}

    <div class="form-switch-widget{{ attr.disabled is defined ? ' disabled' }}">
        <label class="form-switch-widget__label">
            <input type="checkbox"
                {% if value is defined %} value="{{ value }}"{% endif %}
                {% if checked %} checked{% endif %}
                {% if attr %}{{ block('widget_attributes') }}{% endif %}/>
            <span class="form-switch-widget__lever"></span>
        </label>
    </div>
{%- endblock switch_checkbox_widget %}

{% block email_widget %}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form-text-widget')|trim}) %}
    <input type="email" {{ block('widget_attributes') }}
        {% if value is defined %} value="{{ value }}"{% endif %}
        {% if attr %}{{ block('attributes') }}{% endif %}>
{% endblock %}

{% block textarea_widget %}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form-textarea-widget')|trim}) %}
    <textarea {{ block('widget_attributes') }} {% if attr %}{{ block('attributes') }}{% endif %}>{{ value }}</textarea>
{% endblock %}

{% block choice_widget_collapsed %}
    {%- if required and placeholder is none and not placeholder_in_choices and not multiple and (attr.size is not defined or attr.size <= 1) -%}
        {% set required = false %}
    {%- endif -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form-text-widget')|trim}) %}
    <select {{ block('widget_attributes') }}{% if multiple %} multiple="multiple"{% endif %}{% if attr %}{{ block('attributes') }}{% endif %}>
        {%- if placeholder is not none -%}
            <option value=""{% if required and value is empty %} selected="selected"{% endif %}>{{ placeholder != '' ? (translation_domain is same as(false) ? placeholder : placeholder|trans({}, translation_domain)) }}</option>
        {%- endif -%}
        {%- if preferred_choices|length > 0 -%}
            {% set options = preferred_choices %}
            {% set render_preferred_choices = true %}
            {{- block('choice_widget_options') -}}
            {%- if choices|length > 0 and separator is not none -%}
                <option disabled="disabled">{{ separator }}</option>
            {%- endif -%}
        {%- endif -%}
        {%- set options = choices -%}
        {%- set render_preferred_choices = false -%}
        {{- block('choice_widget_options') -}}
    </select>
{% endblock %}

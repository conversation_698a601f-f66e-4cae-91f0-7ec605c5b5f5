{# @var google_tag_manager_id string #}
{# @var apply_gtm_cookie_consent bool #}
{%- set gtm_cookie_consent_callback -%}
dataLayer.push({'event': 'cookie_consent_event', 'cookie_consent': true});
{%- endset -%}
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ google_tag_manager_id }}');</script>
<!-- End Google Tag Manager -->
<script>
    {% if apply_gtm_cookie_consent %}
        {{- gtm_cookie_consent_callback -}}
    {% else %}
        {# Cookie consent by visitor required #}
        appCcc.push(function() {
            {{- gtm_cookie_consent_callback -}}
        });
    {% endif %}
</script>

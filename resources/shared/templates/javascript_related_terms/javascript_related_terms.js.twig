{# @var warnings string[] #}
{# @var allow_page_params string[] #}
{{ entry_javascript('JavaScriptRelatedTerms', false, true)|raw }}

{% for warning in warnings %}
console.warn({{ warning|json_encode|raw }});
{% endfor %}
_vrtIgnoredPageParams = _vrtGetIgnoredPageParams({{ allow_page_params|json_encode|raw }});
_vrtQuery={{ query|json_encode|raw }};
_vrtViewLogUrl={{ view_log_url|json_encode|raw }};
_vrtPageOptions={{ page_options|json_encode_google_csa_properties|raw }};
_vrtUnitOptions={{ unit_options|json_encode_google_csa_properties|raw }};

try {_vrtRun();} catch (error) {
    (new Image).src = '{{ error_url|raw }}?error=' + encodeURIComponent(error) + '&url=' + encodeURIComponent(window.location.href);

        window.console && window.console.error && window.console.error(error);
}

{# @var string domain_script_id #}
{# @var bool add_tcf_stub #}
{% if add_tcf_stub %}
    <script src="https://cdn.cookielaw.org/consent/tcf.stub.js" type="text/javascript" charset="UTF-8"></script>
{% endif %}
<script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" type="text/javascript" charset="UTF-8" data-domain-script="{{ domain_script_id }}"></script>
<script type="text/javascript">
    function OptanonWrapper() {
    }
</script>

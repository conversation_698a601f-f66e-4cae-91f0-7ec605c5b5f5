{# @var page_head_tags App\PageHeadTags\Tags\PageHeadTags #}
{% if page_head_tags.title is not null %}
    <title>{{ page_head_tags.title }}</title>
{% endif %}
{% if page_head_tags.metaDescription is not null %}
    <meta name="description" content="{{ page_head_tags.metaDescription }}">
{% endif %}
{% if page_head_tags.metaKeywords is not null %}
    <meta name="keywords" content="{{ page_head_tags.metaKeywords }}">
{% endif %}
{% if page_head_tags.metaRobots is not null %}
    <meta name="robots" content="{{ page_head_tags.metaRobots }}">
{% endif %}
{% if page_head_tags.openGraph is not null %}
    {% for property in page_head_tags.openGraph.toProperties %}
        <meta property="{{ property.type.value }}" content="{{ property.content }}">
    {% endfor %}
{% endif %}

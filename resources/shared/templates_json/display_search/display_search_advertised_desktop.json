{"options": {"keyword_highlight": "default_true", "organic_keyword_highlight": "default_false", "page_head_tags_type": "display_search_related"}, "components": [{"type": "brand_logo"}, {"type": "columns", "one": [{"type": "bing_ads_top_ad_unit", "amount": 1, "ad_style_id": 6}, {"type": "has_bing_ads", "no": [{"type": "has_related_terms", "no": [{"type": "title", "translation_id": "search_error.no_results.message"}], "yes": [{"type": "title", "translation_id": "search_error.no_results.explore_related_searches"}, {"type": "related_terms", "amount": 6, "columns": 1, "layout": "fallback", "zone": "i"}]}]}]}, {"type": "group", "layout": "search_footer"}]}
{"options": {"keyword_highlight": "forced_true", "organic_keyword_highlight": "forced_false", "organic_link_type": "forced_target_blank", "page_head_tags_type": "display_search_related"}, "container": {"mode": "dark"}, "components": [{"type": "brand_logo", "logo_dark_mode": true}, {"type": "columns", "one": [{"type": "bing_ads_top_ad_unit", "amount": 1, "ad_style_id": 7}, {"type": "has_bing_ads", "no": [{"type": "has_related_terms", "no": [{"type": "title", "translation_id": "search_error.no_results.message"}], "yes": [{"type": "title", "translation_id": "search_error.no_results.explore_related_searches"}, {"type": "related_terms", "amount": 6, "columns": 1, "layout": "fallback-dark", "zone": "i"}]}]}]}, {"type": "footer", "components": [{"type": "footer_logo", "logo_dark_mode": true}, {"type": "footer_navigation", "layout": "borderless"}]}]}
{"options": {"keyword_highlight": "forced_false", "organic_keyword_highlight": "forced_false", "page_head_tags_type": "display_search_related"}, "container": {"layout": "dsr"}, "components": [{"type": "brand_logo", "link_to_home": true}, {"type": "columns", "one": [{"type": "has_content_page", "yes": [{"type": "content_page_header", "component_space_modifiers": ["top"]}, {"type": "content_page_title"}, {"type": "content_page_excerpt", "max_length": 200, "split_on_line_end": true, "component_space_modifiers": ["top", "bottom"]}], "no": [{"type": "title", "translation_id": "content_page.title", "subtitle_translation_id": "content_page.subtitle", "layout": "dsr", "component_space_modifiers": ["top"]}, {"type": "organic_results_with_fallback", "results": {"type": "organic_content_page_results", "result_amount_optimization": false, "amount": 1, "link_to_active_brand": true, "max_description_length": 130, "component_space_modifiers": ["bottom", "top"], "layout": "dsr"}, "fallback": {"type": "organic_results", "result_amount_optimization": false, "amount": 1, "max_description_length": 130, "component_space_modifiers": ["bottom", "top"], "layout": "dsr"}}]}, {"type": "google_related_terms", "amount": 6, "target": "content", "terms_url_parameter_enabled": true, "route": "route_display_search_related_web", "fallback_related_terms": {"type": "related_terms", "amount": 6, "columns": 1, "layout": "fallback", "route": "route_display_search_related_web", "zone": "i"}}, {"type": "has_content_page", "yes": [{"type": "collapse_children", "children": [{"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom"]}, {"type": "content_page_paragraph", "component_space_modifiers": ["top", "bottom"]}, {"type": "google_related_terms", "amount": 6, "target": "content", "terms_url_parameter_enabled": true, "route": "route_display_search_related_web", "fallback_related_terms": {"type": "related_terms", "amount": 6, "columns": 1, "layout": "fallback", "route": "route_display_search_related_web", "zone": "i", "repeat_terms": false}}, {"type": "content_page_paragraphs", "layout": "container", "paragraph_component": {"type": "content_page_paragraph", "component_space_modifiers": ["top-l", "bottom-l"]}}, {"type": "content_page_footer"}, {"type": "share_page", "share": "content_page"}, {"type": "scroll_to_top"}]}], "no": [{"type": "organic_results_with_fallback", "results": {"type": "organic_content_page_results", "result_amount_optimization": false, "amount": 5, "link_to_active_brand": true, "layout": "dsr"}, "fallback": {"type": "organic_results", "result_amount_optimization": false, "amount": 5, "layout": "dsr"}}, {"type": "organic_error_message"}]}]}, {"type": "footer", "components": [{"type": "columns", "section": "footer", "section_css_properties": ["background"], "one": [{"type": "search_bar", "show_search_query": false, "component_space_modifiers": ["top-l"]}, {"type": "footer_logo"}, {"type": "disclaimer"}]}, {"type": "footer_navigation", "layout": "dsr"}]}]}
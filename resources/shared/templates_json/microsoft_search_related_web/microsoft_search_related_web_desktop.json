{"options": {"keyword_highlight": "forced_false", "organic_keyword_highlight": "forced_false", "organic_link_type": "forced_target_blank", "page_head_tags_type": "display_search_related"}, "container": {"layout": "dsr"}, "components": [{"type": "brand_logo", "link_to_home": true, "component_space_modifiers": ["top"]}, {"type": "columns", "layout": "dsrw", "one": [{"type": "dynamic_ads", "amount": 4, "unit": "top"}, {"type": "organic_results_title"}, {"type": "organic_results", "amount": 4}, {"type": "organic_error_message"}, {"type": "has_organic_results", "yes": [{"type": "search_bar", "show_search_query": false, "component_space_modifiers": ["bottom-xl"]}]}]}, {"type": "footer", "components": [{"type": "footer_logo"}, {"type": "footer_navigation"}]}]}
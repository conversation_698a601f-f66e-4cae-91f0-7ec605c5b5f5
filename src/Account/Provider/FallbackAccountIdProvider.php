<?php

declare(strict_types=1);

namespace App\Account\Provider;

use App\WebsiteSettings\Configuration\Brand\WebsiteBrandConfigurationHelper;

final class FallbackAccountIdProvider implements FallbackAccountIdProviderInterface
{
    private const int DEFAULT_AFS_ONLINE_ACCOUNT_ID = 1;

    public function __construct(
        private readonly WebsiteBrandConfigurationHelper $websiteBrandConfigurationHelper
    )
    {
    }

    public function getFallbackAccountId(): ?int
    {
        $brandConfiguration = $this->websiteBrandConfigurationHelper->getConfiguration();

        // Fallback only active for partners
        if ($brandConfiguration->getPartnerSlug() === null) {
            return null;
        }

        return $brandConfiguration->getGoogleAdSenseContractType()->isOnline()
            ? self::DEFAULT_AFS_ONLINE_ACCOUNT_ID
            : null;
    }
}

<?php

declare(strict_types=1);

namespace App\Ads;

use App\WebsiteSettings\Settings\WebsiteSettingsHelper;

readonly class AdTestHelper
{
    public function __construct(
        private bool $adTest,
        private WebsiteSettingsHelper $websiteSettingsHelper
    )
    {
    }

    public function isBingAdsTest(): bool
    {
        return $this->adTest || !$this->websiteSettingsHelper->getSettings()->getBingAds()->isApproval();
    }

    public function isGoogleAdSenseTest(): bool
    {
        return $this->adTest || !$this->websiteSettingsHelper->getSettings()->getGoogleAdSense()->isApproval();
    }
}

<?php

declare(strict_types=1);

namespace App\App\Model;

use App\AutoSuggest\Helper\TermsProviderHelper;
use App\Brand\Settings\BrandSettingsHelper;
use App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchConversionUrlGeneratorInterface;
use App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchRequestInterface;
use App\Http\Url\PersistentUrlParametersHelper;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Menu\Helper\MenuHelper;
use App\Search\Request\SearchRequestInterface;
use App\Statistics\Helper\StatisticsLogEnabledHelper;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class AppFactory
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper,
        private MenuHelper $menuHelper,
        private PersistentUrlParametersHelper $persistentUrlParametersHelper,
        private StatisticsLogEnabledHelper $statisticsLogEnabledHelper,
        private SubmitSearchConversionUrlGeneratorInterface $submitSearchConversionUrlGenerator,
        private SubmitSearchRequestInterface $submitSearchRequest,
        private TermsProviderHelper $termsProviderHelper,
        private DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function create(): App
    {
        $brandSettings = $this->brandSettingsHelper->getSettings();

        return new App(
            brandName                            : $brandSettings->getName(),
            brandSlug                            : $brandSettings->getSlug(),
            menuConfig                           : $this->getMenuConfig(),
            persistentPathQueryString            : $this->getPersistentPathQueryString([]),
            persistentPathQueryStringWithoutQuery: $this->getPersistentPathQueryString([SearchRequestInterface::PARAMETER_QUERY => null]),
            statisticsEnabled                    : $this->statisticsLogEnabledHelper->isLogCreateEnabled(),
            suggestTermsProvider                 : $this->termsProviderHelper->getTermsProviderByRoute()?->getType(),
            conversionTrackingSubmitSearchUrl    : $this->getConversionTrackingSubmitSearchUrl(),
            timestamp                            : $this->dateTimeFactory->createNow(TimezoneEnum::UTC)->getTimestamp(),
        );
    }

    /**
     * @return array<string, string>
     */
    private function getMenuConfig(): array
    {
        $menuConfig = [];

        foreach ($this->menuHelper->getMenuItems() as $menuItem) {
            $menuConfig[$menuItem->text] = $menuItem->url;
        }

        return $menuConfig;
    }

    /**
     * @param array<string, string|null> $routeParameters
     */
    private function getPersistentPathQueryString(array $routeParameters): string
    {
        return http_build_query(
            [
                ...$this->persistentUrlParametersHelper->getPersistentParameters(PersistentUrlParametersPageType::DEFAULT),
                ...$routeParameters,
            ],
        );
    }

    private function getConversionTrackingSubmitSearchUrl(): ?string
    {
        if ($this->submitSearchRequest->hasPreventConversionLoggingFlag()) {
            return null;
        }

        return $this->submitSearchConversionUrlGenerator->generate();
    }
}

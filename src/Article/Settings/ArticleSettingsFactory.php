<?php

declare(strict_types=1);

namespace App\Article\Settings;

use App\Article\ArticleModule;
use App\Debug\Request\DebugRequestInterface;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class ArticleSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return ArticleModule::getModuleName();
    }

    public function create(): ArticleSettings
    {
        if ($this->debugRequest->enableModule()) {
            return new ArticleSettings(
                enabled: true,
            );
        }

        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['article'];

        return new ArticleSettings(
            enabled: $this->isModuleEnabled($moduleConfig),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Assets\Directory;

use App\BrandAssets\Settings\BrandAssetsSettings;

final class BrandBuildFolder
{
    private string $brandBuildFolder;

    private string $relativeBrandBuildFolder;

    public function __construct(
        private readonly BrandAssetsSettings $brandAssetsSettings,
        private readonly string $projectDir
    )
    {
    }

    public function getPath(): string
    {
        if (!isset($this->brandBuildFolder)) {
            $this->brandBuildFolder = sprintf(
                '%s/public%s',
                $this->projectDir,
                $this->getRelativePath(),
            );
        }

        return $this->brandBuildFolder;
    }

    public function getRelativePath(): string
    {
        if (isset($this->relativeBrandBuildFolder)) {
            return $this->relativeBrandBuildFolder;
        }

        $this->relativeBrandBuildFolder = sprintf(
            '/build/%s',
            $this->brandAssetsSettings->brandSlug,
        );

        return $this->relativeBrandBuildFolder;
    }
}

<?php

declare(strict_types=1);

namespace App\AutoSuggest\Controller;

use App\AutoSuggest\Helper\TermsProviderHelper;
use App\AutoSuggest\Request\AutoSuggestRequestInterface;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

final class AutoSuggestController extends AbstractController
{
    private const int SUGGESTED_TERMS_AMOUNT = 6;
    private const int MIN_QUERY_LENGTH       = 2;

    public function __construct(
        private readonly AutoSuggestRequestInterface $autoSuggestRequest,
        private readonly TermsProviderHelper $termsProviderHelper,
        private readonly LocaleSettingsHelperInterface $localeSettingsHelper,
        private readonly LoggerInterface $logger
    )
    {
    }

    #[Route(path: '/suggest', name: 'route_auto_suggest_endpoint', methods: ['GET'])]
    public function suggest(): JsonResponse
    {
        $jsonResponse = new JsonResponse([]);
        $jsonResponse->setEncodingOptions(JSON_UNESCAPED_UNICODE);

        $query = $this->autoSuggestRequest->getQuery();

        if ($query === null || mb_strlen($query) < self::MIN_QUERY_LENGTH) {
            return $jsonResponse;
        }

        $locale = $this->localeSettingsHelper->getSettings()->getCode();

        try {
            $provider = $this->termsProviderHelper->getTermsProvider();
            $suggestedTerms = $provider->getTerms($query, $locale, self::SUGGESTED_TERMS_AMOUNT);
            $data = [];
            foreach ($suggestedTerms as $suggestedTerm) {
                $data[] = [$suggestedTerm->query, $suggestedTerm->term];
            }

            $jsonResponse->setData($data);
        } catch (\Throwable $exception) {
            $this->logger->error(
                sprintf('Caught %s during Suggested Terms request: {message}', $exception::class),
                [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                ],
            );
        }

        return $jsonResponse;
    }
}

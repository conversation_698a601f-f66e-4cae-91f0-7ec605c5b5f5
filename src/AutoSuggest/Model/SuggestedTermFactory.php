<?php

declare(strict_types=1);

namespace App\AutoSuggest\Model;

use App\Highlight\HighlightHelper;

readonly class SuggestedTermFactory
{
    public function __construct(private HighlightHelper $highlightHelper)
    {
    }

    public function create(string $query, string $term): SuggestedTerm
    {
        return new SuggestedTerm($query, $term);
    }

    public function createWithHighlightText(string $query, string $term, string $highlightText): SuggestedTerm
    {
        $highlightedTerm = $this->highlightHelper->highlight($highlightText, $term);

        return $this->create($query, $highlightedTerm);
    }
}

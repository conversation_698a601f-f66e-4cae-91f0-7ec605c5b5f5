<?php

declare(strict_types=1);

namespace App\AutoSuggest\Provider;

use App\AutoSuggest\Model\SuggestedTerm;

interface TermsProviderInterface
{
    public function getType(): string;

    public static function getDefaultPriority(): int;

    public function isEnabled(): bool;

    /**
     * @return SuggestedTerm[]
     */
    public function getTerms(string $query, string $locale, int $amount): array;
}

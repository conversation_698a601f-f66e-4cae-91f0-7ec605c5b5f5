<?php

declare(strict_types=1);

namespace App\AutoSuggest\Provider;

use App\AdBot\Request\AdBotRequestInterface;
use App\AutoSuggest\Model\SuggestedTerm;
use App\AutoSuggest\Model\SuggestedTermFactory;
use App\AutoSuggest\Request\AutoSuggestRequestInterface;
use App\Brand\Settings\BrandSettingsHelper;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Visymo\AutosuggestApiClient\AutosuggestApiClientInterface;
use Visymo\AutosuggestApiClient\RelatedTerms\RelatedTermsRequest;
use Visymo\AutosuggestApiClient\SuggestedTerms\SuggestedTermsRequest;

readonly class VisymoTermsProvider implements TermsProviderInterface
{
    public function __construct(
        private AutoSuggestRequestInterface $autoSuggestRequest,
        private BrandSettingsHelper $brandSettingsHelper,
        private AutosuggestApiClientInterface $autosuggestApiClient,
        private SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private AdBotRequestInterface $adBotRequest,
        private SuggestedTermFactory $suggestedTermFactory
    )
    {
    }

    public function getType(): string
    {
        return 'v';
    }

    public static function getDefaultPriority(): int
    {
        return 100;
    }

    public function isEnabled(): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function getTerms(string $query, string $locale, int $amount): array
    {
        if ($this->autoSuggestRequest->showRelatedTerms()) {
            return $this->getRelated($query, $locale, $amount);
        }

        return $this->getSuggested($query, $locale, $amount);
    }

    /**
     * @return SuggestedTerm[]
     */
    private function getRelated(string $query, string $locale, int $amount): array
    {
        $relatedTermsRequest = RelatedTermsRequest::create(
            query           : $query,
            locale          : $locale,
            limit           : $amount,
            robotAgent      : $this->adBotRequest->isAdBot(),
            brandSlug       : $this->brandSettingsHelper->getSettings()->getSlug(),
            splitTestVariant: $this->splitTestExtendedReader->getVariant(),
        );

        $response = $this->autosuggestApiClient->getRelatedTerms($relatedTermsRequest);
        $terms = [];

        foreach ($response->relatedTerms as $relatedTerm) {
            $terms[] = $this->suggestedTermFactory->createWithHighlightText(
                query        : $relatedTerm->value,
                term         : $relatedTerm->value,
                highlightText: $query,
            );
        }

        return $terms;
    }

    /**
     * @return SuggestedTerm[]
     */
    private function getSuggested(string $query, string $locale, int $amount): array
    {
        $suggestedTermsRequest = SuggestedTermsRequest::create(
            query           : $query,
            locale          : $locale,
            limit           : $amount,
            brandSlug       : $this->brandSettingsHelper->getSettings()->getSlug(),
            splitTestVariant: $this->splitTestExtendedReader->getVariant(),
        );

        $response = $this->autosuggestApiClient->getSuggestedTerms($suggestedTermsRequest);
        $terms = [];

        foreach ($response->suggestedTerms as $suggestedTerm) {
            $terms[] = $this->suggestedTermFactory->createWithHighlightText(
                query        : $suggestedTerm->value,
                term         : $suggestedTerm->value,
                highlightText: $query,
            );
        }

        return $terms;
    }
}

<?php

declare(strict_types=1);

namespace App\AutoSuggest\Request;

use App\Http\Request\RequestInterface;

interface AutoSuggestRequestInterface extends RequestInterface
{
    public const string PARAMETER_SHOW_RELATED_TERMS       = 'r';
    public const string PARAMETER_SUGGESTED_TERMS_PROVIDER = 'stp';

    public const string KEY_QUERY = 'query';

    public function getQuery(): ?string;

    /**
     * Show related terms results instead of auto suggest
     */
    public function showRelatedTerms(): bool;

    public function getSuggestedTermsProvider(): ?string;
}

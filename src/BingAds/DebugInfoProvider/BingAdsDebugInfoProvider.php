<?php

declare(strict_types=1);

namespace App\BingAds\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use Visymo\BingAds\Clarity\Registry\ClarityRegistry;
use Visymo\BingAds\Generic\Property\PropertyHelper;

class BingAdsDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_BING_ADS_REQUEST = 'bing ads request';

    public function __construct(
        private readonly BingAdsDebugInfoRegistry $bingAdsDebugInfoRegistry,
        private readonly PropertyHelper $propertyHelper,
        private readonly ClarityRegistry $clarityRegistry
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        $properties = $this->bingAdsDebugInfoRegistry->getProperties();
        $debugInfo = [];
        $bingAdsDebugData = [];

        if ($properties !== null) {
            $bingAdsDebugData[] = $this->propertyHelper->parsePropertiesToArray(
                $this->propertyHelper->excludeNullValuesOfProperties($properties),
            );
        }

        $clarityId = $this->clarityRegistry->getClarityId();

        if ($clarityId !== null) {
            $bingAdsDebugData[] = ['clarity_id' => $clarityId];
        }

        if ($bingAdsDebugData !== []) {
            $debugInfo[] = new DebugInfo(self::KEY_BING_ADS_REQUEST, $bingAdsDebugData);
        }

        return $debugInfo;
    }

    public static function getDefaultPriority(): int
    {
        return 60;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_BING_ADS_REQUEST,
        ];
    }
}

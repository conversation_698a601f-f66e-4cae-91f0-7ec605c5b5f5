<?php

declare(strict_types=1);

namespace App\BingAds\DebugInfoProvider;

use App\Debug\Request\DebugRequestInterface;
use Visymo\BingAds\BingAdsInterface;
use Visymo\BingAds\Generic\Property\PropertyInterface;

class BingAdsDebugInfoRegistry
{
    /** @var PropertyInterface[] */
    private ?array $properties = null;

    public function __construct(
        private readonly DebugRequestInterface $debugRequest
    )
    {
    }

    public function register(BingAdsInterface $bingAds): void
    {
        if (!$this->debugRequest->debugInfo()) {
            return;
        }

        // Clone properties to avoid modifying the original object before debug has been displayed
        $this->properties = array_map(
            static fn (PropertyInterface $property) => clone $property,
            $bingAds->getPageOptions()->getProperties(),
        );
    }

    /**
     * @return PropertyInterface[]|null
     */
    public function getProperties(): ?array
    {
        return $this->properties;
    }
}

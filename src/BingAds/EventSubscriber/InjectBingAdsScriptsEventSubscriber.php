<?php

declare(strict_types=1);

namespace App\BingAds\EventSubscriber;

use App\BingAds\DebugInfoProvider\BingAdsDebugInfoRegistry;
use App\BingAds\Helper\BingAdsHelper;
use App\Office\Request\OfficeRequestInterface;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Environment;
use Visymo\BingAds\BingAds;
use Visymo\BingAds\BingAdsRendererInterface;

class InjectBingAdsScriptsEventSubscriber implements EventSubscriberInterface
{
    private bool $hasRenderedBingScripts = false;

    public function __construct(
        private readonly BingAdsHelper $bingAdsHelper,
        private readonly BingAdsRendererInterface $bingAdsRenderer,
        private readonly BingAdsDebugInfoRegistry $bingAdsDebugInfoRegistry,
        private readonly OfficeRequestInterface $officeRequest,
        private readonly Environment $twig
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            // Bing Ads script should be loaded before anything else
            RenderTemplateHeadersEvent::NAME => ['renderTemplateHeaders', 1000],
            // Bing Ads JavaScript code only works if the containers are already rendered
            RenderTemplateFootersEvent::NAME => ['renderTemplateFooters', 1000],
        ];
    }

    public function renderTemplateHeaders(RenderTemplateHeadersEvent $event): void
    {
        if (!$this->bingAdsHelper->hasUnits()) {
            return;
        }

        /** @var BingAds $bingAds */
        $bingAds = $this->bingAdsHelper->getBingAds();

        // Debug ad style ID
        $debugAdStyleId = $this->officeRequest->isOffice()
            ? $this->bingAdsHelper->getAdStyleId()
            : null;

        $event->addItem(
            $this->twig->render(
                '@theme/bing_ads/bing_ads_template_headers.html.twig',
                [
                    'script_html'       => $this->bingAdsRenderer->getScriptHtml($bingAds),
                    'debug_ad_style_id' => $debugAdStyleId,
                ],
            ),
        );

        $this->hasRenderedBingScripts = true;
    }

    public function renderTemplateFooters(RenderTemplateFootersEvent $event): void
    {
        if (!$this->hasRenderedBingScripts) {
            return;
        }

        $bingAds = $this->bingAdsHelper->getBingAds();

        if ($bingAds === null) {
            return;
        }

        $event->addItem(
            $this->twig->render(
                '@theme/bing_ads/bing_ads_template_footers.html.twig',
                [
                    'ads_javascript' => $this->bingAdsRenderer->getJavaScript($bingAds),
                ],
            ),
        );

        $this->bingAdsDebugInfoRegistry->register($bingAds);
    }
}

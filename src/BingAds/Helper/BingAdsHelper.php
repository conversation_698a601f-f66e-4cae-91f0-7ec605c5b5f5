<?php

declare(strict_types=1);

namespace App\BingAds\Helper;

use App\BingAds\Factory\BingAdsFactory;
use App\Component\Ads\BingAdsAdUnit\BingAdsAdUnitComponentInterface;
use App\JsonTemplate\View\DataRequest\BingAdsViewDataRequest;
use Visymo\BingAds\AdUnit\AdUnit;
use Visymo\BingAds\AdUnit\AdUnitFactory;
use Visymo\BingAds\AdUnit\Enum\AdTypesFilter;
use Visymo\BingAds\AdUnit\Enum\Position;
use Visymo\BingAds\BingAds;

class BingAdsHelper
{
    private ?BingAds $bingAds = null;

    public function __construct(
        private readonly BingAdsFactory $bingAdsFactory,
        private readonly AdUnitFactory $adUnitFactory
    )
    {
    }

    private function reset(): void
    {
        $this->bingAds = null;
    }

    public function initFromViewDataRequest(BingAdsViewDataRequest $viewDataRequest): void
    {
        $this->reset();

        $adUnits = [];
        $adStyleId = null;

        foreach ($viewDataRequest->getAdUnits() as $dataRequest) {
            $adUnits[] = $this->adUnitFactory->create(
                containerId: $dataRequest->getContainer(),
                adSlots    : $dataRequest->getAmount(),
                position   : $this->matchPosition($dataRequest->getPosition()),
            )->addAdTypesFilter(AdTypesFilter::TEXT_ADS);

            // Use first style ID found
            $adStyleId ??= $dataRequest->getAdStyleId();
        }

        $this->bingAds = $this->bingAdsFactory->create($adUnits, $adStyleId);
    }

    public function getBingAds(): ?BingAds
    {
        return $this->bingAds;
    }

    public function hasUnits(): bool
    {
        return $this->getBingAds()?->getPageOptions()->hasAdUnits() ?? false;
    }

    public function getAdsAmount(): int
    {
        $bingAdsAmount = 0;

        foreach ($this->getAdUnits() as $adUnit) {
            $bingAdsAmount += $adUnit->adSlots;
        }

        return $bingAdsAmount;
    }

    public function getAdStyleId(): ?int
    {
        foreach ($this->getAdUnits() as $adUnit) {
            if ($adUnit->adStyle?->id !== null) {
                return $adUnit->adStyle->id;
            }
        }

        return null;
    }

    /**
     * @return AdUnit[]
     */
    private function getAdUnits(): array
    {
        return $this->getBingAds()?->getPageOptions()->getAdUnits() ?? [];
    }

    private function matchPosition(string $position): Position
    {
        return match ($position) {
            BingAdsAdUnitComponentInterface::POSITION_TOP     => Position::MAIN_LINE,
            BingAdsAdUnitComponentInterface::POSITION_BOTTOM  => Position::BOTTOM,
            BingAdsAdUnitComponentInterface::POSITION_SIDEBAR => Position::SIDEBAR,
            default                                           => throw new \InvalidArgumentException(
                sprintf('Unknown position "%s"', $position),
            ),
        };
    }
}

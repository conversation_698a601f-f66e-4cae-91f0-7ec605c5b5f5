<?php

declare(strict_types=1);

namespace App\BingAds\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsProvider;

class BingAdsStatisticsProvider extends AbstractStatisticsProvider
{
    public function __construct(
        BingAdsStatisticsResolver $bingAdsStatisticsResolver
    )
    {
        parent::__construct(
            $bingAdsStatisticsResolver,
        );
    }

    public static function getContextKey(): string
    {
        return 'bing_ads';
    }

    public static function getPayloadKey(): string
    {
        return 'ba';
    }
}

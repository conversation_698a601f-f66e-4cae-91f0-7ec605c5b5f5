<?php

declare(strict_types=1);

namespace App\BingAds\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;

class BingAdsStatisticsResolver extends AbstractStatisticsResolver
{
    private const string STATISTICS_KEY_AMOUNT_SHOWN = 'amount_shown';
    private const string STATISTICS_KEY_HAS_LOADED   = 'has_loaded';
    private const string STATISTICS_KEY_LOAD_TIME    = 'load_time';

    private const string PAYLOAD_KEY_AMOUNT_SHOWN = 'as';
    private const string PAYLOAD_KEY_HAS_LOADED   = 'hl';
    private const string PAYLOAD_KEY_LOAD_TIME    = 'lt';

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::PAYLOAD_KEY_HAS_LOADED)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->define(self::PAYLOAD_KEY_AMOUNT_SHOWN)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(0));

        $this->optionsResolver->define(self::PAYLOAD_KEY_LOAD_TIME)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(0));
    }

    /**
     * @inheritDoc
     */
    protected function getStatisticsMapping(): array
    {
        return [
            self::STATISTICS_KEY_HAS_LOADED   => self::PAYLOAD_KEY_HAS_LOADED,
            self::STATISTICS_KEY_AMOUNT_SHOWN => self::PAYLOAD_KEY_AMOUNT_SHOWN,
            self::STATISTICS_KEY_LOAD_TIME    => self::PAYLOAD_KEY_LOAD_TIME,
        ];
    }
}

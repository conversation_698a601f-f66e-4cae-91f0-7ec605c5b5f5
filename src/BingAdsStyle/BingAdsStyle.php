<?php

declare(strict_types=1);

namespace App\BingAdsStyle;

readonly class BingAdsStyle
{
    private const string LEVEL_PAGE = 'page';
    private const string LEVEL_UNIT = 'unit';

    /**
     * @param mixed[] $data
     */
    public function __construct(
        public int $id,
        private array $data
    )
    {
    }

    /**
     * @return mixed[]|null
     */
    public function getPageStyleData(): ?array
    {
        return $this->data[self::LEVEL_PAGE] ?? null;
    }

    /**
     * @return mixed[]|null
     */
    public function getUnitStyleData(): ?array
    {
        return $this->data[self::LEVEL_UNIT] ?? null;
    }
}

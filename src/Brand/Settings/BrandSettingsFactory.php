<?php

declare(strict_types=1);

namespace App\Brand\Settings;

class BrandSettingsFactory
{
    /**
     * @param mixed[] $brandConfig
     */
    public function create(array $brandConfig): BrandSettings
    {
        try {
            return new BrandSettings(
                $brandConfig[BrandSettings::KEY_NAME],
                $brandConfig[BrandSettings::KEY_SLUG],
                $brandConfig[BrandSettings::KEY_PARTNER_SLUG],
            );
        } catch (\Throwable $exception) {
            throw InvalidBrandSettingsConfigException::create($exception);
        }
    }
}

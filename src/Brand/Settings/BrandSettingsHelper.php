<?php

declare(strict_types=1);

namespace App\Brand\Settings;

use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;

class BrandSettingsHelper
{
    protected BrandSettings $brandSettings;

    public function __construct(
        private readonly WebsiteConfigurationHelper $websiteConfigurationHelper,
        private readonly BrandSettingsFactory $brandSettingsFactory
    )
    {
    }

    public function getSettings(): BrandSettings
    {
        if (!isset($this->brandSettings)) {
            $this->brandSettings = $this->brandSettingsFactory->create(
                $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig(),
            );
        }

        return $this->brandSettings;
    }
}

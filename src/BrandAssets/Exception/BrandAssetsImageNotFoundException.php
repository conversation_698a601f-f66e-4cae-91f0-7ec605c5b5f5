<?php

declare(strict_types=1);

namespace App\BrandAssets\Exception;

use App\BrandAssets\File\BrandAssetsImageFileName;

final class BrandAssetsImageNotFoundException extends BrandAssetsException
{
    public static function create(BrandAssetsImageFileName $imageFileName, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Could not find "%s" in brand assets', $imageFileName->value),
            0,
            $previous,
        );
    }
}

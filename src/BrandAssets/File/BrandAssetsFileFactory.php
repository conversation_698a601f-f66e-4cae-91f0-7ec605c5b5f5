<?php

declare(strict_types=1);

namespace App\BrandAssets\File;

use Visymo\Filesystem\Opcache\OpcachedNativePhpIncludeFileFactory;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\Filesystem\SerializedFile\Type\NativeJsonFileFactory;
use Visymo\Filesystem\SerializedFile\Type\NativePhpIncludeFileFactory;

final readonly class BrandAssetsFileFactory
{
    public function __construct(
        private NativeJsonFileFactory $jsonAssetsFileFactory,
        private OpcachedNativePhpIncludeFileFactory $opcachedNativePhpIncludeFileFactory,
        private NativePhpIncludeFileFactory $nativePhpIncludeFileFactory
    )
    {
    }

    public function createJson(string $filePath): SerializedFileInterface
    {
        return $this->jsonAssetsFileFactory->create($filePath);
    }

    public function createPhp(string $filePath, bool $useOpcache): SerializedFileInterface
    {
        return $useOpcache
            ? $this->opcachedNativePhpIncludeFileFactory->create($filePath)
            : $this->nativePhpIncludeFileFactory->create($filePath);
    }
}

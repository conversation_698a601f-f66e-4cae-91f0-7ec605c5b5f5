<?php

declare(strict_types=1);

namespace App\BrandAssets\File;

use App\BrandAssets\Exception\BrandAssetsException;
use App\Config\Helper\ConfigFileHelperInterface;

final readonly class BrandAssetsFilePathResolver
{
    public function __construct(
        private ConfigFileHelperInterface $configFileHelper
    )
    {
    }

    public function resolvePhpFromJsonFilePath(string $jsonFilePath): string
    {
        $slug = $this->getSlugFromFilePath($jsonFilePath);

        return $this->configFileHelper->getBrandAssetsPhpFilePath($slug);
    }

    private function getSlugFromFilePath(string $filePath): string
    {
        if (preg_match('~/([a-z0-9]+)\.(json|php)$~', $filePath, $matches) === 1) {
            return $matches[1];
        }

        throw new BrandAssetsException(
            sprintf('Failed to extract slug from file path "%s"', $filePath),
        );
    }
}

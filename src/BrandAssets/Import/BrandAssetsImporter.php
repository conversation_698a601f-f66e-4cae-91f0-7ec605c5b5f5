<?php

declare(strict_types=1);

namespace App\BrandAssets\Import;

use App\BrandAssets\Exception\BrandAssetsImportFailedException;
use App\BrandAssets\File\BrandAssetsFileFactory;
use App\BrandAssets\File\BrandAssetsFilePathResolver;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\Shared\Domain\Validator\JsonSchemaValidator;

final readonly class BrandAssetsImporter
{
    public function __construct(
        private JsonSchemaValidator $brandAssetsJsonSchemaValidator,
        private BrandAssetsFilePathResolver $brandAssetsFilePathResolver,
        private BrandAssetsFileFactory $brandAssetsFileFactory
    )
    {
    }

    public function import(SerializedFileInterface $brandAssetsJsonFile, bool $useOpcache): void
    {
        try {
            $config = $brandAssetsJsonFile->getContents();
            $this->brandAssetsJsonSchemaValidator->assert($config);

            $brandAssetsPhpFilePath = $this->brandAssetsFilePathResolver->resolvePhpFromJsonFilePath(
                jsonFilePath: $brandAssetsJsonFile->getRealFilePath(),
            );

            $this->brandAssetsFileFactory
                ->createPhp(
                    filePath  : $brandAssetsPhpFilePath,
                    useOpcache: $useOpcache,
                )
                ->writeContent($config);
        } catch (\Throwable $exception) {
            throw new BrandAssetsImportFailedException(
                sprintf(
                    'Failed to import brand asset JSON file "%s"',
                    $brandAssetsJsonFile->getRealFilePath(),
                ),
                0,
                $exception,
            );
        }
    }
}

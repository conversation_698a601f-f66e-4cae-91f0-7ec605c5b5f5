<?php

declare(strict_types=1);

namespace App\BrandAssets\Settings;

use App\BrandAssets\Exception\BrandAssetsNotFoundException;
use App\BrandAssets\File\BrandAssetsFileRepository;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;

final readonly class BrandAssetsSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private BrandAssetsFileRepository $brandAssetsFileRepository
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'brand_asset';
    }

    public function create(): BrandAssetsSettings
    {
        $brandSlug = $this->websiteConfigurationHelper->getConfiguration()->getBrandSlug();
        $assetsConfig = $this->brandAssetsFileRepository->getPhpAssetsFileForBrand($brandSlug)->getContents();
        $assetsConfig = $assetsConfig['assets'] ?? null;

        if ($assetsConfig === null) {
            throw BrandAssetsNotFoundException::create($brandSlug);
        }

        return new BrandAssetsSettings($assetsConfig, $brandSlug);
    }
}

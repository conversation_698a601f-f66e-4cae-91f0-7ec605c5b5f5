<?php

declare(strict_types=1);

namespace App\Cheq\Settings;

use App\Debug\Request\DebugRequestInterface;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class CheqSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest,
        private bool $allowEnabled
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'cheq';
    }

    public function create(): CheqSettings
    {
        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['cheq'];

        if ($this->debugRequest->enableCheq()) {
            $enabled = true;
        } else {
            $enabled = $this->allowEnabled && $this->isModuleEnabled($moduleConfig);
        }

        return new CheqSettings(
            enabled: $enabled,
        );
    }
}

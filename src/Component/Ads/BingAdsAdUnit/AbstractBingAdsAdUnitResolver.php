<?php

declare(strict_types=1);

namespace App\Component\Ads\BingAdsAdUnit;

use App\BingAdsStyle\BingAdsStyleRepository;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;

abstract class AbstractBingAdsAdUnitResolver implements ComponentResolverInterface
{
    public const string KEY_AMOUNT      = 'amount';
    public const string KEY_AD_STYLE_ID = 'ad_style_id';

    public const array CONFIG_KEYS = [
        self::KEY_AMOUNT,
        LayoutInterface::KEY,
    ];

    public function __construct(
        private readonly ComponentOptionsResolverInterface $optionsResolver,
        private readonly BingAdsStyleRepository $bingAdsStyleRepository
    )
    {
    }

    abstract public static function getSupportedComponent(): string;

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(BingAdsAdUnitLayout::class);

        $this->optionsResolver->define(self::KEY_AMOUNT)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanValidator(0));

        $this->optionsResolver->define(self::KEY_AD_STYLE_ID)
            ->setAllowedType(OptionType::TYPE_INTEGER, true)
            ->setDefaultValue(null)
            ->addValidator(
                new AllowedValueValidator($this->bingAdsStyleRepository->getAvailableStyleIds()),
            );

        return $this->optionsResolver->resolve($options);
    }
}

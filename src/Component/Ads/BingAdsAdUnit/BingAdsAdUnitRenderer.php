<?php

declare(strict_types=1);

namespace App\Component\Ads\BingAdsAdUnit;

use App\Ads\AdsAmountRegistry;
use App\BingAds\Helper\BingAdsHelper;
use App\Component\Ads\BingAdsBottomAdUnit\BingAdsBottomAdUnitComponent;
use App\Component\Ads\BingAdsSidebarAdUnit\BingAdsSidebarAdUnitComponent;
use App\Component\Ads\BingAdsTopAdUnit\BingAdsTopAdUnitComponent;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\BingAdsAdUnitViewDataRequestInterface;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

class BingAdsAdUnitRenderer extends AbstractComponentRenderer
{
    private const array SUPPORTED_COMPONENTS = [
        BingAdsTopAdUnitComponent::class,
        BingAdsBottomAdUnitComponent::class,
        BingAdsSidebarAdUnitComponent::class,
    ];

    public function __construct(
        private readonly BingAdsHelper $bingAdsHelper,
        private readonly Environment $twig,
        private readonly AdsAmountRegistry $adsAmountRegistry
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $bingAdUnitComponent = $this->getBingAdUnitComponent($component);

        if (!($bingAdUnitComponent instanceof BingAdsAdUnitViewDataRequestInterface)) {
            return;
        }

        $request->bingAds()->addAdUnit($bingAdUnitComponent);

        $this->adsAmountRegistry->increasePreloadedAdsAmount($bingAdUnitComponent->getAmount());
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if ($this->bingAdsHelper->getBingAds() === null) {
            return '';
        }

        $bingAdUnitComponent = $this->getBingAdUnitComponent($component);

        $viewDataRegistry = $view->getDataRegistry();

        return $this->twig->render(
            $bingAdUnitComponent->getLayout()->getTwigTemplate(),
            [
                'query'        => $viewDataRegistry->getQuery(),
                'layout'       => $bingAdUnitComponent->getLayout()->value,
                'container_id' => $bingAdUnitComponent->getContainer(),
            ],
        );
    }

    private function getBingAdUnitComponent(ComponentInterface $component): BingAdsAdUnitComponentInterface
    {
        if (!$component instanceof BingAdsAdUnitComponentInterface) {
            throw UnsupportedComponentException::create($component, self::SUPPORTED_COMPONENTS);
        }

        return $component;
    }
}

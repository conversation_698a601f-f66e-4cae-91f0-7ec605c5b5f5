<?php

declare(strict_types=1);

namespace App\Component\Ads\BingAdsBottomAdUnit;

use App\Component\Ads\BingAdsAdUnit\AbstractBingAdsAdUnitComponent;
use App\Component\Ads\BingAdsAdUnit\BingAdsAdUnitLayout;

class BingAdsBottomAdUnitComponent extends AbstractBingAdsAdUnitComponent
{
    public function __construct(
        BingAdsAdUnitLayout $layout,
        int $amount,
        ?int $adStyleId = null
    )
    {
        parent::__construct($layout, self::POSITION_BOTTOM, $amount, $adStyleId);
    }

    public static function getType(): string
    {
        return 'bing_ads_bottom_ad_unit';
    }
}

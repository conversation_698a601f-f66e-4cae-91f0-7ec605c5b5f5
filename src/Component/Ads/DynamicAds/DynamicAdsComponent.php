<?php

declare(strict_types=1);

namespace App\Component\Ads\DynamicAds;

use App\JsonTemplate\Component\AbstractComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;

final class DynamicAdsComponent extends AbstractComponent implements ParentComponentInterface
{
    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(
        public readonly array $components
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getComponents(): array
    {
        return $this->components;
    }

    public static function getType(): string
    {
        return 'dynamic_ads';
    }

    public function getRenderer(): string
    {
        return DynamicAdsRenderer::class;
    }
}

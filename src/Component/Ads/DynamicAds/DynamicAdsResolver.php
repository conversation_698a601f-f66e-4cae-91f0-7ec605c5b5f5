<?php

declare(strict_types=1);

namespace App\Component\Ads\DynamicAds;

use App\Component\Ads\GoogleAdsBottomUnit\GoogleAdsBottomUnitResolver;
use App\Component\Ads\GoogleAdsTopUnit\GoogleAdsTopUnitResolver;
use App\Generic\Validator\AllowedEnumCaseValidator;
use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;
use App\JsonTemplate\Component\AbstractComponentResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedPatternValidator;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;

final class DynamicAdsResolver extends AbstractComponentResolver
{
    public const string KEY_AMOUNT           = 'amount';
    public const string KEY_CONTAINER_SUFFIX = GoogleAdsTopUnitResolver::KEY_CONTAINER_SUFFIX;
    public const string KEY_REPEATED         = GoogleAdsBottomUnitResolver::KEY_REPEATED;
    public const string KEY_UNIT             = 'unit';

    public static function getSupportedComponent(): string
    {
        return DynamicAdsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->define(self::KEY_AMOUNT)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanValidator(0));

        $this->optionsResolver->define(self::KEY_CONTAINER_SUFFIX)
            ->setDefaultValue(null)
            ->setAllowedType(OptionType::TYPE_STRING, true)
            ->addValidator(
                new AllowedPatternValidator(GoogleCsaContainerAffixHelperInterface::CONTAINER_REGEX),
            );

        $this->optionsResolver->define(self::KEY_REPEATED)
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(0));

        $this->optionsResolver->define(self::KEY_UNIT)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_STRING)
            ->addValidator(new AllowedEnumCaseValidator(DynamicAdsUnit::cases()));

        return $this->optionsResolver->resolve($options);
    }
}

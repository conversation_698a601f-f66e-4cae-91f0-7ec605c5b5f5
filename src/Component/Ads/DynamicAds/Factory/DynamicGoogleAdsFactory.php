<?php

declare(strict_types=1);

namespace App\Component\Ads\DynamicAds\Factory;

use App\Ads\AdProvider;
use App\Component\Ads\DynamicAds\DynamicAdsUnit;
use App\Component\Ads\GoogleAdsBottomUnit\GoogleAdsBottomUnitComponent;
use App\Component\Ads\GoogleAdsBottomUnit\GoogleAdsBottomUnitResolver;
use App\Component\Ads\GoogleAdsTopUnit\GoogleAdsTopUnitComponent;
use App\Component\Ads\GoogleAdsTopUnit\GoogleAdsTopUnitResolver;
use App\Debug\Request\DebugRequestInterface;
use App\JsonTemplate\Component\ComponentFactory;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;

final class DynamicGoogleAdsFactory extends AbstractDynamicAdsFactory
{
    private const array OPTIONS_FILTER_GOOGLE_ADS_BOTTOM = GoogleAdsBottomUnitResolver::CONFIG_KEYS;
    private const array OPTIONS_FILTER_GOOGLE_ADS_TOP    = GoogleAdsTopUnitResolver::CONFIG_KEYS;

    public function __construct(
        private readonly DebugRequestInterface $debugRequest,
        private readonly WebsiteSettingsHelper $websiteSettingsHelper
    )
    {
    }

    public function getPriority(): int
    {
        return 10;
    }

    public function getType(): string
    {
        return AdProvider::TYPE_GOOGLE;
    }

    public function isEnabled(DynamicAdsUnit $unit): bool
    {
        if ($unit === DynamicAdsUnit::SIDEBAR) {
            return false;
        }

        if ($this->debugRequest->forcePrimaryAdsType() !== null) {
            return $this->debugRequest->forcePrimaryAdsType() === $this->getType();
        }

        return $this->websiteSettingsHelper
            ->getSettings()
            ->getGoogleAdSense()
            ->isDynamicAdsEnabled();
    }

    /**
     * @inheritDoc
     */
    public function generateComponents(
        DynamicAdsUnit $unit,
        array $options,
        ComponentFactory $componentFactory
    ): array
    {
        $options = $this->generateGoogleAdsOptions($unit, $options);

        if ($options === []) {
            return [];
        }

        return $componentFactory->createMultipleFromOptions($options);
    }

    /**
     * @param mixed[] $options
     *
     * @return mixed[]
     */
    public function generateGoogleAdsOptions(
        DynamicAdsUnit $unit,
        array $options
    ): array
    {
        if ($unit === DynamicAdsUnit::TOP) {
            return [
                $this->generateComponentOptions(
                    GoogleAdsTopUnitComponent::getType(),
                    self::OPTIONS_FILTER_GOOGLE_ADS_TOP,
                    $options,
                ),
            ];
        }

        if ($unit === DynamicAdsUnit::BOTTOM) {
            return [
                $this->generateComponentOptions(
                    GoogleAdsBottomUnitComponent::getType(),
                    self::OPTIONS_FILTER_GOOGLE_ADS_BOTTOM,
                    $options,
                ),
            ];
        }

        return [];
    }
}

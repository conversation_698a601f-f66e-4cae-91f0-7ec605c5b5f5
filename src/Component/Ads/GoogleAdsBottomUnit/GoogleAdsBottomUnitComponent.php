<?php

declare(strict_types=1);

namespace App\Component\Ads\GoogleAdsBottomUnit;

use App\JsonTemplate\Component\AbstractComponent;

final class GoogleAdsBottomUnitComponent extends AbstractComponent
{
    public function __construct(
        public readonly int $amount,
        public readonly int $numRepeated,
        public readonly string $container
    )
    {
    }

    public static function getType(): string
    {
        return 'google_ads_bottom_unit';
    }

    public function getRenderer(): string
    {
        return GoogleAdsBottomUnitRenderer::class;
    }
}

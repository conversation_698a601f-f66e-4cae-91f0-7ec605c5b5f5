<?php

declare(strict_types=1);

namespace App\Component\Ads\GoogleAdsBottomUnit;

use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedPatternValidator;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;

final class GoogleAdsBottomUnitResolver implements ComponentResolverInterface
{
    public const string KEY_AMOUNT           = 'amount';
    public const string KEY_REPEATED         = 'repeated';
    public const string KEY_CONTAINER_SUFFIX = 'container_suffix';

    public const array CONFIG_KEYS = [
        self::KEY_AMOUNT,
        self::KEY_REPEATED,
        self::KEY_CONTAINER_SUFFIX,
    ];

    public function __construct(
        private readonly ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return GoogleAdsBottomUnitComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->define(self::KEY_AMOUNT)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanValidator(0));

        $this->optionsResolver->define(self::KEY_REPEATED)
            ->setDefaultValue(0)
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(0));

        $this->optionsResolver->define(self::KEY_CONTAINER_SUFFIX)
            ->setDefaultValue(null)
            ->setAllowedType(OptionType::TYPE_STRING, true)
            ->addValidator(
                new AllowedPatternValidator(GoogleCsaContainerAffixHelperInterface::CONTAINER_REGEX),
            );

        return $this->optionsResolver->resolve($options);
    }
}

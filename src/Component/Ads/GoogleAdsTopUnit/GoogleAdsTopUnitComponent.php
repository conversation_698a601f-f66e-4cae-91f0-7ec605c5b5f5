<?php

declare(strict_types=1);

namespace App\Component\Ads\GoogleAdsTopUnit;

use App\JsonTemplate\Component\AbstractComponent;

final class GoogleAdsTopUnitComponent extends AbstractComponent
{
    public function __construct(
        public readonly int $amount,
        public readonly string $container
    )
    {
    }

    public static function getType(): string
    {
        return 'google_ads_top_unit';
    }

    public function getRenderer(): string
    {
        return GoogleAdsTopUnitRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Ads\GoogleAdsTopUnit;

use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

final class GoogleAdsTopUnitFactory implements ComponentFactoryInterface
{
    private const string AD_UNIT_CONTAINER = 'csa-top';

    public function __construct(
        private readonly GoogleCsaContainerAffixHelperInterface $googleCsaContainerAffixHelper
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return GoogleAdsTopUnitComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new GoogleAdsTopUnitComponent(
            amount   : $options[GoogleAdsTopUnitResolver::KEY_AMOUNT],
            container: $this->getContainer($options),
        );
    }

    /**
     * @param mixed[] $options
     */
    private function getContainer(array $options): string
    {
        return $this->googleCsaContainerAffixHelper->getAffixedContainer(
            container      : self::AD_UNIT_CONTAINER,
            containerSuffix: $options[GoogleAdsTopUnitResolver::KEY_CONTAINER_SUFFIX],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Ads\HasBingAds;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum HasBingAdsLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Ads/HasBingAds/layout/default/has_bing_ads-default.html.twig',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPage;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContentPageLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Content/ContentPage/layout/default/content_page-default.html.twig',
        };
    }
}

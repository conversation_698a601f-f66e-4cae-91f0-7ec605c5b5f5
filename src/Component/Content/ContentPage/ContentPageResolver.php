<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPage;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;

final class ContentPageResolver extends AbstractSpaceResolver
{
    public static function getSupportedComponent(): string
    {
        return ContentPageComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(ContentPageLayout::class);
    }
}

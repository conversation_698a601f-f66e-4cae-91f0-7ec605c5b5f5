appReady.push(function () {
    Helper.addDelegateEvent(
        'click',
        '.content-page',
        '.content-page__read-more',
        function (event, targetElement, parentElement) {
            event.preventDefault();

            parentElement.querySelector('.content-page__description').classList.remove('content-page__description--clamp');
            parentElement.querySelector('.content-page__more').classList.add('content-page__more--visible');

            var headerImage = parentElement.querySelector('.content-page__header-image');

            if (headerImage !== null) {
                headerImage.classList.add('content-page__header-image--hidden');
            }

            targetElement.classList.add('content-page__read-more--hidden');
        }
    );
});

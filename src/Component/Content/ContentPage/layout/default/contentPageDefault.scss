/** @define content-page */
.content-page--default {
    // stylelint-disable plugin/selector-bem-pattern
    .text-list {
        &:not(ol) {
            list-style: disc;
        }

        &__item {
            display: list-item;
            margin-left: 2rem;

            &::marker {
                color: var(--brand-primary-color);
            }
        }

        + .text-paragraph {
            margin-top: 2.4rem;
        }
    }

    .text-heading {
        margin: 2rem 0 1rem 0;
    }

    .text-paragraph + .text-list {
        margin-top: 2.4rem;
    }

    // stylelint-enable plugin/selector-bem-pattern

    .content-page {
        &__content {
            background: #f9fafc;
            border-left: 0.3rem solid var(--brand-primary-color);
            font-size: 1.4rem;
            line-height: 1.8rem;
            padding: 1.5rem 1rem;
        }

        &__header {
            display: flex;
        }

        &__header-image {
            align-self: center;
            flex-shrink: 0;
            height: 8rem;
            object-fit: contain;
            width: 8rem;

            &--hidden {
                display: none;
            }
        }

        &__title {
            color: #4f5054;
            font-size: 2.2rem;
            line-height: 2.6rem;
            margin-bottom: 1rem;
        }

        &__reading-time {
            color: #8a8a8a;
            display: inline-block;

            &::after {
                content: " - ";
            }
        }

        &__description {
            color: #4c4c4c;
            display: block;

            &--clamp {
                -webkit-box-orient: vertical;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-word;
            }
        }

        &__read-more {
            color: #4c4c4c;
            text-decoration: underline;

            &--hidden {
                visibility: hidden;
            }
        }

        &__image {
            border-radius: 1rem;
            height: auto;
            max-width: 100%;
        }

        &__more {
            display: none;
            flex-direction: column;
            row-gap: 2rem;

            &--visible {
                display: flex;
            }
        }

        &__paragraph {
            &-title {
                color: #191919;
                font-size: 1.5rem;
            }

            &-description {
                color: #4c4c4c;
            }
        }

        @media #{map-get($media-max, b)} {
            &__header-image {
                display: none;
            }
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageCategoryHeader;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContentPageCategoryHeaderLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Content/ContentPageCategoryHeader/layout/default/content_page_category_header-default.html.twig',
        };
    }
}

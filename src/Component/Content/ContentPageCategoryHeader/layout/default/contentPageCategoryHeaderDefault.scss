/** @define content-page-category-header */
.content-page-category-header--default {
    .content-page-category-header {
        &__content {
            background-color: var(--brand-primary-color);
            border-radius: 0.4rem;
            margin: 0 auto;
            padding: 2.4rem;
        }

        &__title {
            color: #ffffff;
            display: block;
            font-size: 3.6rem;
            line-height: 1;
            overflow-wrap: break-word;
            text-align: left;
        }
    }
}

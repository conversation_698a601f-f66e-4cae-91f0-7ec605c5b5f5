<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageCategoryResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class ContentPageCategoryResultsComponent extends AbstractSpaceComponent
{
    /**
     * @param array{text: string, highlight: ?string}|null $title
     * @param string[]                                     $componentSpaceModifiers
     */
    public function __construct(
        public readonly ?array $title,
        public readonly ?int $maxLevel,
        public readonly ?bool $hasContentPagesWithImage,
        public readonly ContentPageCategoryResultsLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_category_results';
    }

    public function getRenderer(): string
    {
        return ContentPageCategoryResultsRenderer::class;
    }
}

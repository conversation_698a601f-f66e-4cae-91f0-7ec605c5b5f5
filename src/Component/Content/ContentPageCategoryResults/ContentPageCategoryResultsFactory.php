<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageCategoryResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class ContentPageCategoryResultsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContentPageCategoryResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContentPageCategoryResultsComponent(
            title                   : $options[ContentPageCategoryResultsResolver::KEY_TITLE],
            maxLevel                : $options[ContentPageCategoryResultsResolver::KEY_MAX_LEVEL],
            hasContentPagesWithImage: $options[ContentPageCategoryResultsResolver::KEY_HAS_CONTENT_PAGES_WITH_IMAGE],
            layout                  : ContentPageCategoryResultsLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers : $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

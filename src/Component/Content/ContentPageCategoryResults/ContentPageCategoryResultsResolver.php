<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageCategoryResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;

final class ContentPageCategoryResultsResolver extends AbstractSpaceResolver
{
    public const string KEY_TITLE                        = 'title';
    public const string KEY_TITLE_TEXT                   = 'text';
    public const string KEY_TITLE_HIGHLIGHT              = 'highlight';
    public const string KEY_MAX_LEVEL                    = 'max_level';
    public const string KEY_HAS_CONTENT_PAGES_WITH_IMAGE = 'has_content_pages_with_image';

    public static function getSupportedComponent(): string
    {
        return ContentPageCategoryResultsComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::KEY_TITLE)
            ->setAllowedType(OptionType::TYPE_ARRAY, true)
            ->setNestedResolver(
                static function (ComponentOptionsResolverInterface $optionsResolver): void {
                    $optionsResolver->define(self::KEY_TITLE_TEXT)
                        ->setAllowedType(OptionType::TYPE_STRING)
                        ->setRequired();

                    $optionsResolver->define(self::KEY_TITLE_HIGHLIGHT)
                        ->setAllowedType(OptionType::TYPE_STRING, true)
                        ->setDefaultValue(null);
                },
            )
            ->setDefaultValue(null);

        $this->optionsResolver->define(self::KEY_MAX_LEVEL)
            ->setAllowedType(OptionType::TYPE_INTEGER, true)
            ->addValidator(new GreaterThanOrEqualValidator(0));

        $this->optionsResolver->define(self::KEY_HAS_CONTENT_PAGES_WITH_IMAGE)
            ->setAllowedType(OptionType::TYPE_BOOLEAN, true)
            ->setDefaultValue(null);

        $this->optionsResolver->defineLayout(ContentPageCategoryResultsLayout::class);
    }
}

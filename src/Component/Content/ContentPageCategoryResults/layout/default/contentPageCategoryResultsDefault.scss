/** @define content-page-category-results */
.content-page-category-results--default {
    .content-page-category-results {
        &__title {
            border-bottom: 0;
            color: #1d2849;
            font-size: 2.4rem;
            line-height: 3.2rem;
            margin-bottom: 2.2rem;
            padding-bottom: 1.4rem;

            @media #{map-get($media-max, b)} {
                padding-bottom: 1.2rem;
            }

            &--highlighted {
                color: var(--brand-primary-color);
            }
        }

        &__items {
            display: grid;
            gap: 2rem;
            grid-auto-rows: 1fr;
            grid-template-columns: 1fr;
            row-gap: 0.6rem;

            @media #{map-get($media-min, d)} {
                grid-template-columns: repeat(4, 1fr);
            }

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(3, 1fr);
            }

            @media #{map-get($media, b)} {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        &__item-link {
            color: var(--container__section_color, #1d2849);
            font-size: 1.6rem;
            line-height: 2.4rem;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

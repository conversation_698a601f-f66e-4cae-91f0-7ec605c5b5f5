{# @var title array|null #}
{# @var categories Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageNestedCategory[] #}
{# @var layout string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['contentPageCategoryResultsDefault']) }}
{% set component_class = 'content-page-category-results' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__content">
        {% if title is not null %}
            <h2 class="{{ component_class }}__title">
                {{ title.translation_id|trans|highlight_with_class(title.highlight|trans, component_class~"__title--highlighted") }}
            </h2>
        {% endif %}
        <div class="{{ component_class }}__items">
            {% for category in categories %}
                <div class="{{ component_class }}__item">
                    <a href="{{ generate_category_url(category) }}" class="{{ component_class }}__item-link">{{ category.title }}</a>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

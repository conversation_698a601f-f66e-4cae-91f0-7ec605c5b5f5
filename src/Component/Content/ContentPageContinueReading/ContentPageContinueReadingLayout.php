<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageContinueReading;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContentPageContinueReadingLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Content/ContentPageContinueReading/layout/default/content_page_continue_reading-default.html.twig',
        };
    }
}

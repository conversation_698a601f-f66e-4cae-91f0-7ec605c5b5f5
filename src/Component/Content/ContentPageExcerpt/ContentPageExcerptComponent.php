<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageExcerpt;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

class ContentPageExcerptComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ?int $maxLength,
        public readonly ?int $startAfterLength,
        public readonly bool $splitOnLineEnd,
        public readonly ContentPageExcerptLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_excerpt';
    }

    public function getRenderer(): string
    {
        return ContentPageExcerptRenderer::class;
    }
}

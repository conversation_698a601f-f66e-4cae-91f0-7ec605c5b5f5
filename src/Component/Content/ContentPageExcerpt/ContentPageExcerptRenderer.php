<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageExcerpt;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\KeywordHighlightOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;
use Visymo\Shared\Domain\Formatter\TruncateFormatterFactory;

class ContentPageExcerptRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly TruncateFormatterFactory $truncateFormatterFactory,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
                ViewDataProperty::QUERY,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageExcerptComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageExcerptComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageExcerptComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageExcerptComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $contentPage = $viewDataRegistry->getContentPage()->page;

        if ($contentPage === null) {
            return '';
        }

        $description = $this->getDescription($component, $contentPage);

        if ($description === null) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'query'                     => (string)$viewDataRegistry->getQuery(),
                'description'               => $description,
                'layout'                    => $component->layout->value,
                'keyword_highlight_option'  => $viewDataRegistry->getKeywordHighlight(KeywordHighlightOption::DEFAULT_FALSE),
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    private function getDescription(ContentPageExcerptComponent $component, ContentPage $contentPage): ?string
    {
        $truncateFormatter = $this->truncateFormatterFactory->create(
            maxLength       : $component->maxLength,
            startAfterLength: $component->startAfterLength,
            onLineEnd       : $component->splitOnLineEnd,
        );

        $description = $truncateFormatter->format($contentPage->excerpt);

        return $description !== ''
            ? $description
            : null;
    }
}

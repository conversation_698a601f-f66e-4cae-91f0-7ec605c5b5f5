<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageFooter;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class ContentPageFooterFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContentPageFooterComponent::class;
    }

    /**
     * @param mixed[] $options
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContentPageFooterComponent(
            layout                 : ContentPageFooterLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

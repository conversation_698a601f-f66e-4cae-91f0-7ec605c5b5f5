<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageFooter;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;

final class ContentPageFooterResolver extends AbstractSpaceResolver
{
    public static function getSupportedComponent(): string
    {
        return ContentPageFooterComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(ContentPageFooterLayout::class);
    }
}

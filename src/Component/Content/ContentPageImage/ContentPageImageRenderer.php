<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageImage;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class ContentPageImageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager,
        private readonly ComponentRendererLocator $componentRendererLocator
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageImageComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageImageComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageImageComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageImageComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $contentPage = $viewDataRegistry->getContentPage()->page;
        $contentPageImage = $contentPage?->image;

        if ($contentPage === null || $contentPageImage === null) {
            return $this->renderFallbackImage($component, $view);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'caption'                   => $component->showCaption ? $contentPage->title : null,
                'content_page_image'        => $contentPage->image,
                'align'                     => $component->align->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    private function renderFallbackImage(ContentPageImageComponent $component, ViewInterface $view): string
    {
        if ($component->fallbackImage === null) {
            return '';
        }

        $renderer = $this->componentRendererLocator->getRenderer($component->fallbackImage);

        return $renderer->render($component->fallbackImage, $view);
    }
}

{# @var layout string #}
{# @var align string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['contentPageImageDefault']) }}
{% set component_class = 'content-page-image' %}
<div class="{{ component_class(component_class, [layout, 'align-' ~ align], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <img src="{{ content_page_image_url(content_page_image, 'c368x207') }}" alt="{{ content_page_image.title }}" class="{{ component_class }}__image"/>
    {% if caption is not null %}
        <span class="{{ component_class }}__caption">{{ caption }}</span>
    {% endif %}
</div>

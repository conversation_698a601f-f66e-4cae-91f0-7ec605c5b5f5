/** @define content-page-image */
.content-page-image--large {
    .content-page-image {
        &__image {
            border-radius: 1.2rem;
            display: block;
            max-height: 30vh;
            object-fit: cover;
            object-position: center;
            width: 100%;
        }

        &__caption {
            color: var(--container__section-secondary_color, #555555);
            display: block;
            font-size: 1.2rem;
            font-weight: 300;
            line-height: 1.6rem;
            padding: 0.8rem 0;
            text-align: center;
        }
    }
}

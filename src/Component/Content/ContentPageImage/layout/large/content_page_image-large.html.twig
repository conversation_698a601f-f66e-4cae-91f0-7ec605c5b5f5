{# @var layout string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['contentPageImageLarge']) }}
{% set component_class = 'content-page-image' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <picture>
        <source media="(min-width: 768px)" srcset="{{ content_page_image_url(content_page_image, 'c1200x628') }}"/>
        <img src="{{ content_page_image_url(content_page_image, 'c368x207') }}" alt="{{ content_page_image.title }}" class="{{ component_class }}__image"/>
        {% if caption is not null %}
            <figcaption class="{{ component_class }}__caption">{{ caption }}</figcaption>
        {% endif %}
    </picture>
</div>

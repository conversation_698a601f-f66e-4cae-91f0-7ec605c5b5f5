<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageParagraph;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

class ContentPageParagraphComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ?int $maxLength,
        public readonly ?int $startAfterLength,
        public readonly bool $splitOnLineEnd,
        public readonly ContentPageParagraphLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public function isStartOfParagraph(): bool
    {
        return $this->startAfterLength === null;
    }

    public static function getType(): string
    {
        return 'content_page_paragraph';
    }

    public function getRenderer(): string
    {
        return ContentPageParagraphRenderer::class;
    }
}

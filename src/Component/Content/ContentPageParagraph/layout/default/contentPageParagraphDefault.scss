/** @define content-page-paragraph */
.content-page-paragraph--default {
    // stylelint-disable plugin/selector-bem-pattern
    .text-list {
        &:not(ol) {
            list-style: disc;
        }

        &__item {
            display: list-item;
            margin-left: 2rem;

            &::marker {
                color: var(--brand-primary-color);
            }
        }

        + .text-paragraph {
            margin-top: 2.4rem;
        }
    }

    .text-heading {
        margin: 2rem 0 1rem 0;
    }

    .text-paragraph + .text-list {
        margin-top: 2.4rem;
    }

    // stylelint-enable plugin/selector-bem-pattern

    .content-page-paragraph {
        @media #{map-get($media-min, c)} {
            &__description,
            &__title {
                padding-top: 1.5rem;
            }
        }

        &__description {
            color: var(--container__section_color, var(--description_color, #1d2849));
            font-size: 1.6rem;
            line-height: 2.2rem;
        }

        &__title {
            color: var(--container__section_color, #1d2849);
            font-size: 2rem;
            font-weight: 700;
            line-height: 2.7rem;
            scroll-margin-top: 8rem;
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageParagraphs;

use App\Component\Content\ContentPageParagraph\ContentPageParagraphComponent;
use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class ContentPageParagraphsComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ContentPageParagraphsLayout $layout,
        public readonly ?int $amount,
        public readonly ?ContentPageParagraphComponent $paragraphComponent,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_paragraphs';
    }

    public function getRenderer(): string
    {
        return ContentPageParagraphsRenderer::class;
    }
}

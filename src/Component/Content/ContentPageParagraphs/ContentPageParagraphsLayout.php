<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageParagraphs;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContentPageParagraphsLayout: string implements LayoutInterface
{
    case DEFAULT   = 'default';
    case CONTAINER = 'container';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        if ($this === self::CONTAINER) {
            throw new \LogicException('The container layout does not have a twig template');
        }

        return match ($this) {
            self::DEFAULT => '@component/Content/ContentPageParagraphs/layout/default/content_page_paragraphs-default.html.twig',
        };
    }
}

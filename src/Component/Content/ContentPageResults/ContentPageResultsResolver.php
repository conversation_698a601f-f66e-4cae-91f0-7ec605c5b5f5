<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Component\Generic\Title\TitleComponent;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;
use App\JsonTemplate\Component\Parent\ChildComponentProperty;
use App\JsonTemplate\Component\Parent\ParentComponentResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\LesserThanOrEqualValidator;

final class ContentPageResultsResolver extends AbstractSpaceResolver implements ParentComponentResolverInterface
{
    public const string KEY_AMOUNT_IN_ROW   = 'amount_in_row';
    public const string KEY_TITLE_COMPONENT = 'title_component';
    public const string KEY_IGNORE_QUERY    = 'ignore_query';
    public const string KEY_RESET_COUNTER   = 'reset_counter';

    public static function getSupportedComponent(): string
    {
        return ContentPageResultsComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(OrganicResultsComponentInterface::KEY_AMOUNT)
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(1))
            ->setRequired();

        $this->optionsResolver->define(self::KEY_AMOUNT_IN_ROW)
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->setDefaultValue(4)
            ->addValidator(new GreaterThanOrEqualValidator(1))
            ->addValidator(new LesserThanOrEqualValidator(4));

        $this->optionsResolver->define(self::KEY_IGNORE_QUERY)
            ->setDefaultValue(false)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->define(self::KEY_RESET_COUNTER)
            ->setDefaultValue(true)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->define(OrganicResultsComponentInterface::KEY_RESULT_AMOUNT_OPTIMIZATION)
            ->setDefaultValue(true)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->defineComponent(
            self::KEY_TITLE_COMPONENT,
            TitleComponent::class,
            true,
        );

        $this->optionsResolver->defineLayout(ContentPageResultsLayout::class);
    }

    /**
     * @inheritDoc
     */
    public function getChildComponentProperties(): array
    {
        return [
            ChildComponentProperty::createForOneComponent(self::KEY_TITLE_COMPONENT),
        ];
    }
}

@import "../contentPageResultsMixins";

/** @define content-page-results */
.content-page-results--card-1 {
    .content-page-results {
        &__items {
            display: grid;
            gap: 3rem;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        // Image container includes category label
        &__image-container {
            position: relative;

            // Shadow overlay at the bottom of the image
            &::after {
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 100%);
                bottom: 0;
                content: "";
                display: block;
                height: 3.4rem;
                left: 0;
                position: absolute;
                right: 0;
            }
        }

        &__image {
            display: block;
            height: 100%;
            width: 100%;
        }

        &__category {
            backdrop-filter: blur(0.4rem);
            background: linear-gradient(
                180deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.4) 100%
            );
            border-top: 0.1rem solid rgba(255, 255, 255, 0.3);
            bottom: 0;
            color: #ffffff;
            display: block;
            font-size: 1.4rem;
            font-weight: 600;
            line-height: 3.4rem;
            max-width: 100%;
            overflow: hidden;
            padding: 0 1.2rem;
            position: absolute;
            text-overflow: ellipsis;
            white-space: nowrap;
            z-index: 1;
        }

        // Content part below image container
        &__content-container {
            margin-top: 2rem;
        }

        &__title {
            -webkit-box-orient: vertical;
            color: #101828;
            display: block;
            display: -webkit-box;
            font-size: 2rem;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
        }

        &__description {
            -webkit-box-orient: vertical;
            color: #475467;
            display: -webkit-box;
            font-size: 1.6rem;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            max-height: 4.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &__read {
            color: var(--brand-primary-color);
            display: inline-block;
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.4rem;
            margin-top: 0.8rem;

            &::after {
                content: $vsi-arrow-right;
                display: inline-block;
                font-size: 2rem;
                transform: rotate(-45deg);
            }
        }
    }
}

@include rtl-rotate-fix(".content-page-results--card-1");

/** @define content-page-results */
.content-page-results--card-10 {
    .content-page-results {
        &__items {
            display: grid;
            gap: 3rem;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        &__container {
            display: flex;
            flex-direction: column;
            gap: 1.6rem;
        }

        // Image
        &__image {
            width: 100%;
        }

        // Title and description
        &__content-container {
            -webkit-box-orient: vertical;
            color: var(--container__section_color, #1d2849);
            display: -webkit-box;
            font-size: 1.6rem;
            -webkit-line-clamp: 6;
            line-clamp: 6;
            line-height: 2.4rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &__title {
            display: block;
            font-size: 2rem;
        }
    }
}

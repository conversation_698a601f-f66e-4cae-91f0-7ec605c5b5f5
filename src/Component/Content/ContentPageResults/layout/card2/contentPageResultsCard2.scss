@import "../contentPageResultsMixins";

/** @define content-page-results */
.content-page-results--card-2 {
    .content-page-results {
        &__items {
            display: grid;
            gap: 3rem;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        &__container {
            border: 0.1rem solid #eaecf0;
            border-radius: 1.6rem;
            display: block;
            height: 100%;
            overflow: hidden;
        }

        &__image {
            width: 100%;
        }

        &__content-container {
            display: flex;
            flex-direction: column;
            padding: 2.4rem;
            row-gap: 0.8rem;

            @media #{map-get($media-max, c)} {
                padding: 1.6rem;
            }
        }

        // Badges
        &__badge-container {
            column-gap: 1rem;
            display: grid;
            font-size: 1.2rem;
            grid-template-columns: auto 1fr;
            line-height: 1.8rem;
        }

        &__category {
            color: var(--brand-primary-color);
            display: inline-block;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &__read-time {
            color: #667085;
            justify-self: end;
            white-space: nowrap;
        }

        // Title and description
        &__title {
            -webkit-box-orient: vertical;
            color: #101828;
            display: block;
            display: -webkit-box;
            font-size: 2rem;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            overflow: hidden;
            padding-right: 2.7rem;
            position: relative;
            text-overflow: ellipsis;
            word-break: break-word;

            &::after {
                color: var(--brand-primary-color);
                content: $vsi-arrow-right;
                display: inline-block;
                font-size: 2.4rem;
                font-weight: 600;
                position: absolute;
                right: -0.4rem;
                top: 0;
                transform: rotate(-45deg);
            }
        }

        &__description {
            -webkit-box-orient: vertical;
            color: #475467;
            display: -webkit-box;
            font-size: 1.6rem;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            max-height: 4.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

@include rtl-rotate-fix(".content-page-results--card-2");

@import "../contentPageResultsMixins";

/** @define content-page-results */
.content-page-results--card-6 {
    .content-page-results {
        &__items {
            display: grid;
            gap: 3rem;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        &__image {
            width: 100%;
        }

        &__badge-container {
            column-gap: 1rem;
            display: grid;
            grid-template-columns: auto 1fr;
            margin-bottom: 0.8rem;
            margin-top: 2rem;
        }

        &__category {
            color: var(--brand-primary-color);
            display: inline-block;
            flex-grow: 0;
            font-size: 1.4rem;
            font-weight: 700;
            line-height: 2rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &__read-time {
            color: #667085;
            font-size: 1.2rem;
            justify-self: end;
            line-height: 2rem;
            white-space: nowrap;
        }

        &__title {
            -webkit-box-orient: vertical;
            color: var(--container__section_color, #101828);
            display: -webkit-box;
            font-size: 2rem;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.5rem;
            max-height: 5rem;
            overflow: hidden;
            padding-right: 2.7rem;
            position: relative;
            text-overflow: ellipsis;
            word-break: break-word;

            &::after {
                color: var(--brand-primary-color);
                content: $vsi-arrow-right;
                display: inline-block;
                font-size: 2.4rem;
                font-weight: 400;
                position: absolute;
                right: -0.4rem;
                top: 0;
                transform: rotate(-45deg);
            }
        }

        &__description {
            -webkit-box-orient: vertical;
            color: #475467;
            display: -webkit-box;
            font-size: 1.6rem;
            font-weight: 600;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            max-height: 4.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

@include rtl-rotate-fix(".content-page-results--card-6");

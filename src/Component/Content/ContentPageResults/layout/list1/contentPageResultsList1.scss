/** @define content-page-results */
.content-page-results--list-1 {
    .content-page-results {
        &__items {
            column-gap: 3rem;
            display: grid;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        &__item {
            border-top: 0.1rem solid
                var(--container__section-border-color, var(--brand-primary-color));
            padding: 0.8rem 0;
        }

        // Image
        &__image {
            aspect-ratio: 16 / 9;
            border-radius: 1rem;
            object-fit: cover;
            width: 100%;
        }

        // Badges
        &__badge-container {
            align-items: center;
            border: 0.1rem solid var(--container__section-border-color, #d0d5dd);
            border-radius: 1rem;
            color: var(--container__section-secondary_color, #344054);
            column-gap: 1rem;
            display: inline-grid;
            font-size: 1.2rem;
            font-weight: 500;
            grid-template-columns: auto 1fr;
            line-height: 1.8rem;
            margin-bottom: 0.4rem;
            padding: 0.4rem 0.8rem 0.4rem 0.4rem;
            white-space: nowrap;
        }

        &__category {
            border: 0.1rem solid var(--container__section-border-color, #d0d5dd);
            border-radius: 0.6rem;
            display: inline-block;
            overflow: hidden;
            padding: 0.2rem 0.6rem 0.2rem 0.6rem;
            position: relative;
            text-indent: 1.1rem;
            text-overflow: ellipsis;

            // Dot
            &::before {
                background: var(--brand-primary-color);
                border-radius: 50%;
                box-shadow: 0 0 0 0.2rem var(--brand-primary-color_light);
                content: "";
                display: block;
                height: 0.6rem;
                left: 0.6rem;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 0.6rem;
            }
        }

        // Title
        &__title {
            -webkit-box-orient: vertical;
            color: var(--container__section_color, #101828);
            display: -webkit-box;
            font-size: 1.6rem;
            font-weight: 600;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            line-height: 2.4rem;
            max-height: 4.8rem;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
        }
    }
}

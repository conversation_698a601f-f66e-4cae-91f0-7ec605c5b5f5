/** @define content-page-results */
.content-page-results--list-2 {
    .content-page-results {
        &__items {
            column-gap: 3rem;
            display: grid;
            grid-template-columns: repeat(var(--items_grid-template-columns-amount), 1fr);

            @media #{map-get($media, c)} {
                grid-template-columns: repeat(var(--_items_grid-template-columns), 1fr);

                --_items_grid-template-columns: min(var(--items_grid-template-columns-amount), 2);
            }

            @media #{map-get($media-max, b)} {
                grid-template-columns: 1fr;
            }
        }

        &__item {
            border-top: 0.1rem solid var(--brand-primary-color);
            padding: 1.6rem 0;
        }

        &__container {
            column-gap: 1.2rem;
            display: grid;
            grid-template-columns: auto 8rem;
        }

        // Image
        &__image {
            display: block;
            height: 8rem;
            width: 8rem;
        }

        // Content
        &__content-container {
            font-weight: 600;
            line-height: 2rem;
            overflow: hidden;
        }

        &__category {
            color: #bbbbbb;
            display: block;
            font-size: 1.4rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &__title {
            -webkit-box-orient: vertical;
            color: var(--container__section_color, #101828);
            display: -webkit-box;
            font-size: 1.6rem;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            max-height: 6rem;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

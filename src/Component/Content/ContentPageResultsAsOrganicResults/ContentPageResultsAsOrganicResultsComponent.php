<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageResultsAsOrganicResults;

use App\Component\Content\OrganicContentPageResults\OrganicContentPageResultsComponentInterface;
use App\Component\Web\OrganicResults\OrganicResultsComponent;
use App\Component\Web\OrganicResults\OrganicResultsLayout;

final class ContentPageResultsAsOrganicResultsComponent extends OrganicResultsComponent
    implements OrganicContentPageResultsComponentInterface
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        int $amount,
        bool $resultAmountOptimization,
        bool $resultDescriptionMoreLink,
        bool $resultDisplayUrlLink,
        bool $resultTitleLink,
        bool $showResultDisplayUrl,
        ?int $maxDescriptionLength,
        OrganicResultsLayout $layout,
        array $componentSpaceModifiers,
        public readonly ?bool $linkToActiveBrand
    )
    {
        parent::__construct(
            $amount,
            $resultAmountOptimization,
            $resultDescriptionMoreLink,
            $resultDisplayUrlLink,
            $resultTitleLink,
            $showResultDisplayUrl,
            $maxDescriptionLength,
            $layout,
            $componentSpaceModifiers,
        );
    }

    public function linkToActiveBrand(): ?bool
    {
        return $this->linkToActiveBrand;
    }

    public static function getType(): string
    {
        return 'content_page_results_as_organic_results';
    }

    public function getRenderer(): string
    {
        return ContentPageResultsAsOrganicResultsRenderer::class;
    }
}

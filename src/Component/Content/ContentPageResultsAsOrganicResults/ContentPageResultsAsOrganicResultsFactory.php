<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageResultsAsOrganicResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Component\Web\OrganicResults\OrganicResultsLayout;
use App\Component\Web\OrganicResults\OrganicResultsResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;

final class ContentPageResultsAsOrganicResultsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContentPageResultsAsOrganicResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContentPageResultsAsOrganicResultsComponent(
            amount                   : $options[OrganicResultsComponentInterface::KEY_AMOUNT],
            resultAmountOptimization : $options[OrganicResultsComponentInterface::KEY_RESULT_AMOUNT_OPTIMIZATION],
            resultDescriptionMoreLink: $options[OrganicResultsResolver::KEY_RESULT_DESCRIPTION_MORE_LINK],
            resultDisplayUrlLink     : $options[OrganicResultsResolver::KEY_RESULT_DISPLAY_URL_LINK],
            resultTitleLink          : $options[OrganicResultsResolver::KEY_RESULT_TITLE_LINK],
            showResultDisplayUrl     : $options[OrganicResultsResolver::KEY_SHOW_RESULT_DISPLAY_URL],
            maxDescriptionLength     : $options[OrganicResultsResolver::KEY_MAX_DESCRIPTION_LENGTH],
            layout                   : OrganicResultsLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers  : $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
            linkToActiveBrand        : $options[ContentPageResultsAsOrganicResultsResolver::KEY_LINK_TO_ACTIVE_BRAND],
        );
    }
}

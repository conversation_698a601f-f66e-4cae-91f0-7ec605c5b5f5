<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageSpotlight;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class ContentPageSpotlightFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContentPageSpotlightComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContentPageSpotlightComponent(
            title                  : $options[ContentPageSpotlightResolver::KEY_TITLE],
            additionalAmount       : $options[ContentPageSpotlightResolver::KEY_ADDITIONAL_AMOUNT],
            layout                 : ContentPageSpotlightLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

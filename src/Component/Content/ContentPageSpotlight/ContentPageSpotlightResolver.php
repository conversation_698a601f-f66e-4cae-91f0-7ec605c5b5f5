<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageSpotlight;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;

class ContentPageSpotlightResolver extends AbstractSpaceResolver
{
    public const string KEY_TITLE             = 'title';
    public const string KEY_TITLE_TEXT        = 'text';
    public const string KEY_TITLE_HIGHLIGHT   = 'highlight';
    public const string KEY_ADDITIONAL_AMOUNT = 'additional_amount';

    public static function getSupportedComponent(): string
    {
        return ContentPageSpotlightComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::KEY_TITLE)
            ->setAllowedType(OptionType::TYPE_ARRAY, true)
            ->setNestedResolver(
                static function (OptionsResolverInterface $optionsResolver): void {
                    $optionsResolver->define(self::KEY_TITLE_TEXT)
                        ->setAllowedType(OptionType::TYPE_STRING)
                        ->setRequired();

                    $optionsResolver->define(self::KEY_TITLE_HIGHLIGHT)
                        ->setAllowedType(OptionType::TYPE_STRING, true)
                        ->setDefaultValue(null);
                },
            )
            ->setDefaultValue(null);

        $this->optionsResolver->define(self::KEY_ADDITIONAL_AMOUNT)
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->setDefaultValue(3)
            ->addValidator(new GreaterThanValidator(0))
            ->setRequired();

        $this->optionsResolver->defineLayout(ContentPageSpotlightLayout::class);
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageTitle;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

class ContentPageTitleComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly ContentPageTitleLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'content_page_title';
    }

    public function getRenderer(): string
    {
        return ContentPageTitleRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\HasContentPage;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionComponent;

final class HasContentPageComponent extends AbstractSearchApiConditionComponent
{
    public static function getType(): string
    {
        return 'has_content_page';
    }

    public function getRenderer(): string
    {
        return HasContentPageRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;

class OrganicContentPageResultsComponent extends AbstractSpaceComponent
    implements OrganicResultsComponentInterface, OrganicContentPageResultsComponentInterface
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        private int $amount,
        private readonly bool $resultAmountOptimization,
        public readonly bool $resultDescriptionMoreLink,
        public readonly bool $resultDisplayUrlLink,
        public readonly bool $resultTitleLink,
        public readonly bool $resultImageLink,
        public readonly bool $showResultDisplayUrl,
        public readonly ?bool $linkToActiveBrand,
        public readonly ?int $maxDescriptionLength,
        public readonly OrganicContentPageResultsLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public function getAmount(): int
    {
        return $this->amount;
    }

    public function decreaseAmount(int $amount): int
    {
        $this->amount = max(0, $this->amount - $amount);

        return $this->amount;
    }

    public function resultAmountOptimization(): bool
    {
        return $this->resultAmountOptimization;
    }

    public function linkToActiveBrand(): ?bool
    {
        return $this->linkToActiveBrand;
    }

    public static function getType(): string
    {
        return 'organic_content_page_results';
    }

    public function getRenderer(): string
    {
        return OrganicContentPageResultsRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\OrganicContentPageResults\Result;

use App\Component\Content\OrganicContentPageResults\Result\Category\OrganicContentPageCategoryResult;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageImage;

readonly class OrganicContentPageResult
{
    public function __construct(
        public string $url,
        public string $displayUrl,
        public string $displayDomain,
        public string $title,
        public string $description,
        public int $readingTime,
        public ?OrganicContentPageCategoryResult $category,
        public ?ContentPageImage $image
    )
    {
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Content\TableOfContents;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class TableOfContentsComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly TableOfContentsLayout $layout,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'table_of_contents';
    }

    public function getRenderer(): string
    {
        return TableOfContentsRenderer::class;
    }
}

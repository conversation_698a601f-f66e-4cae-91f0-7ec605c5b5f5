<?php

declare(strict_types=1);

namespace App\Component\Content\TableOfContents;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class TableOfContentsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return TableOfContentsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new TableOfContentsComponent(
            layout                 : TableOfContentsLayout::from($options[LayoutInterface::KEY]),
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

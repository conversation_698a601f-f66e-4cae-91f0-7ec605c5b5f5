<?php

declare(strict_types=1);

namespace App\Component\Content\TableOfContents;

use App\Component\Content\TableOfContents\Result\TableOfContentsResultGenerator;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class TableOfContentsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager,
        private readonly TableOfContentsResultGenerator $tableOfContentsResultGenerator
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof TableOfContentsComponent) {
            throw UnsupportedComponentException::create($component, [TableOfContentsComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof TableOfContentsComponent) {
            throw UnsupportedComponentException::create($component, [TableOfContentsComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();
        $contentPage = $viewDataRegistry->getContentPage()->page;

        if ($contentPage === null) {
            return '';
        }

        $tableOfContents = $this->tableOfContentsResultGenerator->generateFromContentPage(
            $contentPage,
        );

        if ($tableOfContents === []) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
                'table_of_contents'         => $tableOfContents,
            ],
        );
    }
}

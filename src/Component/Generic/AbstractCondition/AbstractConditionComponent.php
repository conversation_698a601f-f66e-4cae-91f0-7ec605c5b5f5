<?php

declare(strict_types=1);

namespace App\Component\Generic\AbstractCondition;

use App\JsonTemplate\Component\AbstractComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;
use App\JsonTemplate\View\Segment;
use App\JsonTemplate\View\SegmentInterface;

abstract class AbstractConditionComponent extends AbstractComponent implements ParentComponentInterface
{
    private readonly SegmentInterface $matchingSegment;

    private readonly SegmentInterface $nonMatchingSegment;

    /**
     * @param ComponentInterface[] $matchingChildren
     * @param ComponentInterface[] $nonMatchingChildren
     */
    public function __construct(
        array $matchingChildren,
        array $nonMatchingChildren
    )
    {
        $this->matchingSegment = new Segment();
        $this->matchingSegment->addComponents($matchingChildren);

        $this->nonMatchingSegment = new Segment();
        $this->nonMatchingSegment->addComponents($nonMatchingChildren);
    }

    /**
     * @inheritDoc
     */
    public function getComponents(): array
    {
        return [
            ...$this->matchingSegment->getComponents(),
            ...$this->nonMatchingSegment->getComponents(),
        ];
    }

    public function getMatchingSegment(): SegmentInterface
    {
        return $this->matchingSegment;
    }

    public function getNonMatchingSegment(): SegmentInterface
    {
        return $this->nonMatchingSegment;
    }
}

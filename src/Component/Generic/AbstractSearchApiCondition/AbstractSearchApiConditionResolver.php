<?php

declare(strict_types=1);

namespace App\Component\Generic\AbstractSearchApiCondition;

use App\JsonTemplate\Component\AbstractComponentResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\Parent\ChildComponentProperty;
use App\JsonTemplate\Component\Parent\ParentComponentResolverInterface;

abstract class AbstractSearchApiConditionResolver extends AbstractComponentResolver implements ParentComponentResolverInterface
{
    public const string KEY_YES = 'yes';
    public const string KEY_NO  = 'no';

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->defineChildrenArray(self::KEY_YES);
        $this->defineChildrenArray(self::KEY_NO);

        $options = $this->optionsResolver->resolve($options);
        $options = $this->resolveChildrenArray($options, self::KEY_YES, $componentResolver);
        $options = $this->resolveChildrenArray($options, self::KEY_NO, $componentResolver);

        return $options;
    }

    /**
     * @inheritDoc
     */
    public function getChildComponentProperties(): array
    {
        return [
            ChildComponentProperty::createForMultipleComponents(self::KEY_YES),
            ChildComponentProperty::createForMultipleComponents(self::KEY_NO),
        ];
    }
}

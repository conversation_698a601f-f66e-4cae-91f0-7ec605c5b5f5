<?php

declare(strict_types=1);

namespace App\Component\Generic\AbstractSearchApiCondition;

use App\JsonTemplate\Component\Parent\ParentComponentInterface;
use App\JsonTemplate\View\SegmentInterface;

interface SearchApiConditionComponentInterface extends ParentComponentInterface
{
    /**
     * @return SegmentInterface[]
     */
    public function getAllSegments(): array;

    public function getMatchingSegment(): SegmentInterface;

    public function getNonMatchingSegment(): SegmentInterface;
}

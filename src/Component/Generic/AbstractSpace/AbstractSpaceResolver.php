<?php

declare(strict_types=1);

namespace App\Component\Generic\AbstractSpace;

use App\Generic\Validator\AllowedEnumCasesValidator;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

abstract class AbstractSpaceResolver implements ComponentResolverInterface
{
    public const string KEY_COMPONENT_SPACE_MODIFIERS = 'component_space_modifiers';

    public function __construct(
        protected ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    abstract protected function defineOptions(): void;

    /**
     * @return ComponentSpaceModifier[]
     */
    protected function getDefaultComponentSpaceModifiers(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->defineOptions();

        $this->optionsResolver->define(self::KEY_COMPONENT_SPACE_MODIFIERS)
            ->setDefaultValue(
                array_column($this->getDefaultComponentSpaceModifiers(), 'value'),
            )
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->addValidator(
                new AllowedEnumCasesValidator(ComponentSpaceModifier::cases()),
            );

        return $this->optionsResolver->resolve($options);
    }
}

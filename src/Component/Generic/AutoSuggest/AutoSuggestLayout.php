<?php

declare(strict_types=1);

namespace App\Component\Generic\AutoSuggest;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum AutoSuggestLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/AutoSuggest/layout/default/auto_suggest-default.html.twig',
        };
    }
}

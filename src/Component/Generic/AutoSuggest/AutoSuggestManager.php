<?php

declare(strict_types=1);

namespace App\Component\Generic\AutoSuggest;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Helper\PreferencesHelper;

readonly class AutoSuggestManager
{
    public function __construct(
        private ComponentFactory $componentFactory,
        private AutoSuggestFactory $autoSuggestFactory,
        private AutoSuggestRenderer $autoSuggestRenderer,
        private PreferencesHelper $preferencesHelper
    )
    {
    }

    public function renderAutoSuggest(string $parentComponentClass, ViewInterface $view): string
    {
        if ($this->preferencesHelper->getQuerySuggestion() === false) {
            return '';
        }

        return $this->autoSuggestRenderer->render(
            component: $this->getAutoSuggest($parentComponentClass),
            view     : $view,
        );
    }

    private function getAutoSuggest(string $parentComponentClass): AutoSuggestComponent
    {
        /** @var AutoSuggestComponent $autoSuggestComponent */
        $autoSuggestComponent = $this->autoSuggestFactory->create(
            [
                ComponentInterface::KEY_TYPE                    => AutoSuggestComponent::getType(),
                LayoutInterface::KEY                            => AutoSuggestLayout::DEFAULT->value,
                AutoSuggestResolver::KEY_PARENT_COMPONENT_CLASS => $parentComponentClass,
            ],
            $this->componentFactory,
        );

        return $autoSuggestComponent;
    }
}

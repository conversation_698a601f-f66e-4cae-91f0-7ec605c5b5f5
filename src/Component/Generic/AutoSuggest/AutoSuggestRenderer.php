<?php

declare(strict_types=1);

namespace App\Component\Generic\AutoSuggest;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class AutoSuggestRenderer extends AbstractComponentRenderer
{
    public function __construct(private readonly Environment $twig)
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof AutoSuggestComponent) {
            throw UnsupportedComponentException::create($component, [AutoSuggestComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                 => $component->layout->value,
                'parent_component_class' => $component->parentComponentClass,
            ],
        );
    }
}

function SearchHistory() {
    return {
        _cookieName: 'hist',
        _cookieDomain: '',
        _terms: [],
        _termsCount: 0,
        create: function () {
            if (cookieDomain !== undefined) {
                this._cookieDomain = cookieDomain;
            }

            this._terms = this._getHistoryTerms();
            this._termsCount = this._terms.length;
        },
        _getHistoryTerms: function () {
            var terms = CookieHelper.getCookie(this._cookieName);

            if (terms === null) {
                return [];
            }

            try {
                terms = JSON.parse(terms);

                return terms;
            } catch (error) {
                // Ignore
            }

            return [];
        },
        filterTerms: function (query, limit) {
            var terms = [];
            var queryLowerCase = query.toLowerCase();

            if (limit <= 0) {
                return terms;
            }

            for (var index = 0; index < this._termsCount; index++) {
                var term = this._terms[index];
                var termLowerCase = term.toLowerCase();

                if (queryLowerCase === '' || termLowerCase.indexOf(queryLowerCase) === 0) {
                    terms.push([term, Helper.escapeHtml(term), true]);
                    limit--;

                    if (limit === 0) {
                        break;
                    }
                }
            }

            return terms;
        },
        erase: function () {
            CookieHelper.deleteCookie(this._cookieName);

            this._terms = [];
            this._termsCount = 0;
        }
    };
}

/** @define auto-suggest */
.auto-suggest {
    background-color: var(--background-color, #ffffff);
    border: 0.1rem solid var(--border-color, #eceff1);
    border-radius: 0 0 var(--border-radius, 0.3rem) var(--border-radius, 0.3rem);
    border-top: 0;
    box-shadow: var(--box-shadow, 0 0.3rem 0.3rem rgba(20, 23, 26, 0.1));
    display: none;
    font-size: 1.6rem;
    left: 0;
    line-height: var(--item_height, 2.8rem);
    margin-top: var(--margin-top, -0.1rem);
    overflow: auto;
    position: absolute;
    right: 0;
    text-align: left;
    z-index: 999;

    &--visible {
        display: block;
    }

    &__separator {
        border-top: 0.1rem solid var(--separator_border-color);
        display: var(--separator_display, none);
    }

    &__term {
        word-break: break-word;
    }

    &__icon {
        color: #c2c1c0;
        cursor: pointer;
        height: var(--item_height, 2.8rem);
        position: absolute;
        right: 0;
        text-align: center;
        top: 0;

        &--append::before {
            content: $vsi-auto-suggest;
            font-size: 1.8rem;
            text-align: center;
        }

        &--search::before {
            content: $vsi-search;
            font-size: 2rem;
            text-align: center;
        }
    }

    &__item {
        color: var(--item_color, #666666);
        cursor: pointer;
        display: grid;
        font-weight: 700;
        grid-template-columns: 1fr 2rem;
        line-height: var(--item_height, 2.8rem);
        min-height: var(--item_height, 2.8rem);
        overflow: hidden;
        padding: var(--item_padding, 0 1rem);
        position: relative;

        // stylelint-disable plugin/selector-bem-pattern
        strong {
            font-weight: 400;
        }

        // stylelint-enable plugin/selector-bem-pattern

        &--active {
            background: #eeeeee;
            color: var(--item-active_color, #666666);

            // stylelint-disable max-nesting-depth
            .auto-suggest {
                &__icon {
                    color: var(--item_active-icon_color, var(--brand-primary-color));
                }
            }

            // stylelint-enable max-nesting-depth
        }

        &--history {
            color: var(--history_color, #0000d6);
        }

        &--erase-history {
            background: transparent;
            color: var(--history-erase_color, #0000d6);
            font-size: 1.3rem;
            font-weight: 400;
            height: var(--history-erase_height, 3.4rem);
            line-height: var(--history-erase_height, 3.4rem);

            // stylelint-disable max-nesting-depth
            .auto-suggest {
                &__term:hover {
                    text-decoration: underline;
                }
            }

            // stylelint-enable max-nesting-depth
        }
    }
}

// stylelint-disable plugin/selector-bem-pattern
// stylelint-disable selector-class-pattern
html[dir="rtl"] .auto-suggest .auto-suggest__icon--append::before {
    transform: scaleX(-1);
}

// stylelint-enable plugin/selector-bem-pattern
// stylelint-enable selector-class-pattern

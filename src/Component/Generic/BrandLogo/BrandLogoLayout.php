<?php

declare(strict_types=1);

namespace App\Component\Generic\BrandLogo;

use App\BrandAssets\File\BrandAssetsImageFileName;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Layout\LogoLayoutInterface;

enum BrandLogoLayout: string implements LayoutInterface, LogoLayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/BrandLogo/layout/default/brand_logo-default.html.twig',
        };
    }

    public function getLogo(bool $darkMode): BrandAssetsImageFileName
    {
        return $darkMode
            ? BrandAssetsImageFileName::LOGO_SMALL_DARK_MODE_PNG
            : BrandAssetsImageFileName::LOGO_SMALL_PNG;
    }
}

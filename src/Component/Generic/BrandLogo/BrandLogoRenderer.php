<?php

declare(strict_types=1);

namespace App\Component\Generic\BrandLogo;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class BrandLogoRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof BrandLogoComponent) {
            throw UnsupportedComponentException::create($component, [BrandLogoComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'logo'                      => $component->layout->getLogo($component->logoDarkMode)->value,
                'link_to_home'              => $component->linkToHome,
                'brand_name'                => $this->brandSettingsHelper->getSettings()->getName(),
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }
}

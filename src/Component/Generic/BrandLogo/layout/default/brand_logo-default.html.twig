{# @var layout string #}
{# @var logo string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['brandLogoDefault']) }}
{% set component_space_modifiers = component_space_modifiers|merge(['no-default-space']) %}
{% set component_class = 'brand-logo' %}
{% set image_html -%}
    <img
        src="{{ brand_image_base64(logo) }}"
        loading="lazy"
        alt="{{ brand_name }}" class="{{ component_class }}__image"/>
{%- endset %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    {% if link_to_home %}
        <a href="{{ persistent_new_search_path('route_home') }}" class="{{ component_class }}__container">{{ image_html|raw }}</a>
    {% else %}
        <div class="{{ component_class }}__container">{{ image_html|raw }}</div>
    {% endif %}
</div>

<?php

declare(strict_types=1);

namespace App\Component\Generic\CollapseChildren;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class CollapseChildrenFactory implements ComponentFactoryInterface
{

    public static function getSupportedComponent(): string
    {
        return CollapseChildrenComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new CollapseChildrenComponent(
            CollapseChildrenLayout::from($options[LayoutInterface::KEY]),
            $componentFactory->createMultipleFromOptions($options[CollapseChildrenResolver::KEY_CHILDREN]),
        );
    }
}

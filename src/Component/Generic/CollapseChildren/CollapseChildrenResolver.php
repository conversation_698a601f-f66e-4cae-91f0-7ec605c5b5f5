<?php

declare(strict_types=1);

namespace App\Component\Generic\CollapseChildren;

use App\JsonTemplate\Component\AbstractComponentResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Parent\ChildComponentProperty;
use App\JsonTemplate\Component\Parent\ParentComponentResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;

class CollapseChildrenResolver extends AbstractComponentResolver implements ParentComponentResolverInterface
{
    public const string KEY_CHILDREN = 'children';

    public static function getSupportedComponent(): string
    {
        return CollapseChildrenComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->define(LayoutInterface::KEY)
            ->setAllowedType(OptionType::TYPE_STRING)
            ->setDefaultValue(CollapseChildrenLayout::DEFAULT->value)
            ->addValidator(
                new AllowedValueValidator(
                    array_column(CollapseChildrenLayout::cases(), 'value'),
                ),
            );

        $this->optionsResolver->define(self::KEY_CHILDREN)
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->setRequired();

        $options = $this->resolveChildrenArray($options, self::KEY_CHILDREN, $componentResolver);

        return $this->optionsResolver->resolve($options);
    }

    /**
     * @inheritDoc
     */
    public function getChildComponentProperties(): array
    {
        return [
            ChildComponentProperty::createForMultipleComponents(self::KEY_CHILDREN),
        ];
    }
}

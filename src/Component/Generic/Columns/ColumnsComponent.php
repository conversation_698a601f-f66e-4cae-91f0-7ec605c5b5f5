<?php

declare(strict_types=1);

namespace App\Component\Generic\Columns;

use App\Component\Generic\Section\SectionComponentInterface;
use App\Component\Generic\Section\SectionCssProperty;
use App\JsonTemplate\Component\AbstractComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;

final class ColumnsComponent extends AbstractComponent implements ParentComponentInterface, SectionComponentInterface
{
    /**
     * @param ComponentInterface[] $one
     * @param ComponentInterface[] $two
     * @param ComponentInterface[] $three
     * @param SectionCssProperty[] $sectionCssProperties
     */
    public function __construct(
        public readonly ColumnsLayout $layout,
        public readonly array $one,
        public readonly array $two,
        public readonly array $three,
        public readonly ?ColumnsMapping $mainColumn,
        private readonly ?string $section,
        private readonly bool $sectionVisible,
        private readonly array $sectionCssProperties
    )
    {
    }

    /**
     * @return array<string, ComponentInterface[]>
     */
    public function getColumns(): array
    {
        return [
            ColumnsMapping::ONE->value   => $this->one,
            ColumnsMapping::TWO->value   => $this->two,
            ColumnsMapping::THREE->value => $this->three,
        ];
    }

    /**
     * @return ComponentInterface[]
     */
    public function getComponents(): array
    {
        return array_merge($this->one, $this->two, $this->three);
    }

    public function getSection(): ?string
    {
        return $this->section;
    }

    public function isSectionVisible(): bool
    {
        return $this->sectionVisible;
    }

    /**
     * @inheritDoc
     */
    public function getSectionCssProperties(): array
    {
        return $this->sectionCssProperties;
    }

    public static function getType(): string
    {
        return 'columns';
    }

    public function getRenderer(): string
    {
        return ColumnsRenderer::class;
    }
}

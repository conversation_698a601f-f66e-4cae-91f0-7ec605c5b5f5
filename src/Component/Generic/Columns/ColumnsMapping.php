<?php

declare(strict_types=1);

namespace App\Component\Generic\Columns;

enum ColumnsMapping: string
{
    case ONE   = 'one';
    case TWO   = 'two';
    case THREE = 'three';

    public function getNumber(): int
    {
        return match ($this) {
            self::ONE   => 1,
            self::TWO   => 2,
            self::THREE => 3,
        };
    }

    public static function getMaxNumber(): int
    {
        return 3;
    }

    public static function fromNumber(int $number): self
    {
        /** @phpstan-ignore-next-line PHPStan thinks there are remaining values */
        return match ($number) {
            1 => self::ONE,
            2 => self::TWO,
            3 => self::THREE,
        };
    }
}

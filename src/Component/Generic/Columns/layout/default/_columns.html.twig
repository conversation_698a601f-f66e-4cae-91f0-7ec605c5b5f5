{# @var components App\JsonTemplate\Component\ComponentInterface[] #}
{# @var layout string #}
{# @var section string|null #}
{# @var section_visible bool #}
{# @var section_css_properties App\Generic\Section\SectionCssProperty[] #}
{% set component_class = 'columns' %}
{% set additional_classes = add_section_class(section, section_visible, []) %}
<div class="{{ component_class(component_class, [layout], ['no-default-space'], additional_classes) }}"{{ render_section_style_attribute(section, section_css_properties) }}>
    {% for column, components in columns %}
        {% if components %}
            {% if column == main_column %}
                <main class="{{ component_class }}__column {{ component_class }}__column--{{ column }}">
                    {{ render_components(components, view) }}
                </main>
            {% else %}
                <div class="{{ component_class }}__column {{ component_class }}__column--{{ column }}">
                    {{ render_components(components, view) }}
                </div>
            {% endif %}
        {% endif %}
    {% endfor %}
</div>

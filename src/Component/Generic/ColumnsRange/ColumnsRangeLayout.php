<?php

declare(strict_types=1);

namespace App\Component\Generic\ColumnsRange;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ColumnsRangeLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/ColumnsRange/layout/default/columns_range-default.html.twig',
        };
    }
}

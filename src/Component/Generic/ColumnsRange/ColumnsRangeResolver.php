<?php

declare(strict_types=1);

namespace App\Component\Generic\ColumnsRange;

use App\Component\Generic\Columns\ColumnsMapping;
use App\Component\Generic\Section\SectionComponentInterface;
use App\Component\Generic\Section\SectionCssProperty;
use App\Generic\Validator\AllowedEnumCasesValidator;
use App\JsonTemplate\Component\AbstractComponentResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\Parent\ChildComponentProperty;
use App\JsonTemplate\Component\Parent\ParentComponentResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedPatternValidator;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\LesserThanOrEqualValidator;

final class ColumnsRangeResolver extends AbstractComponentResolver implements ParentComponentResolverInterface
{
    public const string KEY_START      = 'start';
    public const string KEY_END        = 'end';
    public const string KEY_COMPONENTS = 'components';

    public static function getSupportedComponent(): string
    {
        return ColumnsRangeComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(ColumnsRangeLayout::class);

        $this->optionsResolver->define(self::KEY_START)
            ->setRequired()
            ->addValidator(new GreaterThanOrEqualValidator(1))
            ->addValidator(new LesserThanOrEqualValidator(ColumnsMapping::getMaxNumber()))
            ->setAllowedType(OptionType::TYPE_INTEGER);

        $this->optionsResolver->define(self::KEY_END)
            ->setRequired()
            ->addValidator(new GreaterThanOrEqualValidator(1))
            ->addValidator(new LesserThanOrEqualValidator(ColumnsMapping::getMaxNumber()))
            ->setAllowedType(OptionType::TYPE_INTEGER);

        $this->optionsResolver->define(self::KEY_COMPONENTS)
            ->setDefaultValue([])
            ->setAllowedType(OptionType::TYPE_ARRAY);

        $this->optionsResolver->define(SectionComponentInterface::KEY_SECTION)
            ->setAllowedType(OptionType::TYPE_STRING, true)
            ->addValidator(new AllowedPatternValidator(SectionComponentInterface::SECTION_REGEX))
            ->setDefaultValue(null);

        $this->optionsResolver->define(SectionComponentInterface::KEY_SECTION_VISIBLE)
            ->setAllowedType(OptionType::TYPE_BOOLEAN)
            ->setDefaultValue(true);

        $this->optionsResolver->define(SectionComponentInterface::KEY_SECTION_CSS_PROPERTIES)
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->addValidator(
                new AllowedEnumCasesValidator(SectionCssProperty::cases()),
            )
            ->setDefaultValue([]);

        $options = $this->optionsResolver->resolve($options);
        $options = $this->resolveChildrenArray(
            options          : $options,
            key              : self::KEY_COMPONENTS,
            componentResolver: $componentResolver,
        );

        return $options;
    }

    /**
     * @inheritDoc
     */
    public function getChildComponentProperties(): array
    {
        return [
            ChildComponentProperty::createForMultipleComponents(self::KEY_COMPONENTS),
        ];
    }
}

{# @var components App\JsonTemplate\Component\ComponentInterface[] #}
{# @var layout string #}
{# @var section string|null #}
{# @var section_visible bool #}
{# @var section_css_properties App\Generic\Section\SectionCssProperty[] #}
{{ component_style(['columnsRangeDefault']) }}
{% set component_class = 'columns-range' %}
{% set additional_classes = add_section_class(section, section_visible, []) %}
<div class="{{ component_class(component_class, [layout], ['no-default-space'], additional_classes) }}" style="{{ render_columns_range_style_attribute_values(start, end) ~ render_section_style_attribute_values(section, section_css_properties, true) }}">
    <div class="{{ component_class }}__content">
        {{ render_components(components, view) }}
    </div>
</div>

<?php

declare(strict_types=1);

namespace App\Component\Generic\Container;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class ContainerFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ContainerComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ContainerComponent(
            layout    : ContainerLayout::from($options[LayoutInterface::KEY]),
            mode      : ContainerMode::tryFrom((string)$options[ContainerResolver::KEY_MODE]),
            font      : ContainerFont::tryFrom((string)$options[ContainerResolver::KEY_FONT]),
            components: $componentFactory->createMultipleFromOptions($options[ContainerResolver::KEY_COMPONENTS]),
        );
    }
}

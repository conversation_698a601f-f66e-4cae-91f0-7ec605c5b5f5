<?php

declare(strict_types=1);

namespace App\Component\Generic\Container;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ContainerLayout: string implements LayoutInterface
{
    case ARTICLE          = 'article';
    case CONTENT_CATEGORY = 'content-category';
    case CONTENT_HOME_1   = 'content-home-1';
    case CONTENT_HOME_2   = 'content-home-2';
    case CONTENT_HOME_3   = 'content-home-3';
    case CONTENT_HOME_4   = 'content-home-4';
    case CONTENT_HOME_5   = 'content-home-5';
    case CONTENT_HOME_6   = 'content-home-6';
    case DEFAULT          = 'default';
    case DSR              = 'dsr';
    case STARTPAGE        = 'startpage';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::ARTICLE          => '@component/Generic/Container/layout/article/container-article.html.twig',
            self::CONTENT_CATEGORY => '@component/Generic/Container/layout/contentCategory/container-content_category.html.twig',
            self::CONTENT_HOME_1   => '@component/Generic/Container/layout/contentHome1/container-content_home_1.html.twig',
            self::CONTENT_HOME_2   => '@component/Generic/Container/layout/contentHome2/container-content_home_2.html.twig',
            self::CONTENT_HOME_3   => '@component/Generic/Container/layout/contentHome3/container-content_home_3.html.twig',
            self::CONTENT_HOME_4   => '@component/Generic/Container/layout/contentHome4/container-content_home_4.html.twig',
            self::CONTENT_HOME_5   => '@component/Generic/Container/layout/contentHome5/container-content_home_5.html.twig',
            self::CONTENT_HOME_6   => '@component/Generic/Container/layout/contentHome6/container-content_home_6.html.twig',
            self::DEFAULT          => '@component/Generic/Container/layout/default/container-default.html.twig',
            self::DSR              => '@component/Generic/Container/layout/dsr/container-dsr.html.twig',
            self::STARTPAGE        => '@component/Generic/Container/layout/startpage/container-startpage.html.twig',
        };
    }
}

@import "../stickyHeader";

/** @define container */
// stylelint-disable visymo/sort-properties-alphabetically
// stylelint-disable plugin/selector-bem-pattern
.container--content-home-3 {
    --container__column-one_width: 121.6rem;
    --container__columns_grid-template-areas: ". one .";
    --container__columns_grid-template-columns: auto
        minmax(10rem, var(--container__column-one_width)) auto;

    .section {
        &--content {
            --container__section_border-top: 0.1rem solid #eaecf0;
        }

        &--footer-brand {
            --container__section_background: var(--brand-primary-color);
            --container__section_color: #ffffff;
        }

        &--search-brand {
            --container__section_background: var(--brand-primary-color);
            --container__section_color: #ffffff;
            --container__section_box-shadow:
                0 0.4rem 0.8rem -0.2rem rgba(16, 24, 40, 0.1),
                0 0.2rem 0.4rem -0.2rem rgba(16, 24, 40, 0.06);
        }

        &--search-category {
            --container__section_background: #ffffff;
            --container__section_box-shadow:
                0 0.4rem 0.8rem -0.2rem rgba(16, 24, 40, 0.1),
                0 0.2rem 0.4rem -0.2rem rgba(16, 24, 40, 0.06);
        }
    }
}

.container--content-home-3 {
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    @include sticky-header("search-brand", 0);
}

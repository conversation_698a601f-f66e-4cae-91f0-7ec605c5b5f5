@import "../stickyHeader";

/** @define container */
// stylelint-disable visymo/sort-properties-alphabetically
// stylelint-disable plugin/selector-bem-pattern
// stylelint-disable max-line-length
.container--content-home-4 {
    --container__column-one_width: 60.8rem;
    --container__columns_grid-template-areas: ". one two .";
    --container__columns_grid-template-columns: auto
        minmax(10rem, var(--container__column-one_width))
        minmax(10rem, var(--container__column-two_width)) auto;
    --container__column-two_width: 60.8rem;

    @media #{map-get($media-max, b)} {
        --container__columns_grid-template-areas: "one" "two";
        --container__columns_grid-template-columns: 1fr;
    }

    .section {
        &--content {
            --container__section_border-top: 0.1rem solid #eaecf0;
        }

        &--header {
            --container__section_background: var(--brand-primary-color);
            --container__section_color: #ffffff;
        }

        &--search-category {
            --container__section_background: #ffffff;
            --container__section_box-shadow:
                0 0.4rem 0.8rem -0.2rem rgba(16, 24, 40, 0.1),
                0 0.2rem 0.4rem -0.2rem rgba(16, 24, 40, 0.06);
            --container__section_border-top: 0.1rem solid #eaecf0;
        }
    }
}

.container--content-home-4 {
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    @include sticky-header;
}

/** @define container */
.container--default {
    // stylelint-disable-next-line max-line-length
    --container__columns_grid-template-columns: var(--container__column-space_width)
        var(--container__column-one_width) var(--container__column-two_width);

    @media #{map-get($media-min, e)} {
        --container__column-one_width: 67.4rem;
        --container__column-space_width: 10.6rem;
        --container__column-two_width: 39rem;
    }

    @media #{map-get($media, d)} {
        --container__column-one_width: 65.1rem;
        --container__column-space_width: 7.7rem;
        --container__column-two_width: 23.4rem;
    }

    @media #{map-get($media-min, d)} {
        --container__columns_grid-template-areas: ". one two";
    }

    @media #{map-get($media-max, c)} {
        --container__column-one_width: auto;
        --container__column-space_width: 0;
        --container__column-two_width: auto;
        --container__columns_grid-template-areas: "one" "two";
        --container__columns_grid-template-columns: 100%;
    }
}

.container--default {
    background: var(--container__background, none);
    border-top: var(--container__border-top, 0.3rem solid var(--brand-primary-color));
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

// stylelint-disable plugin/selector-bem-pattern
// stylelint-disable visymo/sort-properties-alphabetically
.html--default {
    &.html--mode-dark {
        --container__background: #01074b;
        --container__section_box-shadow: none;
        --container__section_color: #ffffff;
        --container__section_link_color: #a89eff;
        --container__section-highlight_color: var(--brand-primary-color);
        --container__section-secondary_color: #dddddd;
    }
}

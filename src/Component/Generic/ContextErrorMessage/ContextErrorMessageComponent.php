<?php

declare(strict_types=1);

namespace App\Component\Generic\ContextErrorMessage;

use App\JsonTemplate\Component\AbstractComponent;

final class ContextErrorMessageComponent extends AbstractComponent
{
    public function __construct(
        public readonly ContextErrorMessageLayout $layout,
        private readonly bool $hasResults,
        private readonly bool $hasError
    )
    {
    }

    public function hasResults(): bool
    {
        return $this->hasResults;
    }

    public function hasError(): bool
    {
        return $this->hasError;
    }

    public static function getType(): string
    {
        return 'context_error_message';
    }

    public static function isInternal(): bool
    {
        return true;
    }

    public function getRenderer(): string
    {
        return ContextErrorMessageRenderer::class;
    }
}

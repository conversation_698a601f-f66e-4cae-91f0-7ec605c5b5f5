{# @var layout string #}
{# @var translation_id string #}
{# @var query string #}

{{ component_style(['contextErrorMessageDefault']) }}

{% set component_class = 'context-error-message' %}

<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__container">
        <h4 class="{{ component_class }}__title">
            {{ translation_id|trans|raw }}
        </h4>

        {% if has_error %}
            <p class="{{ component_class }}__paragraph">
                {{ 'search_error.fatal_error.message'|trans({"%query%": "<strong>"~(query|escape)~"</strong>"})|raw }}
            </p>
        {% else %}
            <p class="{{ component_class }}__paragraph">
                {{ 'search_error.no_results.message'|trans({"%query%": "<strong>"~(query|escape)~"</strong>"})|raw }}
            </p>

            <ul class="{{ component_class }}__list">
                <li class="{{ component_class }}__list-item">{{ 'search_error.no_results.check_spelling'|trans }}</li>
                <li class="{{ component_class }}__list-item">{{ 'search_error.no_results.different_query'|trans }}</li>
            </ul>
        {% endif %}
    </div>
</div>

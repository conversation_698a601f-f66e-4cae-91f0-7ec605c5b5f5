<?php

declare(strict_types=1);

namespace App\Component\Generic\CookieConsent;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class CookieConsentFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return CookieConsentComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new CookieConsentComponent(
            layout: CookieConsentLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

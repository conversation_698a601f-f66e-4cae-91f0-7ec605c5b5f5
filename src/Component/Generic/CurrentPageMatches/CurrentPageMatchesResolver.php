<?php

declare(strict_types=1);

namespace App\Component\Generic\CurrentPageMatches;

use App\Component\Generic\AbstractCondition\AbstractConditionResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;

final class CurrentPageMatchesResolver extends AbstractConditionResolver
{
    public const string KEY_PAGE = 'page';

    public static function getSupportedComponent(): string
    {
        return CurrentPageMatchesComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->define(self::KEY_PAGE)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanValidator(0));

        return parent::resolve($options, $componentResolver);
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Disclaimer;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum DisclaimerLayout: string implements LayoutInterface
{
    case CONTENT = 'content';
    case DEFAULT = 'default';
    case TITLE   = 'title';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::CONTENT => '@component/Generic/Disclaimer/layout/content/disclaimer-content.html.twig',
            self::DEFAULT => '@component/Generic/Disclaimer/layout/default/disclaimer-default.html.twig',
            self::TITLE   => '@component/Generic/Disclaimer/layout/title/disclaimer-title.html.twig',
        };
    }
}

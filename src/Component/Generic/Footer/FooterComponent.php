<?php

declare(strict_types=1);

namespace App\Component\Generic\Footer;

use App\JsonTemplate\Component\AbstractComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;

final class FooterComponent extends AbstractComponent implements ParentComponentInterface
{
    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(
        public readonly FooterLayout $layout,
        public readonly array $components
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getComponents(): array
    {
        return $this->components;
    }

    public static function getType(): string
    {
        return 'footer';
    }

    public function getRenderer(): string
    {
        return FooterRenderer::class;
    }
}

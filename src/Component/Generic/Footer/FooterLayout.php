<?php

declare(strict_types=1);

namespace App\Component\Generic\Footer;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum FooterLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/Footer/layout/default/footer-default.html.twig',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Footer;

use App\JsonTemplate\Component\AbstractComponentResolver;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\Parent\ChildComponentProperty;
use App\JsonTemplate\Component\Parent\ParentComponentResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class FooterResolver extends AbstractComponentResolver implements ParentComponentResolverInterface
{
    public const string KEY_COMPONENTS = 'components';

    public static function getSupportedComponent(): string
    {
        return FooterComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(FooterLayout::class);

        $this->optionsResolver->define(self::KEY_COMPONENTS)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_ARRAY);

        $options = $this->optionsResolver->resolve($options);
        $options = $this->resolveChildrenArray(
            options          : $options,
            key              : self::KEY_COMPONENTS,
            componentResolver: $componentResolver,
        );

        return $options;
    }

    /**
     * @inheritDoc
     */
    public function getChildComponentProperties(): array
    {
        return [
            ChildComponentProperty::createForMultipleComponents(self::KEY_COMPONENTS),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\FooterLogo;

use App\Generic\Logo\LogoStyleFilter;
use App\JsonTemplate\Component\AbstractComponent;

final class FooterLogoComponent extends AbstractComponent
{
    public function __construct(
        public readonly FooterLogoLayout $layout,
        public readonly bool $logoDarkMode,
        public readonly ?LogoStyleFilter $logoStyleFilter,
        public readonly bool $hideOnDesktop
    )
    {
    }

    public static function getType(): string
    {
        return 'footer_logo';
    }

    public function getRenderer(): string
    {
        return FooterLogoRenderer::class;
    }
}

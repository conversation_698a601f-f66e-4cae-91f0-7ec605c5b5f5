<?php

declare(strict_types=1);

namespace App\Component\Generic\FooterLogo;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Twig\Environment;

final class FooterLogoRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof FooterLogoComponent) {
            throw UnsupportedComponentException::create($component, [FooterLogoComponent::class]);
        }

        $darkModeLogo = $this->splitTestExtendedReader->isVariantActive('wsdm') || $component->logoDarkMode;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'            => $component->layout->value,
                'hide_on_desktop'   => $component->hideOnDesktop,
                'logo'              => $component->layout->getLogo($darkModeLogo)->value,
                'logo_style_filter' => $component->logoStyleFilter,
                'brand_name'        => $this->brandSettingsHelper->getSettings()->getName(),
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\FooterNavigation;

use App\BrandAssets\File\BrandAssetsImageFileName;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\Layout\LogoLayoutInterface;

enum FooterNavigationLayout: string implements LayoutInterface, LogoLayoutInterface
{
    case BORDERLESS    = 'borderless';
    case BRAND         = 'brand';
    case CONTENT_1     = 'content-1';
    case CONTENT_3     = 'content-3';
    case CONTENT_4     = 'content-4';
    case DEFAULT       = 'default';
    case DSR           = 'dsr';
    case SEEKWEB       = 'seekweb';
    case VISYMO_SEARCH = 'visymo-search';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::BORDERLESS    => '@component/Generic/FooterNavigation/layout/borderless/footer_navigation-borderless.html.twig',
            self::BRAND         => '@component/Generic/FooterNavigation/layout/brand/footer_navigation-brand.html.twig',
            self::CONTENT_1     => '@component/Generic/FooterNavigation/layout/content1/footer_navigation-content_1.html.twig',
            self::CONTENT_3     => '@component/Generic/FooterNavigation/layout/content3/footer_navigation-content_3.html.twig',
            self::CONTENT_4     => '@component/Generic/FooterNavigation/layout/content4/footer_navigation-content_4.html.twig',
            self::DEFAULT       => '@component/Generic/FooterNavigation/layout/default/footer_navigation-default.html.twig',
            self::DSR           => '@component/Generic/FooterNavigation/layout/dsr/footer_navigation-dsr.html.twig',
            self::SEEKWEB       => '@component/Generic/FooterNavigation/layout/seekweb/footer_navigation-seekweb.html.twig',
            self::VISYMO_SEARCH => '@component/Generic/FooterNavigation/layout/visymoSearch/footer_navigation-visymo_search.html.twig',
        };
    }

    public function show(FooterNavigationLinkType $linkType): bool
    {
        return match ($linkType) {
            FooterNavigationLinkType::COPYRIGHT,
            FooterNavigationLinkType::CONTACT => $this !== self::DSR,
            default                           => true,
        };
    }

    public function getLogo(bool $darkMode): ?BrandAssetsImageFileName
    {
        return match ($this) {
            self::CONTENT_3 => $darkMode
                ? BrandAssetsImageFileName::LOGO_SMALL_DARK_MODE_PNG
                : BrandAssetsImageFileName::LOGO_SMALL_PNG,
            default         => null
        };
    }
}

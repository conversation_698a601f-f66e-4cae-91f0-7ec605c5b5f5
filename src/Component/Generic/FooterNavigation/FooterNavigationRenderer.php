<?php

declare(strict_types=1);

namespace App\Component\Generic\FooterNavigation;

use App\Brand\Settings\BrandSettingsHelper;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use App\InfoPages\Helper\InfoPagesUrlHelper;
use App\InfoPages\Page\InfoPage;
use App\InfoPages\Settings\InfoPagesSettings;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use App\OneTrust\Helper\OneTrustHelper;
use Twig\Environment;

class FooterNavigationRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly InfoPagesUrlHelper $infoPagesUrlHelper,
        private readonly OneTrustHelper $oneTrustHelper,
        private readonly Environment $twig,
        private readonly InfoPagesSettings $infoPagesSettings,
        private readonly BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof FooterNavigationComponent) {
            throw UnsupportedComponentException::create($component, [FooterNavigationComponent::class]);
        }

        $footerNavigationListItems = $this->getFooterNavigationListItems($component);

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                       => $component->layout->value,
                'footer_navigation_list_items' => $footerNavigationListItems,
                'component_space_modifiers'    => $component->componentSpaceModifiers,
                'logo'                         => $component->layout->getLogo($component->logoDarkMode)?->value,
                'brand_name'                   => $this->brandSettingsHelper->getSettings()->getName(),
            ],
        );
    }

    /**
     * @return FooterNavigationListItem[]
     */
    private function getFooterNavigationListItems(FooterNavigationComponent $component): array
    {
        $footerNavigationListItems = [];

        if ($component->showAbout && $component->layout->show(FooterNavigationLinkType::ABOUT)) {
            $aboutUrl = $this->infoPagesSettings->linkToExternalAboutPage
                ? $this->infoPagesUrlHelper->getExternalUrl(hash: 'about-us')
                : $this->persistentUrlParametersRouter->generate(
                    route   : 'route_info_pages_about',
                    pageType: PersistentUrlParametersPageType::NEW_SEARCH,
                );

            $footerNavigationListItems[] = new FooterNavigationListItem(
                translationId: 'footer.about_us',
                url          : $aboutUrl,
                isExternal   : $this->infoPagesSettings->linkToExternalAboutPage,
            );
        }

        if ($component->showCopyright && $component->layout->show(FooterNavigationLinkType::COPYRIGHT)) {
            $footerNavigationListItems[] = new FooterNavigationListItem(
                translationId: 'footer.copyright',
                url          : $this->infoPagesUrlHelper->getExternalUrl(InfoPage::COPYRIGHT),
                isExternal   : true,
            );
        }

        if ($component->showDisclaimer && $component->layout->show(FooterNavigationLinkType::DISCLAIMER)) {
            $footerNavigationListItems[] = new FooterNavigationListItem(
                translationId: 'footer.disclaimer',
                url          : $this->infoPagesUrlHelper->getExternalUrl(InfoPage::DISCLAIMER),
                isExternal   : true,
            );
        }

        if ($component->showPrivacy && $component->layout->show(FooterNavigationLinkType::PRIVACY)) {
            $footerNavigationListItems[] = new FooterNavigationListItem(
                translationId: 'footer.privacy',
                url          : $this->infoPagesUrlHelper->getExternalUrl(InfoPage::PRIVACY),
                isExternal   : true,
            );
        }

        if ($this->oneTrustHelper->isEnabled()) {
            $footerNavigationListItems[] = new FooterNavigationListItem(
                translationId: 'footer.cookie_settings',
                url          : null,
                class        : 'consent-display',
            );
        }

        if ($component->showContact && $component->layout->show(FooterNavigationLinkType::CONTACT)) {
            $footerNavigationListItems[] = new FooterNavigationListItem(
                translationId: 'footer.contact',
                url          : $this->infoPagesUrlHelper->getExternalUrl(InfoPage::CONTACT),
                isExternal   : true,
            );
        }

        return $footerNavigationListItems;
    }
}

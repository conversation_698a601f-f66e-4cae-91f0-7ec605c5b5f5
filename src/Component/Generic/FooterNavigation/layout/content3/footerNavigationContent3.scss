/** @define footer-navigation */
.footer-navigation--content-3 {
    margin-bottom: 2rem;
    margin-top: 2rem;

    .footer-navigation {
        &__container {
            align-self: stretch;
            display: flex;
            gap: 2rem;
            justify-content: space-between;
        }

        &__link {
            color: #667085;
            font-size: 1.6rem;
            line-height: 2.4rem;
        }

        &__list {
            display: flex;
            gap: 2rem;
        }

        @media #{map-get($media-min, c)} {
            &__copyright {
                flex-grow: 1;
            }
        }

        @media #{map-get($media-max, b)} {
            &__container {
                align-items: center;
                flex-direction: column;
            }
        }
    }
}

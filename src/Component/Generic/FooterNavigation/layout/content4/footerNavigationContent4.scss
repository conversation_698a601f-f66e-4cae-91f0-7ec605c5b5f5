/** @define footer-navigation */
.footer-navigation--content-4 {
    margin-bottom: 2rem;
    margin-top: 2rem;

    .footer-navigation {
        &__container {
            display: flex;
            gap: 2rem;
            justify-content: center;
        }

        &__link {
            color: #667085;
            font-size: 1.4rem;
            font-weight: 700;
            line-height: 2rem;
        }

        &__list {
            display: flex;
            gap: 2rem;
        }

        @media #{map-get($media-max, b)} {
            &__container {
                align-items: center;
                flex-direction: column;
                row-gap: 1.6rem;
            }

            &__link {
                font-weight: 400;
            }

            &__list {
                gap: 1.6rem;
            }
        }
    }
}

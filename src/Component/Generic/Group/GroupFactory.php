<?php

declare(strict_types=1);

namespace App\Component\Generic\Group;

use App\GroupComponent\Settings\GroupComponentSettings;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final readonly class GroupFactory implements ComponentFactoryInterface
{
    public function __construct(
        private GroupComponentSettings $groupComponentSettings
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return GroupComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $layout = GroupLayout::from($options[LayoutInterface::KEY]);
        $variant = $this->groupComponentSettings->getVariant($layout);

        return new GroupComponent(
            layout : $layout,
            variant: $variant,
        );
    }
}

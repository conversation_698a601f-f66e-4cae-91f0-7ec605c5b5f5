<?php

declare(strict_types=1);

namespace App\Component\Generic\Group;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Parent\AbstractComponentParentRenderer;
use App\JsonTemplate\View\ViewInterface;

final class GroupRenderer extends AbstractComponentParentRenderer
{
    /**
     * @inheritDoc
     */
    protected function getComponents(ComponentInterface $component): array
    {
        return [];
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPage;

use Visymo\Shared\Domain\Locale\Language;

enum InfoPageContent: string
{
    case ABOUT                      = 'about';
    case ADD_REMOVE_SITE            = 'add_remove_site';
    case ADVANCED_SEARCH            = 'advanced_search';
    case ADVERTISING                = 'advertising';
    case COMPANY_INFORMATION        = 'company_information';
    case FREQUENTLY_ASKED_QUESTIONS = 'frequently_asked_questions';
    case PLATFORMS                  = 'platforms';
    case SEARCH_COMPONENTS          = 'search_components';
    case SEARCH_MULTIPLE_SOURCES    = 'search_multiple_sources';
    case USEFUL_SEARCH_TIPS         = 'useful_search_tips';

    public function template(string $languageCode): string
    {
        // phpcs:disable Generic.Files.LineLength.MaxExceeded
        if ($languageCode === Language::NL) {
            return match ($this) {
                self::ABOUT                      => '@component/Generic/InfoPage/layout/default/content/about/_about.html.twig',
                self::ADD_REMOVE_SITE            => '@component/Generic/InfoPage/layout/default/content/add_remove_site/_add_remove_site_nl.html.twig',
                self::ADVANCED_SEARCH            => '@component/Generic/InfoPage/layout/default/content/advanced_search/_advanced_search_nl.html.twig',
                self::ADVERTISING                => '@component/Generic/InfoPage/layout/default/content/advertising/_advertising_nl.html.twig',
                self::COMPANY_INFORMATION        => '@component/Generic/InfoPage/layout/default/content/company_information/_company_information_nl.html.twig',
                self::FREQUENTLY_ASKED_QUESTIONS => '@component/Generic/InfoPage/layout/default/content/frequently_asked_questions/_frequently_asked_questions_nl.html.twig',
                self::PLATFORMS                  => '@component/Generic/InfoPage/layout/default/content/platforms/_platforms_nl.html.twig',
                self::SEARCH_COMPONENTS          => '@component/Generic/InfoPage/layout/default/content/search_components/_search_components_nl.html.twig',
                self::SEARCH_MULTIPLE_SOURCES    => '@component/Generic/InfoPage/layout/default/content/search_multiple_sources/_search_multiple_sources_nl.html.twig',
                self::USEFUL_SEARCH_TIPS         => '@component/Generic/InfoPage/layout/default/content/useful_search_tips/_useful_search_tips_nl.html.twig',
            };
        }

        return match ($this) {
            self::ABOUT                      => '@component/Generic/InfoPage/layout/default/content/about/_about.html.twig',
            self::ADD_REMOVE_SITE            => '@component/Generic/InfoPage/layout/default/content/add_remove_site/_add_remove_site_en.html.twig',
            self::ADVANCED_SEARCH            => '@component/Generic/InfoPage/layout/default/content/advanced_search/_advanced_search_en.html.twig',
            self::ADVERTISING                => '@component/Generic/InfoPage/layout/default/content/advertising/_advertising_en.html.twig',
            self::COMPANY_INFORMATION        => '@component/Generic/InfoPage/layout/default/content/company_information/_company_information_en.html.twig',
            self::FREQUENTLY_ASKED_QUESTIONS => '@component/Generic/InfoPage/layout/default/content/frequently_asked_questions/_frequently_asked_questions_en.html.twig',
            self::PLATFORMS                  => '@component/Generic/InfoPage/layout/default/content/platforms/_platforms_en.html.twig',
            self::SEARCH_COMPONENTS          => '@component/Generic/InfoPage/layout/default/content/search_components/_search_components_en.html.twig',
            self::SEARCH_MULTIPLE_SOURCES    => '@component/Generic/InfoPage/layout/default/content/search_multiple_sources/_search_multiple_sources_en.html.twig',
            self::USEFUL_SEARCH_TIPS         => '@component/Generic/InfoPage/layout/default/content/useful_search_tips/_useful_search_tips_en.html.twig',
        };
    }

    public function getPageTitleTranslationId(): string
    {
        return match ($this) {
            self::ABOUT                      => 'page_navigation.link.power_of',
            self::ADD_REMOVE_SITE            => 'page_navigation.link.add_remove_site',
            self::ADVANCED_SEARCH            => 'page_navigation.link.advanced_search',
            self::ADVERTISING                => 'page_navigation.link.advertising',
            self::COMPANY_INFORMATION        => 'page_navigation.link.company_information',
            self::FREQUENTLY_ASKED_QUESTIONS => 'page_navigation.link.frequently_asked_questions',
            self::PLATFORMS                  => 'page_navigation.link.platforms',
            self::SEARCH_COMPONENTS          => 'page_navigation.link.search_components',
            self::SEARCH_MULTIPLE_SOURCES    => 'page_navigation.link.search_multiple_sources',
            self::USEFUL_SEARCH_TIPS         => 'page_navigation.link.useful_search_tips',
        };
    }
}

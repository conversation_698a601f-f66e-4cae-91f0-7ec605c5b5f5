{# @var query string #}
{# @var layout string #}
{{ component_style(['infoPageContent']) }}
{% set component_class = 'info-page' %}
<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    <h1 class="{{ component_class }}__title">
        {{ 'info_page.about.title'|trans }}
    </h1>
    <p class="{{ component_class }}__paragraph">
        {{ 'info_page.about.content'|trans({'%brand%': brand_name})|raw }}
    </p>

    <h2 class="{{ component_class }}__sub-title">{{ 'info_page.about.our_mission.title'|trans }}</h2>
    <p class="{{ component_class }}__paragraph">
        {{ 'info_page.about.our_mission.content'|trans({'%brand%': brand_name})|raw }}
    </p>

    <h2 class="{{ component_class }}__sub-title">{{ 'info_page.about.who_we_are.title'|trans }}</h2>
    <p class="{{ component_class }}__paragraph">
        {{ 'info_page.about.who_we_are.content'|trans|raw }}
    </p>

    <h2 class="{{ component_class }}__sub-title">{{ 'info_page.about.what_we_offer.title'|trans }}</h2>
    <ol class="{{ component_class }}__order-list">
        <li class="{{ component_class }}__list-item"><strong>{{ 'info_page.about.what_we_offer.in_depth_articles.title'|trans }}:</strong> {{ 'info_page.about.what_we_offer.in_depth_articles.content'|trans|raw }}</li>
        <li class="{{ component_class }}__list-item"><strong>{{ 'info_page.about.what_we_offer.search_results.title'|trans|raw }}:</strong> {{ 'info_page.about.what_we_offer.search_results.content'|trans|raw }}</li>
        <li class="{{ component_class }}__list-item"><strong>{{ 'info_page.about.what_we_offer.user_experience.title'|trans }}:</strong> {{ 'info_page.about.what_we_offer.user_experience.content'|trans|raw }}</li>
    </ol>

    <h2 class="{{ component_class }}__sub-title">{{ 'info_page.about.our_vision.title'|trans }}</h2>
    <p class="{{ component_class }}__paragraph">
        {{ 'info_page.about.our_vision.content'|trans({'%brand%': brand_name})|raw }}
    </p>

    <p class="{{ component_class }}__paragraph">
        {{ 'info_page.about.part_of_visymo'|trans({'%brand%': brand_name})|raw }}
    </p>
</div>

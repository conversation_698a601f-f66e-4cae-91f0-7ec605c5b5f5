{# @var component_class string #}
To search for specific websites or information, you can refine the search. This is how you obtain more useful
information. Through <a class="{{ component_class }}__link" href="{{ persistent_path('route_advanced_search') }}"
>{{ url('route_advanced_search') }}</a> you can:<br>
<ul class="{{ component_class }}__list {{ component_class }}__list--disc">
    <li>specify which words should and/or should not be on the web page;</li>
    <li>search worldwide or only web pages from your country/ from another country;</li>
    <li>restrict your search to web pages within a given website (domain);</li>
    <li>specify the maximum number of search results per page.</li>
</ul>

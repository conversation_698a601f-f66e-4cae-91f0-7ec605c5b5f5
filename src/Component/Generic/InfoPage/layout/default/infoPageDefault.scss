/** @define info-page */
.info-page--default {
    color: #666666;
    font-size: var(--font-size, 1.25rem);
    line-height: var(--line-height, 1.8rem);
    margin-top: 2rem;

    .info-page {
        &__title {
            color: var(--title_color, #666666);
            font-size: 1.8rem;
            font-weight: var(--title_font-weight, 400);
            line-height: 1.8rem;
        }

        &__header {
            margin: 0;
            padding: 0;
        }

        &__link {
            color: var(--link_color, #2775bd);

            &:hover {
                text-decoration: underline;
            }
        }

        &__list {
            display: block;
            padding: 1rem 4rem;
        }

        &__list--disc {
            list-style-type: disc;
        }

        // stylelint-disable plugin/selector-bem-pattern
        &__paragraph {
            margin-bottom: 1.4rem;
            margin-top: 1.4rem;
            word-break: break-word;
            word-wrap: break-word;

            &__header {
                margin: 0;
                padding: 0;
            }

            &__link {
                &:hover {
                    text-decoration: underline;
                }
            }

            &__list {
                display: block;
                padding: 1rem 4rem;
            }

            &__list--disc {
                list-style-type: disc;
            }
        }

        &__logo {
            max-width: 100%;
        }

        // stylelint-enable plugin/selector-bem-pattern

        @media #{map-get($media-min, d)} {
            width: 66.6667%;
        }

        @media #{map-get($media-min, e)} {
            width: 67rem;
        }

        @media #{map-get($media-min, f)} {
            width: 77.5rem;
        }
    }
}

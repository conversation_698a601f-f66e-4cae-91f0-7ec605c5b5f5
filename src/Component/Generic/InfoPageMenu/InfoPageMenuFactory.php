<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPageMenu;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class InfoPageMenuFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return InfoPageMenuComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new InfoPageMenuComponent(
            layout: InfoPageMenuLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

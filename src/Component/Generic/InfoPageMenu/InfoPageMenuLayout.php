<?php

declare(strict_types=1);

namespace App\Component\Generic\InfoPageMenu;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum InfoPageMenuLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/InfoPageMenu/layout/default/info_page_menu-default.html.twig',
        };
    }
}

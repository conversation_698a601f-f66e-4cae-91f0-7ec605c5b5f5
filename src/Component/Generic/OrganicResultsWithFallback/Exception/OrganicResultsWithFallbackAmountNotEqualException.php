<?php

declare(strict_types=1);

namespace App\Component\Generic\OrganicResultsWithFallback\Exception;

final class OrganicResultsWithFallbackAmountNotEqualException extends OrganicResultsWithFallbackException
{
    public static function create(int $resultsAmount, int $fallbackAmount, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf(
                'Amount of results and fallback components are not equal: %u / %u',
                $resultsAmount,
                $fallbackAmount,
            ),
            0,
            $previous,
        );
    }
}

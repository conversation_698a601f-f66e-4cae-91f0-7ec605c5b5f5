<?php

declare(strict_types=1);

namespace App\Component\Generic\OrganicResultsWithFallback\Exception;

final class OrganicResultsWithFallbackAmountOptimizationNotEqualException extends OrganicResultsWithFallbackException
{
    public static function create(
        bool $resultsAmountOptimization,
        bool $fallbackAmountOptimization,
        ?\Throwable $previous = null
    ): self
    {
        return new self(
            sprintf(
                'Results amount optimization of results and fallback components are not equal: %s / %s',
                $resultsAmountOptimization ? 'yes' : 'no',
                $fallbackAmountOptimization ? 'yes' : 'no',
            ),
            0,
            $previous,
        );
    }
}

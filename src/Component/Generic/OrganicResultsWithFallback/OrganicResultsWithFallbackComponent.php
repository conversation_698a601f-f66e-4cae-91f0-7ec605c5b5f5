<?php

declare(strict_types=1);

namespace App\Component\Generic\OrganicResultsWithFallback;

use App\JsonTemplate\Component\AbstractComponent;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;

final class OrganicResultsWithFallbackComponent extends AbstractComponent implements ParentComponentInterface
{
    public function __construct(
        public readonly OrganicResultsComponentInterface $resultsComponent,
        public readonly OrganicResultsComponentInterface $fallbackComponent
    )
    {
    }

    /**
     * @return OrganicResultsComponentInterface[]
     */
    public function getComponents(): array
    {
        return [
            $this->resultsComponent,
            $this->fallbackComponent,
        ];
    }

    public static function getType(): string
    {
        return 'organic_results_with_fallback';
    }

    public function getRenderer(): string
    {
        return OrganicResultsWithFallbackRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\OrganicResultsWithFallback;

use App\Ads\AdsAmountRegistry;
use App\Component\Content\OrganicContentPageResults\OrganicContentPageResultsComponentInterface;
use App\Component\Content\OrganicContentPageResults\OrganicContentPageResultsSupport;
use App\Component\Generic\Results\ResultsAmountOptimizer;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;
use App\JsonTemplate\Component\Parent\AbstractComponentParentRenderer;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;
use App\JsonTemplate\Component\Processed\ComponentRendererConditionalSearchApiRequestInterface;
use App\JsonTemplate\View\Data\Condition\AmountOfOrganicResultsCompliesWithAdsAmountCondition;
use App\JsonTemplate\View\Data\Condition\AmountOfOrganicResultsCondition;
use App\JsonTemplate\View\Data\Condition\ComponentProcessedDependencyCondition;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\DataRequest\SearchApiViewPageSizeDataRequestInterface;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\Component\SearchApiComponentRegistry;
use App\SearchApi\SearchApiManager;

final class OrganicResultsWithFallbackRenderer extends AbstractComponentParentRenderer
    implements ComponentRendererConditionalSearchApiRequestInterface
{
    public function __construct(
        private readonly ComponentRendererLocator $componentRendererLocator,
        private readonly SearchApiManager $searchApiManager,
        private readonly SearchApiComponentRegistry $searchApiComponentRegistry,
        private readonly OrganicContentPageResultsSupport $organicContentPageResultsSupport,
        private readonly ResultsAmountRegistry $resultsAmountRegistry,
        private readonly AdsAmountRegistry $adsAmountRegistry,
        private readonly ResultsAmountOptimizer $resultsAmountOptimizer
    )
    {
    }

    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof OrganicResultsWithFallbackComponent) {
            throw UnsupportedComponentException::create($component, [OrganicResultsWithFallbackComponent::class]);
        }

        $fallbackConditions = $conditions;

        if ($this->isComponentSupported($component->resultsComponent)) {
            $this->viewDataRequestBuilder->buildForComponents(
                components: [$component->resultsComponent],
                view      : $view,
                conditions: $conditions,
            );

            // Change conditions for fallback component
            $fallbackConditions = [
                new ComponentProcessedDependencyCondition(
                    component: $component->resultsComponent,
                ),
            ];

            if ($component->resultsComponent->resultAmountOptimization()) {
                $fallbackConditions[] = new AmountOfOrganicResultsCompliesWithAdsAmountCondition(
                    resultsAmountRegistry: $this->resultsAmountRegistry,
                    adsAmountRegistry    : $this->adsAmountRegistry,
                    expectedResult       : false,
                );
            } else {
                $fallbackConditions[] = new AmountOfOrganicResultsCondition(
                    resultsAmountRegistry: $this->resultsAmountRegistry,
                    min                  : $component->resultsComponent->getAmount(),
                    max                  : null,
                    expectedResult       : false,
                    resultsComponent     : $component->resultsComponent,
                );
            }

            $fallbackConditions = new ViewDataConditionCollection($fallbackConditions);
        }

        if ($this->isComponentSupported($component->fallbackComponent)) {
            $this->viewDataRequestBuilder->buildForComponents(
                components: [$component->fallbackComponent],
                view      : $view,
                conditions: $fallbackConditions,
            );
        }

        $this->searchApiComponentRegistry->addParentComponentAsChildComponentProcessedHandler($component);
    }

    public function handleSearchApiRequestCompleted(
        ParentComponentInterface $parentComponent,
        ComponentInterface $childComponent
    ): void
    {
        if (!$parentComponent instanceof OrganicResultsWithFallbackComponent) {
            throw UnsupportedComponentException::create($parentComponent, [OrganicResultsWithFallbackComponent::class]);
        }

        if ($childComponent !== $parentComponent->resultsComponent) {
            return;
        }

        $searchApiRequest = $this->searchApiManager->getSearchApiRequestOfComponent(
            $parentComponent->fallbackComponent,
        );

        if ($searchApiRequest === null) {
            return;
        }

        if (!$searchApiRequest->searchApiViewDataRequest instanceof SearchApiViewPageSizeDataRequestInterface) {
            return;
        }

        $resultsAmount = count($this->resultsAmountRegistry->getResultsByComponent($childComponent));

        if ($resultsAmount === $childComponent->getAmount()) {
            $this->searchApiManager->removeSearchApiRequestForComponent($parentComponent->fallbackComponent);

            return; // no fallback needed
        }

        $this->resultsAmountOptimizer->optimizeAmountOfResults($parentComponent->fallbackComponent);

        // Adjust to amount/page size that the results component is missing
        $searchApiRequest->searchApiViewDataRequest->setPageSize($parentComponent->fallbackComponent->getAmount());
    }

    /**
     * @inheritDoc
     */
    protected function getComponents(ComponentInterface $component): array
    {
        if (!$component instanceof OrganicResultsWithFallbackComponent) {
            throw UnsupportedComponentException::create($component, [OrganicResultsWithFallbackComponent::class]);
        }

        return $component->getComponents();
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof OrganicResultsWithFallbackComponent) {
            throw UnsupportedComponentException::create($component, [OrganicResultsWithFallbackComponent::class]);
        }

        $output = $this->renderComponent(
            $component->resultsComponent,
            OrganicResultsWithFallbackResolver::KEY_RESULTS,
            $view,
        );

        $requiredAmount = $component->resultsComponent->resultAmountOptimization()
            ? $this->adsAmountRegistry->getAmountOfAds()
            : $component->resultsComponent->getAmount();
        $results = $this->resultsAmountRegistry->getResultsByComponent($component->resultsComponent);

        if (count($results) < $requiredAmount) {
            $output .= $this->renderComponent(
                $component->fallbackComponent,
                OrganicResultsWithFallbackResolver::KEY_FALLBACK,
                $view,
            );
        }

        return $output;
    }

    private function renderComponent(
        OrganicResultsComponentInterface $component,
        string $property,
        ViewInterface $view
    ): string
    {
        if (!$this->isComponentSupported($component)) {
            return '';
        }

        $output = $this->componentRendererLocator->getRenderer($component)->render($component, $view);

        if ($output !== '') {
            $this->debugComponentTraceLog->addParentTrace(
                component      : $component,
                childComponents: [$component],
                property       : $property,
            );
        }

        return $output;
    }

    private function isComponentSupported(OrganicResultsComponentInterface $component): bool
    {
        return !$component instanceof OrganicContentPageResultsComponentInterface ||
               $this->organicContentPageResultsSupport->isSupported($component);
    }
}

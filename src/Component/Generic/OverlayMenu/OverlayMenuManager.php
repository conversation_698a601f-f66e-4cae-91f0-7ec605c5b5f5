<?php

declare(strict_types=1);

namespace App\Component\Generic\OverlayMenu;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\View\ViewInterface;

readonly class OverlayMenuManager
{
    public function __construct(
        private ComponentFactory $componentFactory,
        private OverlayMenuFactory $overlayMenuFactory,
        private OverlayMenuRenderer $overlayMenuRenderer
    )
    {
    }

    public function renderOverlayMenu(ViewInterface $view): string
    {
        return $this->overlayMenuRenderer->render(
            component: $this->getOverlayMenu(),
            view     : $view,
        );
    }

    private function getOverlayMenu(): OverlayMenuComponent
    {
        /** @var OverlayMenuComponent $overlayMenuComponent */
        $overlayMenuComponent = $this->overlayMenuFactory->create(
            [
                ComponentInterface::KEY_TYPE => OverlayMenuComponent::getType(),
                LayoutInterface::KEY         => OverlayMenuLayout::DEFAULT->value,
            ],
            $this->componentFactory,
        );

        return $overlayMenuComponent;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\OverlayMenu;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class OverlayMenuRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof OverlayMenuComponent) {
            throw UnsupportedComponentException::create($component, [OverlayMenuComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout' => $component->layout->value,
            ],
        );
    }
}

/** @define overlay-menu */
.overlay-menu {
    background: transparent;
    display: block;
    height: 100%;
    inset: 0.4rem 4rem 0 0;
    max-width: 40rem;
    overflow: hidden;
    position: fixed;
    text-align: left;
    z-index: 1500;

    &--hidden {
        display: none;
    }

    &__inner {
        background: #ffffff;
        height: 100%;
        inset: 0 0 0 0;
        overflow: hidden auto;
        -webkit-overflow-scrolling: touch;
        padding: 2.5rem;
        position: relative;
    }

    &__head {
        height: 5rem;
    }

    &__items {
        line-height: 3.5rem;
    }

    &__item {
        // Prevent miss clicks by adding some vertical space between the menu items
        padding: 0.4rem 0;
    }

    &__link {
        color: #505058;
        display: inline-block;
    }
}

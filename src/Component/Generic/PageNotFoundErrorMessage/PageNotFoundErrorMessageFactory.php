<?php

declare(strict_types=1);

namespace App\Component\Generic\PageNotFoundErrorMessage;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class PageNotFoundErrorMessageFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return PageNotFoundErrorMessageComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new PageNotFoundErrorMessageComponent(
            layout: PageNotFoundErrorMessageLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\PageNotFoundErrorMessage;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class PageNotFoundErrorMessageRenderer extends AbstractComponentRenderer
{
    public function __construct(private readonly TranslatorInterface $translator, private readonly Environment $twig)
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof PageNotFoundErrorMessageComponent) {
            throw UnsupportedComponentException::create($component, [PageNotFoundErrorMessageComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'title'   => $this->translator->trans('error_page.page_not_found.title'),
                'message' => $this->translator->trans('error_page.page_not_found.message'),
                'layout'  => $component->layout->value,
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Pagination;

use App\JsonTemplate\Component\AbstractComponent;

class PaginationComponent extends AbstractComponent
{
    public function __construct(
        public readonly int $currentPage,
        public readonly int $maxPage,
        public readonly PaginationLayout $layout
    )
    {
    }

    public static function getType(): string
    {
        return 'pagination';
    }

    public function getRenderer(): string
    {
        return PaginationRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Pagination;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum PaginationLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';
    case ROUNDED = 'rounded';
    case MODERN  = 'modern';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/Pagination/layout/default/pagination-default.html.twig',
            self::ROUNDED => '@component/Generic/Pagination/layout/rounded/pagination-rounded.html.twig',
            self::MODERN  => '@component/Generic/Pagination/layout/modern/pagination-modern.html.twig',
        };
    }
}

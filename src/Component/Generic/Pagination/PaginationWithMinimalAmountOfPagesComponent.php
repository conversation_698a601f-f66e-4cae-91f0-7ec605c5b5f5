<?php

declare(strict_types=1);

namespace App\Component\Generic\Pagination;

class PaginationWithMinimalAmountOfPagesComponent extends PaginationComponent
{
    public function __construct(
        int $currentPage,
        int $maxPage,
        PaginationLayout $layout,
        public readonly int $minimalAmountOfPages,
        public readonly int $requestedCurrentPage
    )
    {
        parent::__construct($currentPage, $maxPage, $layout);
    }

    public static function getType(): string
    {
        return 'pagination_with_minimal_amount_of_pages';
    }
}

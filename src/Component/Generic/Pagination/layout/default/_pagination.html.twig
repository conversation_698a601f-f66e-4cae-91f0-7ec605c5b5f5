{# @var current_page int #}
{# @var pages array<array<string, string|int>> #}
{# @var previous_page_link string|null #}
{# @var next_page_link string|null #}
{# @var no_follow bool #}
{# @var layout string #}
{% set component_class = 'pagination' %}
{% set prev_page_link_content %}
    <span class="{{ component_class }}__icon {{ component_class }}__icon-prev vsi">&lt;</span>
    <p class="{{ component_class }}__text">{{ 'pagination.previous'|trans }}</p>
{% endset %}
{% set next_page_link_content %}
    <p class="{{ component_class }}__text">{{ 'pagination.next'|trans }}</p>
    <span class="{{ component_class }}__icon {{ component_class }}__icon-next vsi">&gt;</span>
{% endset %}
<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    <ul class="{{ component_class }}__list">
        {%- if previous_page_link is not null -%}
            <li class="{{ component_class }}__item {{ component_class }}__item--prev">
                <a href="{{ previous_page_link }}" class="{{ component_class }}__link {{ component_class }}__link--chevron"{%- if no_follow -%} rel="nofollow"{%- endif -%}>
                    {{- prev_page_link_content -}}
                </a>
            </li>
        {%- else -%}
            <li class="{{ component_class }}__item {{ component_class }}__item--hidden {{ component_class }}__item--disabled {{ component_class }}__item--prev">
                <div class="{{ component_class }}__link {{ component_class }}__link--chevron">
                    {{- prev_page_link_content -}}
                </div>
            </li>
        {%- endif -%}

        {%- for page in pages -%}
            <li class="{{ component_class }}__item {{ component_class }}__item--number{{ page.number == current_page ? ' ' ~ component_class ~ '__item--active' }}">
                <a href="{{ page.link }}" class="{{ component_class }}__link"{%- if no_follow -%} rel="nofollow"{%- endif -%}>{{ page.number }}</a>
            </li>
        {%- endfor -%}

        {%- if next_page_link -%}
            <li class="{{ component_class }}__item {{ component_class }}__item--next">
                <a href="{{ next_page_link }}" class="{{ component_class }}__link {{ component_class }}__link--chevron"{%- if no_follow -%} rel="nofollow"{%- endif -%}>
                    {{- next_page_link_content -}}
                </a>
            </li>
        {%- else -%}
            <li class="{{ component_class }}__item {{ component_class }}__item--hidden {{ component_class }}__item--disabled {{ component_class }}__item--next">
                <div class="{{ component_class }}__link {{ component_class }}__link--chevron">
                    {{- next_page_link_content -}}
                </div>
            </li>
        {%- endif -%}
    </ul>
</div>

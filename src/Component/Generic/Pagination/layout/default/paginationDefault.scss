/** @define pagination */
.pagination--default .pagination {
    &__list {
        display: block;
        height: 3.2rem;
        margin: 2.5rem 0;
    }

    &__link {
        background-color: #ffffff;
        border: 0.1rem solid #eeeeee;
        color: var(--link_color, #0000d6);
        display: block;
        font-size: 1.3rem;
        font-weight: 400;
        height: 3.2rem;
        line-height: 3.2rem;
        overflow: hidden;
        padding: 0 1.2rem;
        text-align: center;

        &:hover {
            background-color: #eeeeee;
        }
    }

    &__item {
        display: inline-block;
        margin-right: 0.4rem;

        &--active {
            .pagination {
                &__link {
                    background-color: var(--brand-primary-color);
                    border-color: var(--item-active_border-color, var(--brand-primary-color_dark));
                    color: #ffffff;
                }
            }
        }

        &:last-child {
            margin-right: 0;
        }

        &--prev .pagination {
            &__text {
                &::before {
                    content: "\00AB\00A0";
                }
            }
        }

        &--next .pagination {
            &__text {
                &::after {
                    content: "\00A0\00BB";
                }
            }
        }

        &--hidden {
            display: none;
        }
    }

    &__icon {
        display: none;
    }

    @media #{map-get($media-max, b)} {
        &__list {
            height: 3.8rem;
        }

        &__link {
            height: 3.8rem;
            line-height: 3.8rem;
        }

        &__item {
            &--number {
                display: none;
            }
        }

        &__link--chevron {
            font-size: 1.4rem;
            padding: 0 1.4rem;
        }
    }
}

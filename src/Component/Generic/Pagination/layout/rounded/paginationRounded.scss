/** @define pagination */
.pagination--rounded .pagination {
    &__list {
        height: 4.4rem;
        margin-bottom: 2.5rem;
        margin-top: 4.8rem;
    }

    &__link {
        border-radius: 100%;
        color: #505058;
        display: block;
        font-size: 2rem;
        font-weight: 700;
        height: 4.4rem;
        line-height: 4.4rem;
        overflow: hidden;
        text-align: center;
        width: 4.4rem;

        &--chevron {
            box-shadow: 0 0 0.8rem 0.4rem rgba(0, 0, 0, 0.12);
            color: #c2c1c0;
            width: 4.4rem;

            &:hover {
                color: var(--brand-primary-color);
            }
        }
    }

    &__text {
        display: none;
    }

    &__item {
        display: inline-block;

        &--active {
            .pagination {
                &__link {
                    background: var(--brand-primary-color);
                    color: #ffffff;
                }
            }
        }

        &--prev {
            margin-right: 3rem;

            .pagination {
                &__icon::before {
                    content: $vsi-chevron-left;
                }
            }
        }

        &--next {
            margin-left: 3rem;

            .pagination {
                &__icon::before {
                    content: $vsi-chevron-right;

                    // Due to optical illusion push it a little bit to the right
                    padding-left: 0.2rem;
                }
            }
        }

        &--hidden {
            display: none;
        }
    }

    @media #{map-get($media-max, b)} {
        &__list {
            display: flex;
            justify-content: space-between;
        }

        &__text {
            display: inline-block;
            line-height: 4.4rem;
            margin: 0 1.2rem;
            text-decoration: none;
        }

        &__item {
            height: 4.6rem;

            &--number {
                display: none;
            }

            &--hidden {
                display: inline-block;
            }

            &--prev,
            &--next {
                border: 0.1rem solid var(--brand-primary-color);
                border-radius: 2rem;
                color: var(--brand-primary-color);
            }

            &--next {
                // stylelint-disable selector-class-pattern
                .pagination__text,
                .pagination__icon::before {
                    margin-right: 1.2rem;
                }

                // stylelint-enable selector-class-pattern
            }

            &--prev {
                // stylelint-disable selector-class-pattern
                .pagination__text,
                .pagination__icon::before {
                    margin-left: 1.2rem;
                }

                // stylelint-enable selector-class-pattern
            }

            &--disabled {
                border: 0.1rem solid #c2c1c0;
                border-radius: 2rem;
                color: #c2c1c0;
            }
        }

        &__link--chevron {
            border: none;
            box-shadow: none;
            color: inherit;
            display: inline-block;
            font-size: 1.4rem;
            font-weight: normal;
            width: 100%;

            &:hover {
                color: inherit;
            }
        }

        &__icon {
            display: inline-block;
        }
    }
}

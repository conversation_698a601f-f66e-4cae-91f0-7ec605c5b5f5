<?php

declare(strict_types=1);

namespace App\Component\Generic\Preferences\Form;

use App\Generic\Form\Extension\SwitchCheckboxType;
use App\Preferences\Helper\PreferencesHelper;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;

final class PreferencesType extends AbstractType
{
    public function __construct(
        private readonly PreferencesHelper $preferencesHelper
    )
    {
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $querySuggestionChecked = $this->preferencesHelper->getQuerySuggestion() ?? true;
        $keywordHighlightChecked = $this->preferencesHelper->getKeywordHighlight() ?? true;
        $previousSearchesChecked = $this->preferencesHelper->getPreviousSearches() ?? true;

        $builder
            ->add(
                PreferencesHelper::SETTING_SAFE_SEARCH,
                SwitchCheckboxType::class,
                [
                    'label' => 'preferences.form.safe_search.label',
                ],
            )
            ->add(
                PreferencesHelper::SETTING_SAME_WINDOW,
                SwitchCheckboxType::class,
                [
                    'label' => 'preferences.form.same_window.label',
                ],
            )
            ->add(
                PreferencesHelper::SETTING_PREVIOUS_SEARCHES,
                SwitchCheckboxType::class,
                [
                    'label' => 'preferences.form.previous_searches.label',
                    'attr'  => [
                        'checked' => $previousSearchesChecked,
                    ],
                ],
            )
            ->add(
                PreferencesHelper::SETTING_KEYWORD_HIGHLIGHT,
                SwitchCheckboxType::class,
                [
                    'label' => 'preferences.form.keyword_highlight.label',
                    'attr'  => [
                        'checked' => $keywordHighlightChecked,
                    ],
                ],
            )
            ->add(
                PreferencesHelper::SETTING_QUERY_SUGGESTION,
                SwitchCheckboxType::class,
                [
                    'label' => 'preferences.form.query_suggestion.label',
                    'attr'  => [
                        'checked' => $querySuggestionChecked,
                    ],
                ],
            )
            ->add(
                'submit',
                SubmitType::class,
                [
                    'label' => 'preferences.form.submit.label',
                ],
            );
    }
}

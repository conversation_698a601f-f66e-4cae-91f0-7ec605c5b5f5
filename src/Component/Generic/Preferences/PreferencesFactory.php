<?php

declare(strict_types=1);

namespace App\Component\Generic\Preferences;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class PreferencesFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return PreferencesComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new PreferencesComponent(
            layout: PreferencesLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

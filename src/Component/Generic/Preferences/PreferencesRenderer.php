<?php

declare(strict_types=1);

namespace App\Component\Generic\Preferences;

use App\Component\Generic\Preferences\Form\PreferencesType;
use App\Http\Request\Main\MainRequestInterface;
use App\Http\Url\PersistentUrlParametersRouter;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Helper\PreferencesHelper;
use App\Search\Registry\RouteRegistry;
use App\SearchHistory\Helper\SearchHistoryHelper;
use Symfony\Component\Form\FormFactoryInterface;
use Twig\Environment;

class PreferencesRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly MainRequestInterface $mainRequest,
        private readonly FormFactoryInterface $formFactory,
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly RouteRegistry $routeRegistry,
        private readonly PreferencesHelper $preferencesHelper,
        private readonly SearchHistoryHelper $searchHistoryHelper
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof PreferencesComponent) {
            throw UnsupportedComponentException::create($component, [PreferencesComponent::class]);
        }

        $form = $this->formFactory->create(
            PreferencesType::class,
            $this->preferencesHelper->getPreferencesValues(),
        );
        $form->handleRequest($this->mainRequest->getRequest());

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $form->getData();

            $this->preferencesHelper->setPreferencesValues(
                $view->getResponse(),
                $formData,
            );

            if ($this->preferencesHelper->getPreviousSearches() === false) {
                $this->searchHistoryHelper->removeHistory($view->getResponse());
            }

            $view->getDataRegistry()->redirectUrl = $this->persistentUrlParametersRouter->generate(
                route: $this->routeRegistry->getSearchRoute(),
            );

            // Form handled, no need to render anything
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout' => $component->layout->value,
                'form'   => $form->createView(),
            ],
        );
    }
}

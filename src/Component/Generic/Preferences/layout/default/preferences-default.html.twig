{# @var query string #}
{# @var layout string #}
{% form_theme form '@shared/templates/form/component.html.twig' %}
{{ component_style(['preferencesDefault']) }}
{% set component_class = 'preferences' %}

<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    {{ form_start(form, {attr: {novalidate: 'novalidate', class: 'form'}}) }}
    {% set form_fields = {
        safe_search: 'preferences.form.safe_search.description'|trans,
        same_window: null,
        previous_searches: 'preferences.form.previous_searches.description'|trans,
        keyword_highlight: 'preferences.form.keyword_highlight.description'|trans,
        query_suggestion: 'preferences.form.query_suggestion.description'|trans
    } %}

    <div class="{{ component_class }}__fields">
        {% for form_field, form_description in form_fields %}
            <div class="{{ component_class }}__field">
                <div class="{{ component_class }}__cell">
                    {{ form_label(
                        form[form_field],
                        null,
                        {
                            component_class: component_class
                        }
                    ) }}
                    {% if form_description is not null %}
                        <p class="{{ component_class }}__description">{{ form_description }}</p>
                    {% endif %}
                </div>
                <div class="{{ component_class }}__cell {{ component_class }}__cell--widget">
                    {{ form_widget(
                        form[form_field],
                        {
                            component_class: component_class
                        }
                    ) }}
                </div>
            </div>
        {% endfor %}
    </div>

    <div class="{{ component_class }}__buttons">
        {{ form_widget(
            form.submit,
            {
                component_class: component_class
            }
        ) }}
    </div>

    {{ form_end(form) }}
</div>

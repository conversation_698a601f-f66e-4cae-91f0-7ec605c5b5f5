<?php

declare(strict_types=1);

namespace App\Component\Generic\RelatedSourceMatchesVisymo;

use App\Component\Generic\AbstractCondition\AbstractConditionComponent;

class RelatedSourceMatchesVisymoComponent extends AbstractConditionComponent
{
    public static function getType(): string
    {
        return 'related_source_matches_visymo';
    }

    public function getRenderer(): string
    {
        return RelatedSourceMatchesVisymoRenderer::class;
    }
}

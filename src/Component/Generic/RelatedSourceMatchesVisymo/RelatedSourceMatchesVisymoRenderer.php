<?php

declare(strict_types=1);

namespace App\Component\Generic\RelatedSourceMatchesVisymo;

use App\Component\Generic\AbstractCondition\AbstractConditionRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ScalarValueCondition;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\SegmentInterface;
use App\RelatedTerms\Request\RelatedTermsRequestInterface;

class RelatedSourceMatchesVisymoRenderer extends AbstractConditionRenderer
{
    public function __construct(
        private readonly RelatedTermsRequestInterface $relatedTermsRequest
    )
    {
    }

    protected function getActiveSegment(ComponentInterface $component): SegmentInterface
    {
        if (!$component instanceof RelatedSourceMatchesVisymoComponent) {
            throw UnsupportedComponentException::create($component, [RelatedSourceMatchesVisymoComponent::class]);
        }

        if ($this->relatedTermsRequest->getRelatedTermsLinkIndex() === null) {
            return $component->getNonMatchingSegment();
        }

        return $component->getMatchingSegment();
    }

    protected function getConditions(ComponentInterface $component, bool $expectedResult): ViewDataConditionCollection
    {
        if (!$component instanceof RelatedSourceMatchesVisymoComponent) {
            throw UnsupportedComponentException::create($component, [RelatedSourceMatchesVisymoComponent::class]);
        }

        return new ViewDataConditionCollection(
            [
                new ScalarValueCondition(
                    value         : $this->relatedTermsRequest->getRelatedTermsLinkIndex() !== null,
                    expectedValue : true,
                    expectedResult: $expectedResult,
                ),
            ],
        );
    }
}

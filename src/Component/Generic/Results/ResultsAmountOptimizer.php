<?php

declare(strict_types=1);

namespace App\Component\Generic\Results;

use App\Ads\AdsAmountRegistry;
use App\JsonTemplate\Component\OrganicResultsComponentInterface;

final readonly class ResultsAmountOptimizer
{
    public function __construct(
        private AdsAmountRegistry $adsAmountRegistry,
        private ResultsAmountRegistry $resultsAmountRegistry
    )
    {
    }

    public function optimizeAmountOfResults(OrganicResultsComponentInterface $component): void
    {
        if (!$component->resultAmountOptimization() || $this->adsAmountRegistry->getAmountOfAds() === 0) {
            return;
        }

        $amountRegisteredAsOrganic = $this->resultsAmountRegistry->getRegisteredAmountByResultsType(ResultsType::ORGANIC);
        $maxAmountOfResults = max(
            0,
            $this->adsAmountRegistry->getAmountOfAds() - $amountRegisteredAsOrganic,
        );

        // The max amount of results should not be more than the amount set on the component
        $maxAmountOfResults = min($maxAmountOfResults, $component->getAmount());

        // subtract the amount set on the component from the max amount of results and decrease the amount of the component
        // This will give a negative value so we use the abs function to make it positive
        // for example: if the component amount is 10 and the max amount of results is 5: abs(5 - 10 = -5) = 5
        $component->decreaseAmount(abs($maxAmountOfResults - $component->getAmount()));
    }
}

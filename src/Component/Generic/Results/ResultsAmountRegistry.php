<?php

declare(strict_types=1);

namespace App\Component\Generic\Results;

use App\JsonTemplate\Component\OrganicResultsComponentInterface;

final class ResultsAmountRegistry
{
    /** @var array<string, mixed[]> */
    private array $componentResults = [];

    /** @var array<string, int> */
    private array $amountOfResultsRegisteredPerKey = [];

    /**
     * @param mixed[] $results
     */
    public function registerResults(
        OrganicResultsComponentInterface $component,
        ResultsType $resultsType,
        string $resultsKey,
        array $results
    ): void
    {
        if (isset($this->componentResults[$component->getId()])) {
            return;
        }

        if (ResultsType::tryFrom($resultsKey) !== null) {
            throw new \InvalidArgumentException(
                'Results key cannot be a ResultsType enum value',
            );
        }

        $amount = $component->getAmount();

        if ($amount === 0) {
            $this->componentResults[$component->getId()] = [];
        } else {
            $sliceOffset = $this->getAmountOfResultsRegistered(
                resultsKey: $resultsKey,
            );
            $this->componentResults[$component->getId()] = array_slice(
                $results,
                $sliceOffset,
                $amount,
            );
        }

        $resultsCount = count($this->componentResults[$component->getId()]);

        $this->increaseAmountOfResultsRegistered(
            resultsType    : $resultsType,
            resultsKey     : $resultsKey,
            amountOfResults: $resultsCount,
        );
    }

    /**
     * @return mixed[]
     */
    public function getResultsByComponent(OrganicResultsComponentInterface $component): array
    {
        return $this->componentResults[$component->getId()] ?? [];
    }

    public function getRegisteredAmountByResultsType(ResultsType $resultsType): int
    {
        $this->amountOfResultsRegisteredPerKey[$resultsType->value] ??= 0;

        return $this->amountOfResultsRegisteredPerKey[$resultsType->value];
    }

    private function getAmountOfResultsRegistered(string $resultsKey): int
    {
        $this->amountOfResultsRegisteredPerKey[$resultsKey] ??= 0;

        return $this->amountOfResultsRegisteredPerKey[$resultsKey];
    }

    private function increaseAmountOfResultsRegistered(
        ResultsType $resultsType,
        string $resultsKey,
        int $amountOfResults
    ): void
    {
        $this->amountOfResultsRegisteredPerKey[$resultsKey] ??= 0;
        $this->amountOfResultsRegisteredPerKey[$resultsType->value] ??= 0;

        $this->amountOfResultsRegisteredPerKey[$resultsKey] += $amountOfResults;
        $this->amountOfResultsRegisteredPerKey[$resultsType->value] += $amountOfResults;
    }
}

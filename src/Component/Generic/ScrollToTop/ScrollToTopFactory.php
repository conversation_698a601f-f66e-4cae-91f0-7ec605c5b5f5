<?php

declare(strict_types=1);

namespace App\Component\Generic\ScrollToTop;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class ScrollToTopFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ScrollToTopComponent::class;
    }

    /**
     * @param mixed[] $options
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ScrollToTopComponent(
            layout: ScrollToTopLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

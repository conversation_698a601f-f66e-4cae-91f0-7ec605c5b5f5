<?php

declare(strict_types=1);

namespace App\Component\Generic\ScrollToTop;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum ScrollToTopLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/ScrollToTop/layout/default/scroll_to_top-default.html.twig',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchBar;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

class SearchBarFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return SearchBarComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new SearchBarComponent(
            layout                 : SearchBarLayout::from($options[LayoutInterface::KEY]),
            showSearchQuery        : $options[SearchBarResolver::KEY_SHOW_SEARCH_QUERY],
            allowStartQuerySearch  : $options[SearchBarResolver::KEY_ALLOW_START_QUERY_SEARCH],
            autofocus              : $options[SearchBarResolver::KEY_AUTOFOCUS],
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchBar;

use App\Component\Generic\AutoSuggest\AutoSuggestManager;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Search\Registry\RouteRegistry;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use Twig\Environment;

class SearchBarRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly AutoSuggestManager $autoSuggestManager,
        private readonly RouteRegistry $routeRegistry,
        private readonly TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
            ],
            $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof SearchBarComponent) {
            throw UnsupportedComponentException::create($component, [SearchBarComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();
        $query = $component->showSearchQuery && !$this->trademarkInfringementResultBlocker->blockResults()
            ? (string)$viewDataRegistry->getQuery()
            : null;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'query'                     => $query,
                'allow_start_query_search'  => $component->allowStartQuerySearch,
                'autofocus'                 => $component->autofocus,
                'component_space_modifiers' => $component->componentSpaceModifiers,
                'search_route'              => $this->routeRegistry->getSearchRoute(),
                'auto_suggest'              => $this->autoSuggestManager->renderAutoSuggest(
                    parentComponentClass: 'search-bar',
                    view                : $view,
                ),
            ],
        );
    }
}

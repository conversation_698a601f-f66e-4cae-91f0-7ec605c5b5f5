<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchFilters;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\SearchFilter\SearchFilter;

final class SearchFiltersFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return SearchFiltersComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new SearchFiltersComponent(
            layout : SearchFiltersLayout::from($options[LayoutInterface::KEY]),
            filters: $options[SearchFiltersResolver::KEY_FILTERS],
        );
    }

    /**
     * @param SearchFilter[] $filters
     */
    public function createWithLayoutAndFilters(SearchFiltersLayout $layout, array $filters): ComponentInterface
    {
        return new SearchFiltersComponent(
            layout : $layout,
            filters: $filters,
        );
    }
}

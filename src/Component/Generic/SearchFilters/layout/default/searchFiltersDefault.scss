/** @define search-filters */
.search-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem 0.5rem;
    margin: 1rem 0;

    &__filter {
        line-height: 4rem;
        min-width: 15rem;
        position: relative;
        user-select: none;

        &--unfolded {
            // stylelint-disable visymo/sort-properties-alphabetically
            --_select_display: block;
            --_label_border-bottom-color: #ffffff;
            --_label_border-radius: 0.5rem 0.5rem 0 0;

            // stylelint-enable visymo/sort-properties-alphabetically
        }
    }

    &__label {
        border: 0.1rem solid #505058;
        border-bottom-color: var(--_label_border-bottom-color, #505058);
        border-radius: var(--_label_border-radius, 0.5rem);
        color: #505058;
        cursor: pointer;
        display: inline-block;
        padding: 0 3rem 0 1rem;
        width: 100%;

        &::before {
            content: $vsi-chevron-down;
            font-size: 1.2rem;
            position: absolute;
            right: 1.1rem;
        }
    }

    &__select {
        display: var(--_select_display, none);
        position: absolute;
        width: 100%;
        z-index: 3;

        &-link {
            color: #505058;
            display: block;
            padding: 0 1rem;
            text-decoration: none;

            &:hover {
                color: var(--brand-primary-color);
            }
        }

        &-list {
            background: #ffffff;
            border: 0.1rem solid #505058;
            border-bottom-left-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
            border-top: none;
        }
    }

    @media #{map-get($media-max, b)} {
        &__filter {
            width: 100%;
        }
    }
}

{# @var layout string #}
{# @var filters \App\Search\Filter\SearchFilter[] #}
{{ component_style('searchFiltersDefault') }}
{{ component_javascript('searchFilters') }}
{% set component_class = 'search-filters' %}
<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    {% for filter in filters %}
        <div class="{{ component_class }}__filter">
            <div class="{{ component_class }}__label vsi">{{ filter.label }}</div>
            <div class="{{ component_class }}__select {{ component_class }}__select--hidden">
                <ul class="{{ component_class }}__select-list">
                    {% for option in filter.options %}
                        <li class="{{ component_class }}__select-item">
                            <a class="{{ component_class }}__select-link" href="{{ option.url }}">{{ option.label }}</a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    {% endfor %}
</div>

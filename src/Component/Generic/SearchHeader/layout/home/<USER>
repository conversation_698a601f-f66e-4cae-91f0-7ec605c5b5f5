@import "../../../../../../resources/shared/assets/scss/BlockSets/overlay";
@import "../default/searchHeaderMixins";

/** @define search-header */
// stylelint-disable plugin/selector-bem-pattern
.search-header--home {
    display: var(--display, block);
    flex-direction: var(--flex-direction, unset);
    padding: 0 1.5rem;

    .search-header {
        // Rows
        &__row-menu {
            min-height: 1.5rem;
            order: var(--row-menu_order, 0);
            padding-top: 0.2rem;
        }

        &__row-menu,
        &__row-search {
            margin: 0 auto;
            max-width: var(--row_max-width, 97rem);
            width: 100%;
        }

        // Area
        &__area {
            display: none;
        }

        // Auto suggest
        &__auto-suggest {
            top: 100%;
        }

        @media #{map-get($media, c)} {
            &__row-menu,
            &__row-search {
                max-width: var(--row_max-width, 75rem);
            }
        }

        @media #{map-get($media-max, b)} {
            &__menu-item--additional {
                display: none;
            }
        }
    }

    // Menu
    // stylelint-disable-next-line selector-class-pattern
    .search-header__menu {
        display: flex;
        flex-wrap: nowrap;
        font-size: 1.3rem;
        height: 3.3rem;
        justify-content: center;
        line-height: 3.4rem;
        margin: var(--menu_margin, 0);

        &-item {
            padding: 0 0.9rem;
        }

        &-item--more {
            display: none;
        }

        &-link {
            color: var(--menu-link_color, #777777);
            display: inline-block;
            text-decoration: var(--menu-link_text-decoration, none);
            white-space: nowrap;

            &--active,
            &:hover {
                color: var(--menu-link-highlight_color, #393e46);
            }

            &--active {
                font-weight: 700;
            }
        }
    }

    // stylelint-disable-next-line no-invalid-position-at-import-rule
    @import "../default/searchHeaderMoreMenu";

    @media #{map-get($media-min, d)} {
        @include search-header-device(desktop);
    }

    @media #{map-get($media, c)} {
        @include search-header-device(tablet);
    }

    @media #{map-get($media-max, b)} {
        @include search-header-device(mobile);
    }

    // stylelint-disable-next-line selector-class-pattern
    .search-bar--default {
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 4rem;
        --field_box-shadow: none;
        --padding: 0;

        // stylelint-disable-next-line selector-class-pattern
        .search-bar__field-button {
            background-color: var(--field-button_background-color, var(--brand-primary-color));
            border-color: var(--field-button_background-color);
            color: var(--field-button-highlight_color, #ffffff);
        }
    }
}

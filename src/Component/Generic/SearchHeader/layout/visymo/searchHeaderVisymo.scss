@import "../../../../../../resources/shared/assets/scss/BlockSets/overlay";
@import "../default/searchHeaderMixins";

/** @define search-header */
.search-header--visymo {
    --column-search_width: calc(var(--container__column-one_width) - 2.4rem);
    --with-background-desktop_box-shadow: 0 0.1rem 0.4rem rgba(20, 23, 26, 0.1);

    @if $language-direction == "rtl" {
        padding: var(--padding, 0 var(--container__column-space_width) 1.5rem 1.5rem);
    } @else {
        padding: var(--padding, 0 1.5rem 1.5rem var(--container__column-space_width));
    }
    position: relative;

    // stylelint-disable-next-line selector-class-pattern
    .search-header__menu {
        display: flex;
        flex-wrap: nowrap;
        font-size: 1.3rem;
        height: 3.3rem;
        line-height: 3.4rem;

        // flexbox gap is not supported in all modern browsers

        &-item {
            padding: 0 0.9rem;
        }

        &-item--more {
            display: none;
        }

        &-link {
            color: #777777;
            display: inline-block;
            white-space: nowrap;

            &--active,
            &:hover {
                color: #393e46;
            }

            &--active {
                font-weight: 700;
            }
        }
    }

    .search-header {
        // Rows
        &__row-menu {
            display: grid;
            grid-template-areas: "main-menu sub-menu";
            grid-template-columns: 1fr auto;
            min-height: var(--row-menu_min-height, 1.5rem);
            padding-left: 0.9rem;
            padding-top: 0.2rem;
        }

        &__row-search {
            display: grid;
            grid-template-areas: "search options logo";
            grid-template-columns: var(--column-search_width) auto auto;
            padding-left: 0.9rem;
        }

        // Area
        &__area {
            display: none;
        }

        // Main menu
        &__main-menu {
            grid-area: main-menu;
            padding-left: 1rem;

            // stylelint-disable-next-line selector-class-pattern
            .search-header__menu-item:first-child {
                padding-left: 0;
            }
        }

        // Sub menu
        &__sub-menu {
            grid-area: sub-menu;
            justify-self: end;

            // stylelint-disable-next-line selector-class-pattern
            .search-header__menu-item:last-child {
                padding-right: 0;
            }
        }

        // Options
        &__options {
            align-self: center;
            grid-area: options;
            justify-self: start;
            padding-left: 2rem;
        }

        // Brand logo
        &__logo {
            align-self: center;
            grid-area: logo;
            justify-self: end;
            padding-left: 1rem;

            @media #{map-get($media-max, b)} {
                display: none;
            }
        }

        &__brand-image {
            height: max-content;
            max-width: 100%;
            object-fit: scale-down;
        }
    }

    // stylelint-disable-next-line no-invalid-position-at-import-rule
    @import "../default/searchHeaderMoreMenu";

    // stylelint-disable-next-line plugin/selector-bem-pattern
    .search-bar--default {
        --field-button-icon_font-size: 1.7rem;
        --field-button_font-size: var(--field-button-icon_font-size, 1.4rem);
        --field-button_width: 4rem;
        --field-corner_border-radius: 0.2rem;
        --field-input_padding_ltr: 0 0 0 0.9rem;
        --field-input_padding_rtl: 0 0.9rem 0 0;
        --field_border: 0.1rem solid #cfcfd2;
        --field_box-shadow: none;
        --padding: 0;
        grid-area: search;
    }

    // Search options
    // stylelint-disable-next-line selector-class-pattern
    .search-header__button-menu {
        &-icon {
            background: var(--button-menu-icon_background, #ffffff);
            border-radius: var(--button-menu-icon_border-radius, 0);
            box-shadow: var(--button-menu-icon_box-shadow, 0 0.1rem 0.4rem rgba(20, 23, 26, 0.1));
            cursor: pointer;
            display: block;
            height: 4rem;
            overflow: hidden;
            padding: 1rem;
            text-align: center;
            width: 4rem;

            &:hover {
                background: var(--button-menu-icon-hover_background, #ebebeb);
            }
        }

        &-line {
            background: var(--button-menu-line_background, var(--brand-primary-color));
            height: 0.2rem;
            margin: 0.4rem 0;
        }
    }

    // Device responsive specific
    @media #{map-get($media-min, d)} {
        @include search-header-device(desktop);

        // stylelint-disable-next-line plugin/selector-bem-pattern
        &.search-header--background-desktop {
            background: var(--with-background-desktop_background, #f5f8fa);
            border-bottom: var(--with-background-desktop_border-bottom, 0);
            margin-bottom: 0.4rem;

            // stylelint-disable-next-line plugin/selector-bem-pattern
            .search-bar--default {
                // stylelint-disable-next-line prettier/prettier
                --field_box-shadow: var(--with-background-desktop_box-shadow, 0 0.1rem 0.4rem rgba(20, 23, 26, 0.1));
            }
        }
    }

    @media #{map-get($media, c)} {
        @include search-header-device(tablet);

        .search-header {
            &__row-menu {
                padding-left: 1.5rem;
            }

            &__row-search {
                grid-template-columns: 1fr auto auto;
                padding-left: 1.5rem;
            }
        }

        // stylelint-disable-next-line plugin/selector-bem-pattern
        &.search-header--background-tablet {
            background: var(--with-background-tablet_background, #fcfcfc);
            border-bottom: var(--with-background-tablet_border-bottom, 0.1rem solid #eeeeee);
            padding-bottom: 1.1rem;

            // stylelint-disable-next-line plugin/selector-bem-pattern
            .search-bar--default {
                --field_box-shadow: var(--with-background-tablet_box-shadow, none);
            }
        }
    }

    @media #{map-get($media-max, b)} {
        @include search-header-device(mobile);

        .search-header {
            &__row-menu {
                padding-left: 1.5rem;
            }

            &__row-search {
                grid-template-areas: "search options";
                grid-template-columns: 1fr auto;
                padding-left: 1.5rem;
            }

            &__menu-item--additional {
                display: none;
            }
        }

        // stylelint-disable-next-line plugin/selector-bem-pattern
        &.search-header--background-mobile {
            background: var(--with-background-mobile_background, #fcfcfc);
            border-bottom: var(--with-background-mobile_border-bottom, 0.1rem solid #eeeeee);
            margin-bottom: 1rem;
            padding-bottom: 1.1rem;

            // stylelint-disable-next-line plugin/selector-bem-pattern
            .search-bar--default {
                --field_box-shadow: var(--with-background-mobile_box-shadow, none);
            }
        }
    }
}

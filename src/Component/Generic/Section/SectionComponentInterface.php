<?php

declare(strict_types=1);

namespace App\Component\Generic\Section;

interface SectionComponentInterface
{
    public const string KEY_SECTION                = 'section';
    public const string KEY_SECTION_VISIBLE        = 'section_visible';
    public const string KEY_SECTION_CSS_PROPERTIES = 'section_css_properties';

    public const string SECTION_REGEX = '~^([a-z][a-z0-9-]*[a-z0-9])$~';

    public function getSection(): ?string;

    public function isSectionVisible(): bool;

    /**
     * @return SectionCssProperty[]
     */
    public function getSectionCssProperties(): array;
}

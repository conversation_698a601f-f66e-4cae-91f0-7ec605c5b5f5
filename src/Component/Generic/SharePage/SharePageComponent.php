<?php

declare(strict_types=1);

namespace App\Component\Generic\SharePage;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class SharePageComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly SharePageLayout $layout,
        public readonly Share $share,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'share_page';
    }

    public function getRenderer(): string
    {
        return SharePageRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\SharePage;

use App\Component\Generic\SharePage\Result\ShareResult;
use App\Component\Generic\SharePage\Result\ShareResultFactory;
use App\ContentPage\Url\ContentPageUrlGenerator;
use App\Http\Request\Info\RequestInfoInterface;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class SharePageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly ShareResultFactory $shareResultFactory,
        private readonly ContentPageUrlGenerator $contentPageUrlGenerator,
        private readonly RequestInfoInterface $requestInfo,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof SharePageComponent) {
            throw UnsupportedComponentException::create($component, [SharePageComponent::class]);
        }

        /** @phpstan-ignore-next-line Only one Share case */
        if ($component->share === Share::CONTENT_PAGE) {
            $request->setRequirements(
                [
                    ViewDataProperty::CONTENT_PAGE,
                ],
                $conditions,
            );
        }
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof SharePageComponent) {
            throw UnsupportedComponentException::create($component, [SharePageComponent::class]);
        }

        /** @phpstan-ignore-next-line Only one Share case */
        if ($component->share === Share::CONTENT_PAGE) {
            $contentPageViewDataRequest = $request->contentPage()->enable();

            $this->searchApiManager->registerComponentSearchRequest(
                component               : $component,
                viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
                searchApiViewDataRequest: $contentPageViewDataRequest,
                conditions              : $conditions,
            );
        }
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof SharePageComponent) {
            throw UnsupportedComponentException::create($component, [SharePageComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();
        $shareResults = $this->getShareResults($component, $viewDataRegistry);

        if ($shareResults === []) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'share_results'             => $shareResults,
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    /**
     * @return ShareResult[]
     */
    private function getShareResults(SharePageComponent $component, ViewDataRegistry $viewDataRegistry): array
    {
        $shareUrl = $this->getShareUrl($component, $viewDataRegistry);

        if ($shareUrl === null) {
            return [];
        }

        $query = $viewDataRegistry->has(ViewDataProperty::QUERY)
            ? $viewDataRegistry->getQuery()
            : null;

        return $this->shareResultFactory->createDefaultResults(
            $query,
            $shareUrl,
        );
    }

    private function getShareUrl(SharePageComponent $component, ViewDataRegistry $viewDataRegistry): ?string
    {
        /** @phpstan-ignore-next-line Only one Share case */
        if ($component->share === Share::CONTENT_PAGE) {
            $contentPage = $viewDataRegistry->getContentPage()->page;

            if ($contentPage === null) {
                return null;
            }

            return $this->contentPageUrlGenerator->generateUrl(
                contentPage    : $contentPage,
                absoluteUrl    : true,
                preferenceRoute: $this->requestInfo->getRoute(),
            );
        }

        /** @phpstan-ignore-next-line Only one Share case */
        return null;
    }
}

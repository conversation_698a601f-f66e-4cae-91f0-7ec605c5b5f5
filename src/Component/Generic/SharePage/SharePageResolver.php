<?php

declare(strict_types=1);

namespace App\Component\Generic\SharePage;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Generic\Validator\AllowedEnumCaseValidator;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class SharePageResolver extends AbstractSpaceResolver
{
    public const string KEY_SHARE = 'share';

    public static function getSupportedComponent(): string
    {
        return SharePageComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::KEY_SHARE)
            ->setAllowedType(OptionType::TYPE_STRING)
            ->setRequired()
            ->addValidator(
                new AllowedEnumCaseValidator(Share::cases()),
            );

        $this->optionsResolver->defineLayout(SharePageLayout::class);
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Slogan;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class SloganComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly SloganLayout $layout,
        public readonly string $translationId,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'slogan';
    }

    public function getRenderer(): string
    {
        return SloganRenderer::class;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Slogan;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum SloganLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';
    case COMPACT = 'compact';
    case SEEKWEB = 'seekweb';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/Slogan/layout/default/slogan-default.html.twig',
            self::COMPACT => '@component/Generic/Slogan/layout/compact/slogan-compact.html.twig',
            self::SEEKWEB => '@component/Generic/Slogan/layout/seekweb/slogan-seekweb.html.twig',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\Slogan;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use App\Translation\TranslationIdProvider;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;

final class SloganResolver extends AbstractSpaceResolver
{
    public const string KEY_TRANSLATION_ID = 'translation_id';

    public function __construct(
        ComponentOptionsResolverInterface $optionsResolver,
        private readonly TranslationIdProvider $translatorIdProvider
    )
    {
        parent::__construct($optionsResolver);
    }

    public static function getSupportedComponent(): string
    {
        return SloganComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(SloganLayout::class);

        $this->optionsResolver->define(self::KEY_TRANSLATION_ID)
            ->setAllowedType(OptionType::TYPE_STRING)
            ->setDefaultValue('slogan.search_and_combine_all_search_engines')
            ->addValidator(
                new AllowedValueValidator(
                    $this->translatorIdProvider->getIds(),
                ),
            );
    }
}

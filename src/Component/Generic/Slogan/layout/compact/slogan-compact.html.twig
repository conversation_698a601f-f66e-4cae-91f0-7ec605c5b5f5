{# @var brand_name string #}
{# @var component_space_modifiers string[] #}
{# @var layout string #}
{# @var translation_id string #}
{{ component_style('sloganCompact') }}
{% set component_class = 'slogan' %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}">
    <div class="{{ component_class }}__text">
        {{ translation_id|trans({"%brand%": brand_name}) }}
    </div>
</div>

<?php

declare(strict_types=1);

namespace App\Component\Generic\SplitTestMatches;

use App\Component\Generic\AbstractCondition\AbstractConditionComponent;
use App\JsonTemplate\Component\ComponentInterface;

final class SplitTestMatchesComponent extends AbstractConditionComponent
{
    /**
     * @param string[]             $oneOfVariants
     * @param ComponentInterface[] $matchingChildren
     * @param ComponentInterface[] $nonMatchingChildren
     */
    public function __construct(
        public readonly array $oneOfVariants,
        array $matchingChildren,
        array $nonMatchingChildren
    )
    {
        parent::__construct($matchingChildren, $nonMatchingChildren);
    }

    public static function getType(): string
    {
        return 'split_test_matches';
    }

    public function getRenderer(): string
    {
        return SplitTestMatchesRenderer::class;
    }
}

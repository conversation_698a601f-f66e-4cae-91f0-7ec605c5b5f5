<?php

declare(strict_types=1);

namespace App\Component\Generic\SplitTestMatches;

use App\Component\Generic\AbstractCondition\AbstractConditionRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ScalarValueCondition;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\SegmentInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;

final class SplitTestMatchesRenderer extends AbstractConditionRenderer
{
    public function __construct(
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader
    )
    {
    }

    protected function getActiveSegment(ComponentInterface $component): SegmentInterface
    {
        if (!$component instanceof SplitTestMatchesComponent) {
            throw UnsupportedComponentException::create($component, [SplitTestMatchesComponent::class]);
        }

        // determine which segment to render
        $variantIsActive = in_array(
            $this->splitTestExtendedReader->getVariant(),
            $component->oneOfVariants,
            true,
        );

        return $variantIsActive
            ? $component->getMatchingSegment()
            : $component->getNonMatchingSegment();
    }

    protected function getConditions(
        ComponentInterface $component,
        bool $expectedResult
    ): ViewDataConditionCollection
    {
        if (!$component instanceof SplitTestMatchesComponent) {
            throw UnsupportedComponentException::create($component, [SplitTestMatchesComponent::class]);
        }

        $variantIsActive = in_array(
            $this->splitTestExtendedReader->getVariant(),
            $component->oneOfVariants,
            true,
        );

        return new ViewDataConditionCollection(
            [
                new ScalarValueCondition(
                    value         : $this->splitTestExtendedReader->getVariant(),
                    expectedValue : $variantIsActive,
                    expectedResult: $expectedResult,
                ),
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\StartPage;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum StartPageLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/StartPage/layout/default/start_page-default.html.twig',
        };
    }
}

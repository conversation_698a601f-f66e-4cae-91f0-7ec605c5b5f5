<?php

declare(strict_types=1);

namespace App\Component\Generic\StartPage;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

class StartPageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof StartPageComponent) {
            throw UnsupportedComponentException::create($component, [StartPageComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout' => $component->layout->value,
            ],
        );
    }
}

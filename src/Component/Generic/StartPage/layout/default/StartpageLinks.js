function StartpageLinks() {
    return {
        _blockClass: 'start-page-links',
        _startpageLinksElement: null,
        _blockNavigationElement: null,
        _blockSelectElement: null,
        _backToMenuHtml: null,
        _foldedBlocksCookieName: 'r',
        browserSupported: function () {
            return document.querySelectorAll && document.querySelector;
        },
        create: function () {
            if (!this.browserSupported()) {
                return null;
            }

            this._startpageLinksElement = document.querySelector(this._getBemClassSelector());
            this._blockNavigationElement = this._startpageLinksElement.querySelector(this._getBemClassSelector('block-navigation'));
            this._blockSelectElement = this._startpageLinksElement.querySelector(this._getBemClassSelector('block-select'));
            this._backToMenuHtml = '<div class="' + this._blockClass + '__back-to-menu">' +
                '<a href="#" class="' + this._blockClass + '__back-to-menu-link">Terug naar het keuzemenu</a>' +
                '</div>';

            this._attachEvents();
            this._applyFoldedBlocksCookie();

            // Make the startpage visible
            ClassList.add(this._startpageLinksElement, this._getBemClass(null, 'visible'));

            return this;
        },
        _attachEvents: function () {
            var self = this;

            Helper.addEvent('change', this._blockSelectElement, function () {
                self._focusBlock(self._blockSelectElement.value);
            });

            Helper.addEvent('click', this._startpageLinksElement, function (event) {
                var targetElement = Helper.eventTarget(event);
                var blockElement = targetElement.closest('.' + self._getBemClass('block'));

                if (blockElement === null) {
                    return;
                }

                var caretElement = targetElement.closest('.' + self._getBemClass('caret'));

                // Caret click
                if (caretElement !== null) {
                    self._unfocusAllBlocks();
                    self._foldBlock(blockElement);
                    self._updateFoldedBlocksCookie();

                    return;
                }

                var backToMenuLinkElement = targetElement.closest('.' + self._getBemClass('back-to-menu-link'));

                // Back to menu click
                if (backToMenuLinkElement !== null) {
                    Helper.eventPreventDefault(event);

                    self._unfocusAllBlocks();
                    self._blockSelectElement.focus();
                    self._blockNavigationElement.scrollIntoView();
                }
            });
        },
        _focusBlock: function (blockId) {
            this._unfocusAllBlocks();

            if (blockId !== '') {
                var blockElement = document.getElementById(blockId);

                if (blockElement) {
                    // Force block to be unfolded
                    this._foldBlock(blockElement, false);
                    this._updateFoldedBlocksCookie();

                    var backToMenuElement = blockElement.querySelector(this._getBemClassSelector('back-to-menu'));

                    if (backToMenuElement === null) {
                        blockElement.insertAdjacentHTML('beforeend', this._backToMenuHtml);
                    }

                    ClassList.add(blockElement, this._getBemClass('block', 'focus'));
                }
            }

            // An empty hash will still cause a hashtag in the URL.
            // The only solution would be using the History API.
            document.location.hash = blockId;
        },
        _unfocusAllBlocks: function () {
            var self = this;

            self._blockSelectElement.value = '';

            // Should be one, but iterate all to be sure
            Helper.iterateHtmlElements(
                this._getBemClassSelector('block', 'focus'), function (blockElement) {
                    ClassList.remove(blockElement, self._getBemClass('block', 'focus'));
                }
            );
        },
        _foldBlock: function (blockElement, add) {
            ClassList.toggle(blockElement, this._getBemClass('block', 'folded'), add);
        },
        _updateFoldedBlocksCookie: function () {
            var foldedBlockIds = [];

            Helper.iterateHtmlElements(this._getBemClassSelector('block', 'folded'), function (blockElement) {
                // Remove prefix `r` from ID
                foldedBlockIds.push(blockElement.id.slice(1));
            });

            CookieHelper.setCookie(this._foldedBlocksCookieName, foldedBlockIds.length > 0 ? foldedBlockIds.join(';') : null);
        },
        _applyFoldedBlocksCookie: function () {
            var self = this;
            var cookieValue = CookieHelper.getCookie(this._foldedBlocksCookieName);

            if (cookieValue === null) {
                return;
            }

            // Legacy support: value starts with ;
            if (cookieValue[0] === ';') {
                cookieValue = cookieValue.slice(1);
            }

            Helper.iterateArray(cookieValue.split(';'), function (blockId) {
                var blockElement = document.getElementById('r' + blockId);

                if (blockElement) {
                    self._foldBlock(blockElement, true);
                }
            });
        },
        _getBemClass: function (elementClass, modifierClass) {
            return Helper.getBemClass(this._blockClass, elementClass, modifierClass);
        },
        _getBemClassSelector: function (elementClass, modifierClass) {
            return Helper.getBemClassSelector(this._blockClass, elementClass, modifierClass);
        }
    };
}

function StartpageNews() {
    return {
        _blockClass: 'start-page-news',
        _rubricLickClass: 'rubrics-item-link',
        _rubricContainerClass: 'rubric',
        _rubricLinkElements: null,
        _rubricContainerElements: null,
        _activeRubricLinkElement: null,
        _activeRubricContainerElement: null,
        browserSupported: function () {
            return document.querySelectorAll && document.querySelector;
        },
        create: function () {
            if (!this.browserSupported()) {
                return null;
            }

            this._rubricLinkElements = document.querySelectorAll(Helper.getBemClassSelector(this._blockClass, this._rubricLickClass));
            this._rubricContainerElements = document.querySelectorAll(Helper.getBemClassSelector(this._blockClass, this._rubricContainerClass));

            if (this._rubricLinkElements.length === 0 || this._rubricContainerElements.length === 0) {
                return null;
            }

            this._activeRubricLinkElement = this._rubricLinkElements[0];
            this._activeRubricContainerElement = this._rubricContainerElements[0];

            this._attachEvents();
            this._init();

            return this;
        },
        _init: function () {
            ClassList.add(this._activeRubricLinkElement, Helper.getBemClass(this._blockClass, this._rubricLickClass, 'active'));

            ClassList.remove(this._activeRubricContainerElement, Helper.getBemClass(this._blockClass, this._rubricContainerClass, 'hide'));
            ClassList.add(this._activeRubricContainerElement, Helper.getBemClass(this._blockClass, this._rubricContainerClass, 'active'));
        },
        _attachEvents: function () {
            var self = this;

            for (var i = 0; i < this._rubricLinkElements.length; i++) {
                Helper.addEvent('click', this._rubricLinkElements[i], function (event) {
                    Helper.eventPreventDefault(event);

                    var rubricLinkElement = Helper.eventTarget(event);

                    if (rubricLinkElement === self._activeRubricLinkElement) {
                        return;
                    }

                    var rubric = rubricLinkElement.getAttribute('data-rubric');

                    ClassList.remove(self._activeRubricLinkElement, Helper.getBemClass(self._blockClass, self._rubricLickClass, 'active'));
                    self._activeRubricLinkElement = rubricLinkElement;
                    ClassList.add(self._activeRubricLinkElement, Helper.getBemClass(self._blockClass, self._rubricLickClass, 'active'));

                    ClassList.remove(self._activeRubricContainerElement, Helper.getBemClass(self._blockClass, self._rubricContainerClass, 'active'));
                    ClassList.add(self._activeRubricContainerElement, Helper.getBemClass(self._blockClass, self._rubricContainerClass, 'hide'));

                    self._activeRubricContainerElement = document.querySelector(Helper.getBemClassSelector(self._blockClass, self._rubricContainerClass, rubric));
                    ClassList.remove(self._activeRubricContainerElement, Helper.getBemClass(self._blockClass, self._rubricContainerClass, 'hide'));
                    ClassList.add(self._activeRubricContainerElement, Helper.getBemClass(self._blockClass, self._rubricContainerClass, 'active'));
                });
            }
        }
    };
}

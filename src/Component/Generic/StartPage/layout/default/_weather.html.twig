{# @var string component_class #}
<div class="{{ component_class }}-weather">
    <div class="{{ component_class }}-weather__container">
        <div class="{{ component_class }}-weather__image">
            <img class="{{ component_class }}-weather__image {{ component_class }}-weather__image--small" src="{{ path('route_startpage_weather_image') }}" alt="Kaart">
            <img class="{{ component_class }}-weather__image {{ component_class }}-weather__image--large {{ component_class }}-weather__image--hidden" src="{{ path('route_startpage_weather_image') }}" alt="Het Weer">
        </div>
        <p class="{{ component_class }}-weather__title">{{ weather_info_text() }}</p>
        <p class="{{ component_class }}-weather__links">
            <a href="https://www.knmi.nl/nederland-nu/weer/waarnemingen" target="_blank" rel="nofollow noopener noreferrer" class="{{ component_class }}-weather__link">Actueel</a> -
            <a href="http://www.knmi.nl/waarschuwingen_en_verwachtingen/" target="_blank" rel="nofollow noopener noreferrer" class="{{ component_class }}-weather__link">Vooruitzichten</a><br/>
            <a href="http://www.buienradar.nl/" target="_blank" rel="nofollow noopener noreferrer" class="{{ component_class }}-weather__link">Buienradar</a>
        </p>
    </div>
</div>

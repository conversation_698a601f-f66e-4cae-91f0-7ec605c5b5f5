<?php

declare(strict_types=1);

namespace App\Component\Generic\StartPageFavorites;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum StartPageFavoritesLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Generic/StartPageFavorites/layout/default/start_page_favorites-default.html.twig',
        };
    }
}

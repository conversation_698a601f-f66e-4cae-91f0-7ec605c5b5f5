<?php

declare(strict_types=1);

namespace App\Component\Generic\StartPageFavorites;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;

class StartpageFavoritesHelper
{
    public const string KEY_NAME = 'name';
    public const string KEY_URL  = 'url';

    private const string COOKIE_TIME_TO_LIVE = '+10 years';
    private const string COOKIE_NAME         = 'favo0';

    private const string DELIMITER_FAVORITES_LEGACY = '¶¶';
    private const string DELIMITER_FAVORITES        = '{0}';
    private const string DELIMITER_FAVORITE         = '  ';

    private readonly ?Request $request;

    /** @var array<array<string, string>>|null */
    private ?array $decodedValues = null;

    public function __construct(RequestStack $requestStack, private readonly LoggerInterface $logger)
    {
        $this->request = $requestStack->getMainRequest();
    }

    /**
     * @return array<array<string, string>>
     */
    public function getFavorites(): array
    {
        return $this->getDecodedCookieValues();
    }

    /**
     * @return array<array<string, string>>|null
     */
    public function getCookieValues(): ?array
    {
        return $this->getDecodedCookieValues() !== []
            ? $this->getDecodedCookieValues()
            : null;
    }

    /**
     * @param array<array<string, string>> $values
     */
    public function setCookieValues(Response $response, array $values): void
    {
        if ($this->request === null) {
            return;
        }

        try {
            $cookieValue = $this->getEncodedCookieValues($values);

            $cookie = Cookie::create(
                self::COOKIE_NAME,
                $cookieValue,
                new \DateTime(self::COOKIE_TIME_TO_LIVE, new \DateTimeZone('UTC')),
                '/',
                $this->request->getHost(),
                true,
                false,
            );

            $response->headers->setCookie($cookie);
        } catch (\Throwable $exception) {
            $this->logger->notice(
                sprintf('Caught %s while setting favorites cookie: {message}', $exception::class),
                [
                    'exception'     => $exception,
                    'message'       => $exception->getMessage(),
                    'cookie_values' => $values,
                ],
            );
        }
    }

    /**
     * @param array<array<string, string>> $values
     */
    private function getEncodedCookieValues(array $values): ?string
    {
        if ($values === []) {
            return null;
        }

        $favorites = [];

        foreach ($values as $value) {
            $favorites[] = implode(
                self::DELIMITER_FAVORITE,
                [
                    $value[self::KEY_URL],
                    $value[self::KEY_NAME],
                ],
            );
        }

        return implode(self::DELIMITER_FAVORITES, $favorites);
    }

    /**
     * @return array<array<string, string>>
     */
    private function getDecodedCookieValues(): array
    {
        if ($this->decodedValues === null) {
            $this->decodedValues = [];

            if ($this->request === null) {
                return $this->decodedValues;
            }

            $cookieValues = $this->request->cookies->getString(self::COOKIE_NAME);

            if ($cookieValues === '') {
                return $this->decodedValues;
            }

            $this->decodedValues = $this->decodeCookieValues($cookieValues);
        }

        return $this->decodedValues;
    }

    /**
     * @return array<array<string, string>>
     */
    private function decodeCookieValues(string $cookieValues): array
    {
        try {
            $favorites = [];

            $delimiter = str_contains($cookieValues, self::DELIMITER_FAVORITES_LEGACY)
                ? self::DELIMITER_FAVORITES_LEGACY : self::DELIMITER_FAVORITES;
            $entries = explode($delimiter, $cookieValues);

            foreach ($entries as $entry) {
                $favoriteValues = explode(self::DELIMITER_FAVORITE, $entry, 2);

                if (count($favoriteValues) !== 2) {
                    continue;
                }

                $favorites[] = [
                    self::KEY_URL  => $favoriteValues[0],
                    self::KEY_NAME => $favoriteValues[1],
                ];
            }

            return $favorites;
        } catch (\Throwable $exception) {
            $this->logger->notice(
                sprintf('Caught %s while getting favorites cookie: {message}', $exception::class),
                [
                    'exception'     => $exception,
                    'message'       => $exception->getMessage(),
                    'cookie_values' => $cookieValues,
                ],
            );
        }

        return [];
    }
}

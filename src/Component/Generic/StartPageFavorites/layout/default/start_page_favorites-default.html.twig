{# @var query string #}
{# @var layout string #}
{% form_theme form '@shared/templates/form/component.html.twig' %}
{{ component_style(['startPageFavoritesDefault']) }}
{% set component_class = 'start-page-favorites' %}
{% set label_replace = {'%tag_open%': '<b>', '%tag_close%': '</b>'} %}
{% set form_fields = ['name', 'url'] %}

<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    <a href="{{ url('route_home') }}">Terug naar de startpagina</a><br/>
    <br/>

    <div class="{{ component_class }}__subtitle">Wijzigen</div>

    <div class="{{ component_class }}__favorites">
        {% for favorite in favorites %}
            <div class="{{ component_class }}__favorites-row">
                <div class="{{ component_class }}__favorites-cell">
                    {{ favorite.name }} ({{ favorite.url }})
                </div>
                <div class="{{ component_class }}__favorites-cell">
                    <a href="{{ url('route_startpage_favorites_edit', {index: loop.index0}) }}" rel="nofollow">wijzigen</a>
                    | <a href="{{ url('route_startpage_favorites_delete', {index: loop.index0}) }}" rel="nofollow">verwijderen</a>
                </div>
            </div>
        {% endfor %}
    </div>

    <div class="{{ component_class }}__subtitle">Toevoegen</div>

    {{ form_start(form, {attr: {novalidate: 'novalidate', class: component_class ~ '__form'}}) }}

    <div class="{{ component_class }}__fields">
        {% for form_field in form_fields %}
            <div class="{{ component_class }}__field">
                <div class="{{ component_class }}__cell">
                    {{ form_label(
                        form[form_field],
                        null,
                        {
                            component_class: component_class,
                            translation_domain: false
                        }
                    )|replace(label_replace)|raw }}
                </div>
                <div class="{{ component_class }}__cell {{ component_class }}__cell--widget">
                    {{ form_widget(
                        form[form_field],
                        {
                            component_class: component_class,
                            attr: { class: component_class ~ '__input' }
                        }
                    ) }}
                    {{ form_errors(form[form_field]) }}
                </div>
            </div>
        {% endfor %}
    </div>

    <div class="{{ component_class }}__buttons">
        {{ form_widget(
            form.submit,
            {
                component_class: component_class,
                translation_domain: false
            }
        ) }}
        <br/><br>
        <a href="{{ url('route_home') }}">Terug naar de startpagina</a>
    </div>

    {{ form_end(form) }}
</div>
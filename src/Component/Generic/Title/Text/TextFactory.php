<?php

declare(strict_types=1);

namespace App\Component\Generic\Title\Text;

use App\Brand\Settings\BrandSettingsHelper;
use App\Search\Request\SearchRequestInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final readonly class TextFactory
{
    public function __construct(
        private TranslatorInterface $translator,
        private SearchRequestInterface $searchRequest,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function create(
        ?string $text,
        ?string $textHighlight,
        ?string $textTranslationId,
        ?string $textHighlightTranslationId
    ): Text
    {
        $text = $this->getValue($text, $textTranslationId, true);
        $highlight = $this->getValue($textHighlight, $textHighlightTranslationId, false);

        return new Text(
            text     : (string)$text,
            highlight: $highlight,
        );
    }

    private function getValue(?string $text, ?string $textTranslationId, bool $required): ?string
    {
        $queryTitleCase = mb_convert_case(
            $this->searchRequest->getQueryAsString(),
            MB_CASE_TITLE,
            'UTF-8',
        );

        $text = $this->trimValue($text);
        $text ??= $textTranslationId !== null
            ? $this->translator->trans($textTranslationId, ['%query%' => $queryTitleCase])
            : null;

        if ($text !== null) {
            $text = str_replace(
                [
                    '%brand%',
                    '%query%',
                ],
                [
                    $this->brandSettingsHelper->getSettings()->getName(),
                    $queryTitleCase,
                ],
                $text,
            );
        }

        if ($required && $text === null) {
            throw CreateTitleTextFailedException::create('Text is missing');
        }

        return $text;
    }

    private function trimValue(?string $value): ?string
    {
        if ($value === null) {
            return null;
        }

        $value = trim($value);

        if ($value !== '') {
            return $value;
        }

        return null;
    }
}

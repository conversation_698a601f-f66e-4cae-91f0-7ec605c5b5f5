/** @define title */
.title--content-3 {
    .title {
        &__container {
            border-bottom: 0.3rem solid var(--brand-primary-color);
            padding-bottom: 1.6rem;
        }

        &__title {
            color: var(--container__section_color, #101828);
            font-size: 2.4rem;
            font-weight: 600;
            line-height: 3rem;
        }

        &__title-highlighted {
            color: var(--brand-primary-color);
        }
    }

    @media #{map-get($media-max, b)} {
        .title {
            &__container {
                padding-bottom: 1.2rem;
            }
        }
    }
}

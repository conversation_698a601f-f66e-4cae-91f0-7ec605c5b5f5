{# @var title App\Component\Generic\Title\Text #}
{# @var subtitle App\Component\Generic\Title\Text|null #}
{# @var layout string #}
{# @var component_space_modifiers string[] #}
{{ component_style(['titleContent4']) }}
{% set component_class = 'title' %}
{% set title_html_element = title_html_element|default('span') %}
<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__container">
        <{{ title_html_element }} class="{{ component_class }}__title">{{ title.text|highlight_with_class(title.highlight, component_class~"__title-highlighted") }}</{{ title_html_element }}>
</div>
</div>

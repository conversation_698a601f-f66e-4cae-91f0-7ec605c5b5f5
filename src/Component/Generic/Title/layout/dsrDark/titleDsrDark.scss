/** @define title */
// stylelint-disable-next-line selector-class-pattern
.title--dsr-dark {
    .title {
        &__title {
            color: #f5f5f6;
            font-size: 2.4rem;
            font-weight: 600;
            letter-spacing: -0.048rem;
            line-height: 3rem;
        }

        &__subtitle {
            color: #f5f5f6;
            font-size: 2.4rem;
            line-height: 3rem;

            @media #{map-get($media-max, a)} {
                color: #94969c;
                display: block;
                font-size: 1.4rem;
                font-weight: 400;
                letter-spacing: normal;
                line-height: 2rem;
            }
        }
    }
}

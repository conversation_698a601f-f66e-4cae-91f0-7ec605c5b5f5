<?php

declare(strict_types=1);

namespace App\Component\Generic\UnexpectedErrorMessage;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class UnexpectedErrorMessageRenderer extends AbstractComponentRenderer
{
    public function __construct(private readonly TranslatorInterface $translator, private readonly Environment $twig)
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof UnexpectedErrorMessageComponent) {
            throw UnsupportedComponentException::create($component, [UnexpectedErrorMessageComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'title'   => $this->translator->trans('error_page.title'),
                'message' => $this->translator->trans('error_page.message'),
                'layout'  => $component->layout->value,
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Generic\UnexpectedErrorMessage;

use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;

readonly class UnexpectedErrorMessageResolver implements ComponentResolverInterface
{
    public function __construct(
        private ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return UnexpectedErrorMessageComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(UnexpectedErrorMessageLayout::class);

        return $this->optionsResolver->resolve($options);
    }
}

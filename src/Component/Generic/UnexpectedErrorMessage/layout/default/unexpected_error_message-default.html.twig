{# @var title string #}
{# @var message string #}
{{ component_style(['unexpectedErrorMessageDefault']) }}

{% set component_class = 'unexpected-error-message' %}

{# Delayed container attributes is not used by intention #}
<div class="{{ component_class(component_class, [layout]) }}">
    <div class="{{ component_class }}-container">
        <h4 class="{{ component_class }}__title">{{ title }}</h4>
        <p class="{{ component_class }}__message">{{ message }}</p>
    </div>
</div>

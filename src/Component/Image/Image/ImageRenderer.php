<?php

declare(strict_types=1);

namespace App\Component\Image\Image;

use App\Assets\AssetsHelper;
use App\BrandAssets\File\BrandAssetsImageFileName;
use App\BrandAssets\Settings\BrandAssetsSettings;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class ImageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly BrandAssetsSettings $brandAssetsSettings,
        private readonly AssetsHelper $assetsHelper
    )
    {
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ImageComponent) {
            throw UnsupportedComponentException::create($component, [ImageComponent::class]);
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => array_merge($component->componentSpaceModifiers, ['no-default-space']),
                'image'                     => $this->getRelativeImageFilePath($component),
            ],
        );
    }

    private function getRelativeImageFilePath(ImageComponent $component): string
    {
        if ($component->image === ImageType::HEADER_DARK_JPG) {
            $hasHeaderJpg = $this->brandAssetsSettings->hasImage(BrandAssetsImageFileName::HEADER_JPG);

            if ($hasHeaderJpg) {
                return $this->assetsHelper->getRelativeBrandImageFilePath(
                    sprintf('/image/%s', BrandAssetsImageFileName::HEADER_JPG->value),
                );
            }
        }

        return $this->assetsHelper->getRelativeSharedImageFilePath(
            sprintf('/header/%s', $component->image->value),
        );
    }
}

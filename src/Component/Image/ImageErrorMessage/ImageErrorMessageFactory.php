<?php

declare(strict_types=1);

namespace App\Component\Image\ImageErrorMessage;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

class ImageErrorMessageFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ImageErrorMessageComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ImageErrorMessageComponent();
    }
}

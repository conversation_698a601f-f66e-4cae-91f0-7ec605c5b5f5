<?php

declare(strict_types=1);

namespace App\Component\Image\ImageFilters;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

class ImageFiltersFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ImageFiltersComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ImageFiltersComponent();
    }
}

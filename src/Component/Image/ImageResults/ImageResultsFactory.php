<?php

declare(strict_types=1);

namespace App\Component\Image\ImageResults;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class ImageResultsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return ImageResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new ImageResultsComponent(
            layout: ImageResultsLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Image\ImageResults;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\LinkTypeOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class ImageResultsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::IMAGE_RESULTS,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $imageViewDataRequest = $request->image()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::IMAGE_RESULTS,
            searchApiViewDataRequest: $imageViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ImageResultsComponent) {
            throw UnsupportedComponentException::create($component, [ImageResultsComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $imageResults = $viewDataRegistry->getImageResults($component)->getResults();

        if ($imageResults === []) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'           => $component->layout->value,
                'image_results'    => $imageResults,
                'link_type_option' => LinkTypeOption::DEFAULT_TARGET_BLANK,
            ],
        );
    }
}

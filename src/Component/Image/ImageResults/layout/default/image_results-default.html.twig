{# @var image_results Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Image\Response\ImageResult[] #}
{# @var layout string #}
{# @var link_type_option string #}
{{ component_style(['imageResultsDefault']) }}
{% set component_class = 'image-results' %}
<div class="{{ component_class(component_class, [layout]) }}"{{ delayed_container_attributes() }}>
    <ul class="{{ component_class }}__items">
        {% for image_result in image_results %}
            <li class="{{ component_class }}__item">
                <img loading="lazy" class="{{ component_class }}__image" src="{{ image_result.imageUrl }}" alt="{{ image_result.title }}"/>
                <div class="{{ component_class }}__content {{ component_class }}--hidden">
                    <a href="{{ image_result.url }}" rel="nofollow noopener noreferrer" {{ link_type_option_attributes(link_type_option) }}>
                        <span class="{{ component_class }}__link">{{ image_result.url }}</span>
                        <div class="{{ component_class }}__title">{{ image_result.title }}</div>
                    </a>
                </div>
            </li>
        {% endfor %}
        <li class="{{ component_class }}__item"></li> {# To prevent image stretching on the last row #}
    </ul>
</div>

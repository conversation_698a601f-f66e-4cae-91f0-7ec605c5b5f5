<?php

declare(strict_types=1);

namespace App\Component\News\NewsFilters;

use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;

readonly class NewsFiltersResolver implements ComponentResolverInterface
{
    public function __construct(private OptionsResolverInterface $optionsResolver)
    {
    }

    public static function getSupportedComponent(): string
    {
        return NewsFiltersComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        return $this->optionsResolver->resolve($options);
    }
}

<?php

declare(strict_types=1);

namespace App\Component\News\NewsResults;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class NewsResultsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return NewsResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new NewsResultsComponent(
            layout: NewsResultsLayout::from($options[LayoutInterface::KEY]),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Component\News\NewsResults;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum NewsResultsLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/News/NewsResults/layout/default/news_results-default.html.twig',
        };
    }
}

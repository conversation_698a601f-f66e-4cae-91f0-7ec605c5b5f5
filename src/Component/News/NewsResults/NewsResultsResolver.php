<?php

declare(strict_types=1);

namespace App\Component\News\NewsResults;

use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;

final readonly class NewsResultsResolver implements ComponentResolverInterface
{
    public function __construct(
        private ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return NewsResultsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(NewsResultsLayout::class);

        return $this->optionsResolver->resolve($options);
    }
}

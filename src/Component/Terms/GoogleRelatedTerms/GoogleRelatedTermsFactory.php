<?php

declare(strict_types=1);

namespace App\Component\Terms\GoogleRelatedTerms;

use App\Component\Terms\RelatedTerms\RelatedTermsComponent;
use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\GoogleCsa\Generator\GoogleRelatedTermsContainerGeneratorInterface;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

final readonly class GoogleRelatedTermsFactory implements ComponentFactoryInterface
{
    public function __construct(
        private GoogleRelatedTermsContainerGeneratorInterface $googleRelatedTermsContainerGenerator,
        private DisplaySearchRelatedSettings $displaySearchRelatedSettings
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return GoogleRelatedTermsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $containerSuffix = $options[GoogleRelatedTermsResolver::KEY_CONTAINER_SUFFIX];
        $container = $this->googleRelatedTermsContainerGenerator->generateContainer($containerSuffix);

        /** @var RelatedTermsComponent|null $fallbackRelatedTerms */
        $fallbackRelatedTerms = $componentFactory->createNullableFromOptions(
            $options[GoogleRelatedTermsResolver::KEY_FALLBACK_RELATED_TERMS] ?? null,
        );

        if ($this->displaySearchRelatedSettings->relatedFallbackEnabled) {
            $fallbackRelatedTerms?->setFallbackComponentClass(sprintf('fallback-%s', $container));
        }

        return new GoogleRelatedTermsComponent(
            amount                  : $options[GoogleRelatedTermsResolver::KEY_AMOUNT],
            route                   : $options[GoogleRelatedTermsResolver::KEY_ROUTE],
            target                  : GoogleRelatedTermsTarget::from($options[GoogleRelatedTermsResolver::KEY_TARGET]),
            termsUrlParameterEnabled: $options[GoogleRelatedTermsResolver::KEY_TERMS_URL_PARAMETER_ENABLED],
            container               : $container,
            fallbackRelatedTerms    : $fallbackRelatedTerms,
        );
    }
}

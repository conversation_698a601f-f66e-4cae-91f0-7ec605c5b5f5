<?php

declare(strict_types=1);

namespace App\Component\Terms\HasRelatedTerms;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionComponent;
use App\JsonTemplate\Component\ComponentInterface;

final class HasRelatedTermsComponent extends AbstractSearchApiConditionComponent
{
    /**
     * @param ComponentInterface[] $matchingChildren
     * @param ComponentInterface[] $nonMatchingChildren
     */
    public function __construct(
        array $matchingChildren,
        array $nonMatchingChildren
    )
    {
        parent::__construct($matchingChildren, $nonMatchingChildren);
    }

    public static function getType(): string
    {
        return 'has_related_terms';
    }

    public function getRenderer(): string
    {
        return HasRelatedTermsRenderer::class;
    }
}

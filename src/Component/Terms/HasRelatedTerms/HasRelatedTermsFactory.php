<?php

declare(strict_types=1);

namespace App\Component\Terms\HasRelatedTerms;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;

final class HasRelatedTermsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return HasRelatedTermsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $matchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_YES],
        );
        $nonMatchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_NO],
        );

        return new HasRelatedTermsComponent(
            matchingChildren   : $matchingChildren,
            nonMatchingChildren: $nonMatchingChildren,
        );
    }
}

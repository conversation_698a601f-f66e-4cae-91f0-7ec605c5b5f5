<?php

declare(strict_types=1);

namespace App\Component\Terms\PillRelatedTerms;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class PillRelatedTermsComponent extends AbstractSpaceComponent
{
    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly PillRelatedTermsLayout $layout,
        public readonly int $amount,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public static function getType(): string
    {
        return 'pill_related_terms';
    }

    public function getRenderer(): string
    {
        return PillRelatedTermsRenderer::class;
    }
}

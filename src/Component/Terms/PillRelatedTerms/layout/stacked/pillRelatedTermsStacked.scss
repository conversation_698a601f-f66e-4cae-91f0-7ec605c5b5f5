/** @define pill-related-terms */
.pill-related-terms--stacked {
    margin-bottom: 1rem;

    .pill-related-terms {
        &__terms {
            display: flex;
            flex-flow: row wrap;
            gap: 0.5rem;
            height: 3.5rem;
            overflow: hidden;
            white-space: nowrap;
        }

        &__link {
            background-color: #ffffff;
            border-radius: 3rem;
            box-shadow: inset 0 0 0 0.1rem #d2d2d2;
            color: #505058;
            cursor: pointer;
            display: flex;
            padding: 1rem 1.5rem;
        }

        &__item {
            &--last {
                z-index: 1;
            }

            &--selected {
                /* stylelint-disable-next-line selector-class-pattern */
                .pill-related-terms__link {
                    background-color: #a3c9ff;
                    gap: 0.5rem;

                    /* stylelint-disable-next-line max-nesting-depth */
                    &::before {
                        content: $vsi-clear;
                    }
                }

                /* stylelint-disable-next-line selector-class-pattern */
                &:not(.pill-related-terms__item--last) {
                    position: relative;

                    /* stylelint-disable-next-line selector-class-pattern */
                    .pill-related-terms__link {
                        display: none;
                    }

                    &::before {
                        align-items: center;
                        background-color: #a3c9ff;
                        border-radius: 2rem 0 0 2rem;
                        box-shadow: inset 0 0 0 0.1rem #d2d2d2;
                        content: "";
                        display: flex;
                        height: 3.5rem;
                        justify-content: flex-start;
                        margin-right: -1.8rem;
                        width: 2.4rem;
                    }

                    &::after {
                        align-items: center;
                        background-color: #ffffff;
                        border-radius: 2rem 0 0 2rem;
                        content: "";
                        display: flex;
                        height: 3.5rem;
                        justify-content: flex-start;
                        position: absolute;
                        right: -2.6rem;
                        top: 0;
                        width: 2.4rem;
                    }
                }
            }
        }
    }
}

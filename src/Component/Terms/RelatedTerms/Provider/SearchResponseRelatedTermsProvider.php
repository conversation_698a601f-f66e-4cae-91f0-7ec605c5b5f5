<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms\Provider;

use App\Component\Terms\RelatedTerms\RelatedTermsComponent;
use App\Component\Terms\RelatedTerms\RelatedTermsProviderInterface;
use App\JsonTemplate\View\ViewInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\RelatedTerms\Response\RelatedTerm;

final readonly class SearchResponseRelatedTermsProvider implements RelatedTermsProviderInterface
{
    public static function getDefaultPriority(): int
    {
        return 0;
    }

    /**
     * @inheritDoc
     */
    public function getRelatedTerms(RelatedTermsComponent $component, ViewInterface $view): ?array
    {
        $relatedTerms = array_map(
            static fn (RelatedTerm $relatedTerm): string => $relatedTerm->getQuery(),
            $view->getDataRegistry()->getRelatedTerms($component)->getResults(),
        );

        return $relatedTerms !== [] ? $relatedTerms : null;
    }

    public function requiresSearchResponse(): ?bool
    {
        return true;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms;

use App\Component\Generic\AbstractSpace\AbstractSpaceComponent;

final class RelatedTermsComponent extends AbstractSpaceComponent
{
    protected ?string $fallbackComponentClass = null;

    /**
     * @param string[] $componentSpaceModifiers
     */
    public function __construct(
        public readonly RelatedTermsLayout $layout,
        public readonly int $amount,
        public readonly ?string $zone,
        public readonly ?string $route,
        public readonly int $columns,
        public readonly bool $showTitle,
        public readonly ?bool $keywordHighlight,
        public readonly bool $repeatTerms,
        array $componentSpaceModifiers
    )
    {
        parent::__construct($componentSpaceModifiers);
    }

    public function getFallbackComponentClass(): ?string
    {
        return $this->fallbackComponentClass;
    }

    public function setFallbackComponentClass(string $class): void
    {
        $this->fallbackComponentClass = $class;
    }

    public static function getType(): string
    {
        return 'related_terms';
    }

    public function getRenderer(): string
    {
        return RelatedTermsRenderer::class;
    }
}

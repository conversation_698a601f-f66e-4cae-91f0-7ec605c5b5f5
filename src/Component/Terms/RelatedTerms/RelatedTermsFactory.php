<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class RelatedTermsFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return RelatedTermsComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new RelatedTermsComponent(
            layout                 : RelatedTermsLayout::from($options[LayoutInterface::KEY]),
            amount                 : $options[RelatedTermsResolver::KEY_AMOUNT],
            zone                   : $options[RelatedTermsResolver::KEY_ZONE],
            route                  : $options[RelatedTermsResolver::KEY_ROUTE],
            columns                : $options[RelatedTermsResolver::KEY_COLUMNS],
            showTitle              : $options[RelatedTermsResolver::KEY_SHOW_TITLE],
            keywordHighlight       : $options[RelatedTermsResolver::KEY_KEYWORD_HIGHLIGHT],
            repeatTerms            : $options[RelatedTermsResolver::KEY_REPEAT_TERMS],
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

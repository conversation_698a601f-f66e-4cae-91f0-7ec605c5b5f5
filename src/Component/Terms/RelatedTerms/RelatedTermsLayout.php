<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum RelatedTermsLayout: string implements LayoutInterface
{
    case CHEVRON         = 'chevron';
    case COMPACT         = 'compact';
    case COMPACT_BLUE    = 'compact-blue';
    case COMPACT_LIGHT   = 'compact-light';
    case COMPACT_ZAPMETA = 'compact-zapmeta';
    case DEFAULT         = 'default';
    case FALLBACK        = 'fallback';
    case FALLBACK_BRAND  = 'fallback-brand';
    case FALLBACK_DARK   = 'fallback-dark';
    case PILL            = 'pill';
    case SEEKWEB         = 'seekweb';
    case VISYMO          = 'visymo';
    case ZAPMETA         = 'zapmeta';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::CHEVRON         => '@component/Terms/RelatedTerms/layout/chevron/related_terms-chevron.html.twig',
            self::COMPACT         => '@component/Terms/RelatedTerms/layout/compact/related_terms-compact.html.twig',
            self::COMPACT_BLUE    => '@component/Terms/RelatedTerms/layout/compactBlue/related_terms-compact_blue.html.twig',
            self::COMPACT_LIGHT   => '@component/Terms/RelatedTerms/layout/compactLight/related_terms-compact_light.html.twig',
            self::COMPACT_ZAPMETA => '@component/Terms/RelatedTerms/layout/compactZapmeta/related_terms-compact_zapmeta.html.twig',
            self::DEFAULT         => '@component/Terms/RelatedTerms/layout/default/related_terms-default.html.twig',
            self::FALLBACK        => '@component/Terms/RelatedTerms/layout/fallback/related_terms-fallback.html.twig',
            self::FALLBACK_BRAND  => '@component/Terms/RelatedTerms/layout/fallbackBrand/related_terms-fallback_brand.html.twig',
            self::FALLBACK_DARK   => '@component/Terms/RelatedTerms/layout/fallbackDark/related_terms-fallback_dark.html.twig',
            self::PILL            => '@component/Terms/RelatedTerms/layout/pill/related_terms-pill.html.twig',
            self::SEEKWEB         => '@component/Terms/RelatedTerms/layout/seekweb/related_terms-seekweb.html.twig',
            self::VISYMO          => '@component/Terms/RelatedTerms/layout/visymo/related_terms-visymo.html.twig',
            self::ZAPMETA         => '@component/Terms/RelatedTerms/layout/zapmeta/related_terms-zapmeta.html.twig',
        };
    }
}

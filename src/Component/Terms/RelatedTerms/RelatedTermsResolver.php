<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Generic\Validator\AllowedEnumCaseValidator;
use App\RelatedTerms\Request\RelatedTermsZone;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;
use Visymo\Shared\Domain\Validator\LesserThanOrEqualValidator;

final class RelatedTermsResolver extends AbstractSpaceResolver
{
    public const string KEY_AMOUNT            = 'amount';
    public const string KEY_ZONE              = 'zone';
    public const string KEY_ROUTE             = 'route';
    public const string KEY_COLUMNS           = 'columns';
    public const string KEY_SHOW_TITLE        = 'show_title';
    public const string KEY_KEYWORD_HIGHLIGHT = 'keyword_highlight';
    public const string KEY_REPEAT_TERMS      = 'repeat_terms';

    public static function getSupportedComponent(): string
    {
        return RelatedTermsComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::KEY_AMOUNT)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanValidator(0));

        $this->optionsResolver->define(self::KEY_ZONE)
            ->setDefaultValue(null)
            ->setAllowedType(OptionType::TYPE_STRING, true)
            ->addValidator(
                new AllowedEnumCaseValidator(RelatedTermsZone::cases()),
            );

        $this->optionsResolver->defineRoute(self::KEY_ROUTE);

        $this->optionsResolver->defineLayout(RelatedTermsLayout::class);

        $this->optionsResolver->define(self::KEY_COLUMNS)
            ->setDefaultValue(2)
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(1))
            ->addValidator(new LesserThanOrEqualValidator(20));

        $this->optionsResolver->define(self::KEY_SHOW_TITLE)
            ->setAllowedType(OptionType::TYPE_BOOLEAN)
            ->setDefaultValue(true);

        $this->optionsResolver->define(self::KEY_KEYWORD_HIGHLIGHT)
            ->setAllowedType(OptionType::TYPE_BOOLEAN, true)
            ->setDefaultValue(null);

        $this->optionsResolver->define(self::KEY_REPEAT_TERMS)
            ->setAllowedType(OptionType::TYPE_BOOLEAN)
            ->setDefaultValue(true);
    }
}

/** @define related-terms */
.related-terms--compact-light {
    margin-top: 0.5rem;

    .related-terms {
        &__title {
            color: #505058;
            font-size: 1.3rem;
            font-weight: 400;
            line-height: 1.5rem;
            margin-bottom: 1rem;
        }

        &__column {
            display: block;
        }

        &__link {
            color: var(--link_color, #2775bc);
            display: inline-block;
            font-size: var(--link_font-size, 1.3rem);
            line-height: var(--link_line-height, 2rem);
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    @media #{map-get($media-min, c)} {
        // stylelint-disable plugin/selector-bem-pattern
        .related-terms {
            &__columns--2 .related-terms {
                &__column {
                    display: inline-block;
                    padding-left: 1rem;
                    vertical-align: top;
                    width: 50%;

                    &:first-child {
                        padding-left: 0;
                        padding-right: 1rem;
                    }
                }
            }
        }

        // stylelint-enable plugin/selector-bem-pattern
    }

    @media #{map-get($media-max, c)} {
        margin: 1rem 0 1.8rem 0;

        .related-terms {
            &__link {
                color: var(--link_color, #2775bc);
                font-size: 1.6rem;
                line-height: var(--link_line-height, 2rem);
            }
        }
    }
}

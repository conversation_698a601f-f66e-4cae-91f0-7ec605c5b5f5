function RelatedTerms() {
    this.httpRequest = new HttpRequest('POST');
    this.httpRequest.url = vrtUrl;
    this.httpRequest.json = false;

    Helper.iterateHtmlElements('.related-terms .related-terms__link', function (link) {
        link.addEventListener(
            'click',
            this.handleClick.bind(this, link.getAttribute('title'))
        );
    }.bind(this));
}

RelatedTerms.prototype.handleClick = function (term) {
    var formData = new FormData();

    formData.append('term', term);

    navigator.sendBeacon
        ? navigator.sendBeacon(this.httpRequest.url, formData)
        : this.httpRequest.send(formData);
};

appReady.push(function () {
    new RelatedTerms();
});

@import "../../../../../../resources/shared/assets/scss/defaultVariables";
@import "relatedTermsFunctions";

/** @define related-terms */
.related-terms--default {
    margin: var(--margin, 1rem 0 1.8rem 0);

    .related-terms {
        &__title {
            color: #505058;
            font-size: var(--title_font-size, 1.4rem);
            font-weight: 400;
            line-height: var(--title_line-height, 1.5rem);
            margin-bottom: var(--title_margin-bottom, 1rem);

            @media #{map-get($media-max, c)} {
                --title_font-size: 1.3rem;
                --title_line-height: 1.8rem;
                --title_margin-bottom: 0.5rem;
            }
        }

        &__column {
            min-width: 19rem;
            padding: 0;
            width: auto;
        }

        &__item {
            border-bottom: var(--item_border-bottom, none);
            display: block;
            position: relative;
        }

        &__link {
            color: var(--link_color, #1900e1);
            display: inline-block;
            font-size: 1.6rem;
            line-height: 3.4rem;
            padding: var(--link_padding, none);
            text-decoration: none;

            &:hover {
                text-decoration: var(--link-hover_text-decoration, underline);
            }
        }

        @media #{map-get($media-min, c)} {
            &__columns--2 {
                display: grid;
                grid-gap: 3rem;
                grid-template-columns: repeat(2, minmax(19rem, max-content));
            }
        }
    }

    @media #{map-get($media-max, b)} {
        @include related-terms-default-with-lines;
        @include related-terms-clickable-block;

        --link_color: #1900e1;
    }

    @media #{map-get($media-max, c)} {
        --link_color: #1900e1;
    }
}

@import "../default/relatedTermsFunctions";

/** @define related-terms */
.related-terms--zapmeta {
    margin: var(--margin, 2rem 0 1.8rem 0);

    .related-terms {
        &__title {
            color: #505058;
            font-size: var(--title_font-size, 1.4rem);
            font-weight: 400;
            line-height: var(--title_line-height, 1.4rem);
            margin-bottom: var(--title_margin-bottom, 1rem);

            @media #{map-get($media-max, c)} {
                --title_font-size: 1.3rem;
                --title_line-height: 1.8rem;
                --title_margin-bottom: 0.5rem;
            }
        }

        &__column {
            min-width: 19rem;
            padding: 0;
            width: auto;
        }

        &__item {
            border-bottom: var(--item_border-bottom, none);
            display: block;
            position: relative;
        }

        &__link {
            color: var(--link_color, #1a0dab);
            display: inline-block;
            font-size: 1.6rem;
            line-height: 3.4rem;
            padding: var(--link_padding, none);
            text-decoration: none;

            &:hover {
                text-decoration: var(--link-hover_text-decoration, underline);
            }
        }

        @media #{map-get($media-min, c)} {
            &__columns--2 {
                display: grid;
                grid-gap: 3rem;
                grid-template-columns: repeat(2, minmax(19rem, max-content));
            }
        }
    }

    @media #{map-get($media-max, b)} {
        @include related-terms-clickable-block;

        --item_border-bottom: 0.1rem solid #eeeeee;
        --link_color: #1558d6;
        --link_padding: 0.4rem 0;
        --link-hover_text-decoration: none;

        .related-terms {
            &__link {
                &::before {
                    color: var(--link-before_color, #505050);
                    content: $vsi-chevron-right;
                    position: absolute;
                    right: 0;
                }
            }
        }

        @media #{map-get($media-max, b)} {
            // stylelint-disable plugin/selector-bem-pattern
            .related-terms__item:nth-of-type(n + #{6 + 1}),
            .related-terms__columns--2 .related-terms__item:nth-of-type(n + #{6 + 1}) {
                display: none;
            }

            .related-terms {
                &__columns--2 .related-terms {
                    // stylelint-disable max-nesting-depth
                    &__column:nth-of-type(n + 2) {
                        display: none;
                    }

                    // stylelint-enable max-nesting-depth
                }
            }

            // stylelint-enable plugin/selector-bem-pattern
        }
    }
}

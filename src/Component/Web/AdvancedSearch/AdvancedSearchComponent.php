<?php

declare(strict_types=1);

namespace App\Component\Web\AdvancedSearch;

use App\JsonTemplate\Component\AbstractComponent;

class AdvancedSearchComponent extends AbstractComponent
{
    public function __construct(
        public readonly AdvancedSearchLayout $layout
    )
    {
    }

    public static function getType(): string
    {
        return 'advanced_search';
    }

    public function getRenderer(): string
    {
        return AdvancedSearchRenderer::class;
    }
}

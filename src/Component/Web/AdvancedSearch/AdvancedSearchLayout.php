<?php

declare(strict_types=1);

namespace App\Component\Web\AdvancedSearch;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum AdvancedSearchLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Web/AdvancedSearch/layout/default/advanced_search-default.html.twig',
        };
    }
}

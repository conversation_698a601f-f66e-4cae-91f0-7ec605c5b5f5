<?php

declare(strict_types=1);

namespace App\Component\Web\HasOrganicResults;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionRenderer;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\AmountOfOrganicResultsCondition;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\SegmentInterface;
use App\JsonTemplate\View\ViewInterface;

final class HasOrganicResultsRenderer extends AbstractSearchApiConditionRenderer
{
    public function __construct(
        private readonly ResultsAmountRegistry $resultsAmountRegistry
    )
    {
    }

    protected function getActiveSegment(ComponentInterface $component, ViewInterface $view): SegmentInterface
    {
        if (!$component instanceof HasOrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [HasOrganicResultsComponent::class]);
        }

        // Based on what is registered, not on what is available in the search response
        if ($this->resultsAmountRegistry->getRegisteredAmountByResultsType(ResultsType::ORGANIC) > 0) {
            return $component->getMatchingSegment();
        }

        return $component->getNonMatchingSegment();
    }

    protected function getConditions(ComponentInterface $component, bool $value): ViewDataConditionCollection
    {
        return new ViewDataConditionCollection(
            [
                new AmountOfOrganicResultsCondition(
                    resultsAmountRegistry: $this->resultsAmountRegistry,
                    min                  : 1,
                    max                  : null,
                    expectedResult       : $value,
                ),
            ],
        );
    }
}

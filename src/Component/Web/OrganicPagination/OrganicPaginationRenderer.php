<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicPagination;

use App\Component\Generic\Pagination\PaginationRenderer;
use App\Component\Generic\Pagination\PaginationWithMinimalAmountOfPagesComponent;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Search\Request\SearchRequestInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResponseContext;

final class OrganicPaginationRenderer extends AbstractComponentRenderer
{
    private const int MINIMAL_AMOUNT_OF_PAGES = 5;

    public function __construct(
        private readonly SearchRequestInterface $searchRequest,
        private readonly PaginationRenderer $paginationRenderer
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::ORGANIC_RESULTS,
            ],
            $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof OrganicPaginationComponent) {
            throw UnsupportedComponentException::create($component, [OrganicPaginationComponent::class]);
        }

        /** @var OrganicResponseContext|null $organicResponse */
        $organicResponse = $view->getDataRegistry()
            ->getSearchResponses()
            ->getSearchResponseContext(ViewDataProperty::ORGANIC_RESULTS);

        if ($organicResponse === null) {
            return '';
        }

        return $this->paginationRenderer->render(
            new PaginationWithMinimalAmountOfPagesComponent(
                currentPage         : $organicResponse->getCurrentPage(),
                maxPage             : $organicResponse->getMaxPage(),
                layout              : $component->getLayout(),
                minimalAmountOfPages: self::MINIMAL_AMOUNT_OF_PAGES,
                requestedCurrentPage: $this->searchRequest->getPage(),
            ),
            $view,
        );
    }
}

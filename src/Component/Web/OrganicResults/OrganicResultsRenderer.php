<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResults;

use App\Component\Generic\Results\ResultsAmountOptimizer;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Processed\ComponentRendererWithSearchApiDependencyInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\KeywordHighlightOption;
use App\Preferences\Option\LinkTypeOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResult;

final class OrganicResultsRenderer extends AbstractComponentRenderer
    implements ComponentRendererWithSearchApiDependencyInterface
{
    public function __construct(
        private readonly Environment $twig,
        private readonly ResultsAmountRegistry $resultsAmountRegistry,
        private readonly SearchApiManager $searchApiManager,
        private readonly ResultsAmountOptimizer $resultsAmountOptimizer
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
                ViewDataProperty::ORGANIC_RESULTS,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof OrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [OrganicResultsComponent::class]);
        }

        $organicViewDataRequest = $request->organic()->increasePageSize($component->getAmount());

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::ORGANIC_RESULTS,
            searchApiViewDataRequest: $organicViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function handleSearchApiCompleted(
        ComponentInterface $component,
        ViewInterface $view
    ): void
    {
        if (!$component instanceof OrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [OrganicResultsComponent::class]);
        }

        $this->resultsAmountOptimizer->optimizeAmountOfResults($component);

        $organicResultsResponse = $view->getDataRegistry()->getOrganicResults($component);

        $this->resultsAmountRegistry->registerResults(
            component  : $component,
            resultsType: ResultsType::ORGANIC,
            resultsKey : spl_object_hash($organicResultsResponse),
            results    : $organicResultsResponse->getResults(),
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof OrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [OrganicResultsComponent::class]);
        }

        $results = $this->resultsAmountRegistry->getResultsByComponent(
            component: $component,
        );

        return $this->renderOrganicResults($component, $view, $results);
    }

    /**
     * @param OrganicResult[] $results
     */
    public function renderOrganicResults(
        OrganicResultsComponent $component,
        ViewInterface $view,
        array $results
    ): string
    {
        if ($results === []) {
            return '';
        }

        $viewDataRegistry = $view->getDataRegistry();

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'results'                      => $results,
                'query'                        => (string)$viewDataRegistry->getQuery(),
                'keyword_highlight_option'     => $viewDataRegistry->getOrganicKeywordHighlight(KeywordHighlightOption::DEFAULT_FALSE),
                'result_description_more_link' => $component->resultDescriptionMoreLink,
                'result_display_url_link'      => $component->resultDisplayUrlLink,
                'result_title_link'            => $component->resultTitleLink,
                'show_result_display_url'      => $component->showResultDisplayUrl,
                'link_type_option'             => $viewDataRegistry->getOrganicLinkType(LinkTypeOption::DEFAULT_TARGET_BLANK),
                'max_description_length'       => (int)$component->maxDescriptionLength,
                'component_space_modifiers'    => $component->componentSpaceModifiers,
                'layout'                       => $component->layout->value,
            ],
        );
    }
}

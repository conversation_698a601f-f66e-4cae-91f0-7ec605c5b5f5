<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResults\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;

class OrganicResultsStatisticsResolver extends AbstractStatisticsResolver
{
    private const string STATISTICS_KEY_AMOUNT_SHOWN = 'amount_shown';

    private const string PAYLOAD_KEY_AMOUNT = 'a';

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::PAYLOAD_KEY_AMOUNT)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_INTEGER)
            ->addValidator(new GreaterThanOrEqualValidator(0));
    }

    /**
     * @inheritDoc
     */
    protected function getStatisticsMapping(): array
    {
        return [
            self::STATISTICS_KEY_AMOUNT_SHOWN => self::PAYLOAD_KEY_AMOUNT,
        ];
    }
}

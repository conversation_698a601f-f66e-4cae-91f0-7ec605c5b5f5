/** @define organic-results */
.organic-results--dark {
    .organic-results {
        &__list {
            list-style: none;
            margin-bottom: 8rem;
            max-width: 60rem;
        }

        &__item {
            margin-bottom: var(--item_margin-bottom, 2.5rem);
        }

        &__title {
            font-size: var(--title_font-size, 1.4rem);
            font-weight: 400;
            line-height: var(--title_line-height, 2.2rem);
            margin-bottom: 0.4rem;
        }

        &__link {
            color: #999999;

            &:hover {
                text-decoration: underline;
            }
        }

        &__description {
            color: #999999;
            font-size: var(--description_font-size, 1.4rem);
            line-height: var(--description_line-height, 2rem);
        }

        &__display-url {
            color: #999999;
            font-size: var(--display-url_font-size, 1.4rem);
            font-style: normal;
            line-height: var(--display-url_line-height, 2rem);
            margin-bottom: 0.4rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

/** Mobile / tablet */
// stylelint-disable plugin/selector-bem-pattern
@media #{map-get($media-max, c)} {
    .organic-results--dark {
        --description_font-size: 1.3rem;
        --description_line-height: 1.6rem;
        --description_max-height: 4.8rem;
        --display-url_font-size: 1.3rem;
        --display-url_line-height: 2rem;
        --item_margin-bottom: 1.5rem;
        --title_font-size: 1.4rem;
        --title_line-height: 1.6rem;

        &__list {
            max-width: none;
        }

        &__description {
            overflow: hidden;
        }
    }
}

/** Mobile */
@media #{map-get($media-max, b)} {
    .organic-results--dark {
        .organic-results {
            &__link {
                display: inline-block;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}

@media #{map-get($media-max, c)} {
    .organic-results--dark {
        --description_font-size: 1.3rem;
        --description_line-height: 1.6rem;
        --description_max-height: 4.8rem;
        --display-url_font-size: 1.3rem;
        --display-url_line-height: 2rem;
        --item_margin-bottom: 1.5rem;
        --title_font-size: 1.4rem;
        --title_line-height: 1.6rem;

        &__list {
            max-width: none;
        }

        &__description {
            overflow: hidden;
        }
    }
}

{# @var results \Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResult[] #}
{# @var query string #}
{# @var keyword_highlight_option string #}
{# @var result_description_more_link bool #}
{# @var result_display_url_link bool #}
{# @var result_title_link bool #}
{# @var show_result_display_url bool #}
{# @var link_type_option string #}
{# @var max_description_length int #}
{# @var component_space_modifiers string[] #}
{% set component_space_modifiers = component_space_modifiers|pushString('top') %}

<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <div class="{{ component_class }}__list">
        {% for result in results %}
            <article class="{{ component_class }}__item organic-results-item">
                <h2 class="{{ component_class }}__title">
                    {% if result_title_link %}
                        <a href="{{ result.url }}" rel="nofollow noopener noreferrer" {{ link_type_option_attributes(link_type_option, {class: component_class ~ '__link'}) }}>
                            {{- result.title|apply_keyword_highlight(query, keyword_highlight_option) -}}
                        </a>
                    {% else %}
                        {{- result.title|apply_keyword_highlight(query, keyword_highlight_option) -}}
                    {% endif %}
                </h2>
                {% if show_result_display_url %}
                    <div class="{{ component_class }}__display-url">
                        {% if result_display_url_link %}
                            <a href="{{ result.url }}" rel="nofollow noopener noreferrer" {{ link_type_option_attributes(link_type_option, {class: component_class ~ '__display-url-link'}) }} title="{{ result.title }}">
                                {{ result.displayDomain|apply_keyword_highlight(query, keyword_highlight_option) }}
                            </a>
                        {% else %}
                            {{ result.displayDomain|apply_keyword_highlight(query, keyword_highlight_option) }}
                        {% endif %}
                    </div>
                {% endif %}
                <p class="{{ component_class }}__description">
                    {% if max_description_length > 0 %}
                        {{ result.description|u.truncate(max_description_length, '...', false)|apply_keyword_highlight(query, keyword_highlight_option) }}
                    {% else %}
                        {{ result.description|apply_keyword_highlight(query, keyword_highlight_option) }}
                    {% endif %}
                    {% if result_description_more_link %}
                        <a href="{{ result.url }}" rel="nofollow noopener noreferrer" {{ link_type_option_attributes(link_type_option, {class: component_class ~ '__description-link vsi'}) }}>
                            <span class="{{ component_class }}__description-link-text">{{ 'organic.more'|trans }}</span>
                        </a>
                    {% endif %}
                </p>
            </article>
        {% endfor %}
    </div>
</div>

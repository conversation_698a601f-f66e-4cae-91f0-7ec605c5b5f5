{# @var results \Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResult[] #}
{# @var query string #}
{# @var keyword_highlight_option string #}
{# @var result_description_more_link bool #}
{# @var result_display_url_link bool #}
{# @var result_title_link bool #}
{# @var show_result_display_url bool #}
{# @var link_type_option string #}
{# @var max_description_length int #}
{# @var component_space_modifiers string[] #}
{# @var component_class string #}
{% set component_space_modifiers = component_space_modifiers|pushString('top') %}

<div class="{{ component_class(component_class, [layout], component_space_modifiers) }}"{{ delayed_container_attributes() }}>
    <ol class="{{ component_class }}__list">
        {% for result in results %}
            <li class="{{ component_class }}__item organic-results-item">
                <h2 class="{{ component_class }}__title">{{ result.title|apply_keyword_highlight(query, keyword_highlight_option) }}</h2>
                <p class="{{ component_class }}__excerpt">
                    {{ result.description|apply_keyword_highlight(query, keyword_highlight_option) }}
                </p>
                <div class="{{ component_class }}__footer">
                    <a class="{{ component_class }}__link" href="{{ result.url }}" rel="nofollow noopener norefferer">
                        {{ 'content_page.read'|trans }}
                    </a>
                </div>
            </li>
        {% endfor %}
    </ol>
</div>

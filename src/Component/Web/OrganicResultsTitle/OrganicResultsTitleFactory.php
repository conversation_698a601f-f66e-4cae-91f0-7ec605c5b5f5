<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResultsTitle;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;

final class OrganicResultsTitleFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return OrganicResultsTitleComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        return new OrganicResultsTitleComponent(
            layout                 : OrganicResultsTitleLayout::from($options[LayoutInterface::KEY]),
            showQuery              : $options[OrganicResultsTitleResolver::KEY_SHOW_QUERY],
            componentSpaceModifiers: $options[AbstractSpaceResolver::KEY_COMPONENT_SPACE_MODIFIERS],
        );
    }
}

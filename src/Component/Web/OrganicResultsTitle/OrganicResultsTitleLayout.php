<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResultsTitle;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum OrganicResultsTitleLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';
    case DARK    = 'dark';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Web/OrganicResultsTitle/layout/default/organic_results_title-default.html.twig',
            self::DARK    => '@component/Web/OrganicResultsTitle/layout/dark/organic_results_title-dark.html.twig',
        };
    }
}

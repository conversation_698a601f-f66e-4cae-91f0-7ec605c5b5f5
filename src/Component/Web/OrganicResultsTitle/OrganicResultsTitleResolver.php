<?php

declare(strict_types=1);

namespace App\Component\Web\OrganicResultsTitle;

use App\Component\Generic\AbstractSpace\AbstractSpaceResolver;
use App\Component\Generic\AbstractSpace\ComponentSpaceModifier;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class OrganicResultsTitleResolver extends AbstractSpaceResolver
{
    public const string KEY_SHOW_QUERY = 'show_query';

    public static function getSupportedComponent(): string
    {
        return OrganicResultsTitleComponent::class;
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->defineLayout(OrganicResultsTitleLayout::class);

        $this->optionsResolver->define(self::KEY_SHOW_QUERY)
            ->setAllowedType(OptionType::TYPE_BOOLEAN)
            ->setDefaultValue(true);
    }

    /**
     * @return ComponentSpaceModifier[]
     */
    protected function getDefaultComponentSpaceModifiers(): array
    {
        return [ComponentSpaceModifier::TOP];
    }
}

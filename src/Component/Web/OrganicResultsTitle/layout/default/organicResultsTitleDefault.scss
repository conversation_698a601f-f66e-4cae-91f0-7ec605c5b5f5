/** @define organic-results-title */
.organic-results-title--default {
    margin-bottom: var(--margin-bottom, 1rem);

    .organic-results-title {
        &__title {
            color: var(--title_color, #505058);
            font-size: var(--title_font-size, 1.4rem);
            font-weight: 400;
            line-height: var(--title_line-height, 1.5rem);
            word-break: break-word;
            word-wrap: break-word;
        }

        &__link {
            color: inherit;

            &:hover {
                text-decoration: underline;
            }
        }

        &--small .organic-results-title {
            &__title {
                font-size: 1.4rem;
                line-height: 1.5rem;
            }
        }
    }

    @media #{map-get($media-min, e)} {
        --title_color: #505058;
    }

    @media #{map-get($media-max, c)} {
        --margin-bottom: 0.5rem;
        --title_font-size: 1.3rem;
        --title_line-height: 1.8rem;
    }
}

<?php

declare(strict_types=1);

namespace App\Component\Web\WebSearchStatsTitle;

use App\Debug\Request\DebugRequestInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

readonly class WebSearchStatsTitleFormatter
{
    public function __construct(
        private TranslatorInterface $translator,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public function formatPageStatistics(
        string $query,
        int $page,
        int $totalResults,
        float $processTime,
        bool $highlightProperties
    ): string
    {
        if ($this->debugRequest->showFixedStats()) {
            $totalResults = 100;
            $processTime = 1.00;
        }

        $queryFormatted = htmlspecialchars($query, ENT_COMPAT);
        $pageFormatted = (string)$page;
        $totalResultsFormatted = number_format($totalResults, 0, '.', '.');
        $processTimeFormatted = number_format($processTime, 3, '.', '.');

        if ($highlightProperties) {
            $queryFormatted = $this->formatHighlight($queryFormatted);
            $pageFormatted = $this->formatHighlight((string)$page);
            $totalResultsFormatted = $this->formatHighlight($totalResultsFormatted);
            $processTimeFormatted = $this->formatHighlight($processTimeFormatted);
        }

        return $this->translator->trans(
            'search_results.title.page_stats',
            [
                '%page%'          => $pageFormatted,
                '%total_results%' => $totalResultsFormatted,
                '%query%'         => $queryFormatted,
                '%process_time%'  => $processTimeFormatted,
            ],
        );
    }

    private function formatHighlight(string $value): string
    {
        return sprintf('<strong>%s</strong>', $value);
    }
}

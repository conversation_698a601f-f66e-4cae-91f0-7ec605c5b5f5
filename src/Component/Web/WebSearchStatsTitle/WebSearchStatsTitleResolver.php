<?php

declare(strict_types=1);

namespace App\Component\Web\WebSearchStatsTitle;

use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

final class WebSearchStatsTitleResolver implements ComponentResolverInterface
{
    public const string KEY_SHOW_RANDOM_STATS = 'show_random_stats';

    public function __construct(
        private readonly ComponentOptionsResolverInterface $optionsResolver
    )
    {
    }

    public static function getSupportedComponent(): string
    {
        return WebSearchStatsTitleComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array
    {
        $this->optionsResolver->defineLayout(WebSearchStatsTitleLayout::class);

        $this->optionsResolver->define(self::KEY_SHOW_RANDOM_STATS)
            ->setDefaultValue(false)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        return $this->optionsResolver->resolve($options);
    }
}

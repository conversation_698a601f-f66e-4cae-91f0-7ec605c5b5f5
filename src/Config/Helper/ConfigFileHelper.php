<?php

declare(strict_types=1);

namespace App\Config\Helper;

final readonly class ConfigFileHelper implements ConfigFileHelperInterface
{
    public function __construct(
        private string $configPath,
        private string $localConfigPath
    )
    {
    }

    public function getBrandAssetsPhpFilePath(string $fileBaseName): string
    {
        return sprintf('%s/%s.php', $this->getBrandAssetsPhpPath(), $fileBaseName);
    }

    public function getBrandAssetsJsonFilePath(string $fileBaseName): string
    {
        return sprintf('%s/artemis/brand-assets/%s.json', $this->configPath, $fileBaseName);
    }

    public function getBrandConfigPhpPath(): string
    {
        return sprintf('%s/artemis/brand-config', $this->localConfigPath);
    }

    public function getBrandConfigPhpFilePath(string $fileBaseName): string
    {
        return sprintf('%s/%s.php', $this->getBrandConfigPhpPath(), $fileBaseName);
    }

    public function getBrandConfigJsonFilePath(string $fileBaseName): string
    {
        return sprintf('%s/artemis/brand-config/%s.json', $this->configPath, $fileBaseName);
    }

    private function getBrandAssetsPhpPath(): string
    {
        return sprintf('%s/artemis/brand-assets', $this->localConfigPath);
    }
}

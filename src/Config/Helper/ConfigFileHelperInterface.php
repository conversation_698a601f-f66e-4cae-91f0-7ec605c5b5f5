<?php

declare(strict_types=1);

namespace App\Config\Helper;

interface ConfigFileHelperInterface
{
    public function getBrandAssetsPhpFilePath(string $fileBaseName): string;

    public function getBrandAssetsJsonFilePath(string $fileBaseName): string;

    public function getBrandConfigPhpPath(): string;

    public function getBrandConfigPhpFilePath(string $fileBaseName): string;

    public function getBrandConfigJsonFilePath(string $fileBaseName): string;
}

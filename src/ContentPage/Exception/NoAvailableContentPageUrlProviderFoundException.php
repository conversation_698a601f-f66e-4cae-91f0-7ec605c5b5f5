<?php

declare(strict_types=1);

namespace App\ContentPage\Exception;

final class NoAvailableContentPageUrlProviderFoundException extends \RuntimeException
{
    public static function create(?\Throwable $previous = null): self
    {
        return new self(
            'Could not find a content page URL provider that is available to generate a URL for the content page.',
            0,
            $previous,
        );
    }
}

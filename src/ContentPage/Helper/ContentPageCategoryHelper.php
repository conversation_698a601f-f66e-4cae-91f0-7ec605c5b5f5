<?php

declare(strict_types=1);

namespace App\ContentPage\Helper;

use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageNestedCategory;

final readonly class ContentPageCategoryHelper
{
    /**
     * @param ContentPageNestedCategory[] $categories
     *
     * @return ContentPageNestedCategory[]
     */
    public function getPreferenceContentPageCategories(array $categories, int $maxAmount): array
    {
        $contentPageCategories = [];

        // Collect available categories with ID as key
        $availableCategories = [];

        foreach ($categories as $category) {
            $availableCategories[$category->id] = $category;
        }

        // Collect preference categories
        foreach ($this->getPreferenceCategoryIds() as $categoryId) {
            if (isset($availableCategories[$categoryId])) {
                $contentPageCategories[$categoryId] = $availableCategories[$categoryId];
                unset($availableCategories[$categoryId]);
            }
        }

        // Add with max amount of categories
        $appendAmount = max(0, $maxAmount - count($contentPageCategories));

        if ($appendAmount > 0) {
            foreach ($availableCategories as $category) {
                $contentPageCategories[$category->id] = $category;
                $appendAmount--;

                if ($appendAmount === 0) {
                    break;
                }
            }
        }

        return $contentPageCategories;
    }

    /**
     * @return int[]
     */
    public function getPreferenceCategoryIds(): array
    {
        return [
            5,
            2,
            4,
            16,
            37,
        ];
    }
}

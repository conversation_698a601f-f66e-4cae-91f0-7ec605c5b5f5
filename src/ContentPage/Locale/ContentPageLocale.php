<?php

declare(strict_types=1);

namespace App\ContentPage\Locale;

enum ContentPageLocale: string
{
    // This is the same list of Content API locales
    case DA_DK = 'da_DK';
    case DE_AT = 'de_AT';
    case DE_CH = 'de_CH';
    case DE_DE = 'de_DE';
    case EL_GR = 'el_GR';
    case EN_AU = 'en_AU';
    case EN_BD = 'en_BD';
    case EN_CA = 'en_CA';
    case EN_GB = 'en_GB';
    case EN_HK = 'en_HK';
    case EN_IE = 'en_IE';
    case EN_IN = 'en_IN';
    case EN_MY = 'en_MY';
    case EN_NG = 'en_NG';
    case EN_NZ = 'en_NZ';
    case EN_PH = 'en_PH';
    case EN_SG = 'en_SG';
    case EN_US = 'en_US';
    case EN_ZA = 'en_ZA';
    case ES_AR = 'es_AR';
    case ES_BO = 'es_BO';
    case ES_CL = 'es_CL';
    case ES_CO = 'es_CO';
    case ES_CR = 'es_CR';
    case ES_DO = 'es_DO';
    case ES_EC = 'es_EC';
    case ES_ES = 'es_ES';
    case ES_GT = 'es_GT';
    case ES_HN = 'es_HN';
    case ES_MX = 'es_MX';
    case ES_NI = 'es_NI';
    case ES_PA = 'es_PA';
    case ES_PE = 'es_PE';
    case ES_PR = 'es_PR';
    case ES_PY = 'es_PY';
    case ES_SV = 'es_SV';
    case ES_UY = 'es_UY';
    case ES_VE = 'es_VE';
    case FI_FI = 'fi_FI';
    case FR_FR = 'fr_FR';
    case ID_ID = 'id_ID';
    case IT_IT = 'it_IT';
    case JA_JP = 'ja_JP';
    case KO_KR = 'ko_KR';
    case NL_NL = 'nl_NL';
    case NO_NO = 'no_NO';
    case PL_PL = 'pl_PL';
    case PT_BR = 'pt_BR';
    case PT_PT = 'pt_PT';
    case RO_RO = 'ro_RO';
    case SK_SK = 'sk_SK';
    case SV_SE = 'sv_SE';
    case TH_TH = 'th_TH';
    case TR_TR = 'tr_TR';
    case VI_VN = 'vi_VN';

    public static function isSupported(string $locale): bool
    {
        return self::tryFrom($locale) !== null;
    }

    public static function getDefault(): self
    {
        return self::EN_US;
    }
}

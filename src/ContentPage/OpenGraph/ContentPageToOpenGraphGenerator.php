<?php

declare(strict_types=1);

namespace App\ContentPage\OpenGraph;

use App\Assets\AssetsHelper;
use App\Brand\Settings\BrandSettingsHelper;
use App\BrandAssets\File\BrandAssetsImageFileName;
use App\ContentPage\Url\ContentPageImageUrlGenerator;
use App\ContentPage\Url\ContentPageUrlGenerator;
use App\PageHeadTags\OpenGraph\OpenGraph;
use App\PageHeadTags\OpenGraph\OpenGraphFactory;
use App\PageHeadTags\OpenGraph\OpenGraphImage;
use App\PageHeadTags\OpenGraph\OpenGraphImageFactory;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

final readonly class ContentPageToOpenGraphGenerator
{
    public function __construct(
        private OpenGraphFactory $openGraphFactory,
        private OpenGraphImageFactory $openGraphImageFactory,
        private ContentPageUrlGenerator $contentPageUrlGenerator,
        private ContentPageImageUrlGenerator $contentPageImageUrlGenerator,
        private AssetsHelper $assetsHelper,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function generate(ContentPage $contentPage, ?string $preferenceRoute = null): OpenGraph
    {
        $url = $this->contentPageUrlGenerator->generateUrl(
            contentPage    : $contentPage,
            absoluteUrl    : true,
            preferenceRoute: $preferenceRoute,
        );

        return $this->openGraphFactory->create(
            type       : 'article',
            siteName   : $this->brandSettingsHelper->getSettings()->getName(),
            locale     : $contentPage->locale,
            url        : $url,
            title      : $contentPage->title,
            description: $contentPage->meta?->description,
            image      : $this->getImage($contentPage),
        );
    }

    private function getImage(ContentPage $contentPage): OpenGraphImage
    {
        if ($contentPage->image !== null) {
            return $this->openGraphImageFactory->create(
                url : $this->contentPageImageUrlGenerator->generate($contentPage->image, 'c1200x628'),
                alt : $contentPage->image->title,
                type: 'image/jpeg',
            );
        }

        // Use brand logo
        $imageUrl = $this->assetsHelper->getRelativeBrandImageFilePath(
            sprintf('/logo/%s', BrandAssetsImageFileName::LOGO_LARGE_PNG->value),
        );

        return $this->openGraphImageFactory->create(
            url : $imageUrl,
            alt : $this->brandSettingsHelper->getSettings()->getName(),
            type: 'image/png',
        );
    }
}

<?php

declare(strict_types=1);

namespace App\ContentPage\Paragraph;

use App\Component\Content\ContentPageParagraph\ContentPageParagraphComponent;
use App\Component\Content\ContentPageParagraphs\ContentPageParagraphsComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\SegmentInterface;
use App\Search\Request\SearchRequestInterface;

final class ParagraphsAmountRegistry
{
    private ?int $amountOfResultsRegistered = null;

    /** @var array<int,mixed> */
    private array $amountOfParagraphsPerPage = [];

    private int $defaultAmountOfParagraphs = 0;

    /** @var array<string, true> */
    private array $pageComponents = [];

    private const int ALL_PARAGRAPHS = PHP_INT_MAX;

    private const array PARAGRAPH_COMPONENTS = [
        ContentPageParagraphComponent::class,
        ContentPageParagraphsComponent::class,
    ];

    public function __construct(
        private readonly SearchRequestInterface $searchRequest
    )
    {
    }

    public function getAmountOfResultsRegistered(): int
    {
        if ($this->amountOfResultsRegistered === null) {
            $this->amountOfResultsRegistered = $this->getCurrentPageStart();
        }

        return $this->amountOfResultsRegistered;
    }

    public function increaseAmountOfResultsRegistered(int $amountOfResults): void
    {
        if ($this->amountOfResultsRegistered === null) {
            $this->amountOfResultsRegistered = $this->getCurrentPageStart();
        }

        $this->amountOfResultsRegistered += $amountOfResults;
    }

    public function getCurrentPageStart(): int
    {
        return $this->calculatePageStart($this->searchRequest->getPage());
    }

    public function getPreviousPageStart(): int
    {
        return $this->calculatePageStart($this->searchRequest->getPage() - 1);
    }

    public function registerParagraphComponent(ComponentInterface $component): void
    {
        if (!in_array($component::class, self::PARAGRAPH_COMPONENTS, true)) {
            throw UnsupportedComponentException::create($component, self::PARAGRAPH_COMPONENTS);
        }

        if (isset($this->pageComponents[$component->getId()])) {
            return;
        }

        if ($this->defaultAmountOfParagraphs === self::ALL_PARAGRAPHS) {
            return;
        }

        if ($component instanceof ContentPageParagraphComponent && !$component->isStartOfParagraph()) {
            return;
        }

        $amountOfParagraphs = $component instanceof ContentPageParagraphsComponent
            ? $component->amount ?? self::ALL_PARAGRAPHS
            : 1;

        if ($amountOfParagraphs === self::ALL_PARAGRAPHS) {
            $this->defaultAmountOfParagraphs = self::ALL_PARAGRAPHS;

            return;
        }

        $this->defaultAmountOfParagraphs += $amountOfParagraphs;
    }

    public function registerParagraphsPerPage(
        int $page,
        SegmentInterface $matchingSegment,
        SegmentInterface $nonMatchingSegment
    ): void
    {
        $this->registerSegmentParagraphs($matchingSegment, $page, true);
        $this->registerSegmentParagraphs($nonMatchingSegment, $page, false);
    }

    private function registerSegmentParagraphs(
        SegmentInterface $segment,
        int $page,
        bool $isMatchingSegment
    ): void
    {
        $this->amountOfParagraphsPerPage[$page] ??= [
            'matching'     => 0,
            'non_matching' => 0,
        ];

        $paragraphsPerPageKey = $isMatchingSegment ? 'matching' : 'non_matching';

        if ($this->amountOfParagraphsPerPage[$page][$paragraphsPerPageKey] === self::ALL_PARAGRAPHS) {
            return;
        }

        foreach ($segment->getComponents() as $component) {
            if ($component instanceof ContentPageParagraphComponent && !$component->isStartOfParagraph()) {
                continue;
            }

            if (in_array($component::class, self::PARAGRAPH_COMPONENTS, true)) {
                $amountOfParagraphs = $component instanceof ContentPageParagraphsComponent
                    ? $component->amount ?? self::ALL_PARAGRAPHS
                    : 1;

                if ($amountOfParagraphs === self::ALL_PARAGRAPHS) {
                    $this->amountOfParagraphsPerPage[$page][$paragraphsPerPageKey] = self::ALL_PARAGRAPHS;

                    break;
                }

                $this->amountOfParagraphsPerPage[$page][$paragraphsPerPageKey] += $amountOfParagraphs;
                $this->pageComponents[$component->getId()] = true;
            }
        }
    }

    private function calculatePageStart(int $page): int
    {
        // $page - 1, because it is the total amount of paragraphs up to the previous page
        $previousPage = $page - 1;

        if ($previousPage > 0 && $this->defaultAmountOfParagraphs === self::ALL_PARAGRAPHS) {
            return self::ALL_PARAGRAPHS;
        }

        $pageStart = $this->defaultAmountOfParagraphs * $previousPage;

        foreach ($this->amountOfParagraphsPerPage as $pageNumber => $paragraphs) {
            if ($pageNumber < $page) {
                if ($paragraphs['matching'] === self::ALL_PARAGRAPHS) {
                    return self::ALL_PARAGRAPHS;
                }

                // $previousPage - 1, because for the non-matching we need to exclude the matching page
                $nonMatchingPages = $previousPage - 1;

                if ($nonMatchingPages > 0 && $paragraphs['non_matching'] === self::ALL_PARAGRAPHS) {
                    return self::ALL_PARAGRAPHS;
                }

                $pageStart += $paragraphs['matching'] + ($paragraphs['non_matching'] * $nonMatchingPages);
            } else {
                if ($previousPage > 0 && $paragraphs['non_matching'] === self::ALL_PARAGRAPHS) {
                    return self::ALL_PARAGRAPHS;
                }

                $pageStart += $paragraphs['non_matching'] * $previousPage;
            }
        }

        return $pageStart;
    }
}

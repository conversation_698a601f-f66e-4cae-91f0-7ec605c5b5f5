<?php

declare(strict_types=1);

namespace App\ContentPage\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class ContentPageRequest implements ContentPageRequestInterface
{
    private int $publicId;

    private int $previousPublicId;

    private string $slug;

    private bool $isPreviousFromFallbackCollection;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getPublicId(): ?int
    {
        if (!isset($this->publicId)) {
            $getContentPagePublicIdFromPath = $this->requestManager
                ->flagBag()
                ->getBool(ContentPageRequestFlag::PUBLIC_ID_IN_PATH);

            if ($getContentPagePublicIdFromPath) {
                $this->publicId = $this->requestManager->attributesBag()->getUnsignedInt(
                    self::ATTRIBUTE_CONTENT_PAGE_PUBLIC_ID,
                );
            } else {
                $this->publicId = $this->requestManager->queryBag()->getUnsignedInt(
                    self::PARAMETER_CONTENT_PAGE_PUBLIC_ID,
                );
            }
        }

        return $this->requestPropertyNormalizer->getInt($this->publicId);
    }

    public function getPreviousPublicId(): ?int
    {
        if (!isset($this->previousPublicId)) {
            $this->previousPublicId = $this->requestManager->queryBag()->getUnsignedInt(
                self::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID,
            );
        }

        return $this->requestPropertyNormalizer->getInt($this->previousPublicId);
    }

    public function getSlug(): ?string
    {
        if (!isset($this->slug)) {
            $this->slug = $this->requestManager->attributesBag()->getString(
                self::ATTRIBUTE_CONTENT_PAGE_SLUG,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->slug);
    }

    public function isPreviousFromFallbackCollection(): bool
    {
        if (!isset($this->isPreviousFromFallbackCollection)) {
            $this->isPreviousFromFallbackCollection = $this->requestManager->queryBag()->getBool(
                self::PARAMETER_PREVIOUS_CONTENT_PAGE_FALLBACK_COLLECTION,
            );
        }

        return $this->isPreviousFromFallbackCollection;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_CONTENT_PAGE_PUBLIC_ID          => $this->getPublicId(),
            self::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID => $this->getPreviousPublicId(),
            self::ATTRIBUTE_CONTENT_PAGE_SLUG               => $this->getSlug(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\ContentPage\Settings;

use App\ContentPage\Author\AuthorFactory;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;
use Psr\Log\LoggerInterface;

final readonly class ContentPageSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    private const string FALLBACK_COLLECTION = 'default2';

    private const string KEY_COLLECTION                    = 'collection';
    private const string KEY_AUTHOR                        = 'author';
    private const string KEY_USE_BRAND_FOR_ORGANIC_RESULTS = 'use_brand_for_organic_results';

    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private LoggerInterface $logger,
        private AuthorFactory $authorFactory
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'content_page';
    }

    public function create(): ContentPageSettings
    {
        $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
        $moduleConfig = $websiteConfiguration->getBrandConfig()['content_page'];

        if (!$this->isModuleEnabled($moduleConfig)) {
            // Use defaults
            $contentPageSettings = new ContentPageSettings(
                collection               : self::FALLBACK_COLLECTION,
                author                   : $this->authorFactory->createEditorialTeam(),
                useBrandForOrganicResults: false,
            );

            $this->logger->error(
                'Content page settings are requested, but the module is disabled for {brand_slug}; Using fallback settings',
                [
                    'brand_slug'   => $websiteConfiguration->getBrandSlug(),
                    'content_page' => $contentPageSettings->toArray(),
                ],
            );

            return $contentPageSettings;
        }

        $authorData = $moduleConfig[self::KEY_AUTHOR];
        $author = $this->authorFactory->create(
            $authorData['slug'],
            $authorData['name'],
        );

        return new ContentPageSettings(
            collection               : $moduleConfig[self::KEY_COLLECTION],
            author                   : $author,
            useBrandForOrganicResults: (bool)$moduleConfig[self::KEY_USE_BRAND_FOR_ORGANIC_RESULTS],
        );
    }
}

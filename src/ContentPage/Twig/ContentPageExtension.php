<?php

declare(strict_types=1);

namespace App\ContentPage\Twig;

use App\ContentPage\Url\ContentPageImageUrlGenerator;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

class ContentPageExtension extends AbstractExtension
{
    public function __construct(
        private readonly ContentPageImageUrlGenerator $contentPageImageUrlGenerator
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'content_page_image_url',
                $this->contentPageImageUrlGenerator->generate(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'get_content_page_category_title',
                $this->getContentPageCategoryTitle(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    public function getContentPageCategoryTitle(ContentPage $contentPage): string
    {
        return $contentPage->category->title ?? 'Default';
    }
}

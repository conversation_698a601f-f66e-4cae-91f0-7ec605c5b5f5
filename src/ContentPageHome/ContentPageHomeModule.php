<?php

declare(strict_types=1);

namespace App\ContentPageHome;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class ContentPageHomeModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'content_page_home';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_content_page_home.yaml']);
    }
}

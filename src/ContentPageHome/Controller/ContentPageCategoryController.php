<?php

declare(strict_types=1);

namespace App\ContentPageHome\Controller;

use App\ContentPageHome\Request\ContentPageCategoryRequestInterface;
use App\ContentPageHome\Settings\ContentPageHomeSettings;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use App\JsonTemplate\View\JsonTemplateViewHandler;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPageCategory\Response\ContentPageCategoryResponseContext;

final class ContentPageCategoryController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateViewHandler $jsonTemplateViewHandler,
        private readonly JsonTemplateViewFactory $jsonTemplateViewFactory,
        private readonly RouteRegistry $routeRegistry,
        private readonly ContentPageCategoryRequestInterface $contentPageCategoryRequest,
        private readonly ContentPageHomeSettings $contentPageHomeSettings
    )
    {
    }

    #[Route(
        path        : '/category/{categoryPublicId}-{categorySlug}',
        name        : 'route_content_page_category',
        requirements: [
            ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_PUBLIC_ID => '^\d+$',
            ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_SLUG      => '^[a-z0-9-]+$',
        ],
        defaults    : [
            SearchRequestFlag::IGNORE_QUERY_FOR_SEARCH => true,
            SearchRequestFlag::QUERY_IS_CATEGORY_SLUG  => true,
        ],
        methods     : ['GET']
    )]
    public function category(): Response
    {
        $view = $this->jsonTemplateViewFactory->create(
            '@themeJson/content_page_category/content_page_category.json',
        );

        $this->routeRegistry->setSearchRoute($this->contentPageHomeSettings->searchRoute);

        $response = $this->jsonTemplateViewHandler->handle($view);

        /** @var ContentPageCategoryResponseContext|null $contentPageCategoryResponse */
        $contentPageCategoryResponse = $view->getDataRegistry()
            ->getSearchResponses()
            ->getSearchResponseContext(ViewDataProperty::CONTENT_PAGE_CATEGORY);
        $category = $contentPageCategoryResponse?->category;

        if ($category === null) {
            throw $this->createNotFoundException();
        }

        if ($category->slug !== $this->contentPageCategoryRequest->getSlug()) {
            return $this->redirectToRoute(
                'route_content_page_category',
                [
                    ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_PUBLIC_ID => $category->publicId,
                    ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_SLUG      => $category->slug,
                ],
                Response::HTTP_FOUND,
            );
        }

        return $response;
    }
}

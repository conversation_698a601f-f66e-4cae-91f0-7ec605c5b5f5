<?php

declare(strict_types=1);

namespace App\ContentPageHome\Type;

enum ContentPageHomeType: string
{
    case HOME_1 = 'home_1';
    case HOME_2 = 'home_2';
    case HOME_3 = 'home_3';
    case HOME_4 = 'home_4';
    case HOME_5 = 'home_5';
    case HOME_6 = 'home_6';
    case HOME_7 = 'home_7';

    public function getJsonTemplate(): string
    {
        return match ($this) {
            self::HOME_1 => '@themeJson/content_page_home/content_page_home_1.json',
            self::HOME_2 => '@themeJson/content_page_home/content_page_home_2.json',
            self::HOME_3 => '@themeJson/content_page_home/content_page_home_3.json',
            self::HOME_4 => '@themeJson/content_page_home/content_page_home_4.json',
            self::HOME_5 => '@themeJson/content_page_home/content_page_home_5.json',
            self::HOME_6 => '@themeJson/content_page_home/content_page_home_6.json',
            self::HOME_7 => '@themeJson/content_page_home/content_page_home_7.json'
        };
    }
}

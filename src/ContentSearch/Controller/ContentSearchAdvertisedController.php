<?php

declare(strict_types=1);

namespace App\ContentSearch\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use App\Statistics\Helper\StatisticsRequestFlag;
use App\Tracking\Entry\Request\TrackingEntryRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ContentSearchAdvertisedController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly RouteRegistry $routeRegistry
    )
    {
    }

    #[Route(
        path    : '/csa',
        name    : 'route_content_search_advertised',
        defaults: [
            StatisticsRequestFlag::LOG_ENABLED                => true,
            TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE => true,
            SearchRequestFlag::TYPE                           => SearchType::SEARCH->value,
        ],
        methods : ['GET']
    )]
    public function search(): Response
    {
        $this->routeRegistry->setCurrentRouteAsSearchRoute();

        return $this->jsonTemplateRenderer->renderForSearchByDevice(
            '@themeJson/content_search/content_search_advertised_mobile.json',
            '@themeJson/content_search/content_search_advertised_desktop.json',
        );
    }
}

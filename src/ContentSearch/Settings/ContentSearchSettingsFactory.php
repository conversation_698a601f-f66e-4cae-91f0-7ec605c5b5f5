<?php

declare(strict_types=1);

namespace App\ContentSearch\Settings;

use App\ContentSearch\ContentSearchModule;
use App\Debug\Request\DebugRequestInterface;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class ContentSearchSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return ContentSearchModule::getModuleName();
    }

    public function create(): ContentSearchSettings
    {
        if ($this->debugRequest->enableModule()) {
            return new ContentSearchSettings(
                enabled: true,
            );
        }

        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['content_search'] ?? [];

        return new ContentSearchSettings(
            enabled: $this->isModuleEnabled($moduleConfig),
        );
    }
}

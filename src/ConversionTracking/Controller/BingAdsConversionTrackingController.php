<?php

declare(strict_types=1);

namespace App\ConversionTracking\Controller;

use App\AdBot\Request\AdBotRequestInterface;
use App\ConversionTracking\Endpoint\BingAds\BingAdsHandlerInterface;
use App\ConversionTracking\Helper\AdClickCounterHelper;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingResponseFactory;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Generic\Response\UncachedPixelResponse;
use App\SplitTest\Request\SplitTestRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class BingAdsConversionTrackingController extends AbstractController
{
    public function __construct(
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly AdClickCounterHelper $adClickCounterHelper,
        private readonly ConversionTrackingResponseFactory $conversionTrackingResponseFactory,
        private readonly FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    #[Route(
        // Path shortened to avoid ad blockers
        path    : '/tp/ba',
        name    : 'route_conversion_tracking_bing_ads',
        defaults: [
            SplitTestRequestFlag::ALWAYS_MATCH_ROUTE => true,
        ],
        methods : ['GET', 'POST']
    )]
    public function bingAds(BingAdsHandlerInterface $bingAdsHandler): Response
    {
        // Endpoint should never be called for ad bots
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return new UncachedPixelResponse();
        }

        // Increase generic ad click counter
        $this->adClickCounterHelper->increaseGenericAdClickCount();

        // Handle Bing Ads conversion
        return $this->conversionTrackingResponseFactory->create(
            $bingAdsHandler->handle(),
        );
    }
}

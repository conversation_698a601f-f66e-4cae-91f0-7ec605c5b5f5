<?php

declare(strict_types=1);

namespace App\ConversionTracking;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class ConversionTrackingModule extends AbstractDependencyInjectionModule
{
    public const string KEY_DEBUG_MODE = 'debug_mode';

    public static function getModuleName(): string
    {
        return 'conversion_tracking';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->addDefaultsIfNotSet()
                ->children();

        $moduleNodeChildren
            ->booleanNode(self::KEY_DEBUG_MODE)
                ->info('Prevents sending conversion tracking requests when true. Logs a warning instead.')
                ->defaultFalse();
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $moduleConfig = $this->getModuleConfig($config);
        $container->setParameter(
            self::getModuleParameterConfigName(),
            $moduleConfig,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\GoogleAdManager;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class GoogleAdManagerRequest implements GoogleAdManagerRequestInterface
{
    private string $adUnitPath;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getAdUnitPath(): ?string
    {
        if (!isset($this->adUnitPath)) {
            $this->adUnitPath = $this->requestManager->queryBag()->getString(self::PARAMETER_AD_UNIT_PATH);
        }

        return $this->requestPropertyNormalizer->getString($this->adUnitPath);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_AD_UNIT_PATH => $this->getAdUnitPath(),
        ];
    }
}

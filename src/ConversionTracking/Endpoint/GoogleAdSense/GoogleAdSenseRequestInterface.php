<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\GoogleAdSense;

use App\Http\Request\RequestInterface;

interface GoogleAdSenseRequestInterface extends RequestInterface
{
    // These parameters are added by Google when a user clicks an ad
    public const string PARAMETER_BLOCK = 'block';
    public const string PARAMETER_AD    = 'ad';

    /**
     * Google Ads block number
     * Starts with 1
     */
    public function getBlock(): ?int;

    /**
     * Google Ads ad number in block
     * Starts with 1
     */
    public function getAd(): ?int;
}

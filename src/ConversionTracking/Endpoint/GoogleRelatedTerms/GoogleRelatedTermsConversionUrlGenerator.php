<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\GoogleRelatedTerms;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use App\Search\Request\SearchRequestInterface;

readonly class GoogleRelatedTermsConversionUrlGenerator implements GoogleRelatedTermsConversionUrlGeneratorInterface
{
    public function __construct(
        private PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private SearchRequestInterface $searchRequest
    )
    {
    }

    public function generate(string $adClientId): string
    {
        $supportTracking = $this->searchRequest->isDisplaySearchRelated();

        return $this->persistentUrlParametersRouter->generate(
            'route_conversion_tracking_google_related_terms',
            [
                GoogleRelatedTermsRequestInterface::PARAMETER_SUPPORTS_TRACKING => (int)$supportTracking,
                ConversionTrackingRequestInterface::PARAMETER_AD_CLIENT_ID      => $adClientId,
            ],
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            true,
        );
    }
}

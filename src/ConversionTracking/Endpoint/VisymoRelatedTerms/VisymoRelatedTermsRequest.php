<?php

declare(strict_types=1);

namespace App\ConversionTracking\Endpoint\VisymoRelatedTerms;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class VisymoRelatedTermsRequest implements VisymoRelatedTermsRequestInterface
{
    private string $term;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getTerm(): ?string
    {
        if (!isset($this->term)) {
            $this->term = $this->requestManager->requestBag()->getString(self::PARAMETER_TERM);
        }

        return $this->requestPropertyNormalizer->getString($this->term);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_TERM => $this->getTerm(),
        ];
    }
}

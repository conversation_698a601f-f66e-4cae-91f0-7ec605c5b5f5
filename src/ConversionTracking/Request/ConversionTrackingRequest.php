<?php

declare(strict_types=1);

namespace App\ConversionTracking\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

/**
 * Contains generic tracking pixel endpoint parameters which might be needed for things like logging or
 * determining order id.
 * Any endpoint-specific parameters should be defined in the endpoint's request object.
 */
final class ConversionTrackingRequest implements ConversionTrackingRequestInterface
{
    private string $originalPageviewId;

    private string $httpHostClient;

    private int $adStyleId;

    private string $adClientId;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getOriginalPageviewId(): ?string
    {
        if (!isset($this->originalPageviewId)) {
            $this->originalPageviewId = $this->requestManager->queryBag()->getString(self::PARAMETER_ORIGINAL_PAGEVIEW_ID);
        }

        return $this->requestPropertyNormalizer->getString($this->originalPageviewId);
    }

    public function getHttpHostClient(): ?string
    {
        if (!isset($this->httpHostClient)) {
            $this->httpHostClient = '';
            $httpHostClient = $this->requestManager->queryBag()->getString(self::PARAMETER_HTTP_HOST_CLIENT);

            if (filter_var($httpHostClient, FILTER_VALIDATE_URL) !== false) {
                $parsedUrl = parse_url($httpHostClient);

                if (isset($parsedUrl['scheme'], $parsedUrl['host'])) {
                    $this->httpHostClient = sprintf('%s://%s', $parsedUrl['scheme'], $parsedUrl['host']);
                }
            }
        }

        return $this->requestPropertyNormalizer->getString($this->httpHostClient);
    }

    public function getAdStyleId(): ?int
    {
        if (!isset($this->adStyleId)) {
            $this->adStyleId = $this->requestManager->queryBag()->getUnsignedInt(self::PARAMETER_AD_STYLE_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->adStyleId);
    }

    public function getAdClientId(): ?string
    {
        if (!isset($this->adClientId)) {
            $this->adClientId = $this->requestManager->queryBag()->getString(self::PARAMETER_AD_CLIENT_ID);
        }

        return $this->requestPropertyNormalizer->getString($this->adClientId);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_ORIGINAL_PAGEVIEW_ID => $this->getOriginalPageviewId(),
            self::PARAMETER_HTTP_HOST_CLIENT     => $this->getHttpHostClient(),
            self::PARAMETER_AD_STYLE_ID          => $this->getAdStyleId(),
            self::PARAMETER_AD_CLIENT_ID         => $this->getAdClientId(),
        ];
    }
}

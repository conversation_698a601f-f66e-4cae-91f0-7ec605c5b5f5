<?php

declare(strict_types=1);

namespace App\ConversionTracking\Tracking\HttpClient;

use App\ConversionTracking\Settings\ConversionTrackingSettings;
use App\Http\Request\Info\RequestInfoInterface;
use GuzzleHttp\ClientInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class ConversionTrackingHttpClient
{
    public function __construct(
        private readonly ConversionTrackingSettings $conversionTrackingSettings,
        protected ClientInterface $httpClient,
        private readonly RequestInfoInterface $requestInfo,
        private readonly LoggerInterface $logger
    )
    {
    }

    public function sendRequest(string $conversionUrl): bool
    {
        if ($this->conversionTrackingSettings->debugMode) {
            $isSuccess = (bool)random_int(0, 1);

            $this->logger->warning(
                'Emulating server side conversion request. Actual conversion tracking is not done in debug mode.',
                [
                    'conversion_url'    => $conversionUrl,
                    'emulating_success' => $isSuccess,
                ],
            );

            return $isSuccess;
        }

        try {
            $response = $this->httpClient->request(
                'GET',
                $conversionUrl,
                [
                    'allow_redirects' => true,
                    'headers'         => [
                        'X-Forwarded-For' => $this->requestInfo->getUserIp(),
                        'User-Agent'      => $this->requestInfo->getUserAgent(),
                        'Accept-Language' => $this->requestInfo->getAcceptLanguage(),
                    ],
                ],
            );

            $conversionSuccess = $response->getStatusCode() < Response::HTTP_BAD_REQUEST;

            if ($conversionSuccess) {
                return true;
            }

            throw new \RuntimeException(
                sprintf(
                    'Unexpected status code %d from conversion pixel request',
                    $response->getStatusCode(),
                ),
            );
        } catch (\Throwable $exception) {
            $this->logger->notice(
                'Send conversion tracking request failed, reason: {message}',
                [
                    'message'        => $exception->getMessage(),
                    'exception'      => ConversionTrackingHttpClientException::wrap($exception),
                    'conversion_url' => $conversionUrl,
                ],
            );
        }

        return false;
    }
}

<?php

declare(strict_types=1);

namespace App\ConversionTracking\TrackingOrder;

interface TrackingOrderInterface
{
    public function getId(): string;

    public function getType(): string;

    public function supportsOnlineConversion(int $clickCount): bool;

    public function supportsClickCount(): bool;

    public function getClickCountPrefix(): ?string;

    /**
     * @return mixed[]
     */
    public function toArray(): array;
}

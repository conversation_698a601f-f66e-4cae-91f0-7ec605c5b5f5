<?php

declare(strict_types=1);

namespace App\ConversionTracking\TrackingOrder\Type;

use App\Generic\Generator\Sha256HashGenerator;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;

readonly class AmountPerClickIdTrackingOrderFactory
{
    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private Sha256HashGenerator $sha256HashGenerator
    )
    {
    }

    /**
     * Generates order ID based on click id parameter
     */
    public function createForOne(): ?OnePerClickIdTrackingOrder
    {
        $orderId = $this->createTrackingOrderId();

        if ($orderId === null) {
            return null;
        }

        return new OnePerClickIdTrackingOrder($orderId);
    }

    private function createTrackingOrderId(): ?string
    {
        $clickId = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->clickId;

        if ($clickId === null) {
            return null;
        }

        return $this->sha256HashGenerator->hashValues(
            [
                $clickId->value,
            ],
        );
    }
}

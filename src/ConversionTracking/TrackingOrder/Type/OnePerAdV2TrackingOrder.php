<?php

declare(strict_types=1);

namespace App\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\TrackingOrder\AbstractTrackingOrder;

class OnePerAdV2TrackingOrder extends AbstractTrackingOrder
{
    public function getType(): string
    {
        return 'one_per_ad_v2';
    }

    public function getClickCountPrefix(): ?string
    {
        return 'opa2';
    }

    public function supportsOnlineConversion(int $clickCount): bool
    {
        if ($clickCount > 2) {
            return false;
        }

        return true;
    }
}

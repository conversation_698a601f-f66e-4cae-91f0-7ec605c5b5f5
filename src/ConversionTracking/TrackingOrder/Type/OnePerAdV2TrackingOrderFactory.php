<?php

declare(strict_types=1);

namespace App\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Generic\Generator\Sha256HashGenerator;

readonly class OnePerAdV2TrackingOrderFactory
{
    public function __construct(
        private ConversionTrackingRequestInterface $conversionTrackingRequest,
        private Sha256HashGenerator $sha256HashGenerator
    )
    {
    }

    /**
     * Generates order ID based on ad click
     * Combination of pageview id + ad block + ad number
     */
    public function create(
        ?int $adBlockNumber,
        ?int $adNumber
    ): ?OnePerAdV2TrackingOrder
    {
        $originalPageviewId = $this->conversionTrackingRequest->getOriginalPageviewId();

        if ($originalPageviewId === null || $adBlockNumber === null || $adNumber === null) {
            return null;
        }

        $orderId = $this->sha256HashGenerator->hashValues(
            [
                $originalPageviewId,
                $adBlockNumber,
                $adNumber,
            ],
        );

        return new OnePerAdV2TrackingOrder($orderId);
    }
}

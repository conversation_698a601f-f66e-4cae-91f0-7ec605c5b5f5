<?php

declare(strict_types=1);

namespace App\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Generic\Generator\Sha256HashGenerator;

readonly class OnePerPageviewIdTrackingOrderFactory
{
    public function __construct(
        private ConversionTrackingRequestInterface $conversionTrackingRequest,
        private Sha256HashGenerator $sha256HashGenerator
    )
    {
    }

    /**
     * Generates order ID based on ad click
     * Uses pageview id only
     */
    public function create(): ?OnePerPageviewIdTrackingOrder
    {
        $orderId = $this->createTrackingOrderId();

        if ($orderId === null) {
            return null;
        }

        return new OnePerPageviewIdTrackingOrder($orderId);
    }

    private function createTrackingOrderId(): ?string
    {
        $originalPageviewId = $this->conversionTrackingRequest->getOriginalPageviewId();

        if ($originalPageviewId === null) {
            return null;
        }

        return $this->sha256HashGenerator->hashValues(
            [
                $originalPageviewId,
            ],
        );
    }
}

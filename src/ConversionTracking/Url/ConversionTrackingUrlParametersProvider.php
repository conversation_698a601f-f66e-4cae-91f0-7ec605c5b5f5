<?php

declare(strict_types=1);

namespace App\ConversionTracking\Url;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Http\Request\GenericRequestInterface;
use App\Http\Url\AbstractCachedPersistentUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;

class ConversionTrackingUrlParametersProvider extends AbstractCachedPersistentUrlParametersProvider
{
    public function __construct(
        private readonly GenericRequestInterface $genericRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function createPersistentUrlParameters(PersistentUrlParametersPageType $pageType): array
    {
        return match ($pageType) {
            PersistentUrlParametersPageType::CONVERSION_TRACKING => $this->createForConversionTracking(),
            PersistentUrlParametersPageType::DEFAULT,
            PersistentUrlParametersPageType::NEW_SEARCH,
            PersistentUrlParametersPageType::RELATED_TERMS       => [],
        };
    }

    /**
     * @return array<string, string>
     */
    private function createForConversionTracking(): array
    {
        $pageviewId = $this->genericRequest->getPageviewId();

        if ($pageviewId === null) {
            return [];
        }

        return [
            ConversionTrackingRequestInterface::PARAMETER_ORIGINAL_PAGEVIEW_ID => $pageviewId,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\CookieConsent\EventSubscriber;

use App\Component\Generic\CookieConsent\CookieConsentLayout;
use App\Component\Generic\CookieConsent\CookieConsentRenderer;
use App\Template\Event\RenderTemplateFootersEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

readonly class InjectCookieConsentScriptEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private CookieConsentRenderer $cookieConsentRenderer
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            RenderTemplateFootersEvent::NAME => 'injectCookieConsentScript',
        ];
    }

    public function injectCookieConsentScript(RenderTemplateFootersEvent $event): void
    {
        // This check has to be done after the components have rendered in the template,
        // otherwise the flag will not have been set yet.
        if ($this->cookieConsentRenderer->hasRendered()) {
            return;
        }

        // Cookie component has not rendered
        $layout = $this->cookieConsentRenderer->getLayout() ?? CookieConsentLayout::BAR;

        $event->addItem(
            $this->cookieConsentRenderer->renderTemplate($layout),
        );

        $event->addItem(
            $this->cookieConsentRenderer->renderScripts($layout),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Debug\Component;

use App\Debug\Request\DebugRequestInterface;
use App\JsonTemplate\Component\ComponentNamespaceMapper;
use Twig\Template;

final class DebugComponentCssClass
{
    private const string KEY_TYPE          = 't';
    private const string KEY_COMPONENT     = 'c';
    private const string KEY_LAYOUT        = 'l';
    private const string KEY_SECTION       = 's';
    private const string KEY_TWIG_TEMPLATE = 'tt';

    public function __construct(
        private readonly DebugRequestInterface $debugRequest,
        private readonly ComponentNamespaceMapper $componentNamespaceMapper
    )
    {
    }

    public function getCssClass(string $componentCssClass): ?string
    {
        // Debug info enabled is required to show the debug CSS class
        if (!$this->debugRequest->debugInfo()) {
            return null;
        }

        $componentDebugInfo = $this->getComponentDebugInfo($componentCssClass);

        if ($componentDebugInfo === null) {
            return null;
        }

        $componentDebugInfo = json_encode(
            $componentDebugInfo,
            JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES,
        );
        $componentDebugInfo = base64_encode($componentDebugInfo);

        return sprintf('debug-%s', $componentDebugInfo);
    }

    /**
     * @return string[]|null
     */
    private function getComponentDebugInfo(string $componentCssClass): ?array
    {
        $componentDebugInfo = $this->getComponentTwigTemplateDebugInfo();

        if ($componentDebugInfo === null) {
            return null;
        }

        $componentNameSpace = sprintf(
            'App\\Component\\%1$s\\%2$s\%2$sComponent',
            $componentDebugInfo[self::KEY_SECTION],
            $componentDebugInfo[self::KEY_COMPONENT],
        );

        // Prevent fatal error if the class does not exist or could not be found
        $componentDebugInfo[self::KEY_TYPE] = $this->componentNamespaceMapper->getType(
            $componentNameSpace,
        ) ?? $componentCssClass;

        return $componentDebugInfo;
    }

    /**
     * @return array<string, string>|null
     */
    private function getComponentTwigTemplateDebugInfo(): ?array
    {
        foreach (debug_backtrace() as $trace) {
            $traceObject = $trace['object'] ?? null;
            $traceClass = $trace['class'] ?? null;

            if ($traceObject === null || $traceClass === null) {
                continue;
            }

            if (!str_contains($traceClass, '_TwigTemplate_')) {
                continue;
            }

            if (!$traceObject instanceof Template) {
                continue;
            }

            preg_match(
                '~^@component/(.+?)/(.+?)/layout/(.+?)/(.+?).html.twig$~',
                $traceObject->getTemplateName(),
                $matches,
            );

            if ($matches === []) {
                continue;
            }

            $componentDebugInfo = [
                self::KEY_SECTION       => $matches[1],
                self::KEY_COMPONENT     => $matches[2],
                self::KEY_LAYOUT        => $matches[3],
                self::KEY_TWIG_TEMPLATE => $matches[4],
            ];

            if (str_starts_with($componentDebugInfo[self::KEY_TWIG_TEMPLATE], '_')) {
                continue;
            }

            return $componentDebugInfo;
        }

        return null;
    }
}

<?php

declare(strict_types=1);

namespace App\Debug\Component;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;

final class DebugParentComponentTrace
{
    /** @var array<string, ComponentInterface[]> */
    private array $propertyComponents = [];

    public function __construct(
        public readonly string $componentId,
        public readonly ?ViewDataConditionCollection $conditions,
        public readonly string $conditionsProperty
    )
    {
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function addPropertyComponents(string $property, array $components): void
    {
        $this->propertyComponents[$property] = $components;
    }

    /**
     * @return array<string, ComponentInterface[]>
     */
    public function getPropertyComponents(): array
    {
        return $this->propertyComponents;
    }
}

<?php

declare(strict_types=1);

namespace App\Debug\Controller;

use App\Debug\Exception\JsonTemplatePreviewException;
use App\Debug\Request\JsonTemplatePreviewRequestInterface;
use App\Debug\Service\JsonTemplatePreviewRenderer;
use App\Search\Registry\RouteRegistryInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class DebugJsonTemplatePreviewController extends AbstractController
{
    #[Route(
        path        : '/debug/json-template-preview',
        name        : 'route_debug_json_template_preview',
        requirements: [
            'json_template' => '.+',
        ],
        methods     : ['POST']
    )]
    public function preview(
        JsonTemplatePreviewRequestInterface $request,
        JsonTemplatePreviewRenderer $jsonTemplatePreviewRenderer,
        RouteRegistryInterface $routeRegistry
    ): Response
    {
        try {
            $routeRegistry->setPaginationRoute('route_search');

            $jsonTemplate = $request->getJsonTemplate();

            return $jsonTemplatePreviewRenderer->renderPreview($jsonTemplate);
        } catch (JsonTemplatePreviewException $exception) {
            return new JsonResponse(
                ['error' => $exception->getMessage()],
                $exception->getCode(),
            );
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Debug;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class DebugModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'debug';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $container->registerForAutoconfiguration(DebugInfoProviderInterface::class)
            ->addTag('brand_website.debug.debug_info_provider');

        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_debug.yaml']);
    }
}

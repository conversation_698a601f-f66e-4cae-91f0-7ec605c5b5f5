<?php

declare(strict_types=1);

namespace App\Debug\Model;

use App\Debug\Request\DebugRequestInterface;
use Symfony\Component\Routing\Route;

final readonly class DebugRouteModel
{
    public function __construct(
        public Route $route,
        public string $path,
        public bool $hasParameters
    )
    {
    }

    public function isPostOnly(): bool
    {
        $methods = $this->route->getMethods();

        return count($methods) === 1 && in_array('POST', $methods, true);
    }

    public function getHtmlId(): string
    {
        return str_replace(['/', '{', '}'], ['-', '', ''], $this->route->getPath());
    }

    public function getPostQuery(): string
    {
        return http_build_query(
            [
                'q'                    => 'ipad',
                'debug_force_style_id' => DebugRequestInterface::DEBUG_GOOGLE_AD_STYLE_ID,
            ],
        );
    }
}

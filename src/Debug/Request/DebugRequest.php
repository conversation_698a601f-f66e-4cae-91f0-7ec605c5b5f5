<?php

declare(strict_types=1);

namespace App\Debug\Request;

use App\Ads\AdProvider;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\Office\Request\OfficeRequestInterface;

final class DebugRequest implements DebugRequestInterface
{
    private string $contentPageCollection;

    private bool $debugInfo;

    private bool $disableBingAds;

    private bool $disableContentPage;

    private bool $disableContentPages;

    private bool $disableGoogleAds;

    private bool $disableProfiler;

    private bool $enableCheq;

    private bool $enableModule;

    private bool $forceBotSearch;

    private bool $forceCacheRefresh;

    private bool $forceMockSearch;

    private bool $isAdBot;

    private bool $isFriendlyBot;

    private bool $isCustomError;

    private bool $rateLimitExceeded;

    private bool $showFixedStats;

    private bool $showGoogleTestAd;

    private bool $showUnpublishedContentPages;

    private int $forceStyleId;

    private int $statusCode;

    private string $countryCode;

    private string $forcePrimaryAdsType;

    private string $preferCsapiServer;

    private string $splitTestVariant;

    private string $forceCsaContainerPrefix;

    private string $forceCsaContainerSuffix;

    private string $userIp;

    private bool $throwException;

    private string $contentPageHomeType;

    private string $templateOverride;

    public function __construct(
        private readonly DebugRequestQueryParameterBagInterface $debugQueryBag,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer,
        private readonly OfficeRequestInterface $officeRequest,
        private readonly bool $debugThrowExceptionAllowed
    )
    {
    }

    public function debugInfo(): bool
    {
        if (!isset($this->debugInfo)) {
            $this->debugInfo = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_INFO);
        }

        return $this->debugInfo;
    }

    public function disableBingAds(): bool
    {
        if (!isset($this->disableBingAds)) {
            $this->disableBingAds = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_DISABLE_BING_ADS);
        }

        return $this->disableBingAds;
    }

    public function disableContentPage(): bool
    {
        if (!isset($this->disableContentPage)) {
            $this->disableContentPage = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_DISABLE_CONTENT_PAGE);
        }

        return $this->disableContentPage;
    }

    public function disableContentPages(): bool
    {
        if (!isset($this->disableContentPages)) {
            $this->disableContentPages = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_DISABLE_CONTENT_PAGES);
        }

        return $this->disableContentPages;
    }

    public function disableGoogleAds(): bool
    {
        if (!isset($this->disableGoogleAds)) {
            $this->disableGoogleAds = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_DISABLE_GOOGLE_ADS);
        }

        return $this->disableGoogleAds;
    }

    public function disableProfiler(): bool
    {
        if (!isset($this->disableProfiler)) {
            $this->disableProfiler = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_DISABLE_PROFILER);
        }

        return $this->disableProfiler;
    }

    public function enableCheq(): bool
    {
        if (!isset($this->enableCheq)) {
            $this->enableCheq = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_ENABLE_CHEQ);
        }

        return $this->enableCheq;
    }

    public function enableModule(): bool
    {
        if (!isset($this->enableModule)) {
            $this->enableModule = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_ENABLE_MODULE);
        }

        return $this->enableModule;
    }

    public function forceBotSearch(): bool
    {
        if (!isset($this->forceBotSearch)) {
            $this->forceBotSearch = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_FORCE_BOT_SEARCH);
        }

        return $this->forceBotSearch;
    }

    public function forceCacheRefresh(): bool
    {
        if (!isset($this->forceCacheRefresh)) {
            $this->forceCacheRefresh = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_FORCE_CACHE_REFRESH);
        }

        return $this->forceCacheRefresh;
    }

    public function forceMockSearch(): bool
    {
        if (!isset($this->forceMockSearch)) {
            $this->forceMockSearch = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_FORCE_MOCK_SEARCH);
        }

        return $this->forceMockSearch;
    }

    public function forcePrimaryAdsType(): ?string
    {
        if (!isset($this->forcePrimaryAdsType)) {
            $forcePrimaryAdsType = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_FORCE_PRIMARY_ADS_TYPE);

            $this->forcePrimaryAdsType = in_array($forcePrimaryAdsType, AdProvider::TYPES, true)
                ? $forcePrimaryAdsType
                : '';
        }

        return $this->requestPropertyNormalizer->getString($this->forcePrimaryAdsType);
    }

    public function forceStyleId(): ?int
    {
        if (!isset($this->forceStyleId)) {
            $this->forceStyleId = $this->debugQueryBag->getInt(self::PARAMETER_DEBUG_FORCE_STYLE_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->forceStyleId);
    }

    public function getContentPageCollection(): ?string
    {
        if (!isset($this->contentPageCollection)) {
            $this->contentPageCollection = $this->debugQueryBag->getString(
                self::PARAMETER_DEBUG_CONTENT_PAGE_COLLECTION,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->contentPageCollection);
    }

    public function getCountryCode(): ?string
    {
        if (!isset($this->countryCode)) {
            $this->countryCode = strtoupper(
                $this->debugQueryBag->getString(self::PARAMETER_DEBUG_COUNTRY_CODE),
            );
        }

        return $this->requestPropertyNormalizer->getString($this->countryCode);
    }

    public function getPreferCsapiServer(): ?string
    {
        if (!isset($this->preferCsapiServer)) {
            $preferCsapiServer = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_PREFER_CSAPI_SERVER);
            $this->preferCsapiServer = preg_match('/^csapi\d+\.serp\.[a-z-]+.vinden\.nl$/', $preferCsapiServer) === 1
                ? $preferCsapiServer
                : '';
        }

        return $this->requestPropertyNormalizer->getString($this->preferCsapiServer);
    }

    public function getSplitTestVariant(): ?string
    {
        if (!isset($this->splitTestVariant)) {
            $this->splitTestVariant = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_SPLIT_TEST_VARIANT);
        }

        return $this->requestPropertyNormalizer->getString($this->splitTestVariant);
    }

    public function getStatusCode(): ?int
    {
        if (!isset($this->statusCode)) {
            $this->statusCode = $this->debugQueryBag->getInt(self::PARAMETER_DEBUG_STATUS_CODE);
        }

        return $this->requestPropertyNormalizer->getInt($this->statusCode);
    }

    public function getUserIp(): ?string
    {
        if (!isset($this->userIp)) {
            $this->userIp = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_USER_IP);
        }

        return $this->requestPropertyNormalizer->getString($this->userIp);
    }

    public function isAdBot(): bool
    {
        if (!isset($this->isAdBot)) {
            $this->isAdBot = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_IS_AD_BOT);
        }

        return $this->isAdBot;
    }

    public function isFriendlyBot(): bool
    {
        if (!isset($this->isFriendlyBot)) {
            $this->isFriendlyBot = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_IS_FRIENDLY_BOT);
        }

        return $this->isFriendlyBot;
    }

    public function isCustomError(): bool
    {
        if (!isset($this->isCustomError)) {
            $this->isCustomError = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_IS_CUSTOM_ERROR);
        }

        return $this->isCustomError;
    }

    public function rateLimitExceeded(): bool
    {
        if (!isset($this->rateLimitExceeded)) {
            $this->rateLimitExceeded = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_RATE_LIMIT_EXCEEDED);
        }

        return $this->rateLimitExceeded;
    }

    public function showFixedStats(): bool
    {
        if (!isset($this->showFixedStats)) {
            $this->showFixedStats = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_SHOW_FIXED_STATS);
        }

        return $this->showFixedStats;
    }

    public function showGoogleTestAd(): bool
    {
        if (!isset($this->showGoogleTestAd)) {
            $this->showGoogleTestAd = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_SHOW_GOOGLE_TEST_AD);
        }

        return $this->showGoogleTestAd;
    }

    public function showUnpublishedContentPages(): bool
    {
        if (!isset($this->showUnpublishedContentPages)) {
            $this->showUnpublishedContentPages = $this->debugQueryBag->getBool(self::PARAMETER_DEBUG_SHOW_UNPUBLISHED_CONTENT_PAGES);
        }

        return $this->showUnpublishedContentPages;
    }

    public function getDebugForceCsaContainerPrefix(): ?string
    {
        if (!isset($this->forceCsaContainerPrefix)) {
            $this->forceCsaContainerPrefix = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_PREFIX);
        }

        return $this->requestPropertyNormalizer->getString($this->forceCsaContainerPrefix);
    }

    public function getDebugForceCsaContainerSuffix(): ?string
    {
        if (!isset($this->forceCsaContainerSuffix)) {
            $this->forceCsaContainerSuffix = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_SUFFIX);
        }

        return $this->requestPropertyNormalizer->getString($this->forceCsaContainerSuffix);
    }

    public function getContentPageHomeType(): ?string
    {
        if (!isset($this->contentPageHomeType)) {
            $this->contentPageHomeType = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_CONTENT_PAGE_HOME_TYPE);
        }

        return $this->requestPropertyNormalizer->getString($this->contentPageHomeType);
    }

    public function throwException(): bool
    {
        if (!isset($this->throwException)) {
            if (!$this->debugThrowExceptionAllowed || !$this->officeRequest->isOffice()) {
                $this->throwException = false;
            } else {
                /**
                 * The query flag disables showing the Symfony exception page.
                 * If the query parameter is truthy the default error page will be shown.
                 */
                $this->throwException = !$this->debugQueryBag->getBool(self::PARAMETER_DEBUG_THROW_EXCEPTION);
            }
        }

        return $this->throwException;
    }

    public function getTemplateOverride(): ?string
    {
        if (!isset($this->templateOverride)) {
            $this->templateOverride = $this->debugQueryBag->getString(self::PARAMETER_DEBUG_TEMPLATE_OVERRIDE);
        }

        return $this->requestPropertyNormalizer->getString($this->templateOverride);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_DEBUG_CONTENT_PAGE_COLLECTION        => $this->getContentPageCollection(),
            self::PARAMETER_DEBUG_COUNTRY_CODE                   => $this->getCountryCode(),
            self::PARAMETER_DEBUG_DISABLE_BING_ADS               => $this->disableBingAds(),
            self::PARAMETER_DEBUG_DISABLE_CONTENT_PAGE           => $this->disableContentPage(),
            self::PARAMETER_DEBUG_DISABLE_CONTENT_PAGES          => $this->disableContentPages(),
            self::PARAMETER_DEBUG_DISABLE_GOOGLE_ADS             => $this->disableGoogleAds(),
            self::PARAMETER_DEBUG_DISABLE_PROFILER               => $this->disableProfiler(),
            self::PARAMETER_DEBUG_ENABLE_CHEQ                    => $this->enableCheq(),
            self::PARAMETER_DEBUG_ENABLE_MODULE                  => $this->enableModule(),
            self::PARAMETER_DEBUG_FORCE_BOT_SEARCH               => $this->forceBotSearch(),
            self::PARAMETER_DEBUG_FORCE_CACHE_REFRESH            => $this->forceCacheRefresh(),
            self::PARAMETER_DEBUG_FORCE_MOCK_SEARCH              => $this->forceMockSearch(),
            self::PARAMETER_DEBUG_FORCE_PRIMARY_ADS_TYPE         => $this->forcePrimaryAdsType(),
            self::PARAMETER_DEBUG_FORCE_STYLE_ID                 => $this->forceStyleId(),
            self::PARAMETER_DEBUG_INFO                           => $this->debugInfo(),
            self::PARAMETER_DEBUG_IS_AD_BOT                      => $this->isAdBot(),
            self::PARAMETER_DEBUG_IS_FRIENDLY_BOT                => $this->isFriendlyBot(),
            self::PARAMETER_DEBUG_IS_CUSTOM_ERROR                => $this->isCustomError(),
            self::PARAMETER_DEBUG_PREFER_CSAPI_SERVER            => $this->getPreferCsapiServer(),
            self::PARAMETER_DEBUG_RATE_LIMIT_EXCEEDED            => $this->rateLimitExceeded(),
            self::PARAMETER_DEBUG_SHOW_FIXED_STATS               => $this->showFixedStats(),
            self::PARAMETER_DEBUG_SHOW_GOOGLE_TEST_AD            => $this->showGoogleTestAd(),
            self::PARAMETER_DEBUG_SHOW_UNPUBLISHED_CONTENT_PAGES => $this->showUnpublishedContentPages(),
            self::PARAMETER_DEBUG_SPLIT_TEST_VARIANT             => $this->getSplitTestVariant(),
            self::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_PREFIX     => $this->getDebugForceCsaContainerPrefix(),
            self::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_SUFFIX     => $this->getDebugForceCsaContainerSuffix(),
            self::PARAMETER_DEBUG_STATUS_CODE                    => $this->getStatusCode(),
            self::PARAMETER_DEBUG_USER_IP                        => $this->getUserIp(),
            self::PARAMETER_DEBUG_CONTENT_PAGE_HOME_TYPE         => $this->getContentPageHomeType(),
            self::PARAMETER_DEBUG_TEMPLATE_OVERRIDE              => $this->getTemplateOverride(),
            /*
             * The throw exception parameter must always be false to prevent it being added to the route query.
             * The value is therefore not persistent between route navigation
             */
            self::PARAMETER_DEBUG_THROW_EXCEPTION                => false,
        ];
    }
}

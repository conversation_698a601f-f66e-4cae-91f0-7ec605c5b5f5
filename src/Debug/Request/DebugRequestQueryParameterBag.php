<?php

declare(strict_types=1);

namespace App\Debug\Request;

use App\Http\Request\Main\MainRequestInterface;
use App\Http\Request\Main\MainRequestNotAvailableException;
use App\Http\Request\Manager\RequestManagerInterface;
use App\Office\Request\OfficeRequestInterface;

final readonly class DebugRequestQueryParameterBag implements DebugRequestQueryParameterBagInterface
{
    public function __construct(
        private RequestManagerInterface $requestManager,
        private OfficeRequestInterface $officeRequest,
        private MainRequestInterface $mainRequest
    )
    {
    }

    public function getBool(string $parameter): bool
    {
        if (!$this->isSupported()) {
            return false;
        }

        return $this->requestManager->queryBag()->getBool($parameter);
    }

    public function getInt(string $parameter): int
    {
        if (!$this->isSupported()) {
            return 0;
        }

        return $this->requestManager->queryBag()->getInt($parameter);
    }

    public function getString(string $parameter): string
    {
        if (!$this->isSupported()) {
            return '';
        }

        return $this->requestManager->queryBag()->getString($parameter);
    }

    private function isSupported(): bool
    {
        try {
            $this->mainRequest->getRequest();
        } catch (MainRequestNotAvailableException) {
            return false;
        }

        return $this->officeRequest->isOffice();
    }
}

<?php

declare(strict_types=1);

namespace App\Debug\Request;

use App\Debug\Exception\JsonTemplatePreviewException;
use App\Http\Request\Manager\RequestManagerInterface;
use Symfony\Component\HttpFoundation\Response;

final class JsonTemplatePreviewRequest implements JsonTemplatePreviewRequestInterface
{
    /** @var array<string, mixed> */
    private array $jsonTemplate;

    public function __construct(
        private readonly RequestManagerInterface $requestManager
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getJsonTemplate(): array
    {
        if (!isset($this->jsonTemplate)) {
            $this->initializeJsonTemplate();
        }

        return $this->jsonTemplate;
    }

    /**
     * @throws JsonTemplatePreviewException
     */
    private function initializeJsonTemplate(): void
    {
        $jsonTemplate = $this->requestManager->requestBag()->getString(self::PARAMETER_JSON_TEMPLATE);

        try {
            $jsonTemplate = json_decode($jsonTemplate, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException) {
            throw JsonTemplatePreviewException::create(
                'JSON template cannot be decoded',
                Response::HTTP_BAD_REQUEST,
            );
        }

        if (!is_array($jsonTemplate)) {
            throw JsonTemplatePreviewException::create(
                'JSON template is not an associative array',
                Response::HTTP_BAD_REQUEST,
            );
        }

        if ($jsonTemplate === []) {
            throw JsonTemplatePreviewException::create(
                'JSON template must not be empty',
                Response::HTTP_BAD_REQUEST,
            );
        }

        $this->jsonTemplate = $jsonTemplate;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_JSON_TEMPLATE => $this->getJsonTemplate(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\DependencyInjection\Compiler;

use App\JsonTemplate\Component\ComponentFactoryInterface;
use App\JsonTemplate\Component\ComponentFactoryLocator;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentNamespaceMapper;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\Component\ComponentResolverInterface;
use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\JsonTemplateModule;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\Compiler\ServiceLocatorTagPass;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

class ComponentCompilerPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        $this->compileComponentNamespaceMapper($container);
        $this->compileComponentFactoryLocator($container);
        $this->compileComponentRendererLocator($container);
        $this->compileComponentResolverLocator($container);
    }

    private function compileComponentNamespaceMapper(ContainerBuilder $container): void
    {
        $services = $this->findComponentServices(
            $container,
            JsonTemplateModule::COMPONENT_TAG,
        );
        $typeNamespaceMap = [];

        foreach ($services as $serviceClass) {
            if (!class_exists($serviceClass)) {
                throw new \RuntimeException('Component service name should match class name');
            }

            $componentImplements = class_implements($serviceClass);

            if ($componentImplements === false || !in_array(ComponentInterface::class, $componentImplements, true)) {
                throw new \RuntimeException(
                    sprintf('Service "%s" should implement %s', $serviceClass, ComponentInterface::class),
                );
            }

            $namespaceType = $serviceClass::getType();

            if (array_key_exists($namespaceType, $typeNamespaceMap)) {
                throw new \RuntimeException(sprintf('Component type "%s" is already defined', $namespaceType));
            }

            $typeNamespaceMap[$namespaceType] = $serviceClass;
        }

        $container
            ->findDefinition(ComponentNamespaceMapper::class)
            ->addArgument($typeNamespaceMap);
    }

    private function compileComponentFactoryLocator(ContainerBuilder $container): void
    {
        $services = $this->findComponentServices(
            $container,
            JsonTemplateModule::COMPONENT_FACTORY_TAG,
        );
        $serviceReferences = [];

        foreach ($services as $serviceClass) {
            if (!class_exists($serviceClass)) {
                throw new \RuntimeException('Component factory service name should match class name');
            }

            $serviceClassImplements = class_implements($serviceClass);

            if ($serviceClassImplements === false
                || !in_array(ComponentFactoryInterface::class, $serviceClassImplements, true)
            ) {
                throw new \RuntimeException(
                    sprintf('Service "%s" should implement %s', $serviceClass, ComponentFactoryInterface::class),
                );
            }

            $componentNamespace = $serviceClass::getSupportedComponent();

            if (array_key_exists($componentNamespace, $serviceReferences)) {
                throw new \RuntimeException(
                    sprintf('Component namespace "%s" already has a factory defined', $componentNamespace),
                );
            }

            $serviceReferences[$componentNamespace] = new Reference($serviceClass);
        }

        $container
            ->findDefinition(ComponentFactoryLocator::class)
            ->addArgument(
                ServiceLocatorTagPass::register($container, $serviceReferences),
            );
    }

    private function compileComponentRendererLocator(ContainerBuilder $container): void
    {
        $services = $this->findComponentServices(
            $container,
            JsonTemplateModule::COMPONENT_RENDERER_TAG,
        );
        $serviceReferences = [];

        foreach ($services as $serviceClass) {
            $serviceReferences[$serviceClass] = new Reference($serviceClass);
        }

        $container
            ->findDefinition(ComponentRendererLocator::class)
            ->addArgument(
                ServiceLocatorTagPass::register($container, $serviceReferences),
            );
    }

    private function compileComponentResolverLocator(ContainerBuilder $container): void
    {
        $services = $this->findComponentServices(
            $container,
            JsonTemplateModule::COMPONENT_RESOLVER_TAG,
        );
        $serviceReferences = [];

        foreach ($services as $serviceClass) {
            if (!class_exists($serviceClass)) {
                throw new \RuntimeException('Component resolver service name should match class name');
            }

            $serviceClassImplements = class_implements($serviceClass);

            if ($serviceClassImplements === false
                || !in_array(ComponentResolverInterface::class, $serviceClassImplements, true)
            ) {
                throw new \RuntimeException(
                    sprintf('Service "%s" should implement %s', $serviceClass, ComponentResolverInterface::class),
                );
            }

            $componentType = $serviceClass::getSupportedComponent()::getType();

            if (array_key_exists($componentType, $serviceReferences)) {
                throw new \RuntimeException(
                    sprintf('Component type "%s" already has a resolver defined', $componentType),
                );
            }

            $serviceReferences[$componentType] = new Reference($serviceClass);
        }

        $container
            ->findDefinition(ComponentResolverLocator::class)
            ->addArgument(
                ServiceLocatorTagPass::register($container, $serviceReferences),
            );
    }

    /**
     * @return string[]
     */
    private function findComponentServices(ContainerBuilder $container, string $taggedServiceId): array
    {
        $services = $container->findTaggedServiceIds($taggedServiceId);

        // Ignore DevelopBundle components
        $services = array_filter(
            array_keys($services),
            static fn (string $serviceClass): bool => !str_contains($serviceClass, 'DevelopBundle'),
        );

        return $services;
    }
}

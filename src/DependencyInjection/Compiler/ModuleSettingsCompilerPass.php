<?php

declare(strict_types=1);

namespace App\DependencyInjection\Compiler;

use App\DependencyInjection\GenericDependencyInjectionModules;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\ModuleSettings\ModuleSettingsFactoryInterface;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

class ModuleSettingsCompilerPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        /** @var array<string, mixed[]> $moduleSettingsFactoryServices */
        $moduleSettingsFactoryServices = $container->findTaggedServiceIds(
            GenericDependencyInjectionModules::MODULE_SETTINGS_FACTORIES_TAG,
        );

        /**
         * @var class-string<ModuleSettingsFactoryInterface|ArtemisModuleSettingsFactoryInterface> $moduleSettingsFactoryServiceClass
         */
        foreach (array_keys($moduleSettingsFactoryServices) as $moduleSettingsFactoryServiceClass) {
            $moduleName = $moduleSettingsFactoryServiceClass::getModuleName();
            $isArtemisModuleSettingsFactory = $this->instanceOfArtemisModuleSettingsFactory($moduleSettingsFactoryServiceClass);

            $moduleSettingsServiceClass = str_replace(
                'Factory',
                '',
                $moduleSettingsFactoryServiceClass,
            );

            if ($isArtemisModuleSettingsFactory) {
                // New
                $arguments = [];
            } else {
                // Old
                $arguments = [
                    '$projectModuleConfig' => sprintf('%%brand_website.%s.config%%', $moduleName),
                ];
            }

            // Module settings are loaded lazy to prevent a request is required in all integration tests
            $container->getDefinition($moduleSettingsServiceClass)
                ->setFactory(
                    [
                        new Reference($moduleSettingsFactoryServiceClass),
                        'create',
                    ],
                )
                ->setLazy(true)
                ->setArguments($arguments)
                ->addTag(GenericDependencyInjectionModules::MODULE_SETTINGS_TAG, ['module' => $moduleName]);
        }
    }

    private function instanceOfArtemisModuleSettingsFactory(string $moduleSettingsFactoryServiceClass): bool
    {
        return is_a(
            $moduleSettingsFactoryServiceClass,
            ArtemisModuleSettingsFactoryInterface::class,
            true,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\DependencyInjection;

class DependencyInjectionModules
{
    private static GenericDependencyInjectionModules $instance;

    public static function getInstance(): GenericDependencyInjectionModules
    {
        if (!isset(self::$instance)) {
            self::$instance = new GenericDependencyInjectionModules(
                dirname(__DIR__),
                'App\%s\%s',
            );
        }

        return self::$instance;
    }
}

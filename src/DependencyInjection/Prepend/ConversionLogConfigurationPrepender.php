<?php

declare(strict_types=1);

namespace App\DependencyInjection\Prepend;

use Symfony\Component\DependencyInjection\ContainerBuilder;

class ConversionLogConfigurationPrepender
{
    /**
     * @param array<string, mixed> $config
     */
    public static function prepend(ContainerBuilder $container, array $config): void
    {
        if (!(bool)$config['tracking']['conversion_log']['file_enabled']) {
            return;
        }

        $container->prependExtensionConfig(
            'monolog',
            [
                'channels' => ['conversion_log'],
                'handlers' => [
                    'conversion_log' => [
                        'type'      => 'rotating_file',
                        'path'      => '%kernel.logs_dir%/%kernel.environment%-conversion-log.log',
                        'level'     => 'info',
                        'max_files' => 5,
                        'channels'  => ['conversion_log'],
                        'formatter' => 'monolog.formatter.json',
                    ],
                ],
            ],
        );
    }
}

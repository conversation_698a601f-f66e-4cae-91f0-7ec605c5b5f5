<?php

declare(strict_types=1);

namespace App\DependencyInjection\Prepend;

use App\Error\Controller\ErrorController;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class FrameworkConfigurationPrepender
{
    public static function prepend(ContainerBuilder $container): void
    {
        $container->prependExtensionConfig(
            'framework',
            [
                'error_controller' => sprintf('%s::show', ErrorController::class),
            ],
        );
    }
}

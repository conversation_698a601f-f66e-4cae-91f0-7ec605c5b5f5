<?php

declare(strict_types=1);

namespace App\DependencyInjection\Prepend;

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Visymo\MonologExtensionsBundle\Formatter\LogstashFormatter;

class JavaScriptErrorLogConfigurationPrepender
{
    /**
     * @param array<string, mixed> $config
     */
    public static function prepend(ContainerBuilder $container, array $config): void
    {
        self::prependJavaScriptErrorMonologFileHandler($container, $config);
        self::prependJavaScriptErrorMonologLogstashHandler($container, $config);
    }

    /**
     * @param array<string, mixed> $config
     */
    private static function prependJavaScriptErrorMonologFileHandler(ContainerBuilder $container, array $config): void
    {
        if (!(bool)$config['javascript_error']['error_log']['file_enabled']) {
            return;
        }

        $container->prependExtensionConfig(
            'monolog',
            [
                'channels' => ['javascript_error'],
                'handlers' => [
                    'javascript_error' => [
                        'type'      => 'rotating_file',
                        'path'      => '%kernel.logs_dir%/%kernel.environment%-javascript_error.log',
                        'level'     => 'info',
                        'max_files' => 5,
                        'channels'  => ['javascript_error'],
                    ],
                ],
            ],
        );
    }

    /**
     * @param array<string, mixed> $config
     */
    private static function prependJavaScriptErrorMonologLogstashHandler(
        ContainerBuilder $container,
        array $config
    ): void
    {
        if (!(bool)$config['javascript_error']['error_log']['logstash_enabled']) {
            return;
        }

        $container->prependExtensionConfig(
            'monolog',
            [
                'channels' => ['javascript_error'],
                'handlers' => [
                    'javascript_error_buffer'  => [
                        'type'     => 'buffer',
                        'handler'  => 'javascript_error_handler',
                        'channels' => ['javascript_error'],
                    ],
                    'javascript_error_handler' => [
                        'type'              => 'socket',
                        'connection_string' => $config['javascript_error']['error_log']['logstash_endpoint'],
                        'persistent'        => true,
                        'formatter'         => LogstashFormatter::class,
                        'level'             => 'info',
                    ],
                ],
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\DependencyInjection\Prepend;

use <PERSON>ymfony\Component\DependencyInjection\ContainerBuilder;
use Visymo\MonologExtensionsBundle\Formatter\LogstashFormatter;

class JavaScriptRelatedTermsViewLogConfigurationPrepender
{
    /**
     * @param array<string, mixed> $config
     */
    public static function prepend(ContainerBuilder $container, array $config): void
    {
        self::prependJavaScriptRelatedTermsViewMonologFileHandler($container, $config);
        self::prependJavaScriptRelatedTermsViewMonologLogstashHandler($container, $config);
    }

    /**
     * @param array<string, mixed> $config
     */
    private static function prependJavaScriptRelatedTermsViewMonologFileHandler(
        ContainerBuilder $container,
        array $config
    ): void
    {
        if (!(bool)$config['javascript_related_terms']['view_log']['file_enabled']) {
            return;
        }

        $container->prependExtensionConfig(
            'monolog',
            [
                'channels' => ['javascript_related_terms_view'],
                'handlers' => [
                    'javascript_related_terms_view' => [
                        'type'      => 'rotating_file',
                        'path'      => '%kernel.logs_dir%/%kernel.environment%-javascript_related_terms_view.log',
                        'level'     => 'info',
                        'max_files' => 5,
                        'channels'  => ['javascript_related_terms_view'],
                    ],
                ],
            ],
        );
    }

    /**
     * @param array<string, mixed> $config
     */
    private static function prependJavaScriptRelatedTermsViewMonologLogstashHandler(
        ContainerBuilder $container,
        array $config
    ): void
    {
        if (!(bool)$config['javascript_related_terms']['view_log']['logstash_enabled']) {
            return;
        }

        $container->prependExtensionConfig(
            'monolog',
            [
                'channels' => ['javascript_related_terms_view'],
                'handlers' => [
                    'javascript_related_terms_view_buffer'  => [
                        'type'     => 'buffer',
                        'handler'  => 'javascript_related_terms_view_handler',
                        'channels' => ['javascript_related_terms_view'],
                    ],
                    'javascript_related_terms_view_handler' => [
                        'type'              => 'socket',
                        'connection_string' => $config['javascript_related_terms']['view_log']['logstash_endpoint'],
                        'persistent'        => true,
                        'formatter'         => LogstashFormatter::class,
                        'level'             => 'info',
                    ],
                ],
            ],
        );
    }
}

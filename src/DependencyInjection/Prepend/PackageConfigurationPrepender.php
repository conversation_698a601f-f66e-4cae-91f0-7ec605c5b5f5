<?php

declare(strict_types=1);

namespace App\DependencyInjection\Prepend;

use Symfony\Component\Config\Resource\FileResource;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\Yaml\Yaml;

class PackageConfigurationPrepender
{
    public static function prepend(ContainerBuilder $container): void
    {
        $environment = $container->getParameter('kernel.environment');

        // Because prepending is first-in-last-out, the environment-specific configurations should be added first
        if (is_string($environment)) {
            self::loadConfigurationsFrom($container, sprintf('%s/../../Resources/config/packages/%s', __DIR__, $environment));
        }

        self::loadConfigurationsFrom($container, sprintf('%s/../../Resources/config/packages', __DIR__));
    }

    private static function loadConfigurationsFrom(ContainerBuilder $container, string $directory): void
    {
        /** @var \DirectoryIterator|\DirectoryIterator[] $configDirectoryIterator */
        $configDirectoryIterator = new \DirectoryIterator($directory);

        foreach ($configDirectoryIterator as $configFile) {
            if ($configFile->isDot() || $configFile->isDir()) {
                continue;
            }

            $configurationFile = sprintf('%s/%s', $directory, $configFile->getFilename());

            // load and parse YAML
            $config = (array)Yaml::parseFile($configurationFile, Yaml::PARSE_CONSTANT);

            foreach ($config as $configurationKey => $configurationValues) {
                if (!$container->hasExtension($configurationKey)) {
                    throw new \RuntimeException(
                        sprintf(
                            'Configuration key "%s" could not be loaded from "%s". Extension "%s" was not found in the container.',
                            $configurationKey,
                            $configurationFile,
                            $configurationKey,
                        ),
                    );
                }

                $container->prependExtensionConfig(
                    $configurationKey,
                    $configurationValues,
                );

                if ($configFile->getRealPath() !== false) {
                    $container->addResource(new FileResource($configFile->getRealPath()));
                }
            }
        }
    }
}

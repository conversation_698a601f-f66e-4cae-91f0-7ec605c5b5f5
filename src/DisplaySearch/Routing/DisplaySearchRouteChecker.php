<?php

declare(strict_types=1);

namespace App\DisplaySearch\Routing;

use App\DisplaySearch\Settings\DisplaySearchSettings;
use App\Generic\Routing\RouteCheckerInterface;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class DisplaySearchRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_display_search';

    public function __construct(
        private DisplaySearchSettings $displaySearchSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->displaySearchSettings->enabled;
    }
}

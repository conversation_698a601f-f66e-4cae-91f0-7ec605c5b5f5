<?php

declare(strict_types=1);

namespace App\DisplaySearch\Settings;

use App\Debug\Request\DebugRequestInterface;
use App\DisplaySearch\DisplaySearchModule;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class DisplaySearchSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return DisplaySearchModule::getModuleName();
    }

    public function create(): DisplaySearchSettings
    {
        if ($this->debugRequest->enableModule()) {
            return new DisplaySearchSettings(
                enabled: true,
            );
        }

        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['display_search'] ?? [];

        return new DisplaySearchSettings(
            enabled: $this->isModuleEnabled($moduleConfig),
        );
    }
}

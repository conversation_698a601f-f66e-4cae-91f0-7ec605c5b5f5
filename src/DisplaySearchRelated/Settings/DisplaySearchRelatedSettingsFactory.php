<?php

declare(strict_types=1);

namespace App\DisplaySearchRelated\Settings;

use App\Debug\Request\DebugRequestInterface;
use App\DisplaySearchRelated\DisplaySearchRelatedModule;
use App\GoogleCsa\Helper\GoogleStyleIdValidator;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\ModuleSettings\Exception\InvalidModuleConfigException;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class DisplaySearchRelatedSettingsFactory
    extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    private const string KEY_RELATED_FALLBACK_ENABLED = 'related_fallback_enabled';
    private const string KEY_STYLE_ID_DESKTOP         = 'style_id_desktop';
    private const string KEY_STYLE_ID_MOBILE          = 'style_id_mobile';
    private const string KEY_WEB_STYLE_ID_DESKTOP     = 'web_style_id_desktop';
    private const string KEY_WEB_STYLE_ID_MOBILE      = 'web_style_id_mobile';

    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return DisplaySearchRelatedModule::getModuleName();
    }

    public function create(): DisplaySearchRelatedSettings
    {
        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['display_search_related'];

        if ($this->debugRequest->enableModule()) {
            $styleId = $this->debugRequest->forceStyleId() ?? DebugRequestInterface::DEBUG_GOOGLE_AD_STYLE_ID;

            return new DisplaySearchRelatedSettings(
                enabled               : true,
                styleIdDesktop        : (int)($moduleConfig[self::KEY_STYLE_ID_DESKTOP] ?? $styleId),
                styleIdMobile         : (int)($moduleConfig[self::KEY_STYLE_ID_MOBILE] ?? $styleId),
                relatedFallbackEnabled: (bool)($moduleConfig[self::KEY_RELATED_FALLBACK_ENABLED] ?? false),
                webStyleIdDesktop     : (int)($moduleConfig[self::KEY_WEB_STYLE_ID_DESKTOP] ?? $styleId),
                webStyleIdMobile      : (int)($moduleConfig[self::KEY_WEB_STYLE_ID_MOBILE] ?? $styleId),
            );
        }

        if (!$this->isModuleEnabled($moduleConfig)) {
            return new DisplaySearchRelatedSettings(
                enabled               : false,
                styleIdDesktop        : null,
                styleIdMobile         : null,
                relatedFallbackEnabled: false,
                webStyleIdDesktop     : null,
                webStyleIdMobile      : null,
            );
        }

        $settings = new DisplaySearchRelatedSettings(
            enabled               : true,
            styleIdDesktop        : (int)$moduleConfig[self::KEY_STYLE_ID_DESKTOP],
            styleIdMobile         : (int)$moduleConfig[self::KEY_STYLE_ID_MOBILE],
            relatedFallbackEnabled: (bool)$moduleConfig[self::KEY_RELATED_FALLBACK_ENABLED],
            webStyleIdDesktop     : (int)$moduleConfig[self::KEY_WEB_STYLE_ID_DESKTOP],
            webStyleIdMobile      : (int)$moduleConfig[self::KEY_WEB_STYLE_ID_MOBILE],
        );
        $this->validateSettings($settings);

        return $settings;
    }

    private function validateSettings(DisplaySearchRelatedSettings $settings): void
    {
        if (!$settings->enabled) {
            return;
        }

        // DSR
        $this->validateStyleId($settings->styleIdDesktop, self::KEY_STYLE_ID_DESKTOP);
        $this->validateStyleId($settings->styleIdMobile, self::KEY_STYLE_ID_MOBILE);

        // DSRW
        $this->validateStyleId($settings->webStyleIdDesktop, self::KEY_WEB_STYLE_ID_DESKTOP);
        $this->validateStyleId($settings->webStyleIdMobile, self::KEY_WEB_STYLE_ID_MOBILE);
    }

    private function validateStyleId(?int $styleId, string $parameter): void
    {
        if ($styleId === null) {
            throw InvalidModuleConfigException::create(
                self::getModuleName(),
                sprintf('"%s.%s" requires a style ID, null given', self::getModuleName(), $parameter),
            );
        }

        if (!GoogleStyleIdValidator::isValid($styleId)) {
            throw InvalidModuleConfigException::create(
                self::getModuleName(),
                sprintf('"%s.%s" requires a valid style ID, "%s" given', self::getModuleName(), $parameter, $styleId),
            );
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Domain\Settings;

final readonly class DomainSettings
{
    public const string KEY_HOST                             = 'host';
    public const string KEY_JAVASCRIPT_RELATED_TERMS_ENABLED = 'javascript_related_terms_enabled';

    public function __construct(
        public string $host,
        public bool $javaScriptRelatedTermsEnabled
    )
    {
    }

    public function getUrl(): string
    {
        return sprintf('https://%s', $this->host);
    }

    /**
     * @return mixed[]
     */
    public function toArray(): array
    {
        return [
            self::KEY_HOST                             => $this->host,
            self::KEY_JAVASCRIPT_RELATED_TERMS_ENABLED => $this->javaScriptRelatedTermsEnabled,
        ];
    }
}

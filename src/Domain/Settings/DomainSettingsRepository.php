<?php

declare(strict_types=1);

namespace App\Domain\Settings;

use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;

readonly class DomainSettingsRepository
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private DomainSettingsFactory $domainSettingsFactory
    )
    {
    }

    public function getJavaScriptRelatedTermsEnabledDomainByLocale(string $locale): ?string
    {
        $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
        $domainsConfig = $websiteConfiguration->getDomainsConfig();

        foreach ($domainsConfig as $domain => $domainConfig) {
            foreach ($domainConfig['locales'] as $domainLocale) {
                if ($domainLocale['locale'] !== $locale) {
                    continue;
                }

                if ((bool)$domainConfig['javascript_related_terms_enabled']) {
                    return $domain;
                }
            }
        }

        return null;
    }

    public function getJavaScriptRelatedTermsEnabledDomain(): ?string
    {
        $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
        $domainsConfig = $websiteConfiguration->getDomainsConfig();

        foreach ($domainsConfig as $domain => $domainConfig) {
            if ((bool)$domainConfig['javascript_related_terms_enabled']) {
                return $domain;
            }
        }

        return null;
    }

    public function getFirstDomain(): string
    {
        $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
        $domainsConfig = $websiteConfiguration->getDomainsConfig();

        return (string)array_key_first($domainsConfig);
    }

    public function getByDomain(string $domain): DomainSettings
    {
        $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
        $domainsConfig = $websiteConfiguration->getDomainConfig($domain);

        return $this->domainSettingsFactory->create($domainsConfig);
    }
}

<?php

declare(strict_types=1);

namespace App\Error\Controller;

use App\Debug\Request\DebugRequest;
use App\GoogleCsa\StyleId\GoogleCsaErrorStyleIdHelper;
use App\JsonTemplate\Component\ComponentRenderRegistry;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use App\JsonTemplate\View\JsonTemplateViewHandler;
use App\Statistics\Helper\StatisticsLogHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;

final class ErrorController extends AbstractController
{
    public const string LOADBALANCER_IS_CUSTOM_ERROR_PAGE = 'X-Loadbalancer-Is-Custom-Error';

    public function __construct(
        private readonly StatisticsLogHelper $statisticsLogHelper,
        private readonly ComponentRenderRegistry $componentRenderRegistry,
        private readonly JsonTemplateViewHandler $jsonTemplateViewHandler,
        private readonly JsonTemplateViewFactory $jsonTemplateViewFactory,
        private readonly GoogleCsaErrorStyleIdHelper $googleCsaErrorStyleIdHelper,
        private readonly DebugRequest $debugRequest
    )
    {
    }

    public function show(?\Throwable $exception = null): Response
    {
        $this->statisticsLogHelper->logError();
        $this->componentRenderRegistry->reset();

        if ($exception instanceof \Throwable && $this->debugRequest->throwException()) {
            throw $exception;
        }

        $response = new Response(
            status : $exception instanceof HttpExceptionInterface
                         ? $exception->getStatusCode()
                         : Response::HTTP_INTERNAL_SERVER_ERROR,
            headers: [
                         self::LOADBALANCER_IS_CUSTOM_ERROR_PAGE => '1',
                     ],
        );

        $this->googleCsaErrorStyleIdHelper->handleStyleId();

        $showNotFoundPage = in_array(
            $response->getStatusCode(),
            [Response::HTTP_BAD_REQUEST, Response::HTTP_NOT_FOUND],
            true,
        );

        $view = $this->jsonTemplateViewFactory->create(
            $showNotFoundPage
                ? '@themeJson/error/error_page_not_found.json'
                : '@themeJson/error/error.json',
            $response,
        );

        return $this->jsonTemplateViewHandler->handle($view);
    }
}

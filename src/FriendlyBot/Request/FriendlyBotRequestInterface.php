<?php

declare(strict_types=1);

namespace App\FriendlyBot\Request;

use App\FriendlyBot\Bot\FriendlyBot;
use App\Http\Request\RequestInterface;

interface FriendlyBotRequestInterface extends RequestInterface
{
    public const string HEADER_X_LOADBALANCER_IS_FRIENDLY_BOT = 'x-loadbalancer-is-friendly-bot';
    public const string HEADER_X_IS_APIFY                     = 'x-is-apify';

    public const string KEY_IS_FRIENDLY_BOT = 'is_friendly_bot';
    public const string KEY_FRIENDLY_BOT    = 'friendly_bot';

    public function isFriendlyBot(): bool;

    public function getFriendlyBot(): ?FriendlyBot;
}

<?php

declare(strict_types=1);

namespace App\Generic\Device\Detection;

use App\Generic\Device\Device;
use Detection\Exception\MobileDetectException;

readonly class MobileDeviceDetection
{
    public function __construct(
        private MobileDetectFactory $mobileDetectFactory
    )
    {
    }

    /**
     * @throws MobileDetectException
     */
    public function getDevice(): ?Device
    {
        $mobileDetect = $this->mobileDetectFactory->create();

        // Check tablet first, because MobileDetect sometimes considers a tablet to be mobile as well
        if ($mobileDetect->isTablet()) {
            return Device::TABLET;
        }

        if ($mobileDetect->isMobile()) {
            return Device::MOBILE;
        }

        return null;
    }
}

<?php

declare(strict_types=1);

namespace App\Generic\Device;

use App\Generic\Device\Exception\UnknownDeviceValueException;

enum Device: string
{
    case MOBILE  = 'mobile';
    case TABLET  = 'tablet';
    case DESKTOP = 'desktop';

    private const string SHORT_VALUE_MOBILE  = 'm';
    private const string SHORT_VALUE_TABLET  = 't';
    private const string SHORT_VALUE_DESKTOP = 'c';

    public function getLabel(): string
    {
        return match ($this) {
            self::MOBILE  => 'Mobile',
            self::TABLET  => 'Tablet',
            self::DESKTOP => 'Desktop',
        };
    }

    public function getShortValue(): string
    {
        return match ($this) {
            self::MOBILE  => self::SHORT_VALUE_MOBILE,
            self::TABLET  => self::SHORT_VALUE_TABLET,
            self::DESKTOP => self::SHORT_VALUE_DESKTOP,
        };
    }

    public static function fromShortValue(string $shortValue): self
    {
        return match ($shortValue) {
            self::SHORT_VALUE_MOBILE  => self::MOBILE,
            self::SHORT_VALUE_TABLET  => self::TABLET,
            self::SHORT_VALUE_DESKTOP => self::DESKTOP,
            default                   => throw UnknownDeviceValueException::createForShortValue($shortValue),
        };
    }

    public static function tryFromShortValue(string $shortValue): ?self
    {
        try {
            return self::fromShortValue($shortValue);
        } catch (UnknownDeviceValueException) {
            return null;
        }
    }

    /**
     * @return string[]
     */
    public static function getSupportedShortValues(): array
    {
        return [
            self::SHORT_VALUE_MOBILE,
            self::SHORT_VALUE_TABLET,
            self::SHORT_VALUE_DESKTOP,
        ];
    }
}

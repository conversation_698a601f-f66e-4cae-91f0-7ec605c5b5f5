<?php

declare(strict_types=1);

namespace App\Generic\Generator;

use Visymo\Filesystem\File\FileInterface;

final class Base64Generator
{
    public function generate(FileInterface $file): string
    {
        return match ($file->getExtension()) {
            'gif',
            'ico',
            'jpg',
            'png'   => $this->generateFromImage($file),
            'svg'   => $this->generateFromImage($file, 'svg+xml'),
            default => throw new \InvalidArgumentException(
                sprintf('Unsupported base64 generator file extension: %s', $file->getExtension()),
            ),
        };
    }

    private function generateFromImage(FileInterface $file, ?string $type = null): string
    {
        return sprintf(
            'data:image/%s;base64,%s',
            $type ?? $file->getExtension(),
            base64_encode($file->readContent()),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Generic\Response;

use Symfony\Component\HttpFoundation\Response;

class UncachedPixelResponse extends UncachedResponse
{
    public function __construct()
    {
        $pixelContent = base64_decode('R0lGODlhAQABAJAAAP8AAAAAACH5BAUQAAAALAAAAAABAAEAAAICBAEAOw==', true);

        if ($pixelContent === false) {
            $pixelContent = null;
        }

        parent::__construct(
            $pixelContent,
            Response::HTTP_OK,
            [
                'Content-Type' => 'image/gif',
            ],
        );
    }
}

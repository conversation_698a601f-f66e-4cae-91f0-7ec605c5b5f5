<?php

declare(strict_types=1);

namespace App\Generic\Sorting;

class ArraySorter
{
    /**
     * @param mixed[] $array
     *
     * @return mixed[]
     */
    public function deepSortArrayKeys(array $array): array
    {
        ksort($array);

        foreach (array_keys($array) as $key) {
            if (is_array($array[$key])) {
                $array[$key] = $this->deepSortArrayKeys($array[$key]);
            }
        }

        return $array;
    }
}

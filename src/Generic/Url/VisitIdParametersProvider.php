<?php

declare(strict_types=1);

namespace App\Generic\Url;

use App\Http\Request\GenericRequestInterface;
use App\Http\Url\AbstractCachedPersistentUrlParametersProvider;
use App\Http\Url\PersistentUrlParametersPageType;

class VisitIdParametersProvider extends AbstractCachedPersistentUrlParametersProvider
{
    public function __construct(
        private readonly GenericRequestInterface $genericRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function createPersistentUrlParameters(PersistentUrlParametersPageType $pageType): array
    {
        $persistentUrlParameters = [];
        $visitId = $this->genericRequest->getVisitId();

        if ($visitId !== null) {
            $persistentUrlParameters = [
                GenericRequestInterface::PARAMETER_VISIT_ID => $visitId,
            ];
        }

        return match ($pageType) {
            PersistentUrlParametersPageType::CONVERSION_TRACKING,
            PersistentUrlParametersPageType::DEFAULT,
            PersistentUrlParametersPageType::NEW_SEARCH,
            PersistentUrlParametersPageType::RELATED_TERMS => $persistentUrlParameters,
        };
    }
}

<?php

declare(strict_types=1);

namespace App\GoogleCsa\EventSubscriber;

use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\GoogleCsa\StatisticsProvider\GoogleAdSenseStatisticsProvider;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class GoogleAdSenseStatisticsLogEventSubscriber implements EventSubscriberInterface
{
    private const string STATISTICS_KEY_AMOUNT_REQUESTED     = 'amount_requested';
    private const string STATISTICS_KEY_TOP_AMOUNT_REQUESTED = 'top_amount_requested';
    private const string STATISTICS_KEY_RELATED_TERMS        = 'related_terms';
    private const string STATISTICS_KEY_FALLBACK_ENABLED     = 'fallback_enabled';
    private const string STATISTICS_KEY_REQUEST_TIMESTAMP    = 'request_timestamp';
    private const string STATISTICS_KEY_CONTRACT_TYPE        = 'contract_type';

    public function __construct(
        private readonly GoogleCsaRegistry $googleCsaRegistry,
        private readonly DisplaySearchRelatedSettings $displaySearchRelatedSettings,
        private readonly WebsiteSettingsHelper $websiteSettingsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            StatisticsLogCreateEvent::NAME => ['onStatisticsLogCreate'],
        ];
    }

    public function onStatisticsLogCreate(StatisticsLogCreateEvent $event): void
    {
        $googleCsa = $this->googleCsaRegistry->getGoogleCsa();

        if ($googleCsa === null || !$googleCsa->hasUnits()) {
            return;
        }

        $statistics = [];
        $adsAmount = $googleCsa->ads()->getTotalAdsAmount();
        $adsTopAmount = $googleCsa->ads()->getTopUnit()?->getAmount() ?? 0;
        $relatedSearchAmount = $googleCsa->relatedSearch()->getTotalRelatedSearchAmount();

        if ($adsAmount > 0) {
            $statistics[self::STATISTICS_KEY_REQUEST_TIMESTAMP] = date('c');
            $statistics[self::STATISTICS_KEY_AMOUNT_REQUESTED] = $adsAmount;
        }

        if ($adsTopAmount > 0) {
            $statistics[self::STATISTICS_KEY_TOP_AMOUNT_REQUESTED] = $adsTopAmount;
        }

        if ($relatedSearchAmount > 0) {
            $statistics[self::STATISTICS_KEY_RELATED_TERMS] = [
                self::STATISTICS_KEY_REQUEST_TIMESTAMP => date('c'),
                self::STATISTICS_KEY_AMOUNT_REQUESTED  => $relatedSearchAmount,
                self::STATISTICS_KEY_FALLBACK_ENABLED  => $this->displaySearchRelatedSettings->relatedFallbackEnabled,
            ];
        }

        if ($statistics === []) {
            return;
        }

        $googleAdSense = $this->websiteSettingsHelper->getSettings()->getGoogleAdSense();
        $statistics[self::STATISTICS_KEY_CONTRACT_TYPE] = $googleAdSense->getContractType()->value;

        $event->addStatistics(
            [
                GoogleAdSenseStatisticsProvider::getContextKey() => $statistics,
            ],
        );
    }
}

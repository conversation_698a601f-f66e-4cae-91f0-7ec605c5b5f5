<?php

declare(strict_types=1);

namespace App\GoogleCsa\EventSubscriber;

use App\ConversionTracking\Endpoint\GoogleAdSenseOnline\GoogleAdSenseOnlineConversionUrlGenerator;
use App\GoogleCsa\Helper\GoogleCsaClickTrackUrlHelper;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameterInterface;
use App\Http\Response\HeaderLink\HeaderLink;
use App\Http\Response\HeaderLink\HeaderLinkAs;
use App\Http\Response\HeaderLink\HeaderLinkFetchPriority;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\Http\Response\HeaderLink\HeaderLinkRel;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Environment;
use Visymo\GoogleCsa\GoogleCsaInterface;
use Visymo\GoogleCsa\GoogleCsaRenderer;

class InjectGoogleCsaScriptsEventSubscriber implements EventSubscriberInterface
{
    private bool $hasRenderedGoogleScripts = false;

    public function __construct(
        private readonly GoogleCsaRegistry $googleCsaRegistry,
        private readonly GoogleCsaClickTrackUrlHelper $googleCsaClickTrackUrlHelper,
        private readonly GoogleCsaStyleIdParameterInterface $googleCsaStyleIdParameter,
        private readonly GoogleCsaRenderer $googleCsaRenderer,
        private readonly Environment $twig,
        private readonly GoogleAdSenseOnlineConversionUrlGenerator $googleAdSenseOnlineConversionUrlGenerator,
        private readonly HeaderLinkRegistry $headerLinkRegistry,
        private readonly WebsiteSettingsHelper $websiteSettingsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            // Google CSA script requires other JavaScript entries to be loaded first
            RenderTemplateHeadersEvent::NAME => ['renderTemplateHeaders', 1000],
        ];
    }

    public function renderTemplateHeaders(RenderTemplateHeadersEvent $event): void
    {
        if ($this->hasRenderedGoogleScripts) {
            return;
        }

        $googleCsa = $this->googleCsaRegistry->getGoogleCsa();

        if ($googleCsa === null || !$googleCsa->hasUnits()) {
            return;
        }

        $this->headerLinkRegistry->add(
            new HeaderLink(
                url          : 'https://www.google.com/adsense/search/ads.js',
                rel          : HeaderLinkRel::PRELOAD,
                as           : HeaderLinkAs::SCRIPT,
                fetchPriority: HeaderLinkFetchPriority::HIGH,
            ),
        );

        // These are the random chosen domains by Google for displaying ads
        $this->headerLinkRegistry->add(
            new HeaderLink(
                url: 'https://syndicatedsearch.goog',
                rel: HeaderLinkRel::PRECONNECT,
            ),
        );
        $this->headerLinkRegistry->add(
            new HeaderLink(
                url: 'https://www.adsensecustomsearchads.com',
                rel: HeaderLinkRel::PRECONNECT,
            ),
        );

        $scriptHTML = $this->googleCsaRenderer->getScriptHtml(false);

        $event->addItem(
            $this->twig->render(
                '@theme/google_csa/google_csa_scripts.html.twig',
                [
                    'google_ads_top_request_amount' => $googleCsa->ads()->getTopUnit()?->getAmount() ?? 0,
                    'script_html'                   => $scriptHTML,
                ],
            ),
        );

        $this->hasRenderedGoogleScripts = true;
        $hasAdsRequest = $googleCsa->ads()->hasUnits();
        $hasRelatedTermsRequest = $googleCsa->relatedSearch()->hasUnits();

        if (($hasAdsRequest || $hasRelatedTermsRequest) && $this->isGoogleAfsOnline()) {
            $this->renderGoogleAfsOnline($event, $googleCsa);
        }

        $event->addItem(
            $this->twig->render(
                '@theme/google_csa/google_csa_javascript.html.twig',
                [
                    'javascript'              => $this->googleCsaRenderer->getJavaScript($googleCsa),
                    'has_ads_request'         => $hasAdsRequest,
                    'requested_related_terms' => $googleCsa->relatedSearch()->getTerms(),
                ],
            ),
        );
    }

    private function renderGoogleAfsOnline(RenderTemplateHeadersEvent $event, GoogleCsaInterface $googleCsa): void
    {
        $googleAfsOnlineTrackingPixelUrl = $this->googleAdSenseOnlineConversionUrlGenerator->generate(
            adClientId: $googleCsa->getPublisherId(),
        );
        $googleAfsOnlineTrackingPixelUrl = $this->googleCsaClickTrackUrlHelper->updateClickTrackUrl(
            clickTrackUrl: $googleAfsOnlineTrackingPixelUrl,
            styleId      : $this->googleCsaStyleIdParameter->getStyleId(),
        );

        $event->addItem(
            $this->twig->render(
                '@theme/google_afs/google_afs_online_javascript.html.twig',
                [
                    'google_afs_online_tracking_pixel_url' => $googleAfsOnlineTrackingPixelUrl,
                ],
            ),
        );
    }

    private function isGoogleAfsOnline(): bool
    {
        return $this->websiteSettingsHelper->getSettings()->getGoogleAdSense()->getContractType()->isOnline();
    }
}

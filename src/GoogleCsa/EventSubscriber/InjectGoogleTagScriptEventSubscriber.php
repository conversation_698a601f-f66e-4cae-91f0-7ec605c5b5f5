<?php

declare(strict_types=1);

namespace App\GoogleCsa\EventSubscriber;

use App\Brand\Settings\BrandSettingsHelper;
use App\Http\Request\GenericRequestInterface;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Environment;

readonly class InjectGoogleTagScriptEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private Environment $twig,
        private GenericRequestInterface $genericRequest,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            RenderTemplateHeadersEvent::NAME => ['injectGoogleTag', -110],
        ];
    }

    public function injectGoogleTag(RenderTemplateHeadersEvent $event): void
    {
        $conversionTrackingSettings = $this->websiteSettingsHelper->getSettings()->getGoogleAdsConversionTracking();
        $brandSlug = $this->brandSettingsHelper->getSettings()->getSlug();

        // Set-up is a test, only for Proadvisr.
        if ($brandSlug !== 'proadvisr'
            || !$conversionTrackingSettings->isEnabled()
            || $conversionTrackingSettings->getConversionTrackingLabel() === null
        ) {
            return;
        }

        $googleTagConfigurationId = sprintf('AW-%d', $conversionTrackingSettings->getConversionTrackingId());
        $conversionGoal = sprintf('%s/%s', $googleTagConfigurationId, $conversionTrackingSettings->getConversionTrackingLabel());

        $event->addItem(
            $this->twig->render(
                '@theme/google_tag/google_tag_script.html.twig',
                [
                    'configuration_id' => $googleTagConfigurationId,
                ],
            ),
        );

        $event->addItem(
            $this->twig->render(
                '@theme/google_tag/google_tag_conversion.html.twig',
                [
                    'conversion_goal' => $conversionGoal,
                    'pageview'        => $this->genericRequest->getPageviewId(),
                ],
            ),
        );
    }
}

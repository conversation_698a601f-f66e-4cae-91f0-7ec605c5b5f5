<?php

declare(strict_types=1);

namespace App\GoogleCsa\Helper;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use App\Generic\Url\UrlHelper;
use Visymo\GoogleCsa\Unit\UnitInterface;

final readonly class GoogleCsaClickTrackUrlHelper
{
    public function __construct(
        private UrlHelper $urlHelper
    )
    {
    }

    public function updateUnitClickTrackUrls(UnitInterface $unit): void
    {
        $clickTrackUrls = [];

        foreach ($unit->getClickTrackUrls() as $clickTrackUrl) {
            $clickTrackUrls[] = $this->updateClickTrackUrl($clickTrackUrl, $unit->getStyleId());
        }

        $unit->setClickTrackUrls($clickTrackUrls);
    }

    public function updateClickTrackUrl(
        string $clickTrackUrl,
        ?int $styleId
    ): string
    {
        return $this->urlHelper->setParameterValue(
            $clickTrackUrl,
            ConversionTrackingRequestInterface::PARAMETER_AD_STYLE_ID,
            $styleId,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\GoogleCsa\Registry;

use Visymo\GoogleCsa\GoogleCsaInterface;

final class GoogleCsaRegistry
{
    private ?GoogleCsaInterface $googleCsa = null;

    public function getGoogleCsa(): ?GoogleCsaInterface
    {
        return $this->googleCsa;
    }

    public function setGoogleCsa(?GoogleCsaInterface $googleCsa): self
    {
        $this->googleCsa = $googleCsa;

        return $this;
    }
}

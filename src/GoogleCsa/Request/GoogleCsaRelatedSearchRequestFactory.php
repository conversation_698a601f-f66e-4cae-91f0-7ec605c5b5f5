<?php

declare(strict_types=1);

namespace App\GoogleCsa\Request;

use App\Debug\Request\DebugRequestInterface;
use App\GoogleCsa\Parameter\GoogleCsaReferrerAdCreativeParameter;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameterInterface;
use App\JsonTemplate\View\ViewInterface;
use App\Monetization\Settings\MonetizationSettings;
use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Search\Query\SearchQueryNormalizer;
use Psr\Log\LoggerInterface;
use Visymo\GoogleCsa\RelatedSearch\Unit\RelatedSearchUnitFactory;

final readonly class GoogleCsaRelatedSearchRequestFactory
{
    public function __construct(
        private DebugRequestInterface $debugRequest,
        private GoogleCsaStyleIdParameterInterface $googleCsaStyleIdParameter,
        private GoogleCsaReferrerAdCreativeParameter $googleCsaReferrerAdCreativeParameter,
        private RelatedSearchUnitFactory $relatedSearchUnitFactory,
        private RelatedTermsRequestInterface $relatedTermsRequest,
        private LoggerInterface $logger,
        private SearchQueryNormalizer $searchQueryNormalizer,
        private MonetizationSettings $monetizationSettings
    )
    {
    }

    public function createFromView(ViewInterface $view): ?GoogleCsaRelatedSearchRequest
    {
        if (!$this->monetizationSettings->relatedTermsEnabled) {
            return null;
        }

        $googleCsaViewDataRequest = $view->getDataRequest()->googleCsa();

        if ($googleCsaViewDataRequest->getRelatedSearchUnits() === []) {
            return null;
        }

        $route = null;
        $forContent = false;
        $addVisymoRelatedTerms = false;
        $termsUrlParameterEnabled = false;
        $units = [];

        foreach ($googleCsaViewDataRequest->getRelatedSearchUnits() as $relatedSearchUnitDataRequest) {
            $unit = $this->relatedSearchUnitFactory->create(
                container      : $relatedSearchUnitDataRequest->container,
                styleId        : $this->googleCsaStyleIdParameter->getStyleId(),
                relatedSearches: $relatedSearchUnitDataRequest->amount,
            );

            $route ??= $relatedSearchUnitDataRequest->route;
            $forContent = $forContent || $relatedSearchUnitDataRequest->forContent;
            $addVisymoRelatedTerms = $addVisymoRelatedTerms || $relatedSearchUnitDataRequest->addVisymoRelatedTerms;
            $termsUrlParameterEnabled = $termsUrlParameterEnabled || $relatedSearchUnitDataRequest->termsUrlParameterEnabled;

            $units[] = $unit;
        }

        if ($this->debugRequest->showGoogleTestAd()) {
            // Using `for search` does not require the page to be crawled by Google
            $forContent = false;

            // Terms are only allowed when using related search for content
            $addVisymoRelatedTerms = false;
        }

        $referrerAdCreative = $forContent
            ? $this->googleCsaReferrerAdCreativeParameter->getReferrerAdCreativeForContent()
            : null;

        $relatedTerms = $addVisymoRelatedTerms
            ? $this->getRelatedTerms($view, $forContent, $termsUrlParameterEnabled)
            : [];

        return new GoogleCsaRelatedSearchRequest(
            route             : $route,
            units             : $units,
            forContent        : $forContent,
            referrerAdCreative: $referrerAdCreative,
            terms             : $relatedTerms,
        );
    }

    /**
     * @return string[]
     */
    private function getRelatedTerms(ViewInterface $view, bool $forContent, bool $termsUrlParameterEnabled): array
    {
        if ($termsUrlParameterEnabled) {
            $relatedTerms = $this->relatedTermsRequest->getRelatedTerms();
        }

        $relatedTerms ??= [];

        foreach ($view->getDataRegistry()->getRelatedTerms()->getResults() as $relatedTerm) {
            if (!in_array($relatedTerm->getQuery(), $relatedTerms, true)) {
                $relatedTerms[] = $relatedTerm->getQuery();
            }
        }

        if ($forContent) {
            $relatedTerms = $this->prependQueryToRelatedTerms($view, $relatedTerms);
        }

        $relatedTerms = array_filter(
            $relatedTerms,
            function (string $relatedTerm) {
                if (mb_strlen($relatedTerm) > GoogleCsaRelatedSearchRequest::MAX_TERM_LENGTH) {
                    $this->logger->notice('Related term "{related_term}" is too long', ['related_term' => $relatedTerm]);

                    return false;
                }

                return true;
            },
        );

        $requiredAmount = $view->getDataRequest()->googleCsa()->getTotalRelatedSearchAmount();

        return array_slice($relatedTerms, 0, $requiredAmount);
    }

    /**
     * @param string[] $relatedTerms
     *
     * @return string[]
     */
    private function prependQueryToRelatedTerms(ViewInterface $view, array $relatedTerms): array
    {
        $query = $this->getQuery($view);

        if ($query !== null) {
            // Add as first item to keep after slice.
            array_unshift($relatedTerms, $query);
        }

        return $relatedTerms;
    }

    private function getQuery(ViewInterface $view): ?string
    {
        $query = $this->relatedTermsRequest->getAlternateRelatedQuery() ?? $view->getDataRegistry()->getQuery();

        return $query !== null
            ? $this->searchQueryNormalizer->getNormalizedQueryWithoutSpecialCharacters($query)
            : null;
    }
}

<?php

declare(strict_types=1);

namespace App\GoogleCsa\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsProvider;

class GoogleAdSenseRelatedTermsStatisticsProvider extends AbstractStatisticsProvider
{
    public function __construct(
        GoogleAdSenseRelatedTermsStatisticsResolver $googleAdSenseRelatedTermsStatisticsResolver
    )
    {
        parent::__construct(
            $googleAdSenseRelatedTermsStatisticsResolver,
        );
    }

    public static function getContextKey(): string
    {
        return 'google_adsense';
    }

    public static function getPayloadKey(): string
    {
        return 'grt';
    }
}

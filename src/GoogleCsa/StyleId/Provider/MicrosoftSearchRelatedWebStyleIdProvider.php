<?php

declare(strict_types=1);

namespace App\GoogleCsa\StyleId\Provider;

use App\GoogleCsa\StyleId\GoogleCsaStyleIdProviderInterface;
use App\MicrosoftSearchRelated\Settings\MicrosoftSearchRelatedSettings;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;

final readonly class MicrosoftSearchRelatedWebStyleIdProvider implements GoogleCsaStyleIdProviderInterface
{
    public function __construct(
        private MicrosoftSearchRelatedSettings $microsoftSearchRelatedSettings,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private SearchRequestInterface $searchRequest
    )
    {
    }

    public static function getDefaultPriority(): int
    {
        return 2500;
    }

    public function validateStyleId(): bool
    {
        return true;
    }

    public function getStyleId(): ?int
    {
        if (!$this->searchRequest->isMicrosoftSearchRelatedWeb()) {
            return null;
        }

        $device = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->device;

        return $this->microsoftSearchRelatedSettings->getStyleIdForDevice($device);
    }
}

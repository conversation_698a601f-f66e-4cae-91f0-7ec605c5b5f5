<?php

declare(strict_types=1);

namespace App\GoogleCsa\StyleId\Provider;

use App\GoogleCsa\StyleId\GoogleCsaStyleIdProviderInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\Search\Request\SearchRequestInterface;
use App\Search\Settings\SearchSettings;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;

final readonly class SearchStyleIdProvider implements GoogleCsaStyleIdProviderInterface
{
    public function __construct(
        private SearchSettings $searchSettings,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private RequestInfoInterface $requestInfo,
        private SearchRequestInterface $searchRequest
    )
    {
    }

    public static function getDefaultPriority(): int
    {
        return 1000;
    }

    public function validateStyleId(): bool
    {
        return true;
    }

    public function getStyleId(): ?int
    {
        $isOneOfRoutes = array_filter(
            [
                $this->requestInfo->isRoute('route_search'),
                $this->searchRequest->isSeoPage(),
                $this->requestInfo->isRoute('route_keyword_ideas_search'),
                $this->requestInfo->isRoute('route_content_search'),
                $this->requestInfo->isRoute('route_content_search_advertised'),
            ],
            static fn ($value) => $value !== false,
        );

        if ($isOneOfRoutes === []) {
            return null;
        }

        $device = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->device;

        return $this->searchSettings->getStyleIdForDevice($device);
    }
}

<?php

declare(strict_types=1);

namespace App\GoogleCsa\StyleId\Provider;

use App\GoogleCsa\StyleId\GoogleCsaStyleIdProviderInterface;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;

final readonly class TrackingEntryStyleIdProvider implements GoogleCsaStyleIdProviderInterface
{
    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private SearchRequestInterface $searchRequest
    )
    {
    }

    public static function getDefaultPriority(): int
    {
        return 4000;
    }

    public function validateStyleId(): bool
    {
        return true;
    }

    public function getStyleId(): ?int
    {
        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        if ($this->searchRequest->isWebSearch() ||
            $this->searchRequest->isWebSearchAdvertised() ||
            $this->searchRequest->isDisplaySearchRelatedWeb()
        ) {
            return $trackingEntry->styleId;
        }

        return null;
    }
}

<?php

declare(strict_types=1);

namespace App\GooglePublisherTag\EventSubscriber;

use App\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerConversionUrlGenerator;
use App\GooglePublisherTag\Tag\GoogleTagRenderHelper;
use App\Http\Response\HeaderLink\HeaderLink;
use App\Http\Response\HeaderLink\HeaderLinkAs;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\Http\Response\HeaderLink\HeaderLinkRel;
use App\Template\Event\RenderTemplateHeadersEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Twig\Environment;

readonly class InjectGooglePublisherTagScriptsEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Environment $twig,
        private GoogleTagRenderHelper $googleTagRenderHelper,
        private GoogleAdManagerConversionUrlGenerator $googleAdManagerConversionUrlGenerator,
        private HeaderLinkRegistry $headerLinkRegistry
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            // Google Publisher Tag script should be loaded as soon as possible
            RenderTemplateHeadersEvent::NAME => ['renderTemplateHeaders', 1000],
        ];
    }

    public function renderTemplateHeaders(RenderTemplateHeadersEvent $event): void
    {
        if (!$this->googleTagRenderHelper->isEnabled()) {
            return;
        }

        if (!$this->googleTagRenderHelper->hasSlots()) {
            return;
        }

        $this->headerLinkRegistry->add(
            new HeaderLink(
                url: 'https://securepubads.g.doubleclick.net/tag/js/gpt.js',
                rel: HeaderLinkRel::PRELOAD,
                as : HeaderLinkAs::SCRIPT,
            ),
        );

        $event->addItem(
            $this->twig->render(
                '@theme/google_gpt/google_gpt_script.html.twig',
                [
                    'pub_ads_service'        => $this->googleTagRenderHelper->getPubAdsService(),
                    'display_conversion_url' => $this->googleAdManagerConversionUrlGenerator->generateDisplay(),
                    'requested_slots'        => $this->googleTagRenderHelper->getSlotsCount(),
                ],
            ),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\GooglePublisherTag\StatisticsProvider;

use App\Http\Request\GenericRequestInterface;
use App\Statistics\Provider\AbstractStatisticsResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;

class GoogleAdManagerStatisticsResolver extends AbstractStatisticsResolver
{
    private const string STATISTICS_KEY_HAS_LOADED            = 'has_loaded';
    private const string STATISTICS_KEY_ADS                   = 'ads';
    private const string STATISTICS_KEY_ADS_HAS_IMPRESSION    = 'has_impression';
    private const string STATISTICS_KEY_ADS_IMPRESSION_VIEWED = 'impression_viewed';

    private const string PAYLOAD_KEY_HAS_LOADED            = 'hl';
    private const string PAYLOAD_KEY_ADS                   = 'a';
    private const string PAYLOAD_KEY_ADS_HAS_IMPRESSION    = 'hi';
    private const string PAYLOAD_KEY_ADS_IMPRESSION_VIEWED = 'iv';

    public function __construct(
        private readonly OptionsResolverInterface $adsOptionsResolver,
        OptionsResolverInterface $optionsResolver,
        GenericRequestInterface $genericRequest
    )
    {
        parent::__construct($optionsResolver, $genericRequest);
    }

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::PAYLOAD_KEY_HAS_LOADED)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->optionsResolver->define(self::PAYLOAD_KEY_ADS)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->setAllowedArrayTypes(
                [
                    OptionType::TYPE_STRING,
                ],
                [
                    OptionType::TYPE_ARRAY,
                ],
            )->addCallbackValidator(
                function ($value): void {
                    foreach ($value as $adsStatistics) {
                        $this->adsOptionsResolver->resolve($adsStatistics);
                    }
                },
            );

        $this->adsOptionsResolver->define(self::PAYLOAD_KEY_ADS_HAS_IMPRESSION)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);

        $this->adsOptionsResolver->define(self::PAYLOAD_KEY_ADS_IMPRESSION_VIEWED)
            ->setAllowedType(OptionType::TYPE_BOOLEAN);
    }

    /**
     * @inheritDoc
     */
    protected function getStatisticsMapping(): array
    {
        return [
            self::STATISTICS_KEY_HAS_LOADED => self::PAYLOAD_KEY_HAS_LOADED,
            self::STATISTICS_KEY_ADS        => [
                'source_key' => self::PAYLOAD_KEY_ADS,
                'mapping'    => [
                    self::STATISTICS_KEY_ADS_HAS_IMPRESSION    => self::PAYLOAD_KEY_ADS_HAS_IMPRESSION,
                    self::STATISTICS_KEY_ADS_IMPRESSION_VIEWED => self::PAYLOAD_KEY_ADS_IMPRESSION_VIEWED,
                ],
            ],
        ];
    }
}

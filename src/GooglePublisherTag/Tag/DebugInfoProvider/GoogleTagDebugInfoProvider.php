<?php

declare(strict_types=1);

namespace App\GooglePublisherTag\Tag\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use App\GooglePublisherTag\Tag\GoogleTagRenderHelper;

final class GoogleTagDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_GOOGLE_GPT_REQUEST = 'google gpt request';

    public function __construct(
        private readonly GoogleTagRenderHelper $googleTagRenderHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        if (!$this->googleTagRenderHelper->isEnabled() || !$this->googleTagRenderHelper->hasSlots()) {
            return [];
        }

        $pubAdsService = $this->googleTagRenderHelper->getPubAdsService();

        return [
            new DebugInfo(
                self::KEY_GOOGLE_GPT_REQUEST,
                [
                    'pub_ads_service'     => [
                        'collapse_empty_divs'   => $pubAdsService->isCollapseEmptyDivs(),
                        'enable_single_request' => $pubAdsService->isEnableSingleRequest(),
                        'targeting'             => $pubAdsService->getTargeting(),
                    ],
                    'multi_request_slots' => $this->googleTagRenderHelper->multiRequestSlots,
                    'rendered_slot_ids'   => $this->googleTagRenderHelper->renderedSlotIds,
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 70;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_GOOGLE_GPT_REQUEST,
        ];
    }
}

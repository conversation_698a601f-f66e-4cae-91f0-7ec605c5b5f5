<?php

declare(strict_types=1);

namespace App\GooglePublisherTag\Tag;

use App\GooglePublisherTag\PubAdsService\PubAdsService;
use App\GooglePublisherTag\PubAdsService\PubAdsServiceFactory;
use App\GooglePublisherTag\Settings\GooglePublisherTagSettings;
use App\GooglePublisherTag\Slot\Slot;
use App\GooglePublisherTag\Slot\SlotFactory;
use App\JsonTemplate\Component\ComponentInterface;
use App\Monetization\Settings\MonetizationSettings;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use Psr\Log\LoggerInterface;

class GoogleTagRenderHelper
{
    private const string CAMPAIGN_TARGETING_KEY   = 'asid';
    private const string SLOT_ELEMENT_ID_TEMPLATE = 'slot-%u';

    private PubAdsService $pubAdsService;

    /** @var Slot[] https://support.google.com/admanager/answer/183282?hl=en#multi-request-mode */
    public array $multiRequestSlots = [];

    /** @var string[] */
    public array $renderedSlotIds = [];

    /** @var \WeakMap<ComponentInterface, Slot> */
    private \WeakMap $componentSlots;

    private int $slotCounter = 1;

    public function __construct(
        private readonly GooglePublisherTagSettings $googlePublisherTagSettings,
        private readonly PubAdsServiceFactory $pubAdsServiceFactory,
        private readonly SlotFactory $slotFactory,
        private readonly ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private readonly LoggerInterface $logger,
        private readonly MonetizationSettings $monetizationSettings
    )
    {
        $this->componentSlots = new \WeakMap();
    }

    public function isEnabled(): bool
    {
        return $this->googlePublisherTagSettings->enabled && $this->monetizationSettings->displayBannersEnabled;
    }

    public function getPubAdsService(): PubAdsService
    {
        if (isset($this->pubAdsService)) {
            return $this->pubAdsService;
        }

        $targeting = [];
        $campaignName = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->campaignName;

        if ($campaignName !== null) {
            $targeting[self::CAMPAIGN_TARGETING_KEY] = [$campaignName];
        }

        $this->pubAdsService = $this->pubAdsServiceFactory->create(true, false, $targeting);

        return $this->pubAdsService;
    }

    /**
     * @param array<array<int,int>> $sizes
     */
    public function registerComponentSlotRequest(ComponentInterface $component, string $adUnitPath, array $sizes): void
    {
        if (!$this->isEnabled()) {
            $this->logger->warning(
                'Google Publisher Tag module must be enabled to register a component slot request',
                [
                    'component_type' => $component::getType(),
                    'ad_unit_path'   => $adUnitPath,
                    'sizes'          => $sizes,
                ],
            );

            return;
        }

        $elementId = $this->getNextSlotElementId();
        $slot = $this->slotFactory->create($adUnitPath, $elementId, $sizes);
        $this->componentSlots[$component] = $slot;

        $this->registerSlotRequest($slot);
    }

    private function registerSlotRequest(Slot $slot): void
    {
        $this->multiRequestSlots[] = $slot;
    }

    public function hasSlots(): bool
    {
        return $this->multiRequestSlots !== [];
    }

    public function getSlotsCount(): int
    {
        return count($this->multiRequestSlots);
    }

    public function getComponentSlot(ComponentInterface $component): ?Slot
    {
        return $this->componentSlots[$component] ?? null;
    }

    public function registerSlotRendered(Slot $slot): void
    {
        $this->renderedSlotIds[] = $slot->elementId;
    }

    private function getNextSlotElementId(): string
    {
        return sprintf(self::SLOT_ELEMENT_ID_TEMPLATE, $this->slotCounter++);
    }
}

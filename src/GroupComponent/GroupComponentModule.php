<?php

declare(strict_types=1);

namespace App\GroupComponent;

use App\BrandOverride\BrandOverrideModuleInterface;
use App\Component\Generic\Group\GroupLayout;
use App\DependencyInjection\AbstractDependencyInjectionModule;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class GroupComponentModule extends AbstractDependencyInjectionModule implements BrandOverrideModuleInterface
{
    public const string KEY_LAYOUT_VARIANT = 'layout_variant';

    public static function getModuleName(): string
    {
        return 'group_component';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->addDefaultsIfNotSet()
                ->children();

        $moduleNodeChildren
            ->booleanNode(self::KEY_ENABLED)
                ->info('Enable the group component module')
                ->defaultNull();

        $layoutVariantNodeChildren = $moduleNodeChildren
            ->arrayNode(self::KEY_LAYOUT_VARIANT)
                ->addDefaultsIfNotSet()
                ->children();

        foreach (GroupLayout::getSupportedLayoutVariants() as $layout => $variants) {
            $layoutVariantNodeChildren
                ->enumNode($layout)
                    ->values(array_keys($variants))
                    ->defaultNull();
        }
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $moduleConfig = $this->getModuleConfig($config);
        $container->setParameter(
            self::getModuleParameterConfigName(),
            $moduleConfig,
        );
    }

    /**
     * @inheritDoc
     */
    public function isModuleConfigDefined(array $moduleConfig): bool
    {
        return $moduleConfig[self::KEY_ENABLED] !== null;
    }
}

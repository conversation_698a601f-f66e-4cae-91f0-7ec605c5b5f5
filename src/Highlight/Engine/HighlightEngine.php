<?php

declare(strict_types=1);

namespace App\Highlight\Engine;

use App\WebSearch\Helper\WebSearchQueryHelper;
use Psr\Log\LoggerInterface;

class HighlightEngine
{
    private readonly string $highlightText;

    private bool $useBoundary;

    private string $pattern;

    private string $replacement;

    private const array DIACRITICS = [
        'a' => 'ẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃąạậặḁⱥᶏ',
        'b' => 'ḃḇḅƀᵬᶀɓƃ',
        'c' => 'ćĉċčçḉȼꞓƈɕᶝ',
        'd' => 'ḋďḑḓḏḍđᵭᶁɗᶑƌȡ',
        'e' => 'èéêềếễểẽēḕḗĕėëẻěȅȇȩḝęḙḛẹệɇᶒⱸ',
        'f' => 'ḟᵮᶂƒꞙ',
        'g' => 'ǵĝḡğġǧģꞡǥᶃɠ',
        'h' => 'ĥḣḧȟḩẖḫḥħꟸɦʱⱨꞕ',
        'i' => 'ìíîĩīĭïḯǐỉȉȋįḭịɨᶤᶖ',
        'j' => 'ĵǰɉʝᶨ',
        'k' => 'ḱǩķḵḳᶄƙⱪꝁꝃꝅꞣ',
        'l' => 'ŀĺľļḽḻḷḹłꝉƚⱡɫꭞɬᶅᶪɭᶩꞎȴ',
        'm' => 'ḿṁᵯṃᶆɱᶬ',
        'n' => 'ǹńñṅňꞥᵰņṋṉṇɲᶮƞꞑᶇɳᶯȵ',
        'o' => 'òóôốồỗổõṍṏȭōṓṑŏȯȱöȫỏőǒȍȏøǿɵᶱơớờỡởǫǭọộợꝍⱺꝋ',
        'p' => 'ṕṗᵽꝑᵱᶈƥꝓꝕ',
        'q' => 'ꝗꝙʠɋ',
        'r' => 'ŕṙřȑȓŗꞧṟṛṝɍᵲᶉɼɽɾᵳ',
        's' => 'śṥŝṡšṧşșꞩṣṩᵴᶊȿ',
        't' => 'ṫẗťţțṱṯṭŧⱦᵵƫᶵƭʈȶ',
        'u' => 'ùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưứừữửựųṷṵụṳᶙ',
        'v' => 'ṽṿꝟᶌʋᶹⱱⱴ',
        'w' => 'ẁẃŵẇẅẘẉⱳ',
        'x' => 'ẋẍᶍ',
        'y' => 'ỳýŷỹȳẏÿẙỷỵɏƴỿ',
        'z' => 'źẑżžẕẓƶᵶȥʐᶼʑᶽɀⱬ',
    ];

    private const int MAX_WORD_LENGTH         = 35;
    private const int MAX_PREG_PATTERN_LENGTH = 5000;

    /**
     * @param string               $highlightText        The phrase to highlight
     * @param bool                 $highlightWords       If words are highlighted individually
     * @param string               $tagOpen              The opening tag to precede the phrase with
     * @param string               $tagClose             The closing tag to end the phrase with
     * @param WebSearchQueryHelper $webSearchQueryHelper Helper for search queries
     * @param LoggerInterface      $logger               Log unexpected behaviour
     */
    public function __construct(
        string $highlightText,
        private readonly bool $highlightWords,
        private readonly string $tagOpen,
        private readonly string $tagClose,
        private readonly WebSearchQueryHelper $webSearchQueryHelper,
        private readonly LoggerInterface $logger
    )
    {
        $this->highlightText = trim($highlightText);
        $this->prepareReplacementPattern();
    }

    /**
     * Highlights a phrase within a text string
     *
     * Sources:
     * - https://codeigniter.com/user_guide/helpers/text_helper.html#highlight_phrase
     * - https://www.the-art-of-web.com/javascript/highlight-words-utf8/
     *
     * @param string $htmlInput Text input to highlight the given highlight text. Must be HTML escaped.
     */
    public function highlight(string $htmlInput): string
    {
        if ($this->highlightText === '' || !isset($this->pattern, $this->replacement)) {
            return $htmlInput;
        }

        try {
            $replaced = @preg_replace($this->pattern, $this->replacement, $htmlInput);

            if ($replaced === null) {
                return $htmlInput;
            }

            return $this->handleNeighboringHighlights($replaced, $this->useBoundary);
        } catch (\Throwable $exception) {
            $this->logger->notice(
                sprintf('Caught %s while highlighting text', $exception::class),
                [
                    'exception'       => $exception,
                    'highlight_text'  => $this->highlightText,
                    'highlight_words' => $this->highlightWords,
                ],
            );

            return $htmlInput;
        }
    }

    private function prepareReplacementPattern(): void
    {
        $highlightText = $this->normalizeHighlightText($this->highlightText);

        if ($highlightText === '') {
            return;
        }

        // Certain languages are character focused and have no word boundaries
        $this->useBoundary = $this->detectCharacterLanguages($highlightText) === '';

        // Remove diacritics from text
        $highlightText = $this->replaceDiacritics($highlightText);

        $words = $this->prepareWords($highlightText);

        if ($words === []) {
            return;
        }

        $boundary = $this->useBoundary ? '\b' : '';

        $pattern = sprintf('/%s(%s)%s/iu', $boundary, implode('|', $words), $boundary);

        if (mb_strlen($pattern) > self::MAX_PREG_PATTERN_LENGTH) {
            $this->logger->notice(
                sprintf('Highlighter regular expression exceeds limit %d', self::MAX_PREG_PATTERN_LENGTH),
                [
                    'highlight_text'  => $this->highlightText,
                    'highlight_words' => $this->highlightWords,
                ],
            );

            return;
        }

        $this->pattern = $pattern;

        $this->replacement = sprintf('%s\\1%s', $this->tagOpen, $this->tagClose);
    }

    /**
     * @return array|string[]
     */
    private function prepareWords(string $highlightText): array
    {
        // Remove extra spaces when replacing words, preserve spaces for phrases
        $words = $this->highlightWords
            ? preg_split('/\s+/', $highlightText)
            : [$highlightText];

        if ($words === false) {
            return [];
        }

        $words = array_unique($words);

        foreach ($words as $index => $word) {
            if (mb_strlen($word) > self::MAX_WORD_LENGTH) {
                unset($words[$index]);

                continue;
            }

            $words[$index] = $this->handleWordRegEx($word);
        }

        return $words;
    }

    /**
     * Replace neighbouring words as phrases for higher ranking
     */
    private function handleNeighboringHighlights(string $input, bool $useBoundaries): string
    {
        $input = str_replace(sprintf('%s%s', $this->tagClose, $this->tagOpen), '', $input);

        if (!$useBoundaries) {
            return $input;
        }

        // Allow multiple spaces in between tags
        $pattern = sprintf(
            '/%s%s%s/iu',
            preg_quote($this->tagClose, '/'),
            '(\s+)',
            preg_quote($this->tagOpen, '/'),
        );
        $input = preg_replace($pattern, '$1', $input) ?? $input;

        return $input;
    }

    private function handleWordRegEx(string $word): string
    {
        $word = preg_quote($word, '/');
        $word = $this->addAccentsRegEx($word);

        return $word;
    }

    private function addAccentsRegEx(string $text): string
    {
        // Assume lowercase for performance
        $text = mb_strtolower($text);

        /**
         * Some letters with diaeresis can be written alternatively with an extra e
         * These are removed first and added as alternatives below
         **/
        $text = preg_replace('/([aou])e/u', '$1', $text) ?? $text;

        // Add diaeresis with extra e
        $extras = [
            'a' => 'ae',
            'o' => 'oe',
            'u' => 'ue',
        ];

        /**
         * Special case for German ß which can be written alternatively as two letters s
         * These are matched and replaced by their possible alternatives
         */
        $text = preg_replace('/(ß|ss)/u', '(ß|ss)', $text) ?? $text;

        foreach (self::DIACRITICS as $letter => $diacritics) {
            if (!str_contains($text, $letter)) {
                continue;
            }

            $replace = sprintf('[%s%s]', $letter, $diacritics);

            if (isset($extras[$letter])) {
                $replace = sprintf('(%s|%s)', $replace, $extras[$letter]);
            }

            $pattern = sprintf('/%s/u', $letter);
            $text = preg_replace($pattern, $replace, $text) ?? $text;
        }

        // Escape text so HTML entities are highlighted too
        $text = htmlspecialchars($text, ENT_COMPAT);

        return $text;
    }

    private function normalizeHighlightText(string $text): string
    {
        $text = $this->webSearchQueryHelper->stripSiteSearch($text);
        $text = $this->webSearchQueryHelper->stripPlusChar($text);
        $text = $this->webSearchQueryHelper->stripNegativeSearchWords($text);

        // Remove (single/double) quotes, Twig pre_escape uses &#039;
        return trim(str_replace(['"', "'", '&quot;', '&apos;', '&#39;', '&#039;'], ' ', $text));
    }

    private function replaceDiacritics(string $text): string
    {
        foreach (self::DIACRITICS as $letter => $diacritics) {
            $pattern = sprintf('/[%s]/iu', $diacritics);
            $text = preg_replace($pattern, $letter, $text) ?? $text;
        }

        return $text;
    }

    /**
     * Detect languages based on characters like Chinese, Japanese, Korean and Hindi
     */
    private function detectCharacterLanguages(string $text): string
    {
        $korean = '([\x{ac00}-\x{d7a3}]+)';
        $japanese = '([\x{3040}-\x{30ff}]+)';
        $chinese = '([\x{4e00}-\x{9fa5}]+)';
        $hindi = '([\x{0900}-\x{097f}]+)';

        $pattern = sprintf('/(%s|%s|%s|%s)/u', $korean, $japanese, $chinese, $hindi);

        if (preg_match($pattern, $text, $matches) > 0) {
            $detect = [
                2 => 'ko',
                3 => 'ja',
                4 => 'zh',
                5 => 'hi',
            ];

            foreach ($detect as $key => $lang) {
                if (array_key_exists($key, $matches) && $matches[$key] !== '') {
                    return $lang;
                }
            }
        }

        return '';
    }
}

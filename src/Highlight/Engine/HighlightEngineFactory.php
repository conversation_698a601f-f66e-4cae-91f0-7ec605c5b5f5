<?php

declare(strict_types=1);

namespace App\Highlight\Engine;

use App\WebSearch\Helper\WebSearchQueryHelper;
use Psr\Log\LoggerInterface;

readonly class HighlightEngineFactory
{
    public function __construct(
        private WebSearchQueryHelper $webSearchQueryHelper,
        private LoggerInterface $logger
    )
    {
    }

    public function create(
        string $highlightText,
        bool $highlightWords,
        string $tagOpen,
        string $tagClose
    ): HighlightEngine
    {
        return new HighlightEngine(
            $highlightText,
            $highlightWords,
            $tagOpen,
            $tagClose,
            $this->webSearchQueryHelper,
            $this->logger,
        );
    }

    public function createWithStrongTags(
        string $highlightText,
        bool $highlightWords,
        ?string $className
    ): HighlightEngine
    {
        return $this->create(
            $highlightText,
            $highlightWords,
            $className !== null ? sprintf('<strong class="%s">', $className) : '<strong>',
            '</strong>',
        );
    }
}

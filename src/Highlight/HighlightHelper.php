<?php

declare(strict_types=1);

namespace App\Highlight;

use App\Highlight\Engine\HighlightEngine;
use App\Highlight\Engine\HighlightEngineFactory;

class HighlightHelper
{
    /** @var HighlightEngine[] */
    private array $highlightEngines = [];

    public function __construct(private readonly HighlightEngineFactory $highlightEngineFactory)
    {
    }

    public function highlight(string $highlightText, string $htmlContent): string
    {
        return $this->highlightWithClass($highlightText, $htmlContent, null);
    }

    public function highlightWithClass(string $highlightText, string $htmlContent, ?string $className): string
    {
        $result = $this->getHighlighterForStrong($highlightText, $className)->highlight($htmlContent);

        if ($result === $htmlContent) {
            $highlightText = strtolower($highlightText);
            $result = $this->getHighlighterForStrong($highlightText, $className)->highlight($htmlContent);
        }

        return $result;
    }

    private function getHighlighterForStrong(string $highlightText, ?string $classname = null): HighlightEngine
    {
        if (!isset($this->highlightEngines[$highlightText])) {
            $this->highlightEngines[$highlightText] = $this->highlightEngineFactory->createWithStrongTags($highlightText, true, $classname);
        }

        return $this->highlightEngines[$highlightText];
    }
}

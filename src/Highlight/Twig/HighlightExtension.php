<?php

declare(strict_types=1);

namespace App\Highlight\Twig;

use App\Highlight\HighlightHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class HighlightExtension extends AbstractExtension
{
    public function __construct(private readonly HighlightHelper $highlightHelper)
    {
    }

    /**
     * @return TwigFilter[]
     */
    public function getFilters(): array
    {
        return [
            new TwigFilter('highlight', $this->highlight(...), ['pre_escape' => 'html', 'is_safe' => ['html']]),
            new TwigFilter('highlight_with_class', $this->highlightWithClass(...), ['pre_escape' => 'html', 'is_safe' => ['html']]),
        ];
    }

    /**
     * Highlighting can be disabled to keep template code clean
     */
    public function highlight(string $input, ?string $highlightText, bool $applyHighlight = true): string
    {
        if (!$applyHighlight || $highlightText === null) {
            return $input;
        }

        return $this->highlightHelper->highlight($highlightText, $input);
    }

    public function highlightWithClass(
        string $input,
        ?string $highlightText,
        string $className,
        bool $applyHighlight = true
    ): string
    {
        if (!$applyHighlight || $highlightText === null) {
            return $input;
        }

        return $this->highlightHelper->highlightWithClass($highlightText, $input, $className);
    }
}

<?php

declare(strict_types=1);

namespace App\Home\Controller;

use App\ContentPageHome\Controller\ContentPageHomeController;
use App\Home\Exception\NoActiveHomeControllerFoundException;
use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Response\ResponseCachingHelper;
use App\Search\Controller\SearchHomeController;
use App\Search\Request\SearchRequestFlag;
use App\Startpage\Controller\StartpageController;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class HomeController extends AbstractController
{
    private const int CACHE_TIME_TO_LIVE_IN_SECONDS = 300;

    public function __construct(
        private readonly ContentPageHomeController $contentPageHomeController,
        private readonly SearchHomeController $searchHomeController,
        private readonly StartpageController $startpageController,
        private readonly RequestInfoInterface $requestInfo,
        private readonly ResponseCachingHelper $responseCachingHelper
    )
    {
    }

    #[Route(
        path    : '/',
        name    : 'route_home',
        defaults: [
            SearchRequestFlag::IGNORE_QUERY_FOR_SEARCH => true,
        ],
        methods : ['GET'],
        priority: 1
    )]
    public function home(): Response
    {
        // Cache index page without any url parameters
        if (!$this->requestInfo->hasUrlParameters()) {
            $this->responseCachingHelper->startResponseCaching(self::CACHE_TIME_TO_LIVE_IN_SECONDS);
        }

        $response = $this->contentPageHomeController->home()
                    ?? $this->startpageController->home()
                       ?? $this->searchHomeController->home();

        if ($response === null) {
            throw NoActiveHomeControllerFoundException::create();
        }

        return $response;
    }
}

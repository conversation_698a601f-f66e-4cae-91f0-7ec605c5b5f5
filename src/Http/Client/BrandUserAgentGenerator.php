<?php

declare(strict_types=1);

namespace App\Http\Client;

use App\Brand\Settings\BrandSettingsHelper;

readonly class BrandUserAgentGenerator
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function generate(): string
    {
        return sprintf(
            '%s-Visymo (+https://www.visymo.com)',
            ucfirst($this->brandSettingsHelper->getSettings()->getSlug()),
        );
    }
}

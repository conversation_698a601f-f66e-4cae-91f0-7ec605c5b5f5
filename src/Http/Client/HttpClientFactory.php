<?php

declare(strict_types=1);

namespace App\Http\Client;

use App\Http\Request\LoadBalancer\LoadBalancerRequestInterface;
use GuzzleHttp\Client as GuzzleClient;
use Http\Adapter\Guzzle7\Client;
use Http\Client\HttpAsyncClient;

readonly class HttpClientFactory
{
    public function __construct(
        private BrandUserAgentGenerator $brandUserAgentGenerator,
        private LoadBalancerRequestInterface $loadBalancerRequest
    )
    {
    }

    public function create(float $timeout, bool $addBrandUserAgentHeader = true): HttpAsyncClient
    {
        return Client::createWithConfig(
            [
                'timeout' => $timeout,
                'headers' => $this->getHeaders($addBrandUserAgentHeader),
            ],
        );
    }

    public function createGuzzleClient(
        float $timeout,
        ?float $timeoutSfo = null,
        bool $addBrandUserAgentHeader = true
    ): GuzzleClient
    {
        if ($timeoutSfo !== null && $this->loadBalancerRequest->isSfo()) {
            $timeout = $timeoutSfo;
        }

        return new GuzzleClient(
            [
                'timeout' => $timeout,
                'headers' => $this->getHeaders($addBrandUserAgentHeader),
            ],
        );
    }

    /**
     * @return array<string, string>
     */
    private function getHeaders(bool $addBrandUserAgentHeader): array
    {
        $headers = [];

        if ($addBrandUserAgentHeader) {
            $headers['User-Agent'] = $this->brandUserAgentGenerator->generate();
        }

        return $headers;
    }
}

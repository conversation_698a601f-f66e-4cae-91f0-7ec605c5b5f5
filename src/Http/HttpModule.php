<?php

declare(strict_types=1);

namespace App\Http;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Http\Request\RequestInterface;
use App\Http\Url\PersistentUrlParametersProviderInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class HttpModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'http';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $container->registerForAutoconfiguration(PersistentUrlParametersProviderInterface::class)
            ->addTag('brand_website.http.url.persistent_parameters_provider');

        $container->registerForAutoconfiguration(RequestInterface::class)
            ->addTag('brand_website.http.request');
    }
}

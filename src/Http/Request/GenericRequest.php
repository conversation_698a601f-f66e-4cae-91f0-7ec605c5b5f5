<?php

declare(strict_types=1);

namespace App\Http\Request;

use App\AdBot\Request\AdBotRequestInterface;
use App\FriendlyBot\Bot\FriendlyBot;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

/**
 * This class is final because it should not be extended. Extending this request object will result in multiple instances that do not share
 * their properties. Which is less efficient and will result in unexpected behaviour for generated fields like visit_id.
 *
 * If this needs to be mocked or stubbed, stub/mock the GenericRequestInterface instead
 */
final class GenericRequest implements GenericRequestInterface
{
    private string $visitId;

    private string $pageviewId;

    private int $appTs;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer,
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    public function getPageviewId(): ?string
    {
        if (isset($this->pageviewId)) {
            return $this->requestPropertyNormalizer->getString($this->pageviewId);
        }

        if ($this->adBotRequest->isAdBot() ||
            ($this->friendlyBotRequest->isFriendlyBot() && $this->friendlyBotRequest->getFriendlyBot() !== FriendlyBot::APIFY)
        ) {
            $this->pageviewId = '';
        } else {
            $this->pageviewId = uuid_create(UUID_TYPE_RANDOM);
        }

        return $this->requestPropertyNormalizer->getString($this->pageviewId);
    }

    public function getVisitId(): ?string
    {
        if (isset($this->visitId)) {
            return $this->requestPropertyNormalizer->getString($this->visitId);
        }

        if ($this->adBotRequest->isAdBot() ||
            ($this->friendlyBotRequest->isFriendlyBot() && $this->friendlyBotRequest->getFriendlyBot() !== FriendlyBot::APIFY)
        ) {
            $this->visitId = '';
        } else {
            $this->visitId = $this->requestManager->queryBag()->getString(self::PARAMETER_VISIT_ID);

            if ($this->visitId === '' ||
                preg_match('/^[a-f\d]{8}(-[a-f\d]{4}){4}[a-f\d]{8}$/i', $this->visitId) !== 1
            ) {
                $this->visitId = uuid_create(UUID_TYPE_RANDOM);
            }
        }

        return $this->requestPropertyNormalizer->getString($this->visitId);
    }

    public function getAppTs(): ?int
    {
        if (!isset($this->appTs)) {
            $this->appTs = $this->requestManager->queryBag()->getInt(self::PARAMETER_APP_TS);
        }

        return $this->requestPropertyNormalizer->getInt($this->appTs);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_PAGEVIEW_ID    => $this->getPageviewId(),
            self::PARAMETER_VISIT_ID => $this->getVisitId(),
            self::PARAMETER_APP_TS   => $this->getAppTs(),
        ];
    }
}

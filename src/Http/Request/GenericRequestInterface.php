<?php

declare(strict_types=1);

namespace App\Http\Request;

/**
 * This class should not be extended. Extending this request object will result in multiple instances that do not share
 * their properties. Which is less efficient and will result in unexpected behaviour for generated fields like visit_id.
 *
 * Final keyword can't be used here because that prevents mocking/stubbing.
 */
interface GenericRequestInterface extends RequestInterface
{
    public const string KEY_PAGEVIEW_ID = 'pageview_id';

    public const string PARAMETER_APP_TS               = 'app_ts';
    public const string PARAMETER_VISIT_ID             = 'vid';
    public const string PARAMETER_PREVIOUS_PAGEVIEW_ID = 'ppid';

    /**
     * Only supports generating a pageview_id, because regular requests do not support passing the pageview_id by
     * querystring parameter. If this is needed, it should be implemented in the request object that needs it.
     * For example, {@see ConversionTrackingRequestInterface::getOriginalPageviewId}
     */
    public function getPageviewId(): ?string;

    /**
     * Visit ID will be generated on the first page visited and passed to any subsequent pages via querystring parameter.
     */
    public function getVisitId(): ?string;

    public function getAppTs(): ?int;
}

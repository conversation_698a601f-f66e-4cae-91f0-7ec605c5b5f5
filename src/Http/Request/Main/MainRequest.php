<?php

declare(strict_types=1);

namespace App\Http\Request\Main;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

final class MainRequest implements MainRequestInterface
{
    private ?Request $request = null;

    public function __construct(
        private readonly RequestStack $requestStack
    )
    {
    }

    public function getRequest(): Request
    {
        if ($this->request !== null) {
            return $this->request;
        }

        $this->request = $this->requestStack->getMainRequest();

        if ($this->request === null) {
            throw MainRequestNotAvailableException::create();
        }

        return $this->request;
    }
}

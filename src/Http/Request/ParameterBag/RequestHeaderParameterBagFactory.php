<?php

declare(strict_types=1);

namespace App\Http\Request\ParameterBag;

use Psr\Log\LoggerInterface;

readonly class RequestHeaderParameterBagFactory
{
    public function __construct(
        private LoggerInterface $logger
    )
    {
    }

    /**
     * @param array<array-key, mixed[]> $parameters
     */
    public function create(array $parameters): RequestHeaderParameterBag
    {
        return new RequestHeaderParameterBag(
            parameters                   : $parameters,
            handleParametersCaseSensitive: false,
            logger                       : $this->logger,
        );
    }
}

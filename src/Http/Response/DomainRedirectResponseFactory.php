<?php

declare(strict_types=1);

namespace App\Http\Response;

use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Url\DevelopHostHelper;
use App\Http\Url\UrlWithParametersHelper;
use Symfony\Component\HttpFoundation\RedirectResponse;

class DomainRedirectResponseFactory
{
    public function __construct(
        private readonly RequestInfoInterface $requestInfo,
        private readonly DevelopHostHelper $developHostHelper,
        private readonly UrlWithParametersHelper $urlWithParametersHelper
    )
    {
    }

    public function createForCurrentRequest(string $domain): RedirectResponse
    {
        $url = $this->urlWithParametersHelper->getUrlWithParameters(
            $this->requestInfo->getPathInfo(),
            $this->developHostHelper->addDevelopToHost($domain),
        );

        return new RedirectResponse($url);
    }
}

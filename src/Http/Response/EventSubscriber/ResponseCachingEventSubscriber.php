<?php

declare(strict_types=1);

namespace App\Http\Response\EventSubscriber;

use App\Http\Response\ResponseCachingHelper;
use App\Kernel\KernelResponseEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

readonly class ResponseCachingEventSubscriber implements EventSubscriberInterface
{
    public function __construct(private ResponseCachingHelper $responseCachingHelper)
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            // Must be the last event subscriber
            KernelResponseEvent::NO_REDIRECT->value => ['enableResponseCaching', -10000],
        ];
    }

    public function enableResponseCaching(ResponseEvent $responseEvent): void
    {
        if (!$this->responseCachingHelper->responseCachingStarted()) {
            return;
        }

        $this->responseCachingHelper->enableResponseCaching($responseEvent->getResponse());
    }
}

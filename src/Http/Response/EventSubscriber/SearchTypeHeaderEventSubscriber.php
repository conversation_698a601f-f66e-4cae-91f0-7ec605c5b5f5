<?php

declare(strict_types=1);

namespace App\Http\Response\EventSubscriber;

use App\Http\Request\Main\MainRequestInterface;
use App\JsonTemplate\Event\JsonTemplateSearchSubmittedEvent;
use App\Kernel\KernelResponseEvent;
use App\Search\Request\SearchRequestFlag;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

class SearchTypeHeaderEventSubscriber implements EventSubscriberInterface
{
    private const string HEADER_X_SEARCH_TYPE = 'X-Log-Search_Type';

    private ?string $searchType = null;

    public function __construct(
        private readonly MainRequestInterface $mainRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            JsonTemplateSearchSubmittedEvent::NAME  => 'setSearchTypeValue',
            KernelResponseEvent::NO_REDIRECT->value => 'setSearchTypeHeaderResponse',
        ];
    }

    public function setSearchTypeValue(): void
    {
        $this->searchType = $this->mainRequest->getRequest()->attributes->get(SearchRequestFlag::TYPE);
    }

    public function setSearchTypeHeaderResponse(ResponseEvent $responseEvent): void
    {
        $response = $responseEvent->getResponse();
        $this->setSearchTypeHeader($response);
    }

    private function setSearchTypeHeader(Response $response): void
    {
        if ($this->searchType === null) {
            return;
        }

        // Make load balancer add search type to access log
        $response->headers->set(self::HEADER_X_SEARCH_TYPE, $this->searchType);
    }
}

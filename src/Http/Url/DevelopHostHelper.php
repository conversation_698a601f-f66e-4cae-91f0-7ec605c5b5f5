<?php

declare(strict_types=1);

namespace App\Http\Url;

class DevelopHostHelper
{
    private const string DEVELOP_HOST_SUFFIX = '.ldev.nl';

    private readonly ?string $devVmName;

    private string $developHostSuffix;

    public function __construct(?string $devVmName = null)
    {
        if ($devVmName !== null && trim($devVmName) === '') {
            $devVmName = null;
        }

        $this->devVmName = $devVmName;
    }

    public function hasDevVmName(): bool
    {
        return $this->devVmName !== null;
    }

    public function getDevVmName(): ?string
    {
        return $this->devVmName;
    }

    /**
     * Strip .user.ldev.nl from example.com.user.ldev.nl
     */
    public function removeDevelopFromHost(string $host): string
    {
        if ($this->devVmName !== null && $this->isDevelopHost($host)) {
            $host = substr($host, 0, -strlen($this->getDevelopHostSuffix()));
        }

        return $host;
    }

    public function addDevelopToHost(string $host): string
    {
        if ($this->devVmName !== null && !$this->isDevelopHost($host)) {
            $host = $this->getHostWithDevVmName($host, $this->devVmName);
        }

        return $host;
    }

    public function addDevelopToUrl(string $url): string
    {
        if (!$this->hasDevVmName()) {
            return $url;
        }

        /** @var mixed[] $urlParts */
        $urlParts = parse_url($url);

        return sprintf(
            '%s://%s%s%s',
            $urlParts['scheme'] ?? 'https',
            $this->addDevelopToHost($urlParts['host'] ?? ''),
            $urlParts['path'] ?? '',
            isset($urlParts['query']) ? '?'.$urlParts['query'] : '',
        );
    }

    public function getHostWithDevVmName(string $host, string $devVmName): string
    {
        return sprintf('%s.%s%s', $host, $devVmName, self::DEVELOP_HOST_SUFFIX);
    }

    public function getDevelopHostSuffix(): string
    {
        if (!isset($this->developHostSuffix)) {
            $this->developHostSuffix = $this->devVmName !== null ? $this->getHostWithDevVmName('', $this->devVmName) : '';
        }

        return $this->developHostSuffix;
    }

    public function isDevelopHost(string $host): bool
    {
        $developHostSuffix = substr($host, -strlen(self::DEVELOP_HOST_SUFFIX));

        return $developHostSuffix === self::DEVELOP_HOST_SUFFIX;
    }
}

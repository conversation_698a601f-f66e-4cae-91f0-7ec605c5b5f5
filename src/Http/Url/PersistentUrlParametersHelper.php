<?php

declare(strict_types=1);

namespace App\Http\Url;

class PersistentUrlParametersHelper
{
    private bool $enabled = true;

    /** @var PersistentUrlParametersProviderInterface[] */
    private array $parameterProviders = [];

    /**
     * @param iterable<PersistentUrlParametersProviderInterface> $parameterProviders
     */
    public function __construct(iterable $parameterProviders)
    {
        foreach ($parameterProviders as $parameterProvider) {
            $this->addParameterProvider($parameterProvider);
        }
    }

    public function disable(): void
    {
        $this->enabled = false;
    }

    private function addParameterProvider(PersistentUrlParametersProviderInterface $parameterProvider): void
    {
        $this->parameterProviders[] = $parameterProvider;
    }

    /**
     * @return array<string, string>
     */
    public function getPersistentParameters(PersistentUrlParametersPageType $pageType): array
    {
        if (!$this->enabled) {
            return [];
        }

        $parameterStacks = [];

        foreach ($this->parameterProviders as $parameterProvider) {
            $parameterStacks[] = $parameterProvider->getPersistentUrlParameters($pageType);
        }

        return array_merge(...$parameterStacks);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Url;

use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Request\Manager\RequestManagerInterface;

readonly class UrlWithParametersHelper
{
    public function __construct(
        private RequestInfoInterface $requestInfo,
        private RequestManagerInterface $requestManager
    )
    {
    }

    public function getUrlWithParameters(string $path, ?string $host = null): string
    {
        $url = sprintf(
            '%s://%s%s',
            $this->requestInfo->getScheme(),
            $host ?? $this->requestInfo->getHost(),
            $path,
        );

        $queryString = http_build_query($this->requestManager->queryBag()->toArray());

        if ($queryString !== '') {
            $url = sprintf('%s?%s', $url, $queryString);
        }

        return $url;
    }
}

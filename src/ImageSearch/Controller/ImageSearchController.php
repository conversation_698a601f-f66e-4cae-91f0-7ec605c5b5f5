<?php

declare(strict_types=1);

namespace App\ImageSearch\Controller;

use App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchRequestFlag;
use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ImageSearchController extends AbstractController
{
    public function __construct(
        private readonly RouteRegistry $routeRegistry,
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer
    )
    {
    }

    #[Route(
        path    : '/image',
        name    : 'route_image_home',
        defaults: [
            SubmitSearchRequestFlag::PREVENT_CONVERSION_LOGGING => true,
            SearchRequestFlag::TYPE                             => SearchType::SEARCH->value,
        ],
        methods : ['GET'],
    )]
    public function home(): Response
    {
        $this->routeRegistry->setSearchRoute('route_image_search');

        return $this->jsonTemplateRenderer->render(
            '@themeJson/image_search/image_search_home.json',
        );
    }

    #[Route(
        path    : '/image/search',
        name    : 'route_image_search',
        defaults: [
            SubmitSearchRequestFlag::PREVENT_CONVERSION_LOGGING => true,
            SearchRequestFlag::TYPE                             => SearchType::SEARCH->value,
        ],
        methods : ['GET']
    )]
    public function search(): Response
    {
        $this->routeRegistry->setCurrentRouteAsSearchRoute();

        return $this->jsonTemplateRenderer->renderForSearch(
            '@themeJson/image_search/image_search.json',
            'route_image_home',
        );
    }
}

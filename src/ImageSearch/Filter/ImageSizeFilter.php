<?php

declare(strict_types=1);

namespace App\ImageSearch\Filter;

use App\ImageSearch\Request\ImageSearchRequestInterface;
use App\JsonTemplate\Component\SearchFilter\SearchFilter;
use App\JsonTemplate\Component\SearchFilter\SearchFilterFactory;
use <PERSON>ymfony\Contracts\Translation\TranslatorInterface;

class ImageSizeFilter
{
    private const string VALUE_SMALL     = 'small';
    private const string VALUE_MEDIUM    = 'medium';
    private const string VALUE_LARGE     = 'large';
    private const string VALUE_WALLPAPER = 'wallpaper';

    public const array SUPPORTED_VALUES = [
        self::VALUE_SMALL,
        self::VALUE_MEDIUM,
        self::VALUE_LARGE,
        self::VALUE_WALLPAPER,
    ];

    public function __construct(
        private readonly ImageSearchRequestInterface $imageSearchRequest,
        private readonly SearchFilterFactory $searchFilterFactory,
        private readonly TranslatorInterface $translator
    )
    {
    }

    /**
     * @return array<string, string>
     */
    private function getOptions(): array
    {
        return [
            self::VALUE_SMALL     => $this->translator->trans('image_search.image_size.item.small'),
            self::VALUE_MEDIUM    => $this->translator->trans('image_search.image_size.item.medium'),
            self::VALUE_LARGE     => $this->translator->trans('image_search.image_size.item.large'),
            self::VALUE_WALLPAPER => $this->translator->trans('image_search.image_size.item.wallpaper'),
        ];
    }

    public function getSearchFilter(): SearchFilter
    {
        $currentFilterValues = $this->imageSearchRequest->getValues();

        return $this->searchFilterFactory->create(
            $this->translator->trans('image_search.image_size.label'),
            'route_image_search',
            $currentFilterValues,
            ImageSearchRequestInterface::PARAMETER_IMAGE_SIZE,
            $this->getOptions(),
        );
    }
}

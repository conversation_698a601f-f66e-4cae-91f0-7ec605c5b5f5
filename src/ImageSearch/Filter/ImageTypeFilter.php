<?php

declare(strict_types=1);

namespace App\ImageSearch\Filter;

use App\ImageSearch\Request\ImageSearchRequestInterface;
use App\JsonTemplate\Component\SearchFilter\SearchFilter;
use App\JsonTemplate\Component\SearchFilter\SearchFilterFactory;
use Symfony\Contracts\Translation\TranslatorInterface;

class ImageTypeFilter
{
    private const string VALUE_ANIMATEDGIF = 'animatedgif';
    private const string VALUE_CLIPART     = 'clipart';
    private const string VALUE_LINE        = 'line';
    private const string VALUE_PHOTO       = 'photo';
    private const string VALUE_TRANSPARENT = 'transparent';

    public const array SUPPORTED_VALUES = [
        self::VALUE_ANIMATEDGIF,
        self::VALUE_CLIPART,
        self::VALUE_LINE,
        self::VALUE_PHOTO,
        self::VALUE_TRANSPARENT,
    ];

    public function __construct(
        private readonly ImageSearchRequestInterface $imageSearchRequest,
        private readonly SearchFilterFactory $searchFilterFactory,
        private readonly TranslatorInterface $translator
    )
    {
    }

    /**
     * @return array<string, string>
     */
    private function getOptions(): array
    {
        $options = [
            self::VALUE_ANIMATEDGIF => $this->translator->trans('image_search.image_type.item.gif'),
            self::VALUE_CLIPART     => $this->translator->trans('image_search.image_type.item.clipart'),
            self::VALUE_LINE        => $this->translator->trans('image_search.image_type.item.line'),
            self::VALUE_PHOTO       => $this->translator->trans('image_search.image_type.item.photo'),
            self::VALUE_TRANSPARENT => $this->translator->trans('image_search.image_type.item.transparent'),
        ];

        asort($options);

        return $options;
    }

    public function getSearchFilter(): SearchFilter
    {
        $currentFilterValues = $this->imageSearchRequest->getValues();

        return $this->searchFilterFactory->create(
            $this->translator->trans('image_search.image_type.label'),
            'route_image_search',
            $currentFilterValues,
            ImageSearchRequestInterface::PARAMETER_IMAGE_TYPE,
            $this->getOptions(),
        );
    }
}

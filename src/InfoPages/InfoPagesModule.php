<?php

declare(strict_types=1);

namespace App\InfoPages;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class InfoPagesModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'info_pages';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_info_pages.yaml']);
    }
}

<?php

declare(strict_types=1);

namespace App\InfoPages\Routing;

use App\Generic\Routing\RouteCheckerInterface;
use App\InfoPages\Settings\InfoPagesSettings;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class InfoPagesAboutRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_info_pages_about';

    public function __construct(
        private InfoPagesSettings $infoPagesSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return !$this->infoPagesSettings->linkToExternalAboutPage;
    }
}

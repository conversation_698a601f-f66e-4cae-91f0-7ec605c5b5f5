<?php

declare(strict_types=1);

namespace App\InfoPages\Settings;

use App\InfoPages\InfoPagesModule;
use App\InfoPages\Page\PageType;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;

final readonly class InfoPagesSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    private const string KEY_LINK_TO_EXTERNAL_ABOUT_PAGE = 'link_to_external_about_page';
    private const string KEY_LINK_TO_VISYMO_PUBLISHING   = 'link_to_visymo_publishing';
    private const string KEY_PAGE_TYPE                   = 'page_type';

    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper
    )
    {
    }

    public static function getModuleName(): string
    {
        return InfoPagesModule::getModuleName();
    }

    public function create(): InfoPagesSettings
    {
        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['info_pages']
                        ?? [];

        if ($moduleConfig === []) {
            return new InfoPagesSettings(
                pageType               : PageType::SEARCH,
                linkToExternalAboutPage: false,
                linkToVisymoPublishing : false,
            );
        }

        return new InfoPagesSettings(
            pageType               : PageType::from((string)$moduleConfig[self::KEY_PAGE_TYPE]),
            linkToExternalAboutPage: (bool)$moduleConfig[self::KEY_LINK_TO_EXTERNAL_ABOUT_PAGE],
            linkToVisymoPublishing : (bool)$moduleConfig[self::KEY_LINK_TO_VISYMO_PUBLISHING],
        );
    }
}

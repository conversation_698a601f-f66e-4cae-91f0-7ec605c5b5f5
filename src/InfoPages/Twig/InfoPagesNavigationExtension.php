<?php

declare(strict_types=1);

namespace App\InfoPages\Twig;

use App\InfoPages\Helper\InfoPagesUrlHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class InfoPagesNavigationExtension extends AbstractExtension
{
    public function __construct(
        private readonly InfoPagesUrlHelper $visymoUrlHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('info_pages_url', $this->visymoUrlHelper->getExternalUrl(...)),
        ];
    }
}

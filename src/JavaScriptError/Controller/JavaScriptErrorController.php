<?php

declare(strict_types=1);

namespace App\JavaScriptError\Controller;

use App\Generic\Response\UncachedPixelResponse;
use App\JavaScriptError\Helper\JavaScriptErrorLogHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class JavaScriptErrorController extends AbstractController
{
    #[Route(
        path   : '/js-error',
        name   : 'route_javascript_error',
        methods: ['GET']
    )]
    public function error(JavaScriptErrorLogHelper $javaScriptErrorLogHelper): Response
    {
        $javaScriptErrorLogHelper->logError();

        return new UncachedPixelResponse();
    }
}

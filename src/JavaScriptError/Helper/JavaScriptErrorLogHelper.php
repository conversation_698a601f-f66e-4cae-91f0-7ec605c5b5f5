<?php

declare(strict_types=1);

namespace App\JavaScriptError\Helper;

use App\AdBot\Request\AdBotRequestInterface;
use App\Brand\Settings\BrandSettingsHelper;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Http\Request\GenericRequestInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\JavaScriptError\Request\JavaScriptErrorRequestInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use Psr\Log\LoggerInterface;

readonly class JavaScriptErrorLogHelper
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper,
        private AdBotRequestInterface $adBotRequest,
        private JavaScriptErrorRequestInterface $javaScriptErrorRequest,
        private RequestInfoInterface $requestInfo,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private LoggerInterface $javaScriptErrorLogger,
        private GenericRequestInterface $genericRequest,
        private FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    public function logError(): void
    {
        $error = $this->javaScriptErrorRequest->getError();
        $url = $this->javaScriptErrorRequest->getUrl();

        if ($error === null || $url === null) {
            return;
        }

        // Include user IP in ACSD errors
        if (str_starts_with($error, 'ACSD: ') && $this->requestInfo->getUserIp() !== null) {
            $error = str_replace(
                'ACSD: ',
                sprintf('ACSD (%s): ', $this->requestInfo->getUserIp()),
                $error,
            );
        }

        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        $context = [
            'error'        => $error,
            'brand_id'     => $this->brandSettingsHelper->getSettings()->getSlug(),
            'url'          => $url,
            'user_agent'   => $this->requestInfo->getUserAgent(),
            'ad_bot'       => $this->adBotRequest->isAdBot(),
            'friendly_bot' => $this->friendlyBotRequest->isFriendlyBot(),
            'device'       => $trackingEntry->device->getShortValue(),
            'pageview_id'  => $this->genericRequest->getPageviewId(),
            'visit_id'     => $this->genericRequest->getVisitId(),
        ];

        $this->javaScriptErrorLogger->info('', $context);
    }
}

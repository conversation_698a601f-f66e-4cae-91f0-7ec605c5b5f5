<?php

declare(strict_types=1);

namespace App\JavaScriptError\Twig;

use App\JavaScriptError\Helper\JavaScriptTryCatchHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class JavaScriptErrorExtension extends AbstractExtension
{
    public function __construct(
        private readonly JavaScriptTryCatchHelper $javaScriptTryCatchHelper
    )
    {
    }

    /**
     * @return TwigFilter[]
     */
    public function getFilters(): array
    {
        return [
            new TwigFilter(
                'js_try_catch',
                $this->javaScriptTryCatchHelper->wrapJavaScript(...),
                ['is_safe' => ['html']],
            ),
        ];
    }
}

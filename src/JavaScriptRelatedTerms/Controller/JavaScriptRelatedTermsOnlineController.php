<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Controller;

use App\JavaScriptRelatedTerms\Helper\JavaScriptRelatedTermsViewLogHelper;
use App\Search\Request\SearchRequestFlag;
use App\Tracking\Entry\Request\TrackingEntryRequestFlag;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * @deprecated use JavaScriptRelatedTermsContentController instead, will be removed in SF-785
 */
class JavaScriptRelatedTermsOnlineController extends JavaScriptRelatedTermsContentController
{
    #[Route(
        path    : '/p/related-terms-online/v1',
        name    : 'route_javascript_related_terms_online',
        defaults: [
            SearchRequestFlag::IS_LANDING_PAGE                  => true,
            TrackingEntryRequestFlag::USE_RAC_AS_QUERY_FALLBACK => true,
        ],
        methods : ['GET']
    )]
    public function index(): Response
    {
        $this->logRouteName = 'route_javascript_related_terms_online_log_view';

        return parent::index();
    }

    #[Route(
        path   : '/p/related-terms-online/v1/lv',
        name   : 'route_javascript_related_terms_online_log_view',
        methods: ['GET']
    )]
    public function logView(
        JavaScriptRelatedTermsViewLogHelper $javaScriptRelatedTermsViewLogHelper
    ): Response
    {
        return parent::logView($javaScriptRelatedTermsViewLogHelper);
    }
}

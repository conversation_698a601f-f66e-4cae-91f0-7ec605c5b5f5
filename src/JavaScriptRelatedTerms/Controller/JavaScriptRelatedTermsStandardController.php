<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Controller;

use App\JavaScriptRelatedTerms\Helper\JavaScriptRelatedTermsViewLogHelper;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * @deprecated use JavaScriptRelatedTermsContentController instead, will be removed in SF-785
 */
class JavaScriptRelatedTermsStandardController extends JavaScriptRelatedTermsContentController
{
    #[Route(
        path   : '/p/related-terms/v1',
        name   : 'route_javascript_related_terms',
        methods: ['GET']
    )]
    public function index(): Response
    {
        $this->logRouteName = 'route_javascript_related_terms_log_view';

        return parent::index();
    }

    #[Route(
        path   : '/p/related-terms/v1/lv',
        name   : 'route_javascript_related_terms_log_view',
        methods: ['GET']
    )]
    public function logView(
        JavaScriptRelatedTermsViewLogHelper $javaScriptRelatedTermsViewLogHelper
    ): Response
    {
        return parent::logView($javaScriptRelatedTermsViewLogHelper);
    }
}

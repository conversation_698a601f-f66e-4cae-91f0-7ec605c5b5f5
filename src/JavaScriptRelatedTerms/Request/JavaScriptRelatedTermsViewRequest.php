<?php

declare(strict_types=1);

namespace App\JavaScriptRelatedTerms\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class JavaScriptRelatedTermsViewRequest implements JavaScriptRelatedTermsViewRequestInterface
{
    private string $query;

    private string $url;

    private int $styleId;

    /** @var string[] */
    private ?array $relatedTerms;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getQuery(): ?string
    {
        if (!isset($this->query)) {
            $this->query = $this->requestManager->queryBag()->getString(self::PARAMETER_QUERY);
        }

        return $this->requestPropertyNormalizer->getString($this->query);
    }

    public function getUrl(): ?string
    {
        if (!isset($this->url)) {
            $value = $this->requestManager->queryBag()->getString(self::PARAMETER_URL);

            if ($value !== '' && filter_var($value, FILTER_VALIDATE_URL) === false) {
                $this->requestManager->queryBag()->logUnexpected(self::PARAMETER_URL, $value);
                $this->url = '';
            } else {
                $this->url = $value;
            }
        }

        return $this->requestPropertyNormalizer->getString($this->url);
    }

    public function getStyleId(): ?int
    {
        if (!isset($this->styleId)) {
            $this->styleId = $this->requestManager->queryBag()->getInt(self::PARAMETER_STYLE_ID);
        }

        return $this->requestPropertyNormalizer->getInt($this->styleId);
    }

    /**
     * @inheritDoc
     */
    public function getRelatedTerms(): ?array
    {
        if (!isset($this->relatedTerms)) {
            $this->relatedTerms = $this->requestManager->queryBag()->getNullableArray(self::PARAMETER_TERMS);
        }

        return $this->relatedTerms;
    }

    /**
     * @return array<string, array<string>|int|string|null>
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_QUERY    => $this->getQuery(),
            self::PARAMETER_URL      => $this->getUrl(),
            self::PARAMETER_STYLE_ID => $this->getStyleId(),
            self::PARAMETER_TERMS    => $this->getRelatedTerms(),
        ];
    }
}

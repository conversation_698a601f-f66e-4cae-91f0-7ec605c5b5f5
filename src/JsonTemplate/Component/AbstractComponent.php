<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

abstract class AbstractComponent implements ComponentInterface
{
    private string $id;

    abstract public static function getType(): string;

    public static function isInternal(): bool
    {
        return false;
    }

    final public function getId(): string
    {
        if (!isset($this->id)) {
            $this->id = sprintf('%s-%u', static::getType(), ComponentIdRegistry::generateId());
        }

        return $this->id;
    }
}

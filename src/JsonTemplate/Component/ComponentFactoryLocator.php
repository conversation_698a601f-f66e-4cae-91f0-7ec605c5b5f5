<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use Symfony\Component\DependencyInjection\ServiceLocator;

readonly class ComponentFactoryLocator
{
    /**
     * @param ServiceLocator<mixed> $serviceLocator
     */
    public function __construct(private ServiceLocator $serviceLocator)
    {
    }

    public function getComponentFactory(string $componentNamespace): ComponentFactoryInterface
    {
        return $this->serviceLocator->get($componentNamespace);
    }
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

final class ComponentRenderRegistry
{
    /** @var array<string, bool> */
    private array $renderedComponentHeaders = [];

    /** @var array<string, bool> */
    private array $renderedComponentStyles = [];

    /** @var array<string, bool> */
    private array $renderedComponentJavaScripts = [];

    public function registerComponentHeaderRender(ComponentInterface $component): void
    {
        $this->renderedComponentHeaders[$component::getType()] = true;
    }

    public function isComponentHeaderRendered(ComponentInterface $component): bool
    {
        return array_key_exists($component::getType(), $this->renderedComponentHeaders);
    }

    public function registerComponentStyleRender(string $componentStyleName): void
    {
        $this->renderedComponentStyles[$componentStyleName] = true;
    }

    public function isComponentStyleRendered(string $componentStyleName): bool
    {
        return array_key_exists($componentStyleName, $this->renderedComponentStyles);
    }

    public function registerComponentJavaScriptRender(string $componentJavaScriptName): void
    {
        $this->renderedComponentJavaScripts[$componentJavaScriptName] = true;
    }

    public function isComponentJavaScriptRendered(string $componentJavaScriptName): bool
    {
        return array_key_exists($componentJavaScriptName, $this->renderedComponentJavaScripts);
    }

    public function reset(): void
    {
        $this->renderedComponentHeaders = [];
        $this->renderedComponentStyles = [];
        $this->renderedComponentJavaScripts = [];
    }
}

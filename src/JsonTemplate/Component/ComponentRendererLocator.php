<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use Symfony\Component\DependencyInjection\ServiceLocator;

readonly class ComponentRendererLocator
{
    /**
     * @param ServiceLocator<mixed> $serviceLocator
     */
    public function __construct(private ServiceLocator $serviceLocator)
    {
    }

    public function getRenderer(ComponentInterface $component): ComponentRendererInterface
    {
        return $this->serviceLocator->get($component->getRenderer());
    }
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

interface ComponentResolverDecoratorInterface
{
    public function getComponentResolverLocator(): ComponentResolverLocator;

    /**
     * @param mixed[][] $options
     *
     * @return mixed[][]
     */
    public function resolveMultiple(array $options): array;

    /**
     * @param mixed[] $options
     *
     * @return mixed[]
     */
    public function resolve(array $options): array;
}

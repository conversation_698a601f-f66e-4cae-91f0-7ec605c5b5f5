<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

interface ComponentResolverInterface
{
    /**
     * @return class-string<ComponentInterface>
     */
    public static function getSupportedComponent(): string;

    /**
     * @param mixed[] $options
     *
     * @return mixed[]
     */
    public function resolve(array $options, ComponentResolverDecoratorInterface $componentResolver): array;
}

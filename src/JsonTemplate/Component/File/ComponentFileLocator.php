<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\File;

use App\JsonTemplate\Component\ComponentResolverLocator;
use App\JsonTemplate\Component\Layout\ComponentLayoutHelper;
use Symfony\Component\String\UnicodeString;
use Visymo\Filesystem\File\FileFactory;
use Visymo\Filesystem\File\FileInterface;

final readonly class ComponentFileLocator
{
    public function __construct(
        private ComponentResolverLocator $componentResolverLocator,
        private ComponentLayoutHelper $componentLayoutHelper,
        private FileFactory $fileFactory,
        private string $projectDir
    )
    {
    }

    public function getComponentFile(string $componentType): FileInterface
    {
        $componentResolver = $this->componentResolverLocator->getResolver($componentType);
        $componentClass = $componentResolver::getSupportedComponent();

        return $this->fileFactory->create($this->getFilePath($componentClass));
    }

    public function getComponentLayoutFile(string $componentType, string $layout): ?FileInterface
    {
        try {
            $layoutClass = $this->componentLayoutHelper->getComponentLayout($componentType);

            if ($layoutClass === null) {
                return null;
            }

            $layoutCase = $layoutClass::from($layout);

            $layoutTwigTemplateParts = explode('/', $layoutCase->getTwigTemplate());
            $layoutTwigTemplateFileName = array_pop($layoutTwigTemplateParts);
            $layoutString = new UnicodeString($layout);

            $filePath = sprintf(
                '%s/layout/%s/%s',
                $this->getFileDir($layoutClass::class),
                $layoutString->camel()->toString(),
                $layoutTwigTemplateFileName,
            );

            return $this->fileFactory->create($filePath);
        } catch (\LogicException) {
            return null;
        }
    }

    private function getFilePath(string $class): string
    {
        $classParts = explode('\\', $class);
        $className = array_pop($classParts);

        return sprintf(
            '%s/%s.php',
            $this->getFileDir($class),
            $className,
        );
    }

    private function getFileDir(string $class): string
    {
        $classParts = explode('\\', $class);
        $isApp = array_shift($classParts) === 'App';

        if ($isApp) {
            $srcPath = '/src';
        } else {
            $bundleName = (string)array_shift($classParts);
            $bundleName = (new UnicodeString($bundleName))->snake()->toString();
            $bundleName = str_replace('_', '-', $bundleName);

            $srcPath = sprintf('/bundles/%s/src', $bundleName);
        }

        array_pop($classParts);

        return sprintf(
            '%s%s/%s',
            $this->projectDir,
            $srcPath,
            implode('/', $classParts),
        );
    }
}

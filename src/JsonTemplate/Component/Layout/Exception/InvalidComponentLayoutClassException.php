<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\Layout\Exception;

final class InvalidComponentLayoutClassException extends \RuntimeException
{
    public static function create(string $layoutClass, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Class "%s" is not a component layout class', $layoutClass),
            0,
            $previous,
        );
    }
}

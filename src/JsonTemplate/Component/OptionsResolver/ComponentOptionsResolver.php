<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\OptionsResolver;

use App\Generic\Validator\AllowedEnumCaseValidator;
use App\Http\Url\UrlValidationRouter;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\Layout\ComponentLayoutHelper;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use App\JsonTemplate\Component\OptionsResolver\Exception\InvalidComponentOptionsException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedPatternValidator;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

final class ComponentOptionsResolver extends SymfonyOptionsResolverBridge implements ComponentOptionsResolverInterface
{
    /** @var array<string, string> */
    private array $componentFields = [];

    public function __construct(
        private readonly ComponentResolverDecoratorInterface $componentResolver,
        private readonly UrlValidationRouter $urlValidationRouter,
        OptionsResolver $optionsResolver = new OptionsResolver()
    )
    {
        parent::__construct($optionsResolver);
    }

    public function define(string $field): ComponentOptionDefinitionInterface
    {
        $this->optionsResolver->setDefined($field);

        return new ComponentOptionDefinition(
            field              : $field,
            componentResolver  : $this->componentResolver,
            optionsResolver    : $this->optionsResolver,
            urlValidationRouter: $this->urlValidationRouter,
        );
    }

    /**
     * @param class-string<ComponentInterface> $componentClass
     */
    public function defineComponent(
        string $field,
        string $componentClass,
        bool $nullable = false
    ): ComponentOptionDefinitionInterface
    {
        $this->componentFields[$field] = $componentClass;

        // Define field here, define and resolve component options during parent component options resolve
        $definition = $this->define($field);
        $definition->setAllowedType(OptionType::TYPE_ARRAY, $nullable);

        if ($nullable) {
            $definition->setDefaultValue(null);
        } else {
            $definition->setRequired();
        }

        return $definition;
    }

    /**
     * @param class-string<LayoutInterface> $layoutClass
     */
    public function defineLayout(string $layoutClass): ComponentOptionDefinitionInterface
    {
        $layout = ComponentLayoutHelper::getLayout($layoutClass);
        $definition = $this->define(LayoutInterface::KEY);
        $definition
            ->setAllowedType(OptionType::TYPE_STRING)
            ->setDefaultValue($layout::getDefault()->value)
            ->addValidator(
                new AllowedEnumCaseValidator($layout::cases()),
            );

        return $definition;
    }

    /**
     * @inheritDoc
     */
    public function defineRoute(
        string $field,
        array $allowedRoutes = [],
        bool $nullable = true
    ): ComponentOptionDefinitionInterface
    {
        $definition = $this->define($field);

        if ($nullable) {
            $definition->setDefaultValue(null);
        }

        $definition
            ->setAllowedType(OptionType::TYPE_STRING, $nullable)
            ->addCallbackValidator(fn (?string $value) => $value === null ? null : $this->urlValidationRouter->validate($value));

        if ($allowedRoutes !== []) {
            $definition->addValidator(new AllowedValueValidator($allowedRoutes));
        } else {
            $definition->addValidator(
                new AllowedPatternValidator('/^route_[a-z][a-z_]+[a-z]$/'),
            );
        }

        return $definition;
    }

    public function setPrototype(bool $prototype): void
    {
        $this->optionsResolver->setPrototype($prototype);
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options): array
    {
        $options = parent::resolve($options);

        if ($this->componentFields === []) {
            return $options;
        }

        // Resolve component field options
        foreach ($this->componentFields as $field => $expectedClass) {
            if ($options[$field] === null) {
                continue;
            }

            $componentOptions = $this->componentResolver->resolve($options[$field]);
            $componentType = $componentOptions[ComponentInterface::KEY_TYPE];

            $componentResolver = $this->componentResolver
                ->getComponentResolverLocator()
                ->getResolver($componentType);

            $accept = $componentResolver::getSupportedComponent() === $expectedClass;

            if (!$accept) {
                $implementsClasses = class_implements($componentResolver::getSupportedComponent());
                $accept = array_key_exists($expectedClass, $implementsClasses);
            }

            if (!$accept) {
                $exceptionMessage = sprintf(
                    '"%s" does not implements "%s"',
                    $componentResolver::getSupportedComponent(),
                    $expectedClass,
                );

                throw InvalidComponentOptionsException::create(
                    componentType: null,
                    field        : $field,
                    message      : $exceptionMessage,
                );
            }

            $options[$field] = $componentOptions;
        }

        return $options;
    }
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\OptionsResolver;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Layout\LayoutInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;

interface ComponentOptionsResolverInterface extends OptionsResolverInterface
{
    public function define(string $field): ComponentOptionDefinitionInterface;

    /**
     * @param class-string<ComponentInterface> $componentClass
     */
    public function defineComponent(
        string $field,
        string $componentClass,
        bool $nullable = true
    ): ComponentOptionDefinitionInterface;

    /**
     * @param class-string<LayoutInterface> $layoutClass
     */
    public function defineLayout(string $layoutClass): ComponentOptionDefinitionInterface;

    /**
     * @param string[] $allowedRoutes
     */
    public function defineRoute(
        string $field,
        array $allowedRoutes = [],
        bool $nullable = false
    ): ComponentOptionDefinitionInterface;

    public function setPrototype(bool $prototype): void;
}

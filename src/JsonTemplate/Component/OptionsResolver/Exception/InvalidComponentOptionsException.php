<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\OptionsResolver\Exception;

use Visymo\Shared\Domain\Exception\ExceptionWithContextInterface;

final class InvalidComponentOptionsException extends \RuntimeException implements ExceptionWithContextInterface
{
    /** @var mixed[] */
    private array $context = [];

    public static function create(
        ?string $componentType,
        string $field,
        string $message,
        ?\Throwable $previous = null
    ): self
    {
        $instance = new self(
            sprintf(
                'Invalid "%s%s" component option given: %s',
                $componentType !== null ? sprintf('%s.', $componentType) : '',
                $field,
                $message,
            ),
            0,
            $previous,
        );
        $instance->context = [
            'componentType' => $componentType,
            'field'         => $field,
            'message'       => $message,
        ];

        return $instance;
    }

    /**
     * @inheritDoc
     */
    public function getContext(): array
    {
        return $this->context;
    }
}

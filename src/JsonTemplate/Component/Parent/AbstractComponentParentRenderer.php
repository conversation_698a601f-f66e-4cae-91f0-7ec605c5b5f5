<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\Parent;

use App\Debug\Component\DebugComponentTraceLog;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\DataRequest\ViewDataRequestBuilder;
use App\JsonTemplate\View\JsonTemplateViewRenderer;
use App\JsonTemplate\View\ViewInterface;
use Symfony\Contracts\Service\Attribute\Required;
use Twig\Environment;

abstract class AbstractComponentParentRenderer implements ComponentRendererInterface
{
    protected readonly Environment $twig;

    protected readonly ViewDataRequestBuilder $viewDataRequestBuilder;

    protected readonly JsonTemplateViewRenderer $jsonTemplateViewRenderer;

    protected readonly DebugComponentTraceLog $debugComponentTraceLog;

    #[Required]
    public function setAbstractDependencies(
        Environment $twig,
        ViewDataRequestBuilder $viewDataRequestBuilder,
        JsonTemplateViewRenderer $jsonTemplateViewRenderer,
        DebugComponentTraceLog $debugComponentTraceLog
    ): void
    {
        $this->twig = $twig;
        $this->viewDataRequestBuilder = $viewDataRequestBuilder;
        $this->jsonTemplateViewRenderer = $jsonTemplateViewRenderer;
        $this->debugComponentTraceLog = $debugComponentTraceLog;
    }

    /**
     * @return ComponentInterface[]
     */
    abstract protected function getComponents(ComponentInterface $component): array;

    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        $this->registerDataRequirements($component, $view->getDataRequest(), $conditions);

        $this->viewDataRequestBuilder->buildForComponents(
            components: $this->getComponents($component),
            view      : $view,
            conditions: $conditions,
        );
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        // can be empty by default
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        // can be empty by default
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        $components = $this->getComponents($component);

        $this->debugComponentTraceLog->addParentTrace(
            component      : $component,
            childComponents: $components,
        );

        return $this->jsonTemplateViewRenderer->renderComponents($components, $view);
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        return $this->jsonTemplateViewRenderer->renderComponentsHeaders(
            $this->getComponents($component),
            $view,
        );
    }

    public function renderFooters(ComponentInterface $component, ViewInterface $view): string
    {
        return $this->jsonTemplateViewRenderer->renderComponentsFooters(
            $this->getComponents($component),
            $view,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component\SearchFilter;

final class SearchFilter
{
    /** @var SearchFilterOption[] */
    private array $options = [];

    /**
     * @param SearchFilterOption[] $options
     */
    public function __construct(
        public readonly string $label,
        array $options
    )
    {
        foreach ($options as $option) {
            $this->addOption($option);
        }
    }

    public function addOption(SearchFilterOption $option): self
    {
        $this->options[] = $option;

        return $this;
    }

    /**
     * @return SearchFilterOption[]
     */
    public function getOptions(): array
    {
        return $this->options;
    }
}

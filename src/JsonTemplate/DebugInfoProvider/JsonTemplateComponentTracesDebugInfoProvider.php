<?php

declare(strict_types=1);

namespace App\JsonTemplate\DebugInfoProvider;

use App\Debug\Component\DebugComponentTrace;
use App\Debug\Component\DebugComponentTraceLog;
use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugLineInfo;

final class JsonTemplateComponentTracesDebugInfoProvider implements DebugInfoProviderInterface
{
    private const string KEY_JSON_TEMPLATE_COMPONENT_TRACES = 'json template component traces';

    public function __construct(
        private readonly DebugComponentTraceLog $debugComponentTraceLog
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        $debugInfo = new DebugLineInfo(
            self::KEY_JSON_TEMPLATE_COMPONENT_TRACES,
        );

        foreach ($this->debugComponentTraceLog->getDebugComponentTraces() as $debugComponentTrace) {
            $this->addDebugComponentTrace($debugInfo, $debugComponentTrace);
        }

        return [
            $debugInfo,
        ];
    }

    private function addDebugComponentTrace(
        DebugLineInfo $debugInfo,
        DebugComponentTrace $debugComponentTrace
    ): void
    {
        $debugInfo->addLine(
            $debugComponentTrace->depth,
            $debugComponentTrace->componentType,
            'color: inherit',
        );

        foreach ($debugComponentTrace->childComponentTraces as $property => $childComponentTraces) {
            $propertyConditions = implode(', ', $debugComponentTrace->conditions[$property] ?? []);

            if ($propertyConditions === '') {
                $debugInfo->addLine(
                    $debugComponentTrace->depth,
                    sprintf(
                        '↳ %s.%s',
                        $debugComponentTrace->componentType,
                        $property,
                    ),
                    'color: blue',
                );
            } else {
                $debugInfo->addLine(
                    $debugComponentTrace->depth,
                    sprintf(
                        '↳ %s.%s: %s',
                        $debugComponentTrace->componentType,
                        $property,
                        $propertyConditions,
                    ),
                    sprintf('color: %s', $property === 'yes' ? 'green' : 'red'),
                );
            }

            foreach ($childComponentTraces as $childComponentTrace) {
                $this->addDebugComponentTrace($debugInfo, $childComponentTrace);
            }
        }
    }

    public static function getDefaultPriority(): int
    {
        return 90;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_JSON_TEMPLATE_COMPONENT_TRACES,
        ];
    }
}

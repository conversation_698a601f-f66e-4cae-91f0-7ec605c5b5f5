<?php

declare(strict_types=1);

namespace App\JsonTemplate\Renderer;

use App\Generic\Device\Device;
use App\Http\Url\PersistentUrlParametersRouter;
use App\JsonTemplate\Event\JsonTemplateSearchSubmittedEvent;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use App\JsonTemplate\View\JsonTemplateViewHandler;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;

final readonly class JsonTemplateRenderer implements JsonTemplateRendererInterface
{
    public function __construct(
        private JsonTemplateViewHandler $jsonTemplateViewHandler,
        private JsonTemplateViewFactory $jsonTemplateViewFactory,
        private SearchRequestInterface $searchRequest,
        private PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private EventDispatcherInterface $eventDispatcher
    )
    {
    }

    public function render(string $jsonTemplateFile): Response
    {
        $view = $this->jsonTemplateViewFactory->create(
            jsonTemplateFile: $jsonTemplateFile,
        );

        return $this->jsonTemplateViewHandler->handle($view);
    }

    public function renderForSearch(string $jsonTemplateFile, ?string $noQueryRedirectRoute = null): Response
    {
        if ($this->searchRequest->getQuery() === null) {
            return $this->persistentUrlParametersRouter->redirectToRoute(
                $noQueryRedirectRoute ?? 'route_home',
            );
        }

        $response = new Response();

        $this->eventDispatcher->dispatch(
            new JsonTemplateSearchSubmittedEvent($this->searchRequest->getQueryAsString(), $response),
            JsonTemplateSearchSubmittedEvent::NAME,
        );

        $view = $this->jsonTemplateViewFactory->create(
            jsonTemplateFile: $jsonTemplateFile,
            response        : $response,
        );

        return $this->jsonTemplateViewHandler->handle($view);
    }

    public function renderForSearchByDevice(
        string $mobileJsonTemplateFile,
        string $desktopJsonTemplateFile,
        ?string $noQueryRedirectRoute = null
    ): Response
    {
        $device = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->device;
        $jsonTemplateFile = match ($device) {
            Device::MOBILE => $mobileJsonTemplateFile,
            default        => $desktopJsonTemplateFile,
        };

        return $this->renderForSearch($jsonTemplateFile, $noQueryRedirectRoute);
    }
}

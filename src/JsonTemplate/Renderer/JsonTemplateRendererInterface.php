<?php

declare(strict_types=1);

namespace App\JsonTemplate\Renderer;

use Symfony\Component\HttpFoundation\Response;

interface JsonTemplateRendererInterface
{
    public function render(string $jsonTemplateFile): Response;

    public function renderForSearch(string $jsonTemplateFile, ?string $noQueryRedirectRoute = null): Response;

    public function renderForSearchByDevice(
        string $mobileJsonTemplateFile,
        string $desktopJsonTemplateFile,
        ?string $noQueryRedirectRoute = null
    ): Response;
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;

final class JsonTemplateRequest implements JsonTemplateRequestInterface
{
    private string $templateVariant;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getTemplateVariant(): ?string
    {
        if (!isset($this->templateVariant)) {
            $value = $this->requestManager->queryBag()->getString(self::PARAMETER_TEMPLATE_VARIANT);

            if (preg_match('/^[a-z0-9]+$/', $value) !== 1) {
                $value = '';
            }

            $this->templateVariant = $value;
        }

        return $this->requestPropertyNormalizer->getString($this->templateVariant);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_TEMPLATE_VARIANT => $this->getTemplateVariant(),
        ];
    }
}

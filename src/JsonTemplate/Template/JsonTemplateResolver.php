<?php

declare(strict_types=1);

namespace App\JsonTemplate\Template;

use App\Component\Generic\Container\ContainerResolver;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentResolverDecoratorInterface;
use App\JsonTemplate\Component\OptionsResolver\ComponentOptionsResolverInterface;
use App\PageHeadTags\Factory\DefaultPageHeadTagsFactory;
use App\PageHeadTags\Tags\PageHeadTagsHelper;
use App\Preferences\Option\KeywordHighlightOption;
use App\Preferences\Option\LinkTypeOption;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;

readonly class JsonTemplateResolver
{
    private const string KEY_DESCRIPTION                      = 'description';
    public const string  KEY_LAYOUT                           = 'layout';
    public const string  KEY_LAYOUT_TEMPLATE                  = 'template';
    public const string  KEY_OPTIONS                          = 'options';
    public const string  KEY_OPTION_KEYWORD_HIGHLIGHT         = 'keyword_highlight';
    public const string  KEY_OPTION_ORGANIC_KEYWORD_HIGHLIGHT = 'organic_keyword_highlight';
    public const string  KEY_OPTION_ORGANIC_LINK_TYPE         = 'organic_link_type';
    public const string  KEY_OPTION_PAGE_HEAD_TAGS_TYPE       = 'page_head_tags_type';
    public const string  KEY_COMPONENTS                       = 'components';
    public const string  KEY_CONTAINER                        = 'container';

    public function __construct(
        private ComponentOptionsResolverInterface $optionsResolver,
        private ContainerResolver $containerResolver,
        private ComponentResolverDecoratorInterface $componentResolver,
        private PageHeadTagsHelper $pageHeadTagsHelper
    )
    {
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     *
     * @throws InvalidOptionException
     */
    public function resolve(array $options): array
    {
        $this->optionsResolver->define(self::KEY_DESCRIPTION)
            ->setDefaultValue(null)
            ->setAllowedType(OptionType::TYPE_STRING, true);

        $this->optionsResolver->define(self::KEY_LAYOUT)
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->setDefaultValue([])
            ->setNestedResolver(
                static function (ComponentOptionsResolverInterface $optionsResolver): void {
                    $optionsResolver->define(self::KEY_LAYOUT_TEMPLATE)
                        ->setDefaultValue('@theme/layout_default_components.html.twig')
                        ->setAllowedType(OptionType::TYPE_STRING);
                },
            );

        $this->optionsResolver->define(self::KEY_OPTIONS)
            ->setDefaultValue([])
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->setNestedResolver(
                function (OptionsResolverInterface $optionsResolver): void {
                    $optionsResolver->define(self::KEY_OPTION_KEYWORD_HIGHLIGHT)
                        ->setDefaultValue(null)
                        ->setAllowedType(OptionType::TYPE_STRING, true)
                        ->addValidator(new AllowedValueValidator(KeywordHighlightOption::SUPPORTED_OPTIONS));

                    // Organic options
                    $optionsResolver->define(self::KEY_OPTION_ORGANIC_KEYWORD_HIGHLIGHT)
                        ->setDefaultValue(null)
                        ->setAllowedType(OptionType::TYPE_STRING, true)
                        ->addValidator(new AllowedValueValidator(KeywordHighlightOption::SUPPORTED_OPTIONS));

                    $optionsResolver->define(self::KEY_OPTION_ORGANIC_LINK_TYPE)
                        ->setDefaultValue(null)
                        ->setAllowedType(OptionType::TYPE_STRING, true)
                        ->addValidator(new AllowedValueValidator(LinkTypeOption::SUPPORTED_OPTIONS));

                    // Page head tags
                    $optionsResolver->define(self::KEY_OPTION_PAGE_HEAD_TAGS_TYPE)
                        ->setDefaultValue(DefaultPageHeadTagsFactory::TYPE)
                        ->setAllowedType(OptionType::TYPE_STRING, true)
                        ->addValidator(
                            new AllowedValueValidator($this->pageHeadTagsHelper->getTypes()),
                        );
                },
            );

        $this->optionsResolver->define(self::KEY_CONTAINER)
            ->setAllowedType(OptionType::TYPE_ARRAY)
            ->setNestedResolver(
                function (ComponentOptionsResolverInterface $optionsResolver): void {
                    $this->containerResolver->defineOptions($optionsResolver);
                },
            );

        $this->optionsResolver->define(self::KEY_COMPONENTS)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_ARRAY);

        // resolve partial data
        $options = $this->optionsResolver->resolve($options);

        $componentsData = [
            [
                ComponentInterface::KEY_TYPE      => self::KEY_CONTAINER,
                ContainerResolver::KEY_COMPONENTS => $options[self::KEY_COMPONENTS] ?? [],
                ...$options[self::KEY_CONTAINER] ?? [],
            ],
        ];
        unset(
            $options[self::KEY_CONTAINER],
            $options[self::KEY_DESCRIPTION],
        );

        $options[self::KEY_COMPONENTS] = $this->componentResolver->resolveMultiple($componentsData);

        return $options;
    }
}

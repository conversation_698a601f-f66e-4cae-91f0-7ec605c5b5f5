<?php

declare(strict_types=1);

namespace App\JsonTemplate\Template\Locator;

use App\JsonTemplate\Request\JsonTemplateRequestInterface;
use App\JsonTemplate\Settings\JsonTemplateSettings;
use Twig\Environment;

readonly class JsonTemplateLocator
{
    public function __construct(
        private JsonTemplateRequestInterface $jsonTemplateRequest,
        private JsonTemplateSettings $jsonTemplateSettings,
        private Environment $twig
    )
    {
    }

    public function locate(string $jsonTemplateFile): JsonTemplateLocation
    {
        // 1. If the request has a variant, try to locate the variant file
        //    -> If this file does not exist, use the default
        // 2. If the request has no variant, try to use the settings variant
        //    -> If this file does not exist, use the default
        $jsonTemplateLocation = $this->getJsonTemplateLocationForVariant(
            $jsonTemplateFile,
            $this->jsonTemplateRequest->getTemplateVariant() ?? $this->jsonTemplateSettings->templateVariant,
        );

        return $jsonTemplateLocation ?? new JsonTemplateLocation(
            $jsonTemplateFile,
            $this->twig->getLoader()->getSourceContext($jsonTemplateFile)->getPath(),
            $this->extractVariantFromJsonTemplateFile($jsonTemplateFile),
        );
    }

    private function getJsonTemplateLocationForVariant(
        string $jsonTemplateFile,
        ?string $templateVariant
    ): ?JsonTemplateLocation
    {
        if ($templateVariant === null) {
            return null;
        }

        $jsonTemplateFileVariant = $this->getJsonTemplateFileVariantCandidate(
            $jsonTemplateFile,
            $templateVariant,
        );

        if (!$this->twig->getLoader()->exists($jsonTemplateFileVariant)) {
            return null;
        }

        $jsonTemplateFilePath = $this->twig->getLoader()->getSourceContext($jsonTemplateFileVariant)->getPath();

        return new JsonTemplateLocation(
            $jsonTemplateFileVariant,
            $jsonTemplateFilePath,
            $templateVariant,
        );
    }

    private function getJsonTemplateFileVariantCandidate(string $jsonTemplateFile, string $templateVariant): string
    {
        return str_replace(
            '.json',
            sprintf('-%s.json', $templateVariant),
            $jsonTemplateFile,
        );
    }

    private function extractVariantFromJsonTemplateFile(string $jsonTemplateFile): ?string
    {
        if (preg_match('~-(\w+)\.json$~', $jsonTemplateFile, $matches) === 1) {
            return $matches[1];
        }

        return null;
    }
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Data\Condition;

use App\Ads\AdsAmountRegistry;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final readonly class AmountOfOrganicResultsCompliesWithAdsAmountCondition implements ViewDataConditionInterface
{
    public function __construct(
        private ResultsAmountRegistry $resultsAmountRegistry,
        private AdsAmountRegistry $adsAmountRegistry,
        private bool $expectedResult
    )
    {
    }

    public function check(ViewDataRegistry $viewDataRegistry): bool
    {
        $amount = $this->resultsAmountRegistry->getRegisteredAmountByResultsType(ResultsType::ORGANIC);

        return $amount >= $this->adsAmountRegistry->getAmountOfAds() === $this->expectedResult;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'type'                       => 'amount_of_organic_results_complies_with_ads_amount',
            'amount_of_ads'              => $this->adsAmountRegistry->getAmountOfAds(),
            'amount_rendered_ad_organic' => $this->resultsAmountRegistry->getRegisteredAmountByResultsType(ResultsType::ORGANIC),
            'expected_result'            => $this->expectedResult,
        ];
    }

    public function toText(): string
    {
        $amountRegisteredAsOrganic = $this->resultsAmountRegistry->getRegisteredAmountByResultsType(ResultsType::ORGANIC);
        $amountOfAds = $this->adsAmountRegistry->getAmountOfAds();
        $expectedText = $this->expectedResult ? '' : 'NOT ';

        if ($amountRegisteredAsOrganic >= $amountOfAds) {
            return sprintf('%u %s>= %u', $amountRegisteredAsOrganic, $expectedText, $amountOfAds);
        }

        return sprintf('%u %s<= %u', $amountRegisteredAsOrganic, $expectedText, $amountOfAds);
    }
}

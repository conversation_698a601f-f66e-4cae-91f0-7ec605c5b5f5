<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Data\Condition;

use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final readonly class ViewDataConditionCollection
{
    /**
     * @param ViewDataConditionInterface[] $conditions
     */
    public function __construct(
        public array $conditions = []
    )
    {
    }

    public function check(ViewDataRegistry $viewDataRegistry): bool
    {
        foreach ($this->conditions as $condition) {
            if (!$condition->check($viewDataRegistry)) {
                return false;
            }
        }

        return true;
    }

    public function merge(self $collection): self
    {
        return new self([...$this->conditions, ...$collection->conditions]);
    }

    /**
     * @return ViewDataProperty[]
     */
    public function getDependencies(): array
    {
        $dependencies = [];

        foreach ($this->conditions as $condition) {
            if ($condition instanceof SearchResponseHasResultsCondition) {
                $dependencies[] = $condition->getViewDataProperty();
            }
        }

        return $dependencies;
    }

    public function equals(self $collection): bool
    {
        $conditions = $this->toArray();
        $collectionConditions = $collection->toArray();

        // sort conditions before comparing
        sort($conditions);
        sort($collectionConditions);

        return $conditions === $collectionConditions;
    }

    /**
     * @return mixed[]
     */
    public function toArray(): array
    {
        return array_map(
            static fn (ViewDataConditionInterface $condition) => $condition->toArray(),
            $this->conditions,
        );
    }
}

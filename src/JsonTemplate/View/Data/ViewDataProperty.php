<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Data;

enum ViewDataProperty: string
{
    case QUERY                     = 'query';
    case SEARCH_RESPONSES          = 'searchResponses';
    case KEYWORD_HIGHLIGHT         = 'keywordHighlight';
    case ORGANIC_KEYWORD_HIGHLIGHT = 'organicKeywordHighlight';
    case ORGANIC_LINK_TYPE         = 'organicLinkType';
    case ORGANIC_RESULTS           = 'organicResults';
    case CONTENT_PAGE              = 'contentPage';
    case CONTENT_PAGES             = 'contentPages';
    case CONTENT_PAGE_CATEGORIES   = 'contentPageCategories';
    case CONTENT_PAGE_CATEGORY     = 'contentPageCategory';
    case RELATED_TERMS             = 'relatedTerms';
    case IMAGE_RESULTS             = 'imageResults';
    case NEWS_RESULTS              = 'newsResults';
}

<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

class ContentPageViewDataRequest implements SearchApiViewDataRequestInterface
{
    private bool $enabled = false;

    private ?int $paragraphAmount = null;

    private ?int $publicId = null;

    /**
     * @inheritDoc
     */
    public function mergeWith(array $registries): void
    {
        foreach ($registries as $registry) {
            if (!$registry instanceof self) {
                continue;
            }

            if ($registry->getPublicId() !== null) {
                if ($this->getPublicId() !== $registry->getPublicId()) {
                    throw new \RuntimeException(
                        'Merging content page public ID with different values is not possible.',
                    );
                }

                $this->setPublicId($registry->getPublicId());
            }

            if ($registry->isEnabled()) {
                $this->enable();
            }

            if ($registry->getParagraphAmount() !== null) {
                $this->increaseParagraphAmount($registry->getParagraphAmount());
            }
        }
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function enable(): self
    {
        $this->enabled = true;

        return $this;
    }

    public function getParagraphAmount(): ?int
    {
        return $this->paragraphAmount;
    }

    public function increaseParagraphAmount(int $paragraphAmount): self
    {
        $this->paragraphAmount ??= 0;
        $this->paragraphAmount += $paragraphAmount;

        return $this;
    }

    public function getPublicId(): ?int
    {
        return $this->publicId;
    }

    public function setPublicId(int $publicId): self
    {
        $this->publicId = $publicId;

        return $this;
    }

    public function canUseSameSearchApiRequest(SearchApiViewDataRequestInterface $viewDataRequest): bool
    {
        if (!$viewDataRequest instanceof self) {
            return false;
        }

        if ($this->publicId !== $viewDataRequest->getPublicId()) {
            return false;
        }

        return true;
    }
}

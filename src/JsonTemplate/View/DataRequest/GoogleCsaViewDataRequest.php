<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

final class GoogleCsaViewDataRequest
{
    private ?int $numRepeated = null;

    private ?int $topAdAmount = null;

    private ?string $topAdContainer = null;

    private ?int $bottomAdAmount = null;

    private ?string $bottomAdContainer = null;

    /** @var GoogleCsaRelatedSearchUnitViewDataRequest[] */
    private array $relatedSearchUnits = [];

    /**
     * @param self[] $registries
     */
    public function mergeWith(array $registries): self
    {
        foreach ($registries as $registry) {
            if ($this->numRepeated !== null && $registry->getNumRepeated() !== null) {
                throw new \RuntimeException('Merging Google CSA numRepeated is not supported');
            }

            if ($this->topAdAmount !== null && $registry->getTopAdAmount() !== null) {
                throw new \RuntimeException('Merging Google CSA topUnitAmount is not supported');
            }

            if ($this->topAdContainer !== null && $registry->getTopAdContainer() !== null) {
                throw new \RuntimeException('Merging Google CSA topAdContainer is not supported');
            }

            if ($this->bottomAdAmount !== null && $registry->getBottomAdAmount() !== null) {
                throw new \RuntimeException('Merging Google CSA bottomUnitAmount is not supported');
            }

            if ($this->bottomAdContainer !== null && $registry->getBottomAdContainer() !== null) {
                throw new \RuntimeException('Merging Google CSA bottomAdContainer is not supported');
            }

            $this->numRepeated ??= $registry->getNumRepeated();
            $this->topAdAmount ??= $registry->getTopAdAmount();
            $this->topAdContainer ??= $registry->getTopAdContainer();
            $this->bottomAdAmount ??= $registry->getBottomAdAmount();
            $this->bottomAdContainer ??= $registry->getBottomAdContainer();

            foreach ($registry->getRelatedSearchUnits() as $relatedSearchUnit) {
                $this->addRelatedSearchUnit($relatedSearchUnit);
            }
        }

        return $this;
    }

    public function getNumRepeated(): ?int
    {
        return $this->numRepeated;
    }

    public function setNumRepeated(?int $numRepeated): self
    {
        $this->numRepeated = $numRepeated;

        return $this;
    }

    public function getTopAdAmount(): ?int
    {
        return $this->topAdAmount;
    }

    public function setTopAdAmount(?int $topAdAmount): self
    {
        $this->topAdAmount = $topAdAmount;

        return $this;
    }

    public function getTopAdContainer(): ?string
    {
        return $this->topAdContainer;
    }

    public function setTopAdContainer(?string $topAdContainer): self
    {
        $this->topAdContainer = $topAdContainer;

        return $this;
    }

    public function getBottomAdAmount(): ?int
    {
        return $this->bottomAdAmount;
    }

    public function setBottomAdAmount(?int $bottomAdAmount): self
    {
        $this->bottomAdAmount = $bottomAdAmount;

        return $this;
    }

    public function getBottomAdContainer(): ?string
    {
        return $this->bottomAdContainer;
    }

    public function setBottomAdContainer(?string $bottomAdContainer): self
    {
        $this->bottomAdContainer = $bottomAdContainer;

        return $this;
    }

    /**
     * @return GoogleCsaRelatedSearchUnitViewDataRequest[]
     */
    public function getRelatedSearchUnits(): array
    {
        return $this->relatedSearchUnits;
    }

    public function addRelatedSearchUnit(GoogleCsaRelatedSearchUnitViewDataRequest $relatedSearchUnit): self
    {
        foreach ($this->relatedSearchUnits as $unit) {
            if ($unit->container === $relatedSearchUnit->container) {
                throw new \InvalidArgumentException(
                    sprintf('Google related search container "%s" already in use', $relatedSearchUnit->container),
                );
            }

            $this->requireSameSettings(
                $relatedSearchUnit,
                'forContent',
                $unit->forContent,
                $relatedSearchUnit->forContent,
            );

            $this->requireSameSettings(
                $relatedSearchUnit,
                'route',
                $unit->route,
                $relatedSearchUnit->route,
            );

            $this->requireSameSettings(
                $relatedSearchUnit,
                'termsUrlParameterEnabled',
                $unit->termsUrlParameterEnabled,
                $relatedSearchUnit->termsUrlParameterEnabled,
            );
        }

        $this->relatedSearchUnits[] = $relatedSearchUnit;

        return $this;
    }

    public function getTotalRelatedSearchAmount(): int
    {
        return array_reduce(
            $this->relatedSearchUnits,
            static fn (int $amount, GoogleCsaRelatedSearchUnitViewDataRequest $unit): int => $amount + $unit->amount,
            0,
        );
    }

    private function requireSameSettings(
        GoogleCsaRelatedSearchUnitViewDataRequest $relatedSearchUnit,
        string $key,
        mixed $previousValue,
        mixed $value
    ): void
    {
        if ($value === $previousValue) {
            return;
        }

        throw new \InvalidArgumentException(
            sprintf(
                'Google related search require identical %s, container "%s" value %s does not match previous %s',
                $key,
                $relatedSearchUnit->container,
                $previousValue ?? 'null',
                $value ?? 'null',
            ),
        );
    }
}

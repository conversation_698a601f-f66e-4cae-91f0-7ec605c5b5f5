<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final class ViewDataRequest
{
    private ?string $query = null;

    private ViewDataRequestRequirements $requirements;

    private bool $isBuildingRequest = false;

    /** @var string[] */
    private array $componentIds;

    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(
        private readonly BingAdsViewDataRequest $bingAdsViewDataRequest = new BingAdsViewDataRequest(),
        private readonly ContentPageViewDataRequest $contentPageViewDataRequest = new ContentPageViewDataRequest(),
        private readonly ContentPagesViewDataRequest $contentPagesViewDataRequest = new ContentPagesViewDataRequest(),
        private readonly ContentPageCategoryViewDataRequest $contentPageCategoryViewDataRequest = new ContentPageCategoryViewDataRequest(),
        private readonly ContentPageCategoriesViewDataRequest $contentPageCategoriesViewDataRequest =
        new ContentPageCategoriesViewDataRequest(),
        private readonly GoogleCsaViewDataRequest $googleCsaViewDataRequest = new GoogleCsaViewDataRequest(),
        private readonly ImageViewDataRequest $imageViewDataRequest = new ImageViewDataRequest(),
        private readonly NewsViewDataRequest $newsViewDataRequest = new NewsViewDataRequest(),
        private readonly OrganicViewDataRequest $organicViewDataRequest = new OrganicViewDataRequest(),
        private readonly RelatedTermsViewDataRequest $relatedTermsViewDataRequest = new RelatedTermsViewDataRequest(),
        private readonly array $components = []
    )
    {
        $this->requirements = new ViewDataRequestRequirements();
    }

    public function getQuery(): ?string
    {
        return $this->query;
    }

    public function setQuery(?string $query): self
    {
        $this->query = $query;

        return $this;
    }

    public function setIsBuildingRequest(bool $isBuildingRequest): self
    {
        $this->isBuildingRequest = $isBuildingRequest;

        return $this;
    }

    /**
     * @return ComponentInterface[]
     */
    public function getComponents(): array
    {
        return $this->components;
    }

    /**
     * @return string[]
     */
    public function getComponentIds(): array
    {
        if (!isset($this->componentIds)) {
            $this->componentIds = array_map(
                static fn (ComponentInterface $component) => $component->getId(),
                $this->components,
            );
        }

        return $this->componentIds;
    }

    /**
     * @return ViewDataProperty[]
     */
    public function getRequirements(ViewDataRegistry $viewDataRegistry): array
    {
        return $this->requirements->getRequirements($viewDataRegistry);
    }

    /**
     * @return array{requirements: ViewDataProperty[], conditions: array<string, ViewDataConditionCollection[]>}
     */
    public function getRequirementsAsArray(): array
    {
        return $this->requirements->toArray();
    }

    /**
     * @param ViewDataProperty[] $requirements
     */
    public function setRequirements(array $requirements, ViewDataConditionCollection $conditions): self
    {
        $this->requirements->setRequirements($requirements, $conditions);

        return $this;
    }

    public function bingAds(): BingAdsViewDataRequest
    {
        return $this->bingAdsViewDataRequest;
    }

    public function contentPage(): ContentPageViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->contentPageViewDataRequest;
        }

        return $this->contentPageViewDataRequest;
    }

    public function contentPages(): ContentPagesViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->contentPagesViewDataRequest;
        }

        return $this->contentPagesViewDataRequest;
    }

    public function contentPageCategory(): ContentPageCategoryViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->contentPageCategoryViewDataRequest;
        }

        return $this->contentPageCategoryViewDataRequest;
    }

    public function contentPageCategories(): ContentPageCategoriesViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->contentPageCategoriesViewDataRequest;
        }

        return $this->contentPageCategoriesViewDataRequest;
    }

    public function googleCsa(): GoogleCsaViewDataRequest
    {
        return $this->googleCsaViewDataRequest;
    }

    public function image(): ImageViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->imageViewDataRequest;
        }

        return $this->imageViewDataRequest;
    }

    public function news(): NewsViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->newsViewDataRequest;
        }

        return $this->newsViewDataRequest;
    }

    public function organic(): OrganicViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->organicViewDataRequest;
        }

        return $this->organicViewDataRequest;
    }

    public function relatedTerms(): RelatedTermsViewDataRequest
    {
        if ($this->isBuildingRequest) {
            return clone $this->relatedTermsViewDataRequest;
        }

        return $this->relatedTermsViewDataRequest;
    }

    /**
     * @return SearchApiViewDataRequestInterface[]
     */
    public function getDataRequests(): array
    {
        return [
            $this->contentPageViewDataRequest,
            $this->contentPagesViewDataRequest,
            $this->contentPageCategoryViewDataRequest,
            $this->contentPageCategoriesViewDataRequest,
            $this->imageViewDataRequest,
            $this->newsViewDataRequest,
            $this->organicViewDataRequest,
            $this->relatedTermsViewDataRequest,
        ];
    }
}

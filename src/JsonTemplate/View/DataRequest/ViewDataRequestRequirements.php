<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;

final class ViewDataRequestRequirements
{
    /** @var ViewDataProperty[] */
    private array $viewDataProperties = [];

    /** @var array<string, ViewDataConditionCollection[]> */
    private array $conditions = [];

    /**
     * @return ViewDataProperty[]
     */
    public function getRequirements(ViewDataRegistry $viewDataRegistry): array
    {
        $requiredViewDataProperties = [];

        foreach ($this->viewDataProperties as $viewDataProperty) {
            if (!isset($this->conditions[$viewDataProperty->value])) {
                $requiredViewDataProperties[] = $viewDataProperty;

                continue;
            }

            foreach ($this->conditions[$viewDataProperty->value] as $condition) {
                if ($condition->check($viewDataRegistry)) {
                    $requiredViewDataProperties[] = $viewDataProperty;

                    break;
                }
            }
        }

        return $requiredViewDataProperties;
    }

    /**
     * @param ViewDataProperty[] $requirements
     */
    public function setRequirements(array $requirements, ViewDataConditionCollection $conditions): self
    {
        foreach ($requirements as $requirement) {
            $addedRequirement = false;

            if (!in_array($requirement, $this->viewDataProperties, true)) {
                $this->viewDataProperties[] = $requirement;
                $addedRequirement = true;
            }

            if (!isset($this->conditions[$requirement->value])) {
                // Only add conditions if the requirement was added
                if ($addedRequirement && $conditions->conditions !== []) {
                    $this->conditions[$requirement->value] = [$conditions];
                }

                continue;
            }

            if ($conditions->conditions === []) {
                // remove conditions because it was added with conditions and now without conditions
                unset($this->conditions[$requirement->value]);

                continue;
            }

            $addConditions = true;

            // Prevent adding the same condition multiple times
            foreach ($this->conditions[$requirement->value] as $existingConditions) {
                if ($existingConditions->toArray() === $conditions->toArray()) {
                    $addConditions = false;

                    break;
                }
            }

            if ($addConditions) {
                $this->conditions[$requirement->value][] = $conditions;
            }
        }

        return $this;
    }

    /**
     * @return array{requirements: ViewDataProperty[], conditions: array<string, ViewDataConditionCollection[]>}
     */
    public function toArray(): array
    {
        return [
            'requirements' => $this->viewDataProperties,
            'conditions'   => $this->conditions,
        ];
    }
}

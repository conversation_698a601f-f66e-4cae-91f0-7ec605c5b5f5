<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\Exception;

use App\Component\Generic\Container\ContainerComponent;
use App\JsonTemplate\Component\ComponentInterface;

class ExpectedContainerException extends \RuntimeException
{
    public static function create(?ComponentInterface $received): self
    {
        return new self(
            sprintf(
                'Expected first component to be an instance of "%s", received: "%s"',
                ContainerComponent::class,
                $received !== null ? $received::class : 'null',
            ),
        );
    }
}

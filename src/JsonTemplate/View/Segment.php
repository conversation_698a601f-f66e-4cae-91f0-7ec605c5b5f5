<?php

declare(strict_types=1);

namespace App\JsonTemplate\View;

use App\JsonTemplate\Component\ComponentInterface;

class Segment implements SegmentInterface
{
    /** @var ComponentInterface[] */
    private array $components = [];

    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(array $components = [])
    {
        $this->addComponents($components);
    }

    /**
     * @param ComponentInterface[]|null[] $components
     *
     * @return $this
     */
    public function addComponents(array $components): self
    {
        foreach ($components as $component) {
            if ($component === null) {
                continue;
            }

            $this->addComponent($component);
        }

        return $this;
    }

    public function addComponent(ComponentInterface $component): self
    {
        $this->components[] = $component;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getComponents(): array
    {
        return $this->components;
    }
}

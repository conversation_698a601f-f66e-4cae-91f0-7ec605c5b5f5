<?php

declare(strict_types=1);

namespace App\JsonTemplate\View;

use App\Component\Generic\Container\ContainerComponent;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Exception\ExpectedContainerException;
use Symfony\Component\HttpFoundation\Response;

final readonly class View implements ViewInterface
{
    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(
        private JsonTemplate $jsonTemplate,
        private ViewDataRequest $viewDataRequest,
        private ViewDataRegistry $viewDataRegistry,
        private array $components,
        private Response $response
    )
    {
    }

    public function getTwigTemplate(): string
    {
        return '@theme/user_interface/layout/layout_type_default_components.html.twig';
    }

    public function getJsonTemplate(): JsonTemplate
    {
        return $this->jsonTemplate;
    }

    public function getDataRequest(): ViewDataRequest
    {
        return $this->viewDataRequest;
    }

    public function getDataRegistry(): ViewDataRegistry
    {
        return $this->viewDataRegistry;
    }

    public function getContainer(): ContainerComponent
    {
        $containerComponent = $this->components[0] ?? null;

        if ($containerComponent instanceof ContainerComponent) {
            return $containerComponent;
        }

        throw ExpectedContainerException::create($containerComponent);
    }

    /**
     * @inheritDoc
     */
    public function getComponents(): array
    {
        return $this->components;
    }

    public function getResponse(): Response
    {
        return $this->response;
    }
}

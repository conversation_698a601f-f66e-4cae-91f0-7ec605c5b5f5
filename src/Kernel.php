<?php

declare(strict_types=1);

namespace App;

use App\DependencyInjection\BrandWebsiteExtension;
use App\DependencyInjection\Compiler\ComponentCompilerPass;
use App\DependencyInjection\Compiler\ModuleSettingsCompilerPass;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\Config\Resource\FileResource;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Routing\Loader\Configurator\RoutingConfigurator;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    private const string CONFIG_FILE_EXTENSIONS = '.{php,xml,yaml,yml}';

    /**
     * @inheritDoc
     */
    public function registerBundles(): iterable
    {
        $contents = require sprintf('%s/config/bundles.php', $this->getProjectDir());

        foreach ($contents as $class => $envs) {
            if ($envs[$this->environment] ?? $envs['all'] ?? false) {
                /** @phpstan-ignore-next-line Expects BundleInterface, object given */
                yield new $class();
            }
        }
    }

    public function getProjectDir(): string
    {
        return dirname(__DIR__);
    }

    protected function build(ContainerBuilder $container): void
    {
        $container->registerExtension(new BrandWebsiteExtension());

        $container->addCompilerPass(new ComponentCompilerPass());
        $container->addCompilerPass(new ModuleSettingsCompilerPass());

        parent::build($container);
    }

    protected function configureContainer(ContainerBuilder $container, LoaderInterface $loader): void
    {
        $container->addResource(new FileResource($this->getProjectDir().'/config/bundles.php'));
        $container->setParameter('.container.dumper.inline_class_loader', $this->debug);
        $container->setParameter('.container.dumper.inline_factories', true);

        $configPath = $this->getConfigPath();
        $loader->load($configPath.'/{packages}/*'.self::CONFIG_FILE_EXTENSIONS, 'glob');
        $loader->load($configPath.'/{packages}/'.$this->environment.'/*'.self::CONFIG_FILE_EXTENSIONS, 'glob');
        $loader->load($configPath.'/{services}'.self::CONFIG_FILE_EXTENSIONS, 'glob');
        $loader->load($configPath.'/{services}_'.$this->environment.self::CONFIG_FILE_EXTENSIONS, 'glob');
    }

    protected function configureRoutes(RoutingConfigurator $routes): void
    {
        $resourcesConfigPath = sprintf('%s/Resources/config', __DIR__);
        $configPath = $this->getConfigPath();

        $routes->import($resourcesConfigPath.'/{routes}/routes_loader.yaml', 'glob');
        $routes->import($resourcesConfigPath.'/{routes}/'.$this->environment.'/*'.self::CONFIG_FILE_EXTENSIONS, 'glob');
        $routes->import($configPath.'/{routes}/'.$this->environment.'/*'.self::CONFIG_FILE_EXTENSIONS, 'glob');
        $routes->import($configPath.'/{routes}/*'.self::CONFIG_FILE_EXTENSIONS, 'glob');
        $routes->import($configPath.'/{routes}'.self::CONFIG_FILE_EXTENSIONS, 'glob');
    }

    private function getConfigPath(): string
    {
        return sprintf('%s/config', $this->getProjectDir());
    }
}

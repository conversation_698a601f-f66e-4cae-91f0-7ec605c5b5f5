<?php

declare(strict_types=1);

namespace App\Kernel\EventSubscriber;

use App\Kernel\KernelResponseEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class KernelResponseEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        protected EventDispatcherInterface $eventDispatcher
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::RESPONSE => ['onKernelResponse', -5000],
        ];
    }

    public function onKernelResponse(ResponseEvent $responseEvent): void
    {
        if ($responseEvent->getRequest()->isMethod('HEAD')) {
            return;
        }

        $response = $responseEvent->getResponse();
        $response->headers->set('X-Log-Memory_Usage', (string)memory_get_peak_usage(true));

        if ($responseEvent->getResponse()->isRedirect()) {
            $this->eventDispatcher->dispatch(
                event    : $responseEvent,
                eventName: KernelResponseEvent::REDIRECT->value,
            );

            return;
        }

        $this->eventDispatcher->dispatch(
            event    : $responseEvent,
            eventName: KernelResponseEvent::NO_REDIRECT->value,
        );
    }
}

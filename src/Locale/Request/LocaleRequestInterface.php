<?php

declare(strict_types=1);

namespace App\Locale\Request;

use App\Http\Request\RequestInterface;
use App\Http\Request\UrlRequestInterface;

interface LocaleRequestInterface extends RequestInterface, UrlRequestInterface
{
    public const string PARAMETER_LOCALE = 'locale';

    /**
     * The locale parameter can be used to change to a supported locale
     */
    public function getLocale(): ?string;
}

<?php

declare(strict_types=1);

namespace App\Locale\Settings;

use Visymo\Shared\Domain\Locale\Language;
use Visymo\Shared\Domain\Locale\Locale;
use Visymo\Shared\Domain\Locale\Region;

final readonly class LocaleSettings
{
    public const string KEY_LOCALE     = 'locale';
    public const string KEY_IS_DEFAULT = 'is_default';

    private Locale $locale;

    public function __construct(string $code, public bool $isDefault)
    {
        $this->locale = new Locale($code);
    }

    public function getCode(): string
    {
        return $this->locale->getCode();
    }

    public function getIsoCode(): string
    {
        return $this->locale->getIsoCode();
    }

    public function getLanguage(): Language
    {
        return $this->locale->getLanguage();
    }

    public function getRegion(): Region
    {
        return $this->locale->getRegion();
    }

    /**
     * @return array<string, string|boolean>
     */
    public function toArray(): array
    {
        return [
            self::KEY_LOCALE     => $this->getCode(),
            self::KEY_IS_DEFAULT => $this->isDefault,
        ];
    }
}

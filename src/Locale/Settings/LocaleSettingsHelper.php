<?php

declare(strict_types=1);

namespace App\Locale\Settings;

use App\ContentPage\Locale\ContentPageLocale;
use App\Domain\Settings\DomainSettingsHelperInterface;
use App\Locale\Request\LocaleRequestInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettings;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsFactory;

final class LocaleSettingsHelper implements LocaleSettingsHelperInterface
{
    public const  string KEY_LOCALES = 'locales';
    private const string KEY_ENABLED = 'enabled';

    private ?LocaleSettings $localeSettings = null;

    public function __construct(
        private readonly WebsiteConfigurationHelper $websiteConfigurationHelper,
        private readonly LocaleSettingsFactory $localeSettingsFactory,
        private readonly LocaleRequestInterface $localeRequest,
        private readonly DomainSettingsHelperInterface $domainSettingsHelper
    )
    {
    }

    public function getSettings(): LocaleSettings
    {
        if ($this->localeSettings === null) {
            $websiteConfiguration = $this->websiteConfigurationHelper->getConfiguration();
            $domainSettings = $this->domainSettingsHelper->getSettings();

            $localeConfig = $this->getLocaleConfig(
                $websiteConfiguration->getBrandConfig(),
                $websiteConfiguration->getDomainConfig(
                    $domainSettings->host,
                ),
                $this->localeRequest->getLocale(),
            );

            $this->localeSettings = $this->localeSettingsFactory->create(
                $localeConfig,
            );
        }

        return $this->localeSettings;
    }

    /**
     * This function is optimised for low resources, because it is called on each request
     *
     * @inheritDoc
     */
    public function getLocaleConfig(array $brandConfig, array $domainConfig, ?string $locale = null): array
    {
        $defaultLocaleConfig = null;

        $googleAdSenseBrandConfig = $brandConfig[GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE];
        $googleAdSenseDomainConfig = $domainConfig[GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE];
        $isGoogleAdSenseEnabled = $googleAdSenseDomainConfig[self::KEY_ENABLED] === true;
        $isGoogleAdSenseAfsOnline = ContractType::from(
            $googleAdSenseBrandConfig[GoogleAdSenseSettings::KEY_CONTRACT_TYPE],
        )->isOnline();

        if ($isGoogleAdSenseEnabled
            && $isGoogleAdSenseAfsOnline
            && $locale !== null
            && ContentPageLocale::isSupported($locale)
        ) {
            return [
                LocaleSettings::KEY_LOCALE     => $locale,
                LocaleSettings::KEY_IS_DEFAULT => false,
            ];
        }

        foreach ($domainConfig[self::KEY_LOCALES] as $localeConfig) {
            // Check most used default config first
            if ($localeConfig[LocaleSettings::KEY_IS_DEFAULT] === true) {
                if ($locale === null) {
                    return $localeConfig;
                }

                // Mark default
                $defaultLocaleConfig = $localeConfig;
            }

            // Locale found
            if ($locale !== null && $localeConfig[LocaleSettings::KEY_LOCALE] === $locale) {
                return $localeConfig;
            }
        }

        if ($defaultLocaleConfig === null) {
            throw LocaleConfigDefaultNotFoundException::create();
        }

        return $defaultLocaleConfig;
    }
}

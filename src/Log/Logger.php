<?php

declare(strict_types=1);

namespace App\Log;

use App\Http\Request\GenericRequestInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Request\Main\MainRequestNotAvailableException;
use Monolog\Attribute\AsMonologProcessor;
use Monolog\LogRecord;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(lazy: true)]
#[AsMonologProcessor]
readonly class Logger
{
    public function __construct(
        private RequestInfoInterface $requestInfo,
        private GenericRequestInterface $genericRequest
    )
    {
    }

    public function __invoke(LogRecord $logRecord): LogRecord
    {
        try {
            $logRecord->extra += [
                'url'         => $this->requestInfo->getUrl(),
                'pageview_id' => $this->genericRequest->getPageviewId(),
                'visit_id'    => $this->genericRequest->getVisitId(),
            ];
        } catch (MainRequestNotAvailableException) {
            // Do nothing
        }

        return $logRecord;
    }
}

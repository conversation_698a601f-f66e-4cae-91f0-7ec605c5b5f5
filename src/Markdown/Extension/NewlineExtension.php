<?php

declare(strict_types=1);

namespace App\Markdown\Extension;

use League\CommonMark\Environment\EnvironmentBuilderInterface;
use League\CommonMark\Extension\ExtensionInterface;
use League\CommonMark\Node\Inline\Newline;
use League\CommonMark\Parser\Inline\NewlineParser;
use League\CommonMark\Renderer\Inline\NewlineRenderer;

final class NewlineExtension implements ExtensionInterface
{
    public function register(EnvironmentBuilderInterface $environment): void
    {
        $environment
            ->addInlineParser(new NewlineParser(), 200)
            ->addRenderer(Newline::class, new NewlineRenderer());
    }
}

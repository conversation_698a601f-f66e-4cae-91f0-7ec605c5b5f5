<?php

declare(strict_types=1);

namespace App\MicrosoftSearch;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class MicrosoftSearchModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'microsoft_search';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_microsoft_search.yaml']);
    }
}

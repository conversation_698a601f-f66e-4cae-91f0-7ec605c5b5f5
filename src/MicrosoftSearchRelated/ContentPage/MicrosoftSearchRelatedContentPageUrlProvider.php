<?php

declare(strict_types=1);

namespace App\MicrosoftSearchRelated\ContentPage;

use App\ContentPage\Request\ContentPageRequestInterface;
use App\ContentPage\Url\ContentPageUrlProviderInterface;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use App\MicrosoftSearchRelated\Settings\MicrosoftSearchRelatedSettings;
use App\Search\Request\SearchRequestInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

final class MicrosoftSearchRelatedContentPageUrlProvider implements ContentPageUrlProviderInterface
{
    private const string ROUTE = 'route_microsoft_search_related';

    public function __construct(
        private readonly MicrosoftSearchRelatedSettings $microsoftSearchRelatedSettings,
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly RouterInterface $router
    )
    {
    }

    public static function getDefaultPriority(): int
    {
        return 100;
    }

    public function isEnabled(): bool
    {
        return $this->microsoftSearchRelatedSettings->enabled;
    }

    public function supportsRoute(string $route): bool
    {
        if ($this->isEnabled()) {
            return $route === self::ROUTE;
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    public function getPersistentUrl(ContentPage $contentPage, array $routeParameters): string
    {
        return $this->persistentUrlParametersRouter->generate(
            self::ROUTE,
            $this->getRouteParameters($contentPage, $routeParameters),
            PersistentUrlParametersPageType::NEW_SEARCH,
        );
    }

    /**
     * @inheritDoc
     */
    public function getUrl(
        ContentPage $contentPage,
        bool $absoluteUrl,
        array $routeParameters
    ): string
    {
        return $this->router->generate(
            self::ROUTE,
            $this->getRouteParameters($contentPage, $routeParameters),
            $absoluteUrl
                ? UrlGeneratorInterface::ABSOLUTE_URL
                : UrlGeneratorInterface::ABSOLUTE_PATH,
        );
    }

    /**
     * @param mixed[] $routeParameters
     *
     * @return mixed[]
     */
    private function getRouteParameters(ContentPage $contentPage, array $routeParameters): array
    {
        return [
            SearchRequestInterface::PARAMETER_QUERY                       => $contentPage->keywords[0] ?? '',
            ContentPageRequestInterface::PARAMETER_CONTENT_PAGE_PUBLIC_ID => $contentPage->publicId,
            ...$routeParameters,
        ];
    }
}

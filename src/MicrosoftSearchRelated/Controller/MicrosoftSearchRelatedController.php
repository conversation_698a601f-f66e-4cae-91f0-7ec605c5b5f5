<?php

declare(strict_types=1);

namespace App\MicrosoftSearchRelated\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Search\Registry\RouteRegistry;
use App\Search\Request\SearchRequestFlag;
use App\Search\SearchType;
use App\Statistics\Helper\StatisticsRequestFlag;
use App\Tracking\Entry\Request\TrackingEntryRequestFlag;
use App\Tracking\Helper\TrafficHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class MicrosoftSearchRelatedController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly RouteRegistry $routeRegistry,
        private readonly TrafficHelper $trafficHelper
    )
    {
    }

    #[Route(
        path    : '/msr',
        name    : 'route_microsoft_search_related',
        defaults: [
            SearchRequestFlag::IS_LANDING_PAGE                => true,
            StatisticsRequestFlag::LOG_ENABLED                => true,
            TrackingEntryRequestFlag::USE_AS_CONVERSION_ROUTE => true,
            SearchRequestFlag::TYPE                           => SearchType::DISPLAY->value,
        ],
        methods : ['GET']
    )]
    public function search(): Response
    {
        if (!$this->trafficHelper->isPaidTraffic()) {
            $this->routeRegistry->setCurrentRouteAsSearchRoute();

            return $this->jsonTemplateRenderer->renderForSearch(
                '@themeJson/microsoft_search_related/microsoft_search_related_direct.json',
            );
        }

        $this->routeRegistry->setSearchRoute('route_microsoft_search_related_web');

        return $this->jsonTemplateRenderer->renderForSearchByDevice(
            '@themeJson/microsoft_search_related/microsoft_search_related_mobile.json',
            '@themeJson/microsoft_search_related/microsoft_search_related_desktop.json',
        );
    }
}

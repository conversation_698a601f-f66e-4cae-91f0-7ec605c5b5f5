<?php

declare(strict_types=1);

namespace App\ModuleSettings\Exception;

class InvalidModuleConfigException extends \InvalidArgumentException
{
    public static function create(string $moduleName, string $message, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Invalid %s module config: %s', $moduleName, $message),
            0,
            $previous,
        );
    }
}

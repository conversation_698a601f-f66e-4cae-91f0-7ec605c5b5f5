<?php

declare(strict_types=1);

namespace App\ModuleSettings;

use App\ModuleSettings\Value\ModuleValue;

/**
 * @deprecated Use ArtemisModuleSettingsFactoryInterface instead
 */
interface ModuleSettingsFactoryInterface
{
    public static function getModuleName(): string;

    /**
     * @param mixed[] $projectModuleConfig
     */
    public function create(array $projectModuleConfig): ModuleSettingsInterface;

    /**
     * Module values to generate brand module config summary
     *
     * @param mixed[] $projectModuleConfig
     *
     * @return ModuleValue[]
     */
    public function getModuleValues(array $projectModuleConfig): array;
}

<?php

declare(strict_types=1);

namespace App\NewsSearch\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\NewsSearch\Filter\NewsCategoryFilter;
use App\NewsSearch\Filter\NewsPeriodFilter;

final class NewsSearchRequest implements NewsSearchRequestInterface
{
    private string $categoryFilter;

    private string $periodFilter;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getCategory(): ?string
    {
        if (!isset($this->categoryFilter)) {
            $this->categoryFilter = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_CATEGORY,
                NewsCategoryFilter::SUPPORTED_VALUES,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->categoryFilter);
    }

    public function getPeriod(): ?string
    {
        if (!isset($this->periodFilter)) {
            $this->periodFilter = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_PERIOD,
                NewsPeriodFilter::SUPPORTED_VALUES,
            );
        }

        return $this->requestPropertyNormalizer->getString($this->periodFilter);
    }

    /**
     * @inheritDoc
     */
    public function getValues(): array
    {
        return array_filter($this->toArray(), static fn ($value) => $value !== null);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_CATEGORY => $this->getCategory(),
            self::PARAMETER_PERIOD   => $this->getPeriod(),
        ];
    }
}

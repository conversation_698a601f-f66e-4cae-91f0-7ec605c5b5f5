<?php

declare(strict_types=1);

namespace App\NewsSearch\Settings;

use App\BingApi\Helper\BingApiHelper;
use App\Debug\Request\DebugRequestInterface;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\ModuleSettings\ModuleSettingsInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

final readonly class NewsSearchSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private BingApiHelper $bingApiHelper,
        private DebugRequestInterface $debugRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'news_search';
    }

    public function create(): ModuleSettingsInterface
    {
        if ($this->debugRequest->enableModule()) {
            return new NewsSearchSettings(
                enabled: true,
            );
        }

        $module = $this->websiteConfigurationHelper->getConfiguration()->getBrandConfig()['news_search'] ?? null;

        if ($module === null) {
            return new NewsSearchSettings(
                enabled: false,
            );
        }

        return new NewsSearchSettings(
            enabled: $this->isModuleEnabled($module) && $this->bingApiHelper->isSupported(),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Office\Request;

use App\Http\Request\Manager\RequestManagerInterface;

final class OfficeRequest implements OfficeRequestInterface
{
    private bool $isOffice;

    public function __construct(
        private readonly RequestManagerInterface $requestManager
    )
    {
    }

    public function isOffice(): bool
    {
        if (!isset($this->isOffice)) {
            $this->isOffice = $this->requestManager->headersBag()->getBool(self::HEADER_X_LOADBALANCER_IS_OFFICE);
        }

        return $this->isOffice;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_IS_OFFICE => $this->isOffice(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\OneTrust\Helper;

use App\OneTrust\Platform\ConsentManagementPlatform;
use App\OneTrust\Request\ConsentManagementPlatformRequestInterface;
use App\OneTrust\Settings\OneTrustSettings;
use App\SplitTest\SplitTestExtendedReaderInterface;

class OneTrustHelper
{
    private bool $enabled;

    public function __construct(
        private readonly ConsentManagementPlatformRequestInterface $consentManagementPlatformRequest,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly OneTrustSettings $oneTrustSettings
    )
    {
    }

    public function isEnabled(): bool
    {
        if (isset($this->enabled)) {
            return $this->enabled;
        }

        if ($this->oneTrustSettings->domainScriptId === null) {
            // Domain script ID is required
            $this->enabled = false;
        } elseif ($this->splitTestExtendedReader->isVariantActive('enc')) {
            $this->enabled = true;
        } elseif ($this->splitTestExtendedReader->isVariantActive('encwi')) {
            $this->enabled = true;
        } else {
            $this->enabled = $this->getRequestPlatform() !== null || $this->oneTrustSettings->enabled;
        }

        return $this->enabled;
    }

    public function getDomainScriptId(): string
    {
        $domainScriptId = $this->oneTrustSettings->domainScriptId;

        if ($domainScriptId === null) {
            return '';
        }

        if ($this->getRequestPlatform()?->isTest() === true) {
            $domainScriptId = sprintf('%s-test', $domainScriptId);
        }

        return $domainScriptId;
    }

    private function getRequestPlatform(): ?ConsentManagementPlatform
    {
        return $this->consentManagementPlatformRequest->getConsentManagementPlatform();
    }
}

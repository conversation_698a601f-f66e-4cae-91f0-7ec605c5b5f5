<?php

declare(strict_types=1);

namespace App\OneTrust\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\OneTrust\Platform\ConsentManagementPlatform;

final class ConsentManagementPlatformRequest implements ConsentManagementPlatformRequestInterface
{
    private ?ConsentManagementPlatform $consentManagementPlatform;

    private bool $consentManagementPlatformInitialized = false;

    public function __construct(
        private readonly RequestManagerInterface $requestManager
    )
    {
    }

    public function getConsentManagementPlatform(): ?ConsentManagementPlatform
    {
        if (!$this->consentManagementPlatformInitialized) {
            $this->consentManagementPlatformInitialized = true;
            $value = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_CONSENT_MANAGEMENT_PLATFORM,
                array_column(ConsentManagementPlatform::cases(), 'value'),
            );

            $this->consentManagementPlatform = ConsentManagementPlatform::tryFrom($value);
        }

        return $this->consentManagementPlatform;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_CONSENT_MANAGEMENT_PLATFORM => $this->getConsentManagementPlatform()?->value,
        ];
    }
}

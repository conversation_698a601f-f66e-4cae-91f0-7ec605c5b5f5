<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\ContentPage\OpenGraph\ContentPageToOpenGraphGenerator;
use App\ContentPage\Paragraph\ParagraphsAmountRegistry;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;
use App\Search\Request\SearchRequestInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

final readonly class MicrosoftSearchRelatedPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public function __construct(
        private SearchRequestInterface $searchRequest,
        private ParagraphsAmountRegistry $paragraphsAmountRegistry,
        private TranslatorInterface $translator,
        private BrandSettingsHelper $brandSettingsHelper,
        private ContentPageToOpenGraphGenerator $contentPageToOpenGraphGenerator
    )
    {
    }

    public function getType(): string
    {
        return 'microsoft_search_related';
    }

    public function createFromView(ViewInterface $view): PageHeadTags
    {
        $query = $this->searchRequest->getQueryAsString();
        $pageHeadTags = $this->createForContentPage($view, $query);

        if ($pageHeadTags === null) {
            $metaDescription = $this->translator->trans(
                'meta.description.display',
                ['%query%' => $query],
            );

            $pageHeadTags = new PageHeadTags(
                title          : $this->getQueryTitle($query),
                metaDescription: $metaDescription,
                metaRobots     : 'noindex',
            );
        }

        return $pageHeadTags;
    }

    private function createForContentPage(
        ViewInterface $view,
        string $query
    ): ?PageHeadTags
    {
        $contentPage = $view->getDataRegistry()->getContentPage()->page;

        if ($contentPage === null) {
            return null;
        }

        $openGraph = $this->contentPageToOpenGraphGenerator->generate(
            $contentPage,
            'route_microsoft_search_related',
        );

        return new PageHeadTags(
            title          : $this->getTitleForContentPage($contentPage, $query),
            metaDescription: $contentPage->meta?->description,
            metaKeywords   : $contentPage->meta?->keywords,
            metaRobots     : 'noindex',
            openGraph      : $openGraph,
        );
    }

    private function getTitleForContentPage(ContentPage $contentPage, string $query): string
    {
        // Excerpt is used as first page with title of article
        if ($this->searchRequest->getPage() === 1) {
            $title = $contentPage->title;
        } else {
            $currentPageStart = $this->paragraphsAmountRegistry->getCurrentPageStart();
            $paragraph = $contentPage->paragraphs[$currentPageStart] ?? null;

            $title = $paragraph->title ?? $query;
        }

        return $this->addBrandNameAsSuffixToTitle($title);
    }

    private function getQueryTitle(string $query): string
    {
        $title = ucwords(mb_strtolower($query));

        return $this->addBrandNameAsSuffixToTitle($title);
    }

    private function addBrandNameAsSuffixToTitle(string $title): string
    {
        return sprintf('%s - %s', $title, $this->brandSettingsHelper->getSettings()->getName());
    }
}

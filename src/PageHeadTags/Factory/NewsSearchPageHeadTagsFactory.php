<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;
use App\Search\Request\SearchRequestInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

final readonly class NewsSearchPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public function __construct(
        private SearchRequestInterface $searchRequest,
        private BrandSettingsHelper $brandSettingsHelper,
        private TranslatorInterface $translator
    )
    {
    }

    public function getType(): string
    {
        return 'news_search';
    }

    public function createFromView(ViewInterface $view): PageHeadTags
    {
        $queryTitleCase = mb_convert_case($this->searchRequest->getQueryAsString(), MB_CASE_TITLE, 'UTF-8');

        $title = sprintf(
            '%s - %s %s',
            $queryTitleCase,
            $this->brandSettingsHelper->getSettings()->getName(),
            $this->translator->trans('news_search.title'),
        );

        return new PageHeadTags(
            title: $title,
        );
    }
}

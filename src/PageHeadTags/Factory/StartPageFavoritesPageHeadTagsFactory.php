<?php

declare(strict_types=1);

namespace App\PageHeadTags\Factory;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\Tags\PageHeadTags;

final readonly class StartPageFavoritesPageHeadTagsFactory implements PageHeadTagsFactoryInterface
{
    public function __construct(
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function getType(): string
    {
        return 'startpage_favorites';
    }

    public function createFromView(ViewInterface $view): PageHeadTags
    {
        $title = sprintf(
            '%s %s',
            $this->brandSettingsHelper->getSettings()->getName(),
            'Favorieten',
        );

        return new PageHeadTags(
            title: $title,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\PageHeadTags\OpenGraph;

final readonly class OpenGraphProperty
{
    public function __construct(
        public OpenGraphPropertyType $type,
        public string $content
    )
    {
    }

    /**
     * @return array<string, string>
     */
    public function toArray(): array
    {
        return [
            $this->type->value => $this->content,
        ];
    }
}

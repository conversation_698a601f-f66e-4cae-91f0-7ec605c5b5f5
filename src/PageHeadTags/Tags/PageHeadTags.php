<?php

declare(strict_types=1);

namespace App\PageHeadTags\Tags;

use App\PageHeadTags\OpenGraph\OpenGraph;

readonly class PageHeadTags
{
    public function __construct(
        public string $title,
        public ?string $metaDescription = null,
        public ?string $metaKeywords = null,
        public ?string $metaRobots = null,
        public ?OpenGraph $openGraph = null
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'title'            => $this->title,
            'meta_description' => $this->metaDescription,
            'meta_keywords'    => $this->metaKeywords,
            'meta_robots'      => $this->metaRobots,
            'open_graph'       => $this->openGraph?->toArray(),
        ];
    }
}

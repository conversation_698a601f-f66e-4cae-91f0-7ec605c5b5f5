<?php

declare(strict_types=1);

namespace App\PageviewConversion\Controller;

use App\AdBot\Request\AdBotRequestInterface;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingResponseFactory;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Generic\Response\UncachedPixelResponse;
use App\PageviewConversion\Tracking\PageviewConversionLandingHandlerInterface;
use App\PageviewConversion\Tracking\PageviewConversionLandingRelatedHandlerInterface;
use App\SplitTest\Request\SplitTestRequestFlag;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class PageviewConversionController extends AbstractController
{
    public function __construct(
        private readonly AdBotRequestInterface $adBotRequest,
        private readonly ConversionTrackingResponseFactory $conversionTrackingResponseFactory,
        private readonly FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    #[Route(
        path    : '/tp/pvl',
        name    : 'route_pageview_conversion_landing',
        defaults: [
            SplitTestRequestFlag::ALWAYS_MATCH_ROUTE => true,
        ],
        methods : ['GET', 'POST']
    )]
    public function landing(
        PageviewConversionLandingHandlerInterface $pageviewConversionLandingHandler
    ): Response
    {
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return new UncachedPixelResponse();
        }

        return $this->conversionTrackingResponseFactory->create(
            $pageviewConversionLandingHandler->handle(),
        );
    }

    #[Route(
        path    : '/tp/pvlr',
        name    : 'route_pageview_conversion_landing_related',
        defaults: [
            SplitTestRequestFlag::ALWAYS_MATCH_ROUTE => true,
        ],
        methods : ['GET', 'POST']
    )]
    public function landingRelated(
        PageviewConversionLandingRelatedHandlerInterface $pageviewConversionLandingRelatedHandler
    ): Response
    {
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return new UncachedPixelResponse();
        }

        return $this->conversionTrackingResponseFactory->create(
            $pageviewConversionLandingRelatedHandler->handle(),
        );
    }
}

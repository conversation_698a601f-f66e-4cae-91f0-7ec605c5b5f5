<?php

declare(strict_types=1);

namespace App\PageviewConversion\Settings;

use App\AdBot\Request\AdBotRequestInterface;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\ModuleSettings\ArtemisModuleSettingsFactoryInterface;
use App\PageviewConversion\PageviewConversionModule;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;

readonly class PageviewConversionSettingsFactory extends AbstractModuleSettingsFactory implements ArtemisModuleSettingsFactoryInterface
{
    private const string KEY_ROUTES = 'routes';

    public function __construct(
        private WebsiteConfigurationHelper $websiteConfigurationHelper,
        private AdBotRequestInterface $adBotRequest,
        private RequestInfoInterface $requestInfo,
        private FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    public static function getModuleName(): string
    {
        return PageviewConversionModule::getModuleName();
    }

    public function create(): PageviewConversionSettings
    {
        $moduleConfig = $this->websiteConfigurationHelper->getConfiguration()
            ->getBrandModuleConfig('pageview_conversion');

        if (!$this->isModuleEnabled($moduleConfig)) {
            return new PageviewConversionSettings(
                enabled          : false,
                enabledForRequest: false,
            );
        }

        return new PageviewConversionSettings(
            enabled          : true,
            enabledForRequest: $this->isEnabledForRequest($moduleConfig),
        );
    }

    /**
     * @param mixed[] $moduleConfig
     */
    private function isEnabledForRequest(array $moduleConfig): bool
    {
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return false;
        }

        return in_array($this->requestInfo->getRoute(), $moduleConfig[self::KEY_ROUTES], true);
    }
}

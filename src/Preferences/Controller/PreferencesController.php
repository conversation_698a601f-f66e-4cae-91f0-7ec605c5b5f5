<?php

declare(strict_types=1);

namespace App\Preferences\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class PreferencesController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer
    )
    {
    }

    #[Route(path: '/pref/', name: 'route_preferences', methods: ['GET', 'POST'])]
    public function preferences(): Response
    {
        return $this->jsonTemplateRenderer->render('@themeJson/preferences/preferences.json');
    }
}

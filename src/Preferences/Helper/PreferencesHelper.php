<?php

declare(strict_types=1);

namespace App\Preferences\Helper;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;

class PreferencesHelper
{
    private const string COOKIE_TIME_TO_LIVE = '+5 days';
    private const string COOKIE_NAME         = 'pref';

    public const string SETTING_SAFE_SEARCH       = 'safe_search';
    public const string SETTING_SAME_WINDOW       = 'same_window';
    public const string SETTING_PREVIOUS_SEARCHES = 'previous_searches';
    public const string SETTING_KEYWORD_HIGHLIGHT = 'keyword_highlight';
    public const string SETTING_QUERY_SUGGESTION  = 'query_suggestion';

    private readonly ?Request $request;

    /** @var array<string, bool>|null */
    private ?array $preferencesValues = null;

    public function __construct(RequestStack $requestStack, private readonly LoggerInterface $logger)
    {
        $this->request = $requestStack->getMainRequest();
    }

    public function getSafeSearch(): ?bool
    {
        return $this->getValueByKey(self::SETTING_SAFE_SEARCH);
    }

    public function getSameWindow(): ?bool
    {
        return $this->getValueByKey(self::SETTING_SAME_WINDOW);
    }

    public function getPreviousSearches(): ?bool
    {
        return $this->getValueByKey(self::SETTING_PREVIOUS_SEARCHES);
    }

    public function getKeywordHighlight(): ?bool
    {
        return $this->getValueByKey(self::SETTING_KEYWORD_HIGHLIGHT);
    }

    public function getQuerySuggestion(): ?bool
    {
        return $this->getValueByKey(self::SETTING_QUERY_SUGGESTION);
    }

    /**
     * @return array<string, bool>
     */
    public function getPreferencesValues(): array
    {
        if ($this->preferencesValues !== null) {
            return $this->preferencesValues;
        }

        $this->preferencesValues = [];

        if ($this->request === null) {
            return $this->preferencesValues;
        }

        $cookieValue = $this->request->cookies->getString(self::COOKIE_NAME);

        if ($cookieValue === '') {
            return $this->preferencesValues;
        }

        $this->preferencesValues = $this->getPreferencesValuesFromCookieValue($cookieValue);

        return $this->preferencesValues;
    }

    /**
     * @param array<string, bool> $values
     */
    public function setPreferencesValues(Response $response, array $values): void
    {
        if ($this->request === null) {
            return;
        }

        try {
            $cookieValue = json_encode($values, JSON_THROW_ON_ERROR);
            $cookie = Cookie::create(
                self::COOKIE_NAME,
                $cookieValue,
                new \DateTime(self::COOKIE_TIME_TO_LIVE, new \DateTimeZone('UTC')),
                '/',
                $this->request->getHost(),
                true,
                false,
            );

            $response->headers->setCookie($cookie);

            // Update cookie value in current request to let the helper working with the new settings
            $this->request->cookies->set(self::COOKIE_NAME, $cookieValue);
            $this->preferencesValues = null;
        } catch (\Throwable $exception) {
            $this->logger->notice(
                sprintf('Caught %s while setting preferences cookie: {message}', $exception::class),
                [
                    'exception'     => $exception,
                    'message'       => $exception->getMessage(),
                    'cookie_values' => $values,
                ],
            );
        }
    }

    private function getValueByKey(string $key): ?bool
    {
        return $this->getPreferencesValues()[$key] ?? null;
    }

    /**
     * @return array<string, bool>
     */
    private function getPreferencesValuesFromCookieValue(string $cookieValue): array
    {
        try {
            $preferencesValues = json_decode(
                $cookieValue,
                true,
                512,
                JSON_THROW_ON_ERROR,
            );
            $this->validatePreferencesValues($preferencesValues);

            return $preferencesValues;
        } catch (\Throwable $exception) {
            $this->logger->notice(
                sprintf('Caught %s while getting preferences from cookie: {message}', $exception::class),
                [
                    'exception'    => $exception,
                    'message'      => $exception->getMessage(),
                    'cookie_value' => $cookieValue,
                ],
            );
        }

        return [];
    }

    /**
     * @param mixed[] $preferencesValues
     */
    private function validatePreferencesValues(array $preferencesValues): void
    {
        $throwException = false;

        foreach ($preferencesValues as $key => $value) {
            if (!is_string($key)) {
                $throwException = true;

                break;
            }

            if (!is_bool($value)) {
                $throwException = true;

                break;
            }
        }

        if ($throwException) {
            throw new \RuntimeException('Invalid preferences values');
        }
    }
}

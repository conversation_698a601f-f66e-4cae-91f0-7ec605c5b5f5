<?php

declare(strict_types=1);

namespace App\Preferences\Option;

class KeywordHighlightOption
{
    public const string DEFAULT_TRUE  = 'default_true'; // true, unless user has set a preference
    public const string DEFAULT_FALSE = 'default_false'; // false, unless user has set a preference
    public const string FORCED_TRUE   = 'forced_true'; // always true, regardless of user preference
    public const string FORCED_FALSE  = 'forced_false'; // always false, regardless of user preference

    public const array SUPPORTED_OPTIONS = [
        self::DEFAULT_TRUE,
        self::DEFAULT_FALSE,
        self::FORCED_TRUE,
        self::FORCED_FALSE,
    ];
}

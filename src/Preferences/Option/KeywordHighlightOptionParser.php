<?php

declare(strict_types=1);

namespace App\Preferences\Option;

use App\Preferences\Helper\PreferencesHelper;

readonly class KeywordHighlightOptionParser
{
    public function __construct(private PreferencesHelper $preferencesHelper)
    {
    }

    public function parseOption(?string $keywordHighlightOption = null): bool
    {
        switch ($keywordHighlightOption) {
            case KeywordHighlightOption::DEFAULT_TRUE:
                return $this->preferencesHelper->getKeywordHighlight() ?? true;
            case KeywordHighlightOption::DEFAULT_FALSE:
                return $this->preferencesHelper->getKeywordHighlight() ?? false;
            case KeywordHighlightOption::FORCED_TRUE:
                return true;
            case KeywordHighlightOption::FORCED_FALSE:
            case null:
                return false;
            default:
                // omitted intentionally
        }

        throw new \RuntimeException(sprintf('invalid highlight mode "%s" received', $keywordHighlightOption));
    }
}

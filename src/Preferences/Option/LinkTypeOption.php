<?php

declare(strict_types=1);

namespace App\Preferences\Option;

class LinkTypeOption
{
    public const string DEFAULT_DIRECT       = 'default_direct';       // direct, unless user has set a preference
    public const string DEFAULT_TARGET_BLANK = 'default_target_blank'; // new tab, unless user has set a preference
    public const string FORCED_DIRECT        = 'forced_direct';        // always direct, regardless of user preference
    public const string FORCED_TARGET_BLANK  = 'forced_target_blank';  // always new tab, regardless of user preference

    public const array SUPPORTED_OPTIONS = [
        self::DEFAULT_DIRECT,
        self::DEFAULT_TARGET_BLANK,
        self::FORCED_DIRECT,
        self::FORCED_TARGET_BLANK,
    ];
}

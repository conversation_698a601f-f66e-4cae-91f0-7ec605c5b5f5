<?php

declare(strict_types=1);

namespace App\Preferences\Twig;

use App\Preferences\Option\LinkTypeOptionParser;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class LinkTypeOptionExtension extends AbstractExtension
{
    public function __construct(private readonly LinkTypeOptionParser $linkTypeOptionParser)
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('link_type_option_attributes', $this->renderLinkTypeAttributes(...), ['is_safe' => ['html']]),
        ];
    }

    /**
     * @param array<string, string> $defaultAttributes
     */
    public function renderLinkTypeAttributes(?string $linkTypeOption, array $defaultAttributes = []): string
    {
        $linkType = $this->linkTypeOptionParser->parseLinkModeOption($linkTypeOption);

        if ($linkType === LinkTypeOptionParser::LINK_TYPE_TARGET_BLANK) {
            $defaultAttributes['target'] = '_blank';
        }

        $defaultAttributes = array_map(
            static fn ($key, $value) => sprintf('%s="%s"', $key, htmlspecialchars($value, ENT_QUOTES)),
            array_keys($defaultAttributes),
            $defaultAttributes,
        );

        return (bool)$defaultAttributes ? ' '.implode(' ', $defaultAttributes) : '';
    }
}

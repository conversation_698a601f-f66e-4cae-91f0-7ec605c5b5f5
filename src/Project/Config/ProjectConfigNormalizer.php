<?php

declare(strict_types=1);

namespace App\Project\Config;

use Visymo\ArrayReader\ArrayReader;

final readonly class ProjectConfigNormalizer
{
    /**
     * @param mixed[] $config
     *
     * @return mixed[]
     */
    public function normalize(array $config): array
    {
        $reader = new ArrayReader($config);
        $config['content_page'] = $this->normalizeContentPageConfig(
            $reader->getChild('content_page'),
        );

        return $config;
    }

    /**
     * @return array<string, mixed[]>
     */
    private function normalizeContentPageConfig(ArrayReader $reader): array
    {
        $collectionConfig = [];
        $routeConfig = [];
        $domainConfig = [];

        // Collection and route
        foreach ($reader->getChildren('brands') as $childReader) {
            $adSenseContractType = $childReader->getString('adsense_contract_type');
            $slug = $childReader->getString('slug');

            $collectionConfig[$adSenseContractType][$slug] = $childReader->getString('collection');
            $routeConfig[$slug] = $childReader->getString('organic_result_route');
        }

        // Domain
        foreach ($reader->getChildren('domains') as $childReader) {
            $locale = $childReader->getString('locale');

            foreach ($childReader->getChildren('brands') as $brandReader) {
                $slug = $brandReader->getString('slug');

                $domainConfig[$locale][$slug] = $brandReader->getString('host');
            }
        }

        return [
            'collection' => $collectionConfig,
            'route'      => $routeConfig,
            'domain'     => $domainConfig,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Project\Config;

use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

final class ProjectConfigRegistry implements ProjectConfigRegistryInterface
{
    private ProjectConfig $projectConfig;

    public function __construct(
        private readonly SerializedFileInterface $projectConfigPhpFile,
        private readonly ProjectConfigFactory $projectConfigFactory
    )
    {
    }

    public function getProjectConfig(): ProjectConfig
    {
        if (!isset($this->projectConfig)) {
            $this->projectConfig = $this->projectConfigFactory->create(
                $this->projectConfigPhpFile->getContents(),
            );
        }

        return $this->projectConfig;
    }
}

<?php

declare(strict_types=1);

namespace App\RelatedTerms\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\Search\Query\SearchQueryNormalizer;

final class RelatedTermsRequest implements RelatedTermsRequestInterface
{
    /** @var string[] */
    private array $relatedTerms;

    private string $alternateRelatedQuery;

    private ?RelatedTermsZone $relatedTermsZone;

    private bool $relatedTermsZoneInitialized = false;

    private int $relatedTermsLinkIndex;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer,
        private readonly SearchQueryNormalizer $searchQueryNormalizer
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getRelatedTerms(): ?array
    {
        if (!isset($this->relatedTerms)) {
            $termsAsString = $this->requestManager->queryBag()->getNullableString(self::PARAMETER_TERMS);

            if ($termsAsString === null) {
                $this->relatedTerms = [];
            } else {
                $termsAsString = $this->searchQueryNormalizer->getNormalizedQuery($termsAsString);
                $termsAsString = trim($termsAsString);

                $this->relatedTerms = explode(',', $termsAsString);
                $this->relatedTerms = array_map('trim', $this->relatedTerms);
                $this->relatedTerms = array_filter($this->relatedTerms, static fn ($value) => $value !== '');
                $this->relatedTerms = array_unique($this->relatedTerms);
                // This should always be last for resetting the keys (otherwise the statistics log wont work)
                $this->relatedTerms = array_values($this->relatedTerms);
            }
        }

        return $this->requestPropertyNormalizer->getStringArray($this->relatedTerms);
    }

    public function getAlternateRelatedQuery(): ?string
    {
        if (!isset($this->alternateRelatedQuery)) {
            $this->alternateRelatedQuery = $this->requestManager->queryBag()->getString(self::PARAMETER_ALTERNATE_RELATED_QUERY);
            $this->alternateRelatedQuery = $this->searchQueryNormalizer->getNormalizedLandingsPageQuery($this->alternateRelatedQuery);
            $this->alternateRelatedQuery = $this->searchQueryNormalizer->getNormalizedQuery($this->alternateRelatedQuery);
        }

        return $this->requestPropertyNormalizer->getString($this->alternateRelatedQuery);
    }

    public function getRelatedTermsZone(): ?RelatedTermsZone
    {
        if (!$this->relatedTermsZoneInitialized) {
            $this->relatedTermsZoneInitialized = true;
            $value = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_RELATED_TERMS_ZONE,
                array_column(RelatedTermsZone::cases(), 'value'),
            );

            $this->relatedTermsZone = RelatedTermsZone::tryFrom($value);
        }

        return $this->relatedTermsZone;
    }

    public function getRelatedTermsLinkIndex(): ?int
    {
        if (!isset($this->relatedTermsLinkIndex)) {
            $this->relatedTermsLinkIndex = $this->requestManager->queryBag()->getUnsignedInt(
                self::PARAMETER_RELATED_TERMS_LINK_INDEX,
            );
        }

        return $this->requestPropertyNormalizer->getInt($this->relatedTermsLinkIndex);
    }

    /**
     * Contains request URL parameters only
     *
     * @return array<string, int|string>
     */
    public function getUrlParameters(): array
    {
        return array_filter(
            $this->toArray(),
            static fn ($value) => $value !== null,
        );
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_TERMS                    => $this->getRelatedTerms(),
            self::PARAMETER_ALTERNATE_RELATED_QUERY  => $this->getAlternateRelatedQuery(),
            self::PARAMETER_RELATED_TERMS_ZONE       => $this->getRelatedTermsZone()?->value,
            self::PARAMETER_RELATED_TERMS_LINK_INDEX => $this->getRelatedTermsLinkIndex(),
        ];
    }
}

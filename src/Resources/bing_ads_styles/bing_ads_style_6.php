<?php

declare(strict_types=1);

return [
    'page' => [
        'disableTextAdExtensions'   => [
            'longAdTitle',
            'smart',
            'genericText',
            'topAds',
            'brand',
            'siteLinks',
            'call',
            'meteredCall',
            'location',
            'merchantRating',
            'dynamicSiteLinks',
            'consumerRatings',
            'enhancedSiteLinks',
            'image',
            'review',
            'price',
            'multiImages',
            'dynamicData',
            'action',
            'logo',
        ],
        'clickOnAllText'            => true,
        'clickableButtonTextOption' => 1,
        'enableClickableButton'     => true,
    ],
    'unit' => [
        'adStyle' => [
            'textAd'        => [
                'attributionBorderWidth'     => 0,
                'attributionFontSize'        => 13,
                'attributionPosition'        => 1,
                'backgroundColor'            => '#F7F7F7',
                'boldTitle'                  => true,
                'boldUrl'                    => false,
                'descriptionColor'           => '#1D2849',
                'descriptionFontSize'        => 15,
                'descriptionLineHeight'      => 30,
                'domainLinkAboveDescription' => true,
                'fontFamily'                 => 'Inter,arial,sans-serif',
                'horizontalSpacing'          => 0,
                'paddingLeftForAd'           => 14,
                'paddingRightForAd'          => 14,
                'paddingBottomForAd'         => 30,
                'paddingTopForAd'            => 10,
                'titleColor'                 => '#1D2849',
                'titleFontSize'              => 21,
                'titleLineHeight'            => 28,
                'topAttributionColor'        => '#1D2849',
                'urlColor'                   => '#1D2849',
                'urlFontSize'                => 14,
                'urlLineHeight'              => 25,
                'verticalSpacing'            => 10,
            ],
            'productAd'     => [],
            'textExtension' => [
                'enhancedSiteLinks' => [
                    'titleFontSize'         => 20,
                    'titleLineHeight'       => 25,
                    'descriptionFontSize'   => 14,
                    'descriptionLineHeight' => 30,
                ],
                'clickableButton'   => [
                    'fontSize'        => 17,
                    'backgroundColor' => '#FFFFFF',
                    'color'           => '#000000',
                ],
            ],
        ],
    ],
];

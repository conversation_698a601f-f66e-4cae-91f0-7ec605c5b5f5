<?php

declare(strict_types=1);

return [
    'page' => [
        'disableTextAdExtensions'   => [
            'longAdTitle',
            'smart',
            'genericText',
            'topAds',
            'brand',
            'siteLinks',
            'call',
            'meteredCall',
            'location',
            'merchantRating',
            'dynamicSiteLinks',
            'consumerRatings',
            'enhancedSiteLinks',
            'image',
            'review',
            'price',
            'multiImages',
            'dynamicData',
            'action',
            'logo',
        ],
        'clickOnAllText'            => true,
        'clickableButtonTextOption' => 1,
        'enableClickableButton'     => true,
    ],
    'unit' => [
        'adStyle' => [
            'textAd'        => [
                'attributionBorderWidth'     => 0,
                'attributionFontSize'        => 13,
                'attributionPosition'        => 1,
                'backgroundColor'            => '#01074B',
                'borderColorForAd'           => '#01074B',
                'borderColorForAdContainer'  => '#01074B',
                'boldTitle'                  => true,
                'boldUrl'                    => false,
                'descriptionColor'           => '#FFFFFF',
                'descriptionFontSize'        => 15,
                'descriptionLineHeight'      => 30,
                'domainLinkAboveDescription' => true,
                'fontFamily'                 => 'Inter,arial,sans-serif',
                'horizontalSpacing'          => 0,
                'paddingLeftForAd'           => 14,
                'paddingRightForAd'          => 14,
                'paddingBottomForAd'         => 30,
                'paddingTopForAd'            => 10,
                'titleColor'                 => '#FFFFFF',
                'titleFontSize'              => 21,
                'titleLineHeight'            => 28,
                'topAttributionColor'        => '#FFFFFF',
                'urlColor'                   => '#FFFFFF',
                'urlFontSize'                => 14,
                'urlLineHeight'              => 25,
                'verticalSpacing'            => 10,
            ],
            'productAd'     => [],
            'textExtension' => [
                'enhancedSiteLinks' => [
                    'titleFontSize'         => 20,
                    'titleLineHeight'       => 25,
                    'descriptionFontSize'   => 14,
                    'descriptionLineHeight' => 30,
                ],
                'clickableButton'   => [
                    'fontSize'        => 17,
                    'color'           => '#000000',
                    'backgroundColor' => '#FFFFFF',
                ],
            ],
        ],
    ],
];

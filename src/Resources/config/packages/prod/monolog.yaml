monolog:
    handlers:
        sentry_buffer:
            type: buffer
            level: warning
            channels: [ "!event", "!conversion", "!conversion_log", "!statistics", "!javascript_error", "!javascript_related_terms_view" ]
            handler: sentry
        sentry:
            type: service
            id: Visymo\MonologExtensionsBundle\Handler\SentryHandler

        socket_buffer:
            type: buffer
            level: notice
            channels: [ "!event", "!conversion", "!conversion_log", "!statistics", "!javascript_error", "!javascript_related_terms_view" ]
            handler: socket
        socket:
            type: socket
            connection_string: "%env(LOGSTASH_MONOLOG_CONNECTION_STRING)%"
            persistent: true
            formatter: Visymo\MonologExtensionsBundle\Formatter\LogstashFormatter
            level: notice

        console:
            type: console

sentry:
    dsn: "%env(SENTRY_DSN)%"
    environment: "%kernel.environment%"
    in_app_exclude:
        - "%kernel.project_dir%/vendor"
        - "%kernel.project_dir%/var/cache"
    prefixes:
        - "%kernel.project_dir%"
    release: '%release%'
    default_integrations: false
    integrations:
        - Sentry\Integration\RequestIntegration
        - Sentry\Integration\EnvironmentIntegration
        - Sentry\Integration\FrameContextifierIntegration
    fingerprint_groups:
        -   exception: Http\Client\Exception\NetworkException
        -   exception: RedisClusterException
            messages:
                - is not covered by any node in this cluster
    fingerprint_fallback: exception_details

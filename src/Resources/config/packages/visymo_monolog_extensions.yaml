monolog_extensions:
    project_name: brand-websites
    exception_logging_levels:
        - { namespace: Http\Client\Exception\NetworkException, message_pattern: '/www\.nu\.nl\/rss/i', level: !php/enum Monolog\Level::Notice }
        - { namespace: Symfony\Component\HttpKernel\Exception\BadRequestHttpException, level: !php/enum Monolog\Level::Notice }
        - { namespace: Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException, level: !php/enum Monolog\Level::Debug }
        - { namespace: Symfony\Component\HttpKernel\Exception\NotFoundHttpException, level: !php/enum Monolog\Level::Debug }
        - { namespace: Twig\Error\RuntimeError, message_pattern: '/CompositeSearchApiClient.+NetworkException.+Operation timed out/i', level: !php/enum Monolog\Level::Info }
        - { namespace: App\Startpage\KnmiWeather\Exception\KnmiWeatherImageException, level: !php/enum Monolog\Level::Notice }
        - { namespace: App\Startpage\News\Exception\NewsImageException, level: !php/enum Monolog\Level::Notice }
        - { namespace: App\Startpage\News\Exception\UnsupportedHashAndImageUrlException, level: !php/enum Monolog\Level::Notice }
        - { namespace: App\Startpage\News\Exception\UnableToExtractHashAndImageUrlException, level: !php/enum Monolog\Level::Notice }
        - { namespace: App\Startpage\News\Exception\NewsRubricException, level: !php/enum Monolog\Level::Notice }
        - { namespace: App\RobotsTxt\File\ReadRobotsTxtContentFailedException, level: !php/enum Monolog\Level::Critical }
        - { namespace: App\Debug\Controller\Exception\DebugErrorException, level: !php/enum Monolog\Level::Debug }
        - { namespace: Visymo\AutosuggestApiClient\HttpClient\Exception\UnexpectedResponseStatusCodeException, message_pattern: '/(500|502|503)/i', level: !php/enum Monolog\Level::Notice }
        - { namespace: Visymo\AutosuggestApiClient\HttpClient\Exception\PromiseRejectedException, message_pattern: '/Operation timed out/i', level: !php/enum Monolog\Level::Notice }
        - { namespace: Visymo\BingApiClient\Exception\BingNetworkException, level: !php/enum Monolog\Level::Notice }

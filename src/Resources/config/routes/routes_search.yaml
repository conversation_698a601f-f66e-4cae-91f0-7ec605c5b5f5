brand_website.legacy_search_seo:
    resource: ../../../Search/Controller/LegacySearchSeoController.php
    type: attribute

brand_website.search:
    resource: ../../../Search/Controller/SearchController.php
    type: attribute
    condition: "service('route_checker_search').check()"

brand_website.search_seo:
    resource: ../../../Search/Controller/SearchSeoController.php
    type: attribute
    condition: "service('route_checker_search_seo').check()"

brand_website.advanced_search:
    resource: ../../../AdvancedSearch/Controller/
    type: attribute
    condition: "service('route_checker_search').check()"

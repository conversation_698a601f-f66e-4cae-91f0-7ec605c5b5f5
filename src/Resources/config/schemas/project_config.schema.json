{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://www.visymo.com/project_config.schema.json", "definitions": {"locale": {"type": "string", "pattern": "^[a-z]{2}_[A-Z]{2}$"}, "contentPage": {"type": "object", "additionalProperties": true, "required": ["brands", "domains"], "properties": {"brands": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"slug": {"type": "string", "minLength": 1}, "collection": {"type": "string"}, "adsense_contract_type": {"type": "string", "enum": ["direct", "online"]}, "organic_result_route": {"type": "string", "pattern": "^route_.+$"}}}}, "domains": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"locale": {"$ref": "#/definitions/locale"}, "brands": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"slug": {"type": "string", "minLength": 1}, "host": {"type": "string", "minLength": 1}}}}}}}}}, "trademarkInfringement": {"type": "object", "additionalProperties": true, "required": ["rules"], "properties": {"rules": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"query": {"type": "string", "minLength": 1}, "match_type": {"type": "string", "enum": ["phrase", "exact"]}, "locales": {"type": "array", "items": {"$ref": "#/definitions/locale"}}}}}}}}, "type": "object", "required": ["content_page", "trademark_infringement"], "additionalProperties": true, "properties": {"content_page": {"$ref": "#/definitions/contentPage"}, "trademark_infringement": {"$ref": "#/definitions/trademarkInfringement"}}}
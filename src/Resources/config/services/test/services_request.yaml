# Any services defined here will only be used in the "test" environment
services:
    _defaults:
        public: true # All public to enable overriding service
        autowire: true
        autoconfigure: true

    # Request override
    App\Account\Request\AccountRequestInterface:
        alias: App\Account\Request\AccountRequest

    App\AdBot\Request\AdBotRequestInterface:
        alias: App\AdBot\Request\AdBotRequest

    App\AutoSuggest\Request\AutoSuggestRequestInterface:
        alias: App\AutoSuggest\Request\AutoSuggestRequest

    App\ConversionTracking\Endpoint\BingAds\BingAdsRequestInterface:
        alias: App\ConversionTracking\Endpoint\BingAds\BingAdsRequest

    App\ContentPage\Request\ContentPageRequestInterface:
        alias: App\ContentPage\Request\ContentPageRequest

    App\ContentPageHome\Request\ContentPageCategoryRequestInterface:
        alias: App\ContentPageHome\Request\ContentPageCategoryRequest

    App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseRequestInterface:
        alias: App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseRequest

    App\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerRequestInterface:
        alias: App\ConversionTracking\Endpoint\GoogleAdManager\GoogleAdManagerRequest

    App\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsRequestInterface:
        alias: App\ConversionTracking\Endpoint\GoogleRelatedTerms\GoogleRelatedTermsRequest

    App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchRequestInterface:
        alias: App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchRequest

    App\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsRequestInterface:
        alias: App\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsRequest

    App\ConversionTracking\Request\ConversionTrackingRequestInterface:
        alias: App\ConversionTracking\Request\ConversionTrackingRequest

    App\CookieConsent\Request\CookieConsentRequestInterface:
        alias: App\CookieConsent\Request\CookieConsentRequest

    App\Debug\Request\DebugRequestInterface:
        alias: App\Debug\Request\DebugRequest

    App\Http\Request\GenericRequestInterface:
        alias: App\Http\Request\GenericRequest

    App\ImageSearch\Request\ImageSearchRequestInterface:
        alias: App\ImageSearch\Request\ImageSearchRequest

    App\JavaScriptError\Request\JavaScriptErrorRequestInterface:
        alias: App\JavaScriptError\Request\JavaScriptErrorRequest

    App\JavaScriptRelatedTerms\Request\JavaScriptRelatedTermsViewRequestInterface:
        alias: App\JavaScriptRelatedTerms\Request\JavaScriptRelatedTermsViewRequest

    App\JsonTemplate\Request\JsonTemplateRequestInterface:
        alias: App\JsonTemplate\Request\JsonTemplateRequest

    App\Locale\Request\LocaleRequestInterface:
        alias: App\Locale\Request\LocaleRequest

    App\Http\Request\NdJson\NdJsonRequestInterface:
        alias: App\Http\Request\NdJson\NdJsonRequest

    App\NewsSearch\Request\NewsSearchRequestInterface:
        alias: App\NewsSearch\Request\NewsSearchRequest

    App\Office\Request\OfficeRequestInterface:
        alias: App\Office\Request\OfficeRequest

    App\OneTrust\Request\ConsentManagementPlatformRequestInterface:
        alias: App\OneTrust\Request\ConsentManagementPlatformRequest

    App\RelatedTerms\Request\RelatedTermsRequestInterface:
        alias: App\RelatedTerms\Request\RelatedTermsRequest

    App\Http\Request\Info\RequestInfoInterface:
        alias: App\Http\Request\Info\RequestInfo

    App\Search\Request\SearchRequestInterface:
        alias: App\Search\Request\SearchRequest

    App\SplitTest\Request\SplitTestRequestInterface:
        alias: App\SplitTest\Request\SplitTestRequest

    App\Tracking\Entry\Request\TrackingEntryRequestInterface:
        alias: App\Tracking\Entry\Request\TrackingEntryRequest

    App\Tracking\Request\SeaRequestInterface:
        alias: App\Tracking\Request\SeaRequest

    # Request validation data provider
    Tests\Integration\Http\Request\RequestValidationDataProvider:
        arguments:
            $requestServiceLocator: !tagged_locator brand_website.http.request

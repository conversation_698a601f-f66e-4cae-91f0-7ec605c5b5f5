<?php

declare(strict_types=1);

namespace App\RobotsTxt\Controller;

use App\RobotsTxt\File\RobotsTxtFileReader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class RobotsTxtController extends AbstractController
{
    public const string ROBOTS_TXT_CONTENT_TYPE = 'text/plain; charset=UTF-8';

    #[Route(path: '/robots.txt', name: 'route_robots_txt', methods: ['GET'])]
    public function robotsTxt(RobotsTxtFileReader $robotsTxtFileReader): Response
    {
        return new Response(
            $robotsTxtFileReader->getContent(),
            Response::HTTP_OK,
            [
                'Content-Type' => self::ROBOTS_TXT_CONTENT_TYPE,
            ],
        );
    }
}

<?php

declare(strict_types=1);

namespace App\RobotsTxt\File;

use App\Domain\Settings\DomainSettingsHelperInterface;
use App\RobotsTxt\Settings\RobotsTxtSettings;
use Visymo\Filesystem\File\Reader\FileReaderFactoryInterface;

readonly class RobotsTxtFileReader
{
    public function __construct(
        private RobotsTxtSettings $robotsTxtSettings,
        private FileReaderFactoryInterface $fileReaderFactory,
        private DomainSettingsHelperInterface $domainSettingsHelper
    )
    {
    }

    /**
     * @throws ReadRobotsTxtContentFailedException
     */
    public function getContent(): string
    {
        try {
            // Could be an empty string which is valid
            return $this->fileReaderFactory->create(
                $this->robotsTxtSettings->fileName,
            )->readContent();
        } catch (\Throwable $exception) {
            $host = $this->domainSettingsHelper->getSettings()->host;

            throw ReadRobotsTxtContentFailedException::create($host, $exception);
        }
    }
}

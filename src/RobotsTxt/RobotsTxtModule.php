<?php

declare(strict_types=1);

namespace App\RobotsTxt;

use App\BrandOverride\BrandOverrideModuleInterface;
use App\DependencyInjection\AbstractDependencyInjectionModule;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class RobotsTxtModule extends AbstractDependencyInjectionModule implements BrandOverrideModuleInterface
{
    public const string KEY_NAME  = 'name';
    public const string KEY_HOSTS = 'hosts';

    public static function getModuleName(): string
    {
        return 'robots_txt';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodePrototypeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->defaultValue([])
                ->arrayPrototype()
                ->children();

        $moduleNodePrototypeChildren
            ->scalarNode(self::KEY_NAME)
                ->info('Name of the robots.txt file name.')
                ->example('robots-seo.txt')
                ->isRequired();

        $moduleNodePrototypeChildren->arrayNode(self::KEY_HOSTS)
            ->info('Exact hostnames for which this robots.txt should be made available')
                ->isRequired()
                ->scalarPrototype();
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $moduleConfig = $this->getModuleConfig($config);
        $container->setParameter(
            self::getModuleParameterConfigName(),
            $moduleConfig,
        );
    }

    /**
     * @inheritDoc
     */
    public function isModuleConfigDefined(array $moduleConfig): bool
    {
        return $moduleConfig !== [];
    }
}

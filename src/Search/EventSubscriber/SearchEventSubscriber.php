<?php

declare(strict_types=1);

namespace App\Search\EventSubscriber;

use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\Search\Request\SearchRequestInterface;
use App\Tracking\Request\SeaRequestInterface;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class SearchEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private SeaRequestInterface $seaRequest,
        private SearchRequestInterface $searchRequest,
        private TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            JsonTemplateViewCreatedEvent::NAME => ['onJsonTemplateViewCreated'],
        ];
    }

    public function onJsonTemplateViewCreated(JsonTemplateViewCreatedEvent $event): void
    {
        $query = null;

        if (!$this->trademarkInfringementResultBlocker->blockResults()) {
            $query = $this->seaRequest->getUserQuery() ?? $this->searchRequest->getQuery();
        }

        $event->view->getDataRequest()->setQuery($query);
        $event->view->getDataRegistry()->setQuery($query);
    }
}

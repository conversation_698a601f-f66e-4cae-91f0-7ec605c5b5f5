<?php

declare(strict_types=1);

namespace App\Search\Request;

use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use App\Search\Query\SearchQueryNormalizer;

/**
 * Provide a singular interface for retrieving normalised search request parameters
 */
final class SearchRequest implements SearchRequestInterface
{
    private string $query;

    private bool $ignoreQueryForSearch;

    private bool $landingPage;

    private bool $seoPage;

    private int $page;

    private int $searchRequestRate;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer,
        private readonly RequestInfoInterface $requestInfo,
        private readonly SearchRequestQueryHelper $searchRequestQueryHelper,
        private readonly SearchQueryNormalizer $searchQueryNormalizer
    )
    {
    }

    public function getQuery(): ?string
    {
        if (!isset($this->query)) {
            $this->query = $this->determineQuery();

            if ($this->isLandingPage()) {
                $this->query = $this->searchQueryNormalizer->getNormalizedLandingsPageQuery($this->query);
            }

            $this->query = $this->searchQueryNormalizer->getNormalizedQuery($this->query);
        }

        return $this->requestPropertyNormalizer->getString($this->query);
    }

    private function determineQuery(): string
    {
        $flagBag = $this->requestManager->flagBag();

        if ($flagBag->getBool(SearchRequestFlag::QUERY_IN_PATH)) {
            return $this->searchRequestQueryHelper->getQueryFromPath();
        }

        if ($flagBag->getBool(SearchRequestFlag::QUERY_IS_CATEGORY_SLUG)) {
            return $this->searchRequestQueryHelper->getQueryFromCategory();
        }

        return $this->searchRequestQueryHelper->getQueryFromParameter();
    }

    public function ignoreQueryForSearch(): bool
    {
        if (!isset($this->ignoreQueryForSearch)) {
            $this->ignoreQueryForSearch = $this->requestManager->flagBag()->getBool(
                SearchRequestFlag::IGNORE_QUERY_FOR_SEARCH,
            );
        }

        return $this->ignoreQueryForSearch;
    }

    public function isLandingPage(): bool
    {
        if (!isset($this->landingPage)) {
            $this->landingPage = $this->requestManager->flagBag()->getBool(SearchRequestFlag::IS_LANDING_PAGE);
        }

        return $this->landingPage;
    }

    public function isSeoPage(): bool
    {
        if (!isset($this->seoPage)) {
            $this->seoPage = $this->requestManager->flagBag()->getBool(SearchRequestFlag::IS_SEO_PAGE);
        }

        return $this->seoPage;
    }

    public function isArticle(): bool
    {
        return $this->requestInfo->isRoute('route_article');
    }

    public function isDisplaySearch(): bool
    {
        return $this->requestInfo->isRoute('route_display_search');
    }

    public function isDisplaySearchAdvertised(): bool
    {
        return $this->requestInfo->isRoute('route_display_search_advertised');
    }

    public function isDisplaySearchRelated(): bool
    {
        return $this->requestInfo->isRoute('route_display_search_related');
    }

    public function isDisplaySearchRelatedWeb(): bool
    {
        return $this->requestInfo->isRoute('route_display_search_related_web');
    }

    public function isMicrosoftSearchRelatedWeb(): bool
    {
        return $this->requestInfo->isRoute('route_microsoft_search_related_web');
    }

    public function isWebSearch(): bool
    {
        return $this->requestInfo->isRoute('route_web_search');
    }

    public function isWebSearchAdvertised(): bool
    {
        return $this->requestInfo->isRoute('route_web_search_advertised');
    }

    public function getQueryAsString(): string
    {
        return (string)$this->getQuery();
    }

    public function getPage(): int
    {
        if (!isset($this->page)) {
            $page = $this->requestManager->queryBag()->getUnsignedInt(self::PARAMETER_PAGE);

            $this->page = max($page, 1);
        }

        return $this->page;
    }

    public function getSearchRequestRate(): int
    {
        if (!isset($this->searchRequestRate)) {
            $this->searchRequestRate = $this->requestManager->headersBag()->getInt(self::HEADER_SEARCH_REQUEST_RATE);
        }

        return $this->searchRequestRate;
    }

    public function isRequestedPageAllowed(): bool
    {
        if ($this->requestManager->flagBag()->getBool(SearchRequestFlag::LIMIT_AMOUNT_OF_PAGES)) {
            $requestedPage = $this->getPage();

            if ($requestedPage > SearchRequestInterface::MAX_PAGE_SIZE) {
                // reset page, otherwise it will also be used on the error page
                $this->page = 1;

                return false;
            }
        }

        return true;
    }

    /**
     * Contains request URL parameters only
     *
     * @return array<string, int|string>
     */
    public function getUrlParameters(): array
    {
        return array_filter(
            [
                self::PARAMETER_QUERY => $this->getQuery(),
                self::PARAMETER_PAGE  => $this->getPage(),
            ],
            static fn ($value) => $value !== null,
        );
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_QUERY            => $this->getQuery(),
            self::PARAMETER_PAGE             => $this->getPage(),
            self::HEADER_SEARCH_REQUEST_RATE => $this->getSearchRequestRate(),
        ];
    }
}

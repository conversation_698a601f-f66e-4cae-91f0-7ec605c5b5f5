<?php

declare(strict_types=1);

namespace App\Search\Request;

use App\Http\Request\RequestInterface;
use App\Http\Request\UrlRequestInterface;

interface SearchRequestInterface extends RequestInterface, UrlRequestInterface
{
    public const string ATTRIBUTE_QUERY = 'query';

    public const string PARAMETER_QUERY = 'q';
    public const string PARAMETER_PAGE  = 'pg';

    public const string HEADER_SEARCH_REQUEST_RATE = 'X-Loadbalancer-Search-Request-Rate';

    public const int MAX_PAGE_SIZE = 5;

    public function getQuery(): ?string;

    public function ignoreQueryForSearch(): bool;

    public function isLandingPage(): bool;

    public function isSeoPage(): bool;

    public function isArticle(): bool;

    public function isDisplaySearch(): bool;

    public function isDisplaySearchAdvertised(): bool;

    public function isDisplaySearchRelated(): bool;

    public function isDisplaySearchRelatedWeb(): bool;

    public function isMicrosoftSearchRelatedWeb(): bool;

    public function isWebSearch(): bool;

    public function isWebSearchAdvertised(): bool;

    public function getQueryAsString(): string;

    public function getPage(): int;

    public function getSearchRequestRate(): int;

    public function isRequestedPageAllowed(): bool;
}

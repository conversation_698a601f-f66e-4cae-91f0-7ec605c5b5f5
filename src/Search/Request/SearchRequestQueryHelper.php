<?php

declare(strict_types=1);

namespace App\Search\Request;

use App\ContentPageHome\Request\ContentPageCategoryRequestInterface;
use App\Http\Request\Manager\RequestManagerInterface;

class SearchRequestQueryHelper
{
    private const string DEPRECATED_PARAMETER_QUERY = 'query';

    public function __construct(
        private readonly RequestManagerInterface $requestManager
    )
    {
    }

    public function getQueryFromParameter(): string
    {
        $query = $this->requestManager->queryBag()->getString(SearchRequestInterface::PARAMETER_QUERY);

        if ($query === '') {
            $query = $this->requestManager->queryBag()->getString(self::DEPRECATED_PARAMETER_QUERY);
        }

        return $query;
    }

    public function getQueryFromPath(): string
    {
        return rtrim(
            urldecode(
                $this->requestManager->attributesBag()->getString(SearchRequestInterface::ATTRIBUTE_QUERY),
            ),
            '/',
        );
    }

    public function getQueryFromCategory(): string
    {
        return urldecode(
            $this->requestManager->attributesBag()->getString(ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_SLUG),
        );
    }
}

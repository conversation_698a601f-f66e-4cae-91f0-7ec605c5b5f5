<?php

declare(strict_types=1);

namespace App\Search\Routing;

use App\Generic\Routing\RouteCheckerInterface;
use App\Search\Settings\SearchSettings;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class SearchSeoRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_search_seo';

    public function __construct(
        private SearchSettings $searchSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->searchSettings->seoEnabled;
    }
}

<?php

declare(strict_types=1);

namespace App\Search;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class SearchModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'search';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_search.yaml']);
    }
}

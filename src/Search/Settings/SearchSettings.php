<?php

declare(strict_types=1);

namespace App\Search\Settings;

use App\Generic\Device\Device;
use App\ModuleSettings\ModuleSettingsInterface;

readonly class SearchSettings implements ModuleSettingsInterface
{
    public function __construct(
        public bool $enabled,
        public bool $seoEnabled,
        public ?int $styleIdDesktop,
        public ?int $styleIdMobile
    )
    {
    }

    public function getStyleIdForDevice(Device $device): ?int
    {
        return match ($device) {
            Device::MOBILE => $this->styleIdMobile,
            default        => $this->styleIdDesktop,
        };
    }
}

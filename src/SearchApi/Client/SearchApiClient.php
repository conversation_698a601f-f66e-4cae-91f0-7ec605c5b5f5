<?php

declare(strict_types=1);

namespace App\SearchApi\Client;

use App\Debug\Helper\DebugHelper;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Visymo\CompositeSearchApiClient\CompositeSearchApiClientInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Request\SearchRequestInterface as CompositeSearchApiRequestInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\SearchResponseInterface as CompositeSearchApiResponseInterface;

readonly class SearchApiClient
{
    public function __construct(
        private CompositeSearchApiClientInterface $compositeSearchApiClient,
        private TrademarkInfringementResultBlocker $trademarkInfringementResultBlocker,
        private DebugHelper $debugHelper
    )
    {
    }

    public function search(
        CompositeSearchApiRequestInterface $compositeSearchApiRequest
    ): CompositeSearchApiResponseInterface
    {
        if ($this->trademarkInfringementResultBlocker->blockResults()) {
            throw new NotFoundHttpException('Do not execute search because of search result blocking');
        }

        $compositeSearchApiResponse = $this->compositeSearchApiClient->search($compositeSearchApiRequest);

        $this->debugHelper->addCompositeSearchApiRequestAndResponse(
            compositeSearchApiRequest : $compositeSearchApiRequest,
            compositeSearchApiResponse: $compositeSearchApiResponse,
        );

        return $compositeSearchApiResponse;
    }
}

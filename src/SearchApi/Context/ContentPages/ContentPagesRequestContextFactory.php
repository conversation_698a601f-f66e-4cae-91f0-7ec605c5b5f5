<?php

declare(strict_types=1);

namespace App\SearchApi\Context\ContentPages;

use App\Brand\Settings\BrandSettingsHelper;
use App\ContentPage\Helper\ContentPageHelper;
use App\ContentPage\Settings\ContentPageSettings;
use App\ContentPageHome\Request\ContentPageCategoryRequestInterface;
use App\Debug\Request\DebugRequestInterface;
use App\JsonTemplate\View\DataRequest\ContentPagesViewDataRequest;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\Search\Request\SearchRequestInterface;
use App\SearchApi\Context\RequestContextFactoryInterface;
use App\Tracking\Helper\ActiveTrackingEntryHelper;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPages\Request\ContentPagesRequestContext;

readonly class ContentPagesRequestContextFactory implements RequestContextFactoryInterface
{
    public function __construct(
        private ActiveTrackingEntryHelper $activeTrackingEntryHelper,
        private SearchRequestInterface $searchRequest,
        private ContentPageCategoryRequestInterface $contentPageCategoryRequest,
        private DebugRequestInterface $debugRequest,
        private ContentPageHelper $contentPageHelper,
        private BrandSettingsHelper $brandSettingsHelper,
        private ContentPageSettings $contentPageSettings
    )
    {
    }

    public function createFromViewDataRequest(ViewDataRequest $viewDataRequest): ?ContentPagesRequestContext
    {
        if ($this->debugRequest->disableContentPages()) {
            return null;
        }

        $contentPagesViewDataRequest = $viewDataRequest->contentPages();

        if (!$contentPagesViewDataRequest->isEnabled()) {
            return null;
        }

        $categoryPublicId = $contentPagesViewDataRequest->getCategoryPublicId()
                            ?? $this->contentPageCategoryRequest->getPublicId();

        $paragraphAmount = $contentPagesViewDataRequest->getExcludeParagraphs() === true
            ? 0
            : $contentPagesViewDataRequest->getParagraphAmount();

        return ContentPagesRequestContext::create(
            collectionSlugs    : $this->getCollectionSlugs($contentPagesViewDataRequest),
            isHomepage         : $contentPagesViewDataRequest->isHomepage(),
            publicIds          : $contentPagesViewDataRequest->getPublicIds(),
            excludedPublicIds  : $contentPagesViewDataRequest->getExcludedPublicIds(),
            relevantForPublicId: $contentPagesViewDataRequest->getRelevantPublicId(),
            categoryPublicId   : $categoryPublicId,
            hasImage           : $contentPagesViewDataRequest->getHasImage(),
            sort               : $this->getSort($viewDataRequest),
            pageSize           : $contentPagesViewDataRequest->getPageSize(),
            page               : $this->searchRequest->getPage(),
            paragraphAmount    : $paragraphAmount,
        );
    }

    private function getSort(ViewDataRequest $viewDataRequest): string
    {
        $contentPagesViewDataRequest = $viewDataRequest->contentPages();

        if ($contentPagesViewDataRequest->getSort() !== null) {
            return $contentPagesViewDataRequest->getSort();
        }

        if ($this->searchRequest->ignoreQueryForSearch() || $viewDataRequest->getQuery() === null) {
            return 'date_created_desc';
        }

        return 'relevance_asc';
    }

    /**
     * @return array<int, string>
     */
    private function getCollectionSlugs(ContentPagesViewDataRequest $contentPagesViewDataRequest): array
    {
        $debugContentPageCollection = $this->debugRequest->getContentPageCollection();

        if ($debugContentPageCollection !== null) {
            return [$debugContentPageCollection];
        }

        if ($contentPagesViewDataRequest->getMultipleCollectionsSupported() !== true) {
            return [$this->contentPageSettings->collection];
        }

        $device = $this->activeTrackingEntryHelper->getActiveTrackingEntry()->device;

        return $this->contentPageHelper->getCollectionsByDeviceAndExcludeBrand(
            device          : $device,
            excludeBrandSlug: $this->brandSettingsHelper->getSettings()->getSlug(),
        );
    }
}

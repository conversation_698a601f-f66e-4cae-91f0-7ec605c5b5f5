<?php

declare(strict_types=1);

namespace App\SearchApi\Context\Image;

use App\ImageSearch\Request\ImageSearchRequestInterface;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\SearchApi\Context\RequestContextFactoryInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Image\Request\ImageRequestContext;

class ImageRequestContextFactory implements RequestContextFactoryInterface
{
    private const int DEFAULT_AMOUNT = 50;

    public function __construct(private readonly ImageSearchRequestInterface $imageSearchRequest)
    {
    }

    public function create(): ?ImageRequestContext
    {
        return ImageRequestContext::create(
            self::DEFAULT_AMOUNT,
            $this->imageSearchRequest->getPeriod(),
            $this->imageSearchRequest->getImageType(),
            $this->imageSearchRequest->getImageSize(),
            $this->imageSearchRequest->getColor(),
        );
    }

    public function createFromViewDataRequest(ViewDataRequest $viewDataRequest): ?ImageRequestContext
    {
        $imageViewDataRequest = $viewDataRequest->image();

        if (!$imageViewDataRequest->isEnabled()) {
            return null;
        }

        return $this->create();
    }
}

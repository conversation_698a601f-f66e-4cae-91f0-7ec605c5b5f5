<?php

declare(strict_types=1);

namespace App\SearchApi\Context\News;

use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\NewsSearch\Request\NewsSearchRequestInterface;
use App\SearchApi\Context\RequestContextFactoryInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\News\Request\NewsRequestContext;

class NewsRequestContextFactory implements RequestContextFactoryInterface
{
    private const int DEFAULT_AMOUNT = 10;

    public function __construct(private readonly NewsSearchRequestInterface $newsSearchRequest)
    {
    }

    public function create(): ?NewsRequestContext
    {
        return NewsRequestContext::create(
            self::DEFAULT_AMOUNT,
            $this->newsSearchRequest->getCategory(),
            $this->newsSearchRequest->getPeriod(),
        );
    }

    public function createFromViewDataRequest(ViewDataRequest $viewDataRequest): ?NewsRequestContext
    {
        $newsViewDataRequest = $viewDataRequest->news();

        if (!$newsViewDataRequest->isEnabled()) {
            return null;
        }

        return $this->create();
    }
}

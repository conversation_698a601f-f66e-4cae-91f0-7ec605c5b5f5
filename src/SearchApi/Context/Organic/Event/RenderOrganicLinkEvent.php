<?php

declare(strict_types=1);

namespace App\SearchApi\Context\Organic\Event;

class RenderOrganicLinkEvent
{
    public const string NAME = 'organic_link.render';

    /** @var string[] */
    private array $dataAttributes = [];

    public function __construct(private readonly string $url)
    {
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function hasDataAttribute(string $key): bool
    {
        return array_key_exists($key, $this->dataAttributes);
    }

    /**
     * @return array<string, string>
     */
    public function getDataAttributes(): array
    {
        return $this->dataAttributes;
    }

    public function setDataAttribute(string $key, string $value): self
    {
        $this->dataAttributes[$key] = $value;

        return $this;
    }
}

<?php

declare(strict_types=1);

namespace App\SearchApi\Exception;

use App\JsonTemplate\Component\ComponentInterface;
use Visymo\Shared\Domain\Exception\ExceptionWithContextInterface;

final class NoSearchResponseFoundForComponentException extends \RuntimeException implements ExceptionWithContextInterface
{
    private ComponentInterface $component;

    public static function create(ComponentInterface $component, ?\Throwable $previous = null): self
    {
        $instance = new self(
            sprintf(
                'Search response not found for component: %s',
                $component::getType(),
            ),
            0,
            $previous,
        );
        $instance->component = $component;

        return $instance;
    }

    /**
     * @inheritDoc
     */
    public function getContext(): array
    {
        return [
            'component'    => $this->component::class,
            'component_id' => $this->component->getId(),
        ];
    }
}

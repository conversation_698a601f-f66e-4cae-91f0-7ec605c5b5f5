<?php

declare(strict_types=1);

namespace App\SearchApi\Request;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\Condition\ComponentProcessedDependencyCondition;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\SearchApiViewDataRequestInterface;

final class SearchApiRequest
{
    /**
     * @param ComponentInterface[] $components
     */
    public function __construct(
        public readonly ViewDataProperty $viewDataProperty,
        public readonly SearchApiViewDataRequestInterface $searchApiViewDataRequest,
        public readonly ViewDataConditionCollection $conditions,
        public array $components,
        public readonly bool $ignoreQuery
    )
    {
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function addComponents(array $components): void
    {
        $this->components = [...$this->components, ...$components];
    }

    public function hasComponent(ComponentInterface $component): bool
    {
        return in_array($component, $this->components, true);
    }

    public function equals(self $searchApiRequest): bool
    {
        if ($this->viewDataProperty !== $searchApiRequest->viewDataProperty) {
            return false;
        }

        if ($this->ignoreQuery !== $searchApiRequest->ignoreQuery) {
            return false;
        }

        if (!$this->conditions->equals($searchApiRequest->conditions)) {
            return false;
        }

        return $this->searchApiViewDataRequest->canUseSameSearchApiRequest($searchApiRequest->searchApiViewDataRequest);
    }

    /**
     * @param array<string, true> $processedComponentIds
     */
    public function isReadyToSend(ViewDataRegistry $viewDataRegistry, array $processedComponentIds): bool
    {
        foreach ($this->conditions->conditions as $condition) {
            if (!$condition->check($viewDataRegistry)) {
                return false;
            }

            if (!$condition instanceof ComponentProcessedDependencyCondition) {
                continue;
            }

            if (!$condition->isComponentProcessed($processedComponentIds)) {
                return false;
            }
        }

        return true;
    }
}

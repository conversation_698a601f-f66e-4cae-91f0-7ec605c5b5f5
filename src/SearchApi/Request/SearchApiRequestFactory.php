<?php

declare(strict_types=1);

namespace App\SearchApi\Request;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\SearchApiViewDataRequestInterface;

final class SearchApiRequestFactory
{
    /**
     * @param ComponentInterface[] $components
     */
    public function create(
        ViewDataProperty $viewDataProperty,
        SearchApiViewDataRequestInterface $searchApiViewDataRequest,
        ViewDataConditionCollection $conditions,
        array $components,
        bool $ignoreQuery
    ): SearchApiRequest
    {
        return new SearchApiRequest(
            viewDataProperty        : $viewDataProperty,
            searchApiViewDataRequest: $searchApiViewDataRequest,
            conditions              : $conditions,
            components              : $components,
            ignoreQuery             : $ignoreQuery,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\SearchApi\Response;

use Visymo\CompositeSearchApiClient\Domain\JsonRpc\Response\AbstractJsonRpcResponse;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPage\Response\ContentPageResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPageCategories\Response\ContentPageCategoriesResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPageCategory\Response\ContentPageCategoryResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPages\Response\ContentPagesResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Image\Response\ImageResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\News\Response\NewsResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\RelatedTerms\Response\RelatedTermsResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\Debug\SearchResponseDebug;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\SearchResponseInterface;

class EmptySearchResponse extends AbstractJsonRpcResponse implements SearchResponseInterface
{
    public function getResponseProcessTime(): ?float
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    protected function getResponseData(): array
    {
        return [];
    }

    public function getContentPage(): ContentPageResponseContext
    {
        return ContentPageResponseContext::createEmpty();
    }

    public function getContentPages(): ContentPagesResponseContext
    {
        return ContentPagesResponseContext::createEmpty();
    }

    public function getDebug(): SearchResponseDebug
    {
        return SearchResponseDebug::createEmpty();
    }

    public function getContentPageCategory(): ContentPageCategoryResponseContext
    {
        return ContentPageCategoryResponseContext::createEmpty();
    }

    public function getContentPageCategories(): ContentPageCategoriesResponseContext
    {
        return ContentPageCategoriesResponseContext::createEmpty();
    }

    public function getImage(): ImageResponseContext
    {
        return ImageResponseContext::createEmpty();
    }

    public function getNews(): NewsResponseContext
    {
        return NewsResponseContext::createEmpty();
    }

    public function getOrganic(): OrganicResponseContext
    {
        return OrganicResponseContext::createEmpty();
    }

    public function getRelatedTerms(): RelatedTermsResponseContext
    {
        return RelatedTermsResponseContext::createEmpty();
    }

    /**
     * @inheritDoc
     */
    protected function resultToArray(): array
    {
        return [];
    }
}

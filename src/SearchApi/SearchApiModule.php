<?php

declare(strict_types=1);

namespace App\SearchApi;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\SearchApi\Context\RequestContextFactoryInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class SearchApiModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'search_api';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $container->registerForAutoconfiguration(RequestContextFactoryInterface::class)
            ->addTag('brand_website.search_api.request_context_factory');
    }
}

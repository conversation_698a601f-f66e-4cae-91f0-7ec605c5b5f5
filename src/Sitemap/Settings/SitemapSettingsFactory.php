<?php

declare(strict_types=1);

namespace App\Sitemap\Settings;

use App\BrandOverride\BrandOverrideModuleInterface;
use App\BrandOverride\BrandOverrideModuleState;
use App\ModuleSettings\ModuleSettingsFactoryInterface;
use App\ModuleSettings\Value\ModuleValueFactory;
use App\Sitemap\SitemapModule;

final readonly class SitemapSettingsFactory implements ModuleSettingsFactoryInterface
{
    public function __construct(
        private BrandOverrideModuleState $brandOverrideModuleState,
        private ModuleValueFactory $moduleValueFactory
    )
    {
    }

    public static function getModuleName(): string
    {
        return SitemapModule::getModuleName();
    }

    /**
     * @inheritDoc
     */
    public function create(array $projectModuleConfig): SitemapSettings
    {
        $brandModuleConfig = $this->brandOverrideModuleState->getModuleConfigOverride(
            self::getModuleName(),
        ) ?? $projectModuleConfig;

        $enabled = (bool)$brandModuleConfig[BrandOverrideModuleInterface::KEY_ENABLED];

        return new SitemapSettings(
            enabled          : $enabled,
            conversionChannel: $enabled ? $brandModuleConfig[SitemapModule::KEY_CONVERSION_CHANNEL] : null,
        );
    }

    /**
     * @inheritDoc
     */
    public function getModuleValues(array $projectModuleConfig): array
    {
        $settings = $this->create($projectModuleConfig);

        return [
            $this->moduleValueFactory->create(
                moduleName: self::getModuleName(),
                property  : BrandOverrideModuleInterface::KEY_ENABLED,
                title     : 'Sitemaps',
                value     : $settings->enabled,
            ),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Sitemap;

use App\BrandOverride\BrandOverrideModuleInterface;
use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class SitemapModule extends AbstractDependencyInjectionModule implements BrandOverrideModuleInterface
{
    public const string KEY_CONVERSION_CHANNEL = 'conversion_channel';

    public static function getModuleName(): string
    {
        return 'sitemap';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->addDefaultsIfNotSet()
                ->children();

        $moduleNodeChildren
            ->booleanNode(self::KEY_ENABLED)
                ->info('Enable the sitemap module')
                ->defaultNull();

        $moduleNodeChildren
            ->scalarNode(self::KEY_CONVERSION_CHANNEL)
                ->info('Conversion channel to use for tracking')
                ->defaultNull();
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $moduleConfig = $this->getModuleConfig($config);
        $container->setParameter(
            self::getModuleParameterConfigName(),
            $moduleConfig,
        );

        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_sitemap.yaml']);
    }

    /**
     * @inheritDoc
     */
    public function isModuleConfigDefined(array $moduleConfig): bool
    {
        return $moduleConfig[self::KEY_ENABLED] !== null;
    }
}

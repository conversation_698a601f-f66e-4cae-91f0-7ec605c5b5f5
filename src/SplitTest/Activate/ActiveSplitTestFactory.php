<?php

declare(strict_types=1);

namespace App\SplitTest\Activate;

class ActiveSplitTestFactory
{
    public function create(int $splitTestId, ?string $splitTestVariant): ActiveSplitTest
    {
        return new ActiveSplitTest($splitTestId, $splitTestVariant);
    }

    /**
     * @param array<string, int|string|null> $data
     */
    public function createFromArray(array $data): ActiveSplitTest
    {
        $splitTestId = (int)$data[ActiveSplitTest::KEY_ID];
        $splitTestVariant = $data[ActiveSplitTest::KEY_VARIANT] ?? null;
        $splitTestVariant = $splitTestVariant !== null ? (string)$splitTestVariant : null;

        return $this->create($splitTestId, $splitTestVariant);
    }
}

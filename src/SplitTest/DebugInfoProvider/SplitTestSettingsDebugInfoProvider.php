<?php

declare(strict_types=1);

namespace App\SplitTest\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use App\SplitTest\Settings\SplitTestListSettingsRepository;

class SplitTestSettingsDebugInfoProvider implements DebugInfoProviderInterface
{
    private const string KEY_SPLIT_TEST_LIST_SETTINGS = 'split test list settings';

    public function __construct(
        private readonly SplitTestListSettingsRepository $splitTestListSettingsRepository
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        $splitTestListSettings = $this->splitTestListSettingsRepository->getSplitTestListSettings();

        if (!$splitTestListSettings->hasSplitTests()) {
            return [];
        }

        return [
            new DebugInfo(
                self::KEY_SPLIT_TEST_LIST_SETTINGS,
                [
                    $splitTestListSettings->toArray(),
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 30;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_SPLIT_TEST_LIST_SETTINGS,
        ];
    }
}

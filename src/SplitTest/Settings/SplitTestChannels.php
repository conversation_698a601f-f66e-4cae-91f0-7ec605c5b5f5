<?php

declare(strict_types=1);

namespace App\SplitTest\Settings;

final readonly class SplitTestChannels
{
    public const string KEY_ADVERTISED   = 'advertised';
    public const string KEY_LANDING_PAGE = 'landingpage';

    public function __construct(private string $advertised, private string $landingPage)
    {
    }

    /**
     * @param string[] $splitTestChannels
     */
    public static function createFromArray(array $splitTestChannels): self
    {
        return new self(
            $splitTestChannels[self::KEY_ADVERTISED],
            $splitTestChannels[self::KEY_LANDING_PAGE],
        );
    }

    public function getAdvertised(): string
    {
        return $this->advertised;
    }

    public function getLandingPage(): string
    {
        return $this->landingPage;
    }

    /**
     * @return string[]
     */
    public function toArray(): array
    {
        return [
            self::KEY_ADVERTISED   => $this->getAdvertised(),
            self::KEY_LANDING_PAGE => $this->getLandingPage(),
        ];
    }
}

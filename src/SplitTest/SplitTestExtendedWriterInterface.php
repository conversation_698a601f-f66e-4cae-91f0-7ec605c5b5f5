<?php

declare(strict_types=1);

namespace App\SplitTest;

use Visymo\Shared\Domain\SplitTest\SplitTestWriterInterface;

interface SplitTestExtendedWriterInterface extends SplitTestWriterInterface
{
    public function setChannel(?string $channel): void;

    public function setContainerSuffix(?string $containerSuffix): void;

    public function setId(?int $id): void;

    /**
     * @param string[] $variants
     */
    public function setContainsVariants(array $variants): void;

    /**
     * Reset the split test to an empty state
     */
    public function reset(): void;
}

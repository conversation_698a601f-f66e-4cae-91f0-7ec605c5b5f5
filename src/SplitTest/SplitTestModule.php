<?php

declare(strict_types=1);

namespace App\SplitTest;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\SplitTest\Activate\SplitTestActivationMatcherInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class SplitTestModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'split_test';
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $container->registerForAutoconfiguration(SplitTestActivationMatcherInterface::class)
            ->addTag('brand_website.split_test_activation_matcher');
    }
}

<?php

declare(strict_types=1);

namespace App\SplitTest\Twig;

use App\SplitTest\Helper\SplitTestAssetsHelper;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class SplitTestExtension extends AbstractExtension
{
    public function __construct(
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly SplitTestAssetsHelper $splitTestAssetsHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('split_test_variant_active', $this->splitTestExtendedReader->isVariantActive(...)),
            new TwigFunction(
                'split_test_style',
                $this->splitTestAssetsHelper->getSplitTestStyle(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'split_test_script',
                $this->splitTestAssetsHelper->getSplitTestJs(...),
                ['is_safe' => ['html']],
            ),
        ];
    }
}

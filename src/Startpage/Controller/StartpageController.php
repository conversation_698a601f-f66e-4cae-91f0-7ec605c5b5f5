<?php

declare(strict_types=1);

namespace App\Startpage\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use App\Startpage\Settings\StartpageSettings;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;

final class StartpageController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer,
        private readonly StartpageSettings $startpageSettings
    )
    {
    }

    public function home(): ?Response
    {
        if (!$this->startpageSettings->enabledForRequest) {
            return null;
        }

        return $this->jsonTemplateRenderer->render(
            '@themeJson/startpage/startpage.json',
        );
    }
}

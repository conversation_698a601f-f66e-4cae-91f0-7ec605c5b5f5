<?php

declare(strict_types=1);

namespace App\Startpage\Controller;

use App\JsonTemplate\Renderer\JsonTemplateRendererInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class StartpageFavoritesController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateRendererInterface $jsonTemplateRenderer
    )
    {
    }

    #[Route(
        path   : '/favo',
        name   : 'route_startpage_favorites',
        methods: ['GET', 'POST']
    )]
    #[Route(
        path   : '/favo/edit/{index}',
        name   : 'route_startpage_favorites_edit',
        methods: ['GET', 'POST']
    )]
    #[Route(
        path   : '/favo/delete/{index}',
        name   : 'route_startpage_favorites_delete',
        methods: ['GET']
    )]
    public function favorites(): Response
    {
        return $this->jsonTemplateRenderer->render(
            '@themeJson/startpage/startpage_favorites.json',
        );
    }
}

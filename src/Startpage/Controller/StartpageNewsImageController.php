<?php

declare(strict_types=1);

namespace App\Startpage\Controller;

use App\Startpage\News\Client\NewsImageClient;
use App\Startpage\News\Helper\ImageUrlSerializer;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class StartpageNewsImageController extends AbstractController
{
    public function __construct(
        private readonly NewsImageClient $newsImageClient,
        private readonly ImageUrlSerializer $imageUrlSerializer,
        private readonly LoggerInterface $logger
    )
    {
    }

    #[Route(
        path   : '/images/news/{serializeHashAndImageUrl}.{extension}',
        name   : 'route_startpage_news_image',
        methods: ['GET']
    )]
    public function image(string $serializeHashAndImageUrl): Response
    {
        try {
            $imageUrl = $this->imageUrlSerializer->deserialize($serializeHashAndImageUrl);
        } catch (\Exception $exception) {
            $this->logger->warning(
                sprintf('Caught %s while loading news image', $exception::class),
                [
                    'message'                       => $exception->getMessage(),
                    'exception'                     => $exception,
                    'serialized_hash_and_image_url' => $serializeHashAndImageUrl,
                ],
            );

            return new Response('', Response::HTTP_NOT_FOUND);
        }

        return $this->newsImageClient->requestNewsImage($imageUrl);
    }
}

<?php

declare(strict_types=1);

namespace App\Startpage\Controller;

use App\Generic\Response\UncachedPixelResponse;
use App\Startpage\KnmiWeather\Client\KnmiWeatherClient;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class StartpageWeatherImageController extends AbstractController
{
    public function __construct(
        private readonly KnmiWeatherClient $knmiWeatherClient,
        private readonly LoggerInterface $logger
    )
    {
    }

    #[Route(
        path   : '/images/weather/map',
        name   : 'route_startpage_weather_image',
        methods: ['GET'],
    )]
    public function image(): Response
    {
        try {
            return $this->knmiWeatherClient->requestWeatherImage();
        } catch (\Throwable $exception) {
            $this->logger->warning(
                sprintf('Caught %s while loading weather image', $exception::class),
                [
                    'message'   => $exception->getMessage(),
                    'exception' => $exception,
                ],
            );

            return new UncachedPixelResponse();
        }
    }
}

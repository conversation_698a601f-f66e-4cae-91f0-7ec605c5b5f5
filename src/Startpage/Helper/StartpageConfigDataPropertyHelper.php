<?php

declare(strict_types=1);

namespace App\Startpage\Helper;

class StartpageConfigDataPropertyHelper
{
    public function getBoolValue(mixed $value): bool
    {
        if (is_bool($value)) {
            return $value;
        }

        if (is_numeric($value)) {
            return (bool)$value;
        }

        return false;
    }

    public function getNullableStringValue(string $value): ?string
    {
        return $value !== '' ? $value : null;
    }

    public function getNullableIntValue(mixed $value): ?int
    {
        return $value !== '' && is_numeric($value) ? (int)$value : null;
    }
}

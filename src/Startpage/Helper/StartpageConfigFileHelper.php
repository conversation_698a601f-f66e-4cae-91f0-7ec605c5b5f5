<?php

declare(strict_types=1);

namespace App\Startpage\Helper;

use Psr\Log\LoggerInterface;
use Visymo\Filesystem\File\FileInterface;

class StartpageConfigFileHelper
{
    private const string KEY_COLUMNS = 'columns';

    /** @var mixed[]|null */
    private ?array $config = null;

    public function __construct(
        private readonly FileInterface $configFile,
        private readonly LoggerInterface $logger
    )
    {
    }

    /**
     * @return mixed[]
     */
    private function getConfig(): array
    {
        if ($this->config === null) {
            try {
                $this->config = json_decode($this->configFile->readContent(), true, 512, JSON_THROW_ON_ERROR) ?? [];
            } catch (\Throwable $exception) {
                $this->logger->error(
                    sprintf('Caught %s while loading brand startpage config.', $exception::class),
                    [
                        'message'   => $exception->getMessage(),
                        'exception' => $exception,
                    ],
                );

                $this->config = [];
            }
        }

        return $this->config;
    }

    /**
     * @return mixed[]
     */
    public function getColumns(): array
    {
        return $this->getConfig()[self::KEY_COLUMNS] ?? [];
    }
}

<?php

declare(strict_types=1);

namespace App\Startpage\Helper;

use App\Startpage\Model\Block;
use App\Startpage\Model\Column;
use App\Startpage\Model\ColumnFactory;

class StartpageHelper
{
    /** @var Column[] */
    private array $columns;

    public function __construct(
        private readonly StartpageConfigFileHelper $startpageConfigFileHelper,
        private readonly ColumnFactory $columnFactory
    )
    {
    }

    /**
     * @return Column[]
     */
    public function getColumns(): array
    {
        if (!isset($this->columns)) {
            $this->columns = [];

            $columnsData = $this->startpageConfigFileHelper->getColumns();

            foreach ($columnsData as $columnData) {
                $this->columns[] = $this->columnFactory->createFromArray($columnData);
            }
        }

        return $this->columns;
    }

    /**
     * Get all blocks used in columns sorted alphabetically
     *
     * @return Block[]
     */
    public function getSortedBlocks(): array
    {
        $blocks = [];

        foreach ($this->getColumns() as $column) {
            foreach ($column->getBlocks() as $block) {
                $blocks[] = $block;
            }
        }

        usort(
            $blocks,
            static fn (Block $blockA, Block $blockB) => strcmp($blockA->getTitle(), $blockB->getTitle()),
        );

        return $blocks;
    }
}

<?php

declare(strict_types=1);

namespace App\Startpage\KnmiWeather\Client;

use App\Http\Response\ResponseCachingHelper;
use App\Startpage\KnmiWeather\Exception\KnmiWeatherImageException;
use App\Startpage\KnmiWeather\Exception\KnmiWeatherInfoException;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;
use Symfony\Component\HttpFoundation\Response;

class KnmiWeatherClient
{
    private const string DATA_URL  = 'https://cdn.knmi.nl/knmi/xml/rss/rss_KNMIverwachtingen.xml';
    private const string IMAGE_URL = 'https://cdn.knmi.nl/knmi/map/general/weather-map.gif';

    private const int TIME_TO_LIVE_IN_SECONDS = 300;

    public function __construct(
        private readonly RequestFactoryInterface $httpRequestFactory,
        private readonly ClientInterface $httpClient,
        private readonly ResponseCachingHelper $responseCachingHelper
    )
    {
    }

    /**
     * @throws KnmiWeatherInfoException
     */
    public function requestWeatherInfo(): KnmiWeatherResponse
    {
        try {
            $httpRequest = $this->httpRequestFactory->createRequest('GET', self::DATA_URL)
                ->withAddedHeader('Accept-Encoding', 'gzip');

            $httpResponse = $this->httpClient->sendRequest($httpRequest);
            $httpStatusCode = $httpResponse->getStatusCode();

            if ($httpStatusCode !== 200) {
                throw new \RuntimeException(
                    sprintf('Received HTTP status code %s from KNMI Weather API', $httpStatusCode),
                );
            }

            $httpResponseBody = $httpResponse->getBody()->getContents();
            $xmlDocument = new \SimpleXMLElement($httpResponseBody);

            if (!isset($xmlDocument->channel->item->title)) {
                throw new \RuntimeException('expected "title" not available in KNMI Weather API response');
            }

            $weatherText = strip_tags((string)$xmlDocument->channel->item->title);

            return new KnmiWeatherResponse($weatherText);
        } catch (\Throwable $exception) {
            throw KnmiWeatherInfoException::create($exception);
        }
    }

    /**
     * @throws KnmiWeatherImageException
     */
    public function requestWeatherImage(): Response
    {
        $imageUrl = self::IMAGE_URL;

        try {
            $httpRequest = $this->httpRequestFactory->createRequest('GET', $imageUrl);
            $httpResponse = $this->httpClient->sendRequest($httpRequest);
            $httpStatusCode = $httpResponse->getStatusCode();

            if ($httpStatusCode !== Response::HTTP_OK) {
                throw new \RuntimeException(
                    sprintf('Received HTTP status code %s while downloading weather image', $httpStatusCode),
                );
            }

            $httpResponseBody = $httpResponse->getBody()->getContents();
            $contentType = $httpResponse->getHeader('Content-Type');

            $response = new Response($httpResponseBody, Response::HTTP_OK);
            $response->headers->set('Content-Type', $contentType);

            $this->responseCachingHelper->startResponseCaching(self::TIME_TO_LIVE_IN_SECONDS);

            return $response;
        } catch (\Throwable $exception) {
            throw KnmiWeatherImageException::create($imageUrl, $exception);
        }
    }
}

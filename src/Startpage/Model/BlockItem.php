<?php

declare(strict_types=1);

namespace App\Startpage\Model;

class BlockItem
{
    /** @var BlockItemLink[] */
    private array $links = [];

    public function addLink(BlockItemLink $blockItemLink): void
    {
        $this->links[] = $blockItemLink;
    }

    /**
     * @return BlockItemLink[]
     */
    public function getLinks(): array
    {
        return $this->links;
    }

    public function hasLinks(): bool
    {
        return $this->links !== [];
    }
}

<?php

declare(strict_types=1);

namespace App\Startpage\Model;

class BlockItemLinkFactory
{
    public function createByBlockAndBlockContent(Block $block, BlockContent $blockContent): BlockItemLink
    {
        return new BlockItemLink(
            $blockContent->getUrlVisible() ?? $blockContent->getUrl(),
            $blockContent->getTitle(),
            $blockContent->getTarget(),
            $blockContent->isNoFollow(),
            $blockContent->isNew(),
            $blockContent->isTip(),
            $blockContent->isInternalLink(),
            $blockContent->isExtra() ? $blockContent->getExtraText() : null,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Startpage\Model;

class Column
{
    public const string KEY_ID    = 'id';
    public const string KEY_BLOCK = 'block';

    /** @var Block[] */
    private array $blocks = [];

    /**
     * @param Block[] $blocks
     */
    public function __construct(private readonly int $id, array $blocks)
    {
        foreach ($blocks as $block) {
            $this->addBlock($block);
        }
    }

    private function addBlock(Block $block): void
    {
        $this->blocks[] = $block;
    }

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return Block[]
     */
    public function getBlocks(): array
    {
        return $this->blocks;
    }
}

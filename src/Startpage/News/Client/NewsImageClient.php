<?php

declare(strict_types=1);

namespace App\Startpage\News\Client;

use App\Http\Response\ResponseCachingHelper;
use App\Startpage\News\Exception\NewsImageException;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;
use Symfony\Component\HttpFoundation\Response;

class NewsImageClient
{
    private const int TIME_TO_LIVE_IN_SECONDS = 3600;

    public function __construct(
        private readonly ClientInterface $httpClient,
        private readonly RequestFactoryInterface $httpRequestFactory,
        private readonly ResponseCachingHelper $responseCachingHelper
    )
    {
    }

    public function requestNewsImage(string $imageUrl): Response
    {
        try {
            $httpRequest = $this->httpRequestFactory->createRequest('GET', $imageUrl)
                ->withAddedHeader('Accept-Encoding', 'gzip');

            $httpResponse = $this->httpClient->sendRequest($httpRequest);
            $httpStatusCode = $httpResponse->getStatusCode();

            if ($httpStatusCode !== Response::HTTP_OK) {
                throw new \RuntimeException(
                    sprintf('Received HTTP status code %s for %s', $httpStatusCode, $imageUrl),
                );
            }

            $httpResponseBody = $httpResponse->getBody()->getContents();

            $response = new Response($httpResponseBody, Response::HTTP_OK);
            $response->headers->set('Content-Type', $httpResponse->getHeader('Content-Type'));

            $this->responseCachingHelper->startResponseCaching(self::TIME_TO_LIVE_IN_SECONDS);

            return $response;
        } catch (\Exception $exception) {
            throw NewsImageException::create($imageUrl, $exception);
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Startpage\News\Helper;

use App\Startpage\News\Exception\NewsRubricException;
use App\Startpage\News\Model\News;
use App\Startpage\News\Model\Rubric;
use App\Startpage\News\Repository\RubricRepositoryInterface;
use Psr\Log\LoggerInterface;

class NewsHelper
{
    public const array NEWS_RUBRICS = [
        Rubric::GENERAL_NAME,
        Rubric::NATIONAL_NAME,
        Rubric::INTERNATIONAL_NAME,
        Rubric::SPORT_NAME,
        Rubric::ECONOMICS_NAME,
        Rubric::TECH_NAME,
        Rubric::ENTERTAINMENT_NAME,
    ];

    public function __construct(
        private readonly RubricRepositoryInterface $rubricRepository,
        private readonly LoggerInterface $logger
    )
    {
    }

    public function getNews(): News
    {
        try {
            return new News(
                array_filter(
                    array_map(
                        fn (string $rubricName): ?Rubric => $this->rubricRepository->findByName($rubricName),
                        self::NEWS_RUBRICS,
                    ),
                    static fn (?Rubric $rubric): bool => $rubric !== null,
                ),
            );
        } catch (\Throwable $previousException) {
            // Wrap all exceptions in NewsRubricException
            if (!$previousException instanceof NewsRubricException) {
                $exception = NewsRubricException::create(
                    message : $previousException->getMessage(),
                    previous: $previousException,
                );
            } else {
                $exception = $previousException;
            }

            $this->logger->error(
                sprintf('Caught %s while loading news', $exception::class),
                [
                    'message'   => $exception->getMessage(),
                    'exception' => $exception,
                ],
            );

            return new News([]);
        }
    }
}

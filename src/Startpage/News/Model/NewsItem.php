<?php

declare(strict_types=1);

namespace App\Startpage\News\Model;

class NewsItem
{
    public const int MAX_LENGTH_TITLE = 55;

    public function __construct(
        private readonly string $title,
        private readonly string $link,
        private readonly string $imageUrl,
        private readonly \DateTimeInterface $publicationDateTime
    )
    {
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getShortTitle(): string
    {
        $title = $this->title;

        if (strlen($title) > self::MAX_LENGTH_TITLE) {
            $title = sprintf('%s...', mb_substr($title, 0, self::MAX_LENGTH_TITLE - 3));
        }

        return $title;
    }

    public function getLink(): string
    {
        return $this->link;
    }

    public function getImageUrl(): string
    {
        return $this->imageUrl;
    }

    public function getImageExtension(): string
    {
        $urlInfo = explode('.', $this->imageUrl);

        if (count($urlInfo) <= 1) {
            throw new \InvalidArgumentException('Invalid url for image detected.');
        }

        return end($urlInfo);
    }

    public function getPublicationDateTime(): \DateTimeInterface
    {
        return $this->publicationDateTime;
    }
}

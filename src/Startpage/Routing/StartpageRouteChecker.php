<?php

declare(strict_types=1);

namespace App\Startpage\Routing;

use App\Generic\Routing\RouteCheckerInterface;
use App\Startpage\Settings\StartpageSettings;
use Symfony\Bundle\FrameworkBundle\Routing\Attribute\AsRoutingConditionService;

#[AsRoutingConditionService(alias: self::ALIAS)]
final readonly class StartpageRouteChecker implements RouteCheckerInterface
{
    private const string ALIAS = 'route_checker_startpage';

    public function __construct(
        private StartpageSettings $startpageSettings
    )
    {
    }

    public static function getAlias(): string
    {
        return self::ALIAS;
    }

    public function check(): bool
    {
        return $this->startpageSettings->enabledForRequest;
    }
}

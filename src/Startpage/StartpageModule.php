<?php

declare(strict_types=1);

namespace App\Startpage;

use App\BrandOverride\BrandOverrideModuleInterface;
use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Generic\Routing\RoutesLoader;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class StartpageModule extends AbstractDependencyInjectionModule implements BrandOverrideModuleInterface
{
    public const string KEY_HOSTS = 'hosts';

    public static function getModuleName(): string
    {
        return 'startpage';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->addDefaultsIfNotSet()
                ->children();

        $moduleNodeChildren
            ->booleanNode(self::KEY_ENABLED)
                ->info('Enable the startpage module')
                ->defaultNull();

        $moduleNodeChildren->arrayNode(self::KEY_HOSTS)
            ->info('Exact hostnames for which the startpage should be made available')
                ->isRequired()
                ->scalarPrototype();
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $moduleConfig = $this->getModuleConfig($config);
        $container->setParameter(
            self::getModuleParameterConfigName(),
            $moduleConfig,
        );

        $routesLoaderDefinition = $container->getDefinition(RoutesLoader::class);
        $routesLoaderDefinition->addMethodCall('addRoutesImport', ['routes_startpage.yaml']);
    }

    /**
     * @inheritDoc
     */
    public function isModuleConfigDefined(array $moduleConfig): bool
    {
        return $moduleConfig[self::KEY_ENABLED] !== null;
    }
}

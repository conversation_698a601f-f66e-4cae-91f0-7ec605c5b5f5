<?php

declare(strict_types=1);

namespace App\Startpage\Twig;

use App\Startpage\Helper\StartpageBlockHelper;
use App\Startpage\Helper\StartpageHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class StartPageExtension extends AbstractExtension
{
    public function __construct(
        private readonly StartpageHelper $startpageHelper,
        private readonly StartpageBlockHelper $startpageBlockHelper
    )
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('startpage_helper', $this->getStartPageHelper(...), ['is_safe' => ['html']]),
            new TwigFunction('startpage_block_helper', $this->getStartPageBlockHelper(...), ['is_safe' => ['html']]),
        ];
    }

    public function getStartPageHelper(): StartpageHelper
    {
        return $this->startpageHelper;
    }

    public function getStartPageBlockHelper(): StartpageBlockHelper
    {
        return $this->startpageBlockHelper;
    }
}

<?php

declare(strict_types=1);

namespace App\Statistics\Controller;

use App\Http\Request\NdJson\NdJsonRequestInterface;
use App\Statistics\Helper\StatisticsLogEnabledHelper;
use App\Statistics\Helper\StatisticsLogHelper;
use App\Statistics\Provider\AbstractStatisticsResolver;
use App\Statistics\Provider\StatisticsProviderManager;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

final class StatisticsLogController extends AbstractController
{
    public function __construct(
        private readonly StatisticsLogEnabledHelper $statisticsLogEnabledHelper,
        private readonly StatisticsProviderManager $statisticsProviderManager,
        private readonly StatisticsLogHelper $statisticsLogHelper,
        private readonly NdJsonRequestInterface $ndJsonRequest,
        private readonly LoggerInterface $logger
    )
    {
    }

    /**
     * Universal statistics endpoint to update existing statistics entry with additional data
     * Multiple data entries are supported through NDJSON (Newline Delimited JSON)
     */
    #[Route(path: '/s/u', name: 'route_statistics_update', methods: ['POST'])]
    public function update(): JsonResponse
    {
        if (!$this->statisticsLogEnabledHelper->isLogUpdateEnabled()) {
            return new JsonResponse('');
        }

        $statisticsLog = [];
        $payloads = $this->ndJsonRequest->getJsonData();

        foreach ($payloads as $payload) {
            try {
                $key = $payload[AbstractStatisticsResolver::PAYLOAD_KEY_KEY] ?? null;

                if ($key === null) {
                    throw new \RuntimeException(
                        sprintf('Invalid JSON payload without key %s', AbstractStatisticsResolver::PAYLOAD_KEY_KEY),
                    );
                }

                $statisticsProvider = $this->statisticsProviderManager->getStatisticsProvider((string)$key);
                $statistics = $statisticsProvider->getFromPayload($payload);

                $statisticsLog = array_replace_recursive($statisticsLog, $statistics);
            } catch (\Throwable $exception) {
                $this->logger->notice(
                    sprintf('Caught %s while updating statistics', $exception::class),
                    [
                        'exception' => $exception,
                        'message'   => $exception->getMessage(),
                        'payload'   => $payload,
                    ],
                );
            }
        }

        $this->statisticsLogHelper->updateStatisticsLog($statisticsLog);

        return new JsonResponse('');
    }
}

<?php

declare(strict_types=1);

namespace App\Statistics\EventSubscriber;

use App\Kernel\KernelResponseEvent;
use App\Statistics\Helper\StatisticsLogEnabledHelper;
use App\Statistics\Helper\StatisticsLogHelper;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

readonly class CreateStatisticsLogEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private StatisticsLogEnabledHelper $statisticsLogEnabledHelper,
        private StatisticsLogHelper $statisticsLogHelper,
        private EventDispatcherInterface $eventDispatcher
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelResponseEvent::NO_REDIRECT->value => ['onKernelResponse'],
        ];
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        // This is needed to prevent this event from being re-fired when an error occurs and a different controller is used
        if (!$event->isMainRequest()) {
            return;
        }

        if (!$this->statisticsLogEnabledHelper->isLogCreateEnabled()) {
            return;
        }

        $statisticsLogEvent = new StatisticsLogCreateEvent();
        $this->eventDispatcher->dispatch($statisticsLogEvent, StatisticsLogCreateEvent::NAME);
        $this->statisticsLogHelper->createStatisticsLog($statisticsLogEvent->getStatistics());
    }
}

<?php

declare(strict_types=1);

namespace App\Statistics\Provider;

use App\Http\Request\GenericRequestInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedPatternValidator;

abstract class AbstractStatisticsResolver implements StatisticsResolverInterface
{
    public const string  STATISTICS_KEY_PAGEVIEW_ID        = 'pageview_id';
    private const string STATISTICS_KEY_RESPONSE_TIMESTAMP = 'response_timestamp';
    public const string  STATISTICS_KEY_VISIT_ID           = 'visit_id';

    public const  string PAYLOAD_KEY_KEY         = 'key';
    private const string PAYLOAD_KEY_PAGEVIEW_ID = 'pvid';

    private const string REGEX_KEY         = '~^[a-z]+$~';
    private const string REGEX_UUID_RANDOM = '~^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}$~';

    private const array REQUIRED_STATISTICS_MAPPING = [
        self::STATISTICS_KEY_PAGEVIEW_ID => self::PAYLOAD_KEY_PAGEVIEW_ID,
    ];

    public function __construct(
        protected readonly OptionsResolverInterface $optionsResolver,
        private readonly GenericRequestInterface $genericRequest
    )
    {
        $this->optionsResolver->ignoreUndefinedOptions(true);
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options): array
    {
        $this->defineRequiredOptions();
        $this->defineOptions();

        $resolved = $this->optionsResolver->resolve($options);

        $mapping = [
            ...self::REQUIRED_STATISTICS_MAPPING,
            ...$this->getStatisticsMapping(),
        ];

        $mappedProperties = $this->getMappedProperties($mapping, $resolved);
        $mappedProperties[self::STATISTICS_KEY_RESPONSE_TIMESTAMP] = date('c');

        return [
            self::STATISTICS_KEY_VISIT_ID => $this->genericRequest->getVisitId(),
            ...$mappedProperties,
        ];
    }

    /**
     * @param array<string, string|array{source_key: string, mapping: array<string, string>}> $mapping
     * @param mixed[]                                                                         $array
     *
     * @return mixed[]
     */
    private function getMappedProperties(array $mapping, array $array): array
    {
        $mappedArray = [];

        foreach ($mapping as $targetKey => $sourceKey) {
            if (is_array($sourceKey)) {
                $sourceKeyData = $array[$sourceKey['source_key']];

                if ($sourceKeyData === []) {
                    $mappedArray[$targetKey] = [];

                    continue;
                }

                $firstKey = array_key_first($sourceKeyData);

                if (is_array($sourceKeyData[$firstKey])) {
                    $mappedArray[$targetKey] = array_map(
                        fn ($value) => $this->getMappedProperties(
                            $sourceKey['mapping'],
                            $value,
                        ),
                        $sourceKeyData,
                    );

                    continue;
                }

                $mappedArray[$targetKey] = $this->getMappedProperties(
                    $sourceKey['mapping'],
                    $sourceKeyData,
                );
            } else {
                if (!array_key_exists($sourceKey, $array)) {
                    continue;
                }

                $mappedArray[$targetKey] = $array[$sourceKey];
            }
        }

        return $mappedArray;
    }

    private function defineRequiredOptions(): void
    {
        $this->optionsResolver->define(self::PAYLOAD_KEY_KEY)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_STRING)
            ->addValidator(new AllowedPatternValidator(self::REGEX_KEY));

        $this->optionsResolver->define(self::PAYLOAD_KEY_PAGEVIEW_ID)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_STRING)
            ->addValidator(new AllowedPatternValidator(self::REGEX_UUID_RANDOM));
    }

    abstract protected function defineOptions(): void;

    /**
     * @return array<string, string|array{source_key: string, mapping: array<string, string>}>
     */
    abstract protected function getStatisticsMapping(): array;
}

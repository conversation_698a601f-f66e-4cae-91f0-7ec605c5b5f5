<?php

declare(strict_types=1);

namespace App\Statistics\Provider\Event;

class StatisticsLogCreateEvent
{
    public const string NAME = 'statistics.statistics_log_create';

    /**
     * @param array<string, mixed> $statistics
     */
    public function __construct(
        public array $statistics = []
    )
    {
    }

    /**
     * @param array<string, mixed> $statistics
     */
    public function addStatistics(array $statistics): void
    {
        $this->statistics = [...$this->statistics, ...$statistics];
    }

    /**
     * @return array<string, mixed>
     */
    public function getStatistics(): array
    {
        return $this->statistics;
    }
}

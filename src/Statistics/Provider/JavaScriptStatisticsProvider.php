<?php

declare(strict_types=1);

namespace App\Statistics\Provider;

class JavaScriptStatisticsProvider extends AbstractStatisticsProvider
{
    public function __construct(
        JavaScriptStatisticsResolver $javaScriptStatisticsResolver
    )
    {
        parent::__construct(
            $javaScriptStatisticsResolver,
        );
    }

    public static function getContextKey(): string
    {
        return 'javascript';
    }

    public static function getPayloadKey(): string
    {
        return 'j';
    }
}

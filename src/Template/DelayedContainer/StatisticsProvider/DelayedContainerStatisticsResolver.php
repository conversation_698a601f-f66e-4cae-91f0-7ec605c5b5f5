<?php

declare(strict_types=1);

namespace App\Template\DelayedContainer\StatisticsProvider;

use App\Statistics\Provider\AbstractStatisticsResolver;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;

class DelayedContainerStatisticsResolver extends AbstractStatisticsResolver
{
    private const string STATISTICS_KEY_FIRST_SHOWN = 'first_shown';

    private const string PAYLOAD_KEY_FIRST_SHOWN = 'fs';

    private const string FIRST_SHOWN_ADS             = 'ads';
    private const string FIRST_SHOWN_DELAYED_CONTENT = 'delayed_content';
    private const string FIRST_SHOWN_RELATED_TERMS   = 'related_terms';

    private const array FIRST_SHOWN_VALUES = [
        self::FIRST_SHOWN_ADS,
        self::FIRST_SHOWN_DELAYED_CONTENT,
        self::FIRST_SHOWN_RELATED_TERMS,
    ];

    protected function defineOptions(): void
    {
        $this->optionsResolver->define(self::PAYLOAD_KEY_FIRST_SHOWN)
            ->setRequired()
            ->setAllowedType(OptionType::TYPE_STRING)
            ->addValidator(new AllowedValueValidator(self::FIRST_SHOWN_VALUES));
    }

    /**
     * @inheritDoc
     */
    protected function getStatisticsMapping(): array
    {
        return [
            self::STATISTICS_KEY_FIRST_SHOWN => self::PAYLOAD_KEY_FIRST_SHOWN,
        ];
    }
}

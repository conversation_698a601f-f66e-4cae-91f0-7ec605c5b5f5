<?php

declare(strict_types=1);

namespace App\Template\Twig;

use App\Template\DelayedContainer\DelayedContainerHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class DelayedContainerExtension extends AbstractExtension
{
    private static int $containerId = 0;

    public function __construct(private readonly DelayedContainerHelper $delayedContainerHelper)
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'delayed_container_attributes',
                $this->renderDelayedContainerAttributes(...),
                [
                    'pre_escape' => 'html',
                    'is_safe'    => ['html'],
                ],
            ),
        ];
    }

    public function renderDelayedContainerAttributes(): string
    {
        if (!$this->delayedContainerHelper->isDelayedContainerRequired()) {
            return '';
        }

        self::$containerId++;

        return sprintf(' id="delayed-container-%d" style="visibility: hidden;"', self::$containerId);
    }
}

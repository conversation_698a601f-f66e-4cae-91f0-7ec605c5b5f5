<?php

declare(strict_types=1);

namespace App\Template\Twig;

use League\CommonMark\ConverterInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class MarkdownExtension extends AbstractExtension
{
    public function __construct(private readonly ConverterInterface $markdownConverter)
    {
    }

    /**
     * @return TwigFilter[]
     */
    public function getFilters(): array
    {
        return [
            new TwigFilter(
                'markdown_to_html',
                $this->convertMarkdownToHtml(...),
                ['is_safe' => ['all']],
            ),
        ];
    }

    public function convertMarkdownToHtml(string $content): string
    {
        return $this->markdownConverter->convert($content)->getContent();
    }
}

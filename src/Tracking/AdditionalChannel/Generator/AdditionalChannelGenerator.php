<?php

declare(strict_types=1);

namespace App\Tracking\AdditionalChannel\Generator;

use Visymo\Shared\Domain\Generator\SeededMultipleRandomIntegersGenerator;

readonly class AdditionalChannelGenerator
{
    public function __construct(
        private SeededMultipleRandomIntegersGenerator $seededMultipleRandomIntegersGenerator
    )
    {
    }

    /**
     * @return string[]
     */
    public function generate(
        string $seed,
        string $additionalChannelPrefix,
        int $channelNumberMin,
        int $channelNumberMax,
        int $numberOfChannels
    ): array
    {
        $numbers = $this->seededMultipleRandomIntegersGenerator->generateUniqueRandomIntegers(
            $channelNumberMin,
            $channelNumberMax,
            $numberOfChannels,
            $seed,
        );

        return array_map(static fn (int $number) => sprintf('%s%u', $additionalChannelPrefix, $number), $numbers);
    }
}

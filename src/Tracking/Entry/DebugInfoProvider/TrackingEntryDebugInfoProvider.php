<?php

declare(strict_types=1);

namespace App\Tracking\Entry\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use App\Tracking\Entry\Serializer\TrackingEntryArraySerializer;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;

class TrackingEntryDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_TRACKING_ENTRY = 'tracking entry';

    public function __construct(
        private readonly ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private readonly TrackingEntryArraySerializer $trackingEntryArraySerializer
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        return [
            new DebugInfo(
                self::KEY_TRACKING_ENTRY,
                [
                    $this->trackingEntryArraySerializer->serialize(
                        $this->activeTrackingEntryHelper->getActiveTrackingEntry(),
                    ),
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 20;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_TRACKING_ENTRY,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Exception;

use App\Tracking\Entry\Factory\TrackingEntryFactoryInterface;
use Visymo\Shared\Domain\Exception\ExceptionWithContextInterface;

class TrackingEntryNotCreatedException extends TrackingEntryException implements ExceptionWithContextInterface
{
    private TrackingEntryFactoryInterface $trackingEntryFactory;

    public static function create(
        TrackingEntryFactoryInterface $trackingEntryFactory,
        string $reason,
        ?\Throwable $previous = null
    ): self
    {
        $instance = new self(
            sprintf('Could not create tracking entry. Reason: %s', $reason),
            0,
            $previous,
        );
        $instance->trackingEntryFactory = $trackingEntryFactory;

        return $instance;
    }

    /**
     * @inheritDoc
     */
    public function getContext(): array
    {
        return [
            'tracking_entry_factory' => $this->trackingEntryFactory::class,
        ];
    }
}

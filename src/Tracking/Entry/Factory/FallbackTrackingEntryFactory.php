<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Factory;

use App\Tracking\Entry\Parameter\DeviceParameter;
use App\Tracking\Entry\Parameter\TrafficSourceParameter;
use App\Tracking\Entry\TrackingEntry;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class FallbackTrackingEntryFactory implements TrackingEntryFactoryInterface
{
    public function __construct(
        protected DeviceParameter $deviceParameter,
        protected DateTimeFactory $dateTimeFactory,
        protected TrafficSourceParameter $trafficSourceParameter
    )
    {
    }

    public function create(): TrackingEntry
    {
        return new TrackingEntry(
            isEmpty                : true,
            query                  : null,
            campaignId             : null,
            campaignName           : null,
            device                 : $this->deviceParameter->getDevice(),
            network                : null,
            accountId              : null,
            adGroupId              : null,
            keywordId              : null,
            trafficSource          : $this->trafficSourceParameter->getTrafficSource(),
            clickId                : null,
            googleLocationId       : null,
            genericSecondaryClickId: null,
            publisher              : null,
            additionalChannels     : [],
            activeSplitTest        : null,
            createdAt              : $this->dateTimeFactory->createNow(),
            originRoute            : null,
            styleId                : null,
            customId               : null,
            conversionRoute        : null,
        );
    }
}

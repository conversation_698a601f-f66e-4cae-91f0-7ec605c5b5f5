<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Mutator;

use App\Tracking\Entry\TrackingEntry;
use App\Tracking\Validator\CampaignNameValidator;

final readonly class TrackingEntryCampaignMutator implements TrackingEntryMutatorInterface
{
    public function __construct(
        private CampaignNameValidator $campaignNameValidator
    )
    {
    }

    public function mutate(TrackingEntry $trackingEntry): TrackingEntry
    {
        $isCampaignNameValid = $this->campaignNameValidator->isValid(
            $trackingEntry->campaignName,
            $trackingEntry->accountId,
        );

        if ($isCampaignNameValid) {
            return $trackingEntry;
        }

        return $trackingEntry->withoutCampaignName();
    }
}

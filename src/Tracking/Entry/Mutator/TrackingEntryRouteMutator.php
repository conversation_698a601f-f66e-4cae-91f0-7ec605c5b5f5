<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Mutator;

use App\Http\Request\Info\RequestInfoInterface;
use App\Tracking\Entry\Request\TrackingEntryRequestInterface;
use App\Tracking\Entry\TrackingEntry;

final readonly class TrackingEntryRouteMutator implements TrackingEntryMutatorInterface
{
    public function __construct(
        private RequestInfoInterface $requestInfo,
        private TrackingEntryRequestInterface $trackingEntryRequest
    )
    {
    }

    public function mutate(TrackingEntry $trackingEntry): TrackingEntry
    {
        if (!$this->trackingEntryRequest->hasUseAsConversionRouteFlag()) {
            return $trackingEntry;
        }

        $currentRoute = $this->requestInfo->getRoute();

        if ($currentRoute === '' || $currentRoute === $trackingEntry->conversionRoute) {
            return $trackingEntry;
        }

        return $trackingEntry->withConversionRoute($currentRoute);
    }
}

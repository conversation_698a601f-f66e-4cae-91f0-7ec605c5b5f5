<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Parameter;

use App\Generic\Device\Detection\AdBotDeviceDetection;
use App\Generic\Device\Detection\MobileDeviceDetection;
use App\Generic\Device\Device;
use App\Tracking\Request\SeaRequestInterface;

final class DeviceParameter
{
    private Device $device;

    public function __construct(
        private readonly SeaRequestInterface $seaRequest,
        private readonly AdBotDeviceDetection $adBotDeviceDetection,
        private readonly MobileDeviceDetection $mobileDeviceDetection
    )
    {
    }

    public function getDevice(): Device
    {
        if (!isset($this->device)) {
            $this->device = $this->seaRequest->getDevice()
                            ?? $this->adBotDeviceDetection->getDevice()
                               ?? $this->mobileDeviceDetection->getDevice()
                                  ?? Device::DESKTOP;
        }

        return $this->device;
    }
}

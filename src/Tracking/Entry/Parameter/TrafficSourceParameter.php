<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Parameter;

use App\Account\Request\AccountRequestInterface;
use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettingsRepository;
use App\AdBot\Bot\AdBot;
use App\AdBot\Request\AdBotRequestInterface;
use App\Tracking\Model\TrafficSource;
use App\Tracking\Request\SeaRequestInterface;

final readonly class TrafficSourceParameter
{
    public function __construct(
        private AdBotRequestInterface $adBotRequest,
        private SeaRequestInterface $seaRequest,
        private AccountRequestInterface $accountRequest,
        private AccountSettingsRepository $accountSettingsRepository
    )
    {
    }

    public function getTrafficSource(): ?TrafficSource
    {
        return $this->getFromAdBotTraffic()
               ?? $this->getFromService()
                  ?? $this->getFromClickId();
    }

    private function getFromAdBotTraffic(): ?TrafficSource
    {
        return match ($this->adBotRequest->getAdBot()) {
            AdBot::GOOGLE    => TrafficSource::GOOGLE,
            AdBot::MICROSOFT => TrafficSource::MICROSOFT,
            default          => null
        };
    }

    private function getFromService(): ?TrafficSource
    {
        $accountId = $this->accountRequest->getAccountId();

        // Account settings are requested from source to prevent a circular dependency with WebsiteSettings
        $accountSettings = $accountId === null ? null : $this->accountSettingsRepository->getByAccountId($accountId);

        return match ($accountSettings?->service) {
            AccountService::GOOGLE_ADS            => TrafficSource::GOOGLE,
            AccountService::MEDIAGO               => TrafficSource::MEDIAGO,
            AccountService::MGID                  => TrafficSource::MGID,
            AccountService::MICROSOFT_ADVERTISING => TrafficSource::MICROSOFT,
            AccountService::TABOOLA               => TrafficSource::TABOOLA,
            AccountService::TIKTOK                => TrafficSource::TIKTOK,
            AccountService::ZEMANTA               => TrafficSource::ZEMANTA,
            default                               => null
        };
    }

    private function getFromClickId(): ?TrafficSource
    {
        return $this->seaRequest->getClickId()?->getTrafficSource();
    }
}

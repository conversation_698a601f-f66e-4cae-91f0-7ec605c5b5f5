<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Request;

use App\Http\Request\RequestInterface;

interface TrackingEntryRequestInterface extends RequestInterface
{
    public const string PARAMETER_SERIALIZED_TRACKING_ENTRY = 'ste';

    public function getSerializedTrackingEntry(): ?string;

    public function hasUseRacAsQueryFallbackFlag(): bool;

    public function hasUseAsConversionRouteFlag(): bool;
}

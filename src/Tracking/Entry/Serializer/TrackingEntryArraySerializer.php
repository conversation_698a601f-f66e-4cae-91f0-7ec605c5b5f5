<?php

declare(strict_types=1);

namespace App\Tracking\Entry\Serializer;

use App\Generic\Device\Device;
use App\Search\Request\SearchRequestInterface;
use App\SplitTest\Activate\ActiveSplitTestFactory;
use App\Tracking\Entry\Exception\TrackingEntryDeserializationFailedException;
use App\Tracking\Entry\Resolver\TrackingEntryArrayResolver;
use App\Tracking\Entry\TrackingEntry;
use App\Tracking\Model\ClickId\Factory\ClickIdFactory;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficSource;
use App\Tracking\Request\SeaRequestInterface;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;

final class TrackingEntryArraySerializer
{
    public const string KEY_QUERY                      = SearchRequestInterface::PARAMETER_QUERY;
    public const string KEY_CAMPAIGN_NAME              = SeaRequestInterface::PARAMETER_CAMPAIGN_NAME;
    public const string KEY_CAMPAIGN_ID                = SeaRequestInterface::PARAMETER_CAMPAIGN_ID;
    public const string KEY_DEVICE                     = SeaRequestInterface::PARAMETER_DEVICE;
    public const string KEY_NETWORK                    = SeaRequestInterface::PARAMETER_NETWORK;
    public const string KEY_ACCOUNT_ID                 = SeaRequestInterface::PARAMETER_ACCOUNT_ID;
    public const string KEY_AD_GROUP_ID                = SeaRequestInterface::PARAMETER_AD_GROUP_ID;
    public const string KEY_KEYWORD_ID                 = SeaRequestInterface::PARAMETER_KEYWORD_ID;
    public const string KEY_TRAFFIC_SOURCE             = 'traffic_source';
    public const string KEY_CLICK_ID                   = 'click_id';
    public const string KEY_GOOGLE_LOCATION_ID         = SeaRequestInterface::PARAMETER_GOOGLE_LOCATION_ID;
    public const string KEY_GENERIC_SECONDARY_CLICK_ID = SeaRequestInterface::PARAMETER_GENERIC_SECONDARY_CLICK_ID;
    public const string KEY_PUBLISHER                  = SeaRequestInterface::PARAMETER_PUBLISHER;
    public const string KEY_ADDITIONAL_CHANNELS        = 'additional_channels';
    public const string KEY_ACTIVE_SPLIT_TEST          = 'active_split_test';
    public const string KEY_CREATED_AT                 = 'created_at';
    public const string KEY_ORIGIN_ROUTE               = 'origin_route';
    public const string KEY_STYLE_ID                   = 'style_id';
    public const string KEY_CUSTOM_ID                  = SeaRequestInterface::PARAMETER_CUSTOM_ID;
    public const string KEY_CONVERSION_ROUTE           = 'conversion_route';

    public function __construct(
        private readonly TrackingEntryArrayResolver $trackingEntryArrayResolver,
        private readonly ActiveSplitTestFactory $activeSplitTestFactory,
        private readonly ClickIdFactory $clickIdFactory
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function serialize(TrackingEntry $trackingEntry): array
    {
        if ($trackingEntry->isEmpty) {
            return [];
        }

        $data = [
            self::KEY_QUERY                      => $trackingEntry->query,
            self::KEY_CAMPAIGN_ID                => $trackingEntry->campaignId,
            self::KEY_CAMPAIGN_NAME              => $trackingEntry->campaignName,
            self::KEY_DEVICE                     => $trackingEntry->device->getShortValue(),
            self::KEY_NETWORK                    => $trackingEntry->network?->value,
            self::KEY_ACCOUNT_ID                 => $trackingEntry->accountId,
            self::KEY_AD_GROUP_ID                => $trackingEntry->adGroupId,
            self::KEY_KEYWORD_ID                 => $trackingEntry->keywordId,
            self::KEY_TRAFFIC_SOURCE             => $trackingEntry->trafficSource?->getShortValue(),
            self::KEY_CLICK_ID                   => $trackingEntry->clickId?->toArray(),
            self::KEY_GOOGLE_LOCATION_ID         => $trackingEntry->googleLocationId,
            self::KEY_GENERIC_SECONDARY_CLICK_ID => $trackingEntry->genericSecondaryClickId,
            self::KEY_PUBLISHER                  => $trackingEntry->publisher,
            self::KEY_ACTIVE_SPLIT_TEST          => $trackingEntry->activeSplitTest?->toArray(),
            self::KEY_CREATED_AT                 => $trackingEntry->createdAt->getTimestamp(),
            self::KEY_ORIGIN_ROUTE               => $trackingEntry->originRoute,
            self::KEY_STYLE_ID                   => $trackingEntry->styleId,
            self::KEY_CUSTOM_ID                  => $trackingEntry->customId,
            self::KEY_CONVERSION_ROUTE           => $trackingEntry->conversionRoute,
        ];
        $data = array_filter($data, static fn ($value) => $value !== null);

        if ($trackingEntry->additionalChannels !== []) {
            $data[self::KEY_ADDITIONAL_CHANNELS] = $trackingEntry->additionalChannels;
        }

        return $data;
    }

    /**
     * @param mixed[] $data
     *
     * @throws TrackingEntryDeserializationFailedException
     */
    public function deserialize(array $data): TrackingEntry
    {
        try {
            $resolvedData = $this->trackingEntryArrayResolver->resolve($data);

            $activeSplitTestData = $resolvedData[self::KEY_ACTIVE_SPLIT_TEST];

            $activeSplitTest = $activeSplitTestData !== null
                ? $this->activeSplitTestFactory->createFromArray($activeSplitTestData) : null;

            $createdAt = new \DateTime(
                sprintf('@%d', $resolvedData[self::KEY_CREATED_AT]),
                TimezoneEnum::UTC->toDateTimeZone(),
            );

            return new TrackingEntry(
                isEmpty                : false,
                query                  : $resolvedData[self::KEY_QUERY],
                campaignId             : $resolvedData[self::KEY_CAMPAIGN_ID],
                campaignName           : $resolvedData[self::KEY_CAMPAIGN_NAME],
                device                 : Device::fromShortValue($resolvedData[self::KEY_DEVICE]),
                network                : Network::tryFrom((string)$resolvedData[self::KEY_NETWORK]),
                accountId              : $resolvedData[self::KEY_ACCOUNT_ID],
                adGroupId              : $resolvedData[self::KEY_AD_GROUP_ID],
                keywordId              : $resolvedData[self::KEY_KEYWORD_ID],
                trafficSource          : TrafficSource::tryFromShortValue($resolvedData[self::KEY_TRAFFIC_SOURCE]),
                clickId                : $this->clickIdFactory->fromArray($resolvedData[self::KEY_CLICK_ID] ?? []),
                googleLocationId       : $resolvedData[self::KEY_GOOGLE_LOCATION_ID],
                genericSecondaryClickId: $resolvedData[self::KEY_GENERIC_SECONDARY_CLICK_ID],
                publisher              : $resolvedData[self::KEY_PUBLISHER],
                additionalChannels     : $resolvedData[self::KEY_ADDITIONAL_CHANNELS],
                activeSplitTest        : $activeSplitTest,
                createdAt              : $createdAt,
                originRoute            : $resolvedData[self::KEY_ORIGIN_ROUTE],
                styleId                : $resolvedData[self::KEY_STYLE_ID],
                customId               : $resolvedData[self::KEY_CUSTOM_ID],
                conversionRoute        : $resolvedData[self::KEY_CONVERSION_ROUTE],
            );
        } catch (InvalidOptionException $exception) {
            throw TrackingEntryDeserializationFailedException::createForArray(
                $data,
                $exception->getMessage(),
                $exception,
            );
        }
    }
}

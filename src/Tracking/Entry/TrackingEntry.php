<?php

declare(strict_types=1);

namespace App\Tracking\Entry;

use App\Generic\Device\Device;
use App\SplitTest\Activate\ActiveSplitTest;
use App\Tracking\Entry\Exception\TrackingEntryException;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficSource;

final readonly class TrackingEntry
{
    public const int TTL_IN_DAYS = 30;

    /** @var array<string> */
    public array $additionalChannels;

    /**
     * @param array<string> $additionalChannels
     */
    public function __construct(
        public bool $isEmpty,
        public ?string $query,
        public ?int $campaignId,
        public ?string $campaignName,
        public Device $device,
        public ?Network $network,
        public ?int $accountId,
        public ?int $adGroupId,
        public ?int $keywordId,
        public ?TrafficSource $trafficSource,
        public ?ClickId $clickId,
        public ?string $googleLocationId,
        public ?string $genericSecondaryClickId,
        public ?string $publisher,
        array $additionalChannels,
        public ?ActiveSplitTest $activeSplitTest,
        public \DateTime $createdAt,
        public ?string $originRoute,
        public ?int $styleId,
        public ?string $customId,
        public ?string $conversionRoute
    )
    {
        if ($query === null && !$this->isEmpty) {
            throw new TrackingEntryException('Query is required if entry is not empty');
        }

        $additionalChannels = array_map(
            static fn (string $additionalChannel) => $additionalChannel,
            $additionalChannels,
        );
        $additionalChannels = array_unique($additionalChannels);

        $this->additionalChannels = $additionalChannels;
    }

    public function getClickIdFromSource(ClickIdSource $source): ?ClickId
    {
        if ($this->clickId === null || $this->clickId->source !== $source) {
            return null;
        }

        return $this->clickId;
    }

    public function isFresh(\DateTime $currentDateTime): bool
    {
        $cutoffDate = clone $currentDateTime;
        $cutoffDate->modify(sprintf('-%d day', self::TTL_IN_DAYS));

        return $this->createdAt > $cutoffDate;
    }

    public function withoutCampaignName(): self
    {
        return new self(
            isEmpty                : $this->isEmpty,
            query                  : $this->query,
            campaignId             : $this->campaignId,
            campaignName           : null,
            device                 : $this->device,
            network                : $this->network,
            accountId              : $this->accountId,
            adGroupId              : $this->adGroupId,
            keywordId              : $this->keywordId,
            trafficSource          : $this->trafficSource,
            clickId                : $this->clickId,
            googleLocationId       : $this->googleLocationId,
            genericSecondaryClickId: $this->genericSecondaryClickId,
            publisher              : $this->publisher,
            additionalChannels     : $this->additionalChannels,
            activeSplitTest        : $this->activeSplitTest,
            createdAt              : $this->createdAt,
            originRoute            : $this->originRoute,
            styleId                : $this->styleId,
            customId               : $this->customId,
            conversionRoute        : $this->conversionRoute,
        );
    }

    /**
     * @param array<string> $additionalChannels
     */
    public function withAdditionalChannels(array $additionalChannels): self
    {
        return new self(
            isEmpty                : $this->isEmpty,
            query                  : $this->query,
            campaignId             : $this->campaignId,
            campaignName           : $this->campaignName,
            device                 : $this->device,
            network                : $this->network,
            accountId              : $this->accountId,
            adGroupId              : $this->adGroupId,
            keywordId              : $this->keywordId,
            trafficSource          : $this->trafficSource,
            clickId                : $this->clickId,
            googleLocationId       : $this->googleLocationId,
            genericSecondaryClickId: $this->genericSecondaryClickId,
            publisher              : $this->publisher,
            additionalChannels     : $additionalChannels,
            activeSplitTest        : $this->activeSplitTest,
            createdAt              : $this->createdAt,
            originRoute            : $this->originRoute,
            styleId                : $this->styleId,
            customId               : $this->customId,
            conversionRoute        : $this->conversionRoute,
        );
    }

    public function withExtraAdditionalChannel(string $additionalChannel): self
    {
        $additionalChannels = $this->additionalChannels;
        $additionalChannels[] = $additionalChannel;

        return $this->withAdditionalChannels($additionalChannels);
    }

    public function withStyleId(int $styleId): self
    {
        return new self(
            isEmpty                : $this->isEmpty,
            query                  : $this->query,
            campaignId             : $this->campaignId,
            campaignName           : $this->campaignName,
            device                 : $this->device,
            network                : $this->network,
            accountId              : $this->accountId,
            adGroupId              : $this->adGroupId,
            keywordId              : $this->keywordId,
            trafficSource          : $this->trafficSource,
            clickId                : $this->clickId,
            googleLocationId       : $this->googleLocationId,
            genericSecondaryClickId: $this->genericSecondaryClickId,
            publisher              : $this->publisher,
            additionalChannels     : $this->additionalChannels,
            activeSplitTest        : $this->activeSplitTest,
            createdAt              : $this->createdAt,
            originRoute            : $this->originRoute,
            styleId                : $styleId,
            customId               : $this->customId,
            conversionRoute        : $this->conversionRoute,
        );
    }

    public function withActiveSplitTest(?ActiveSplitTest $activeSplitTest): self
    {
        return new self(
            isEmpty                : $this->isEmpty,
            query                  : $this->query,
            campaignId             : $this->campaignId,
            campaignName           : $this->campaignName,
            device                 : $this->device,
            network                : $this->network,
            accountId              : $this->accountId,
            adGroupId              : $this->adGroupId,
            keywordId              : $this->keywordId,
            trafficSource          : $this->trafficSource,
            clickId                : $this->clickId,
            googleLocationId       : $this->googleLocationId,
            genericSecondaryClickId: $this->genericSecondaryClickId,
            publisher              : $this->publisher,
            additionalChannels     : $this->additionalChannels,
            activeSplitTest        : $activeSplitTest,
            createdAt              : $this->createdAt,
            originRoute            : $this->originRoute,
            styleId                : $this->styleId,
            customId               : $this->customId,
            conversionRoute        : $this->conversionRoute,
        );
    }

    public function withConversionRoute(string $currentRoute): self
    {
        return new self(
            isEmpty                : $this->isEmpty,
            query                  : $this->query,
            campaignId             : $this->campaignId,
            campaignName           : $this->campaignName,
            device                 : $this->device,
            network                : $this->network,
            accountId              : $this->accountId,
            adGroupId              : $this->adGroupId,
            keywordId              : $this->keywordId,
            trafficSource          : $this->trafficSource,
            clickId                : $this->clickId,
            googleLocationId       : $this->googleLocationId,
            genericSecondaryClickId: $this->genericSecondaryClickId,
            publisher              : $this->publisher,
            additionalChannels     : $this->additionalChannels,
            activeSplitTest        : $this->activeSplitTest,
            createdAt              : $this->createdAt,
            originRoute            : $this->originRoute,
            styleId                : $this->styleId,
            customId               : $this->customId,
            conversionRoute        : $currentRoute,
        );
    }
}

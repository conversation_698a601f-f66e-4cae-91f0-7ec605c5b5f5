<?php

declare(strict_types=1);

namespace App\Tracking\Helper;

use App\Tracking\Entry\TrackingEntry;

final class ActiveTrackingEntryHelper implements ActiveTrackingEntryHelperInterface
{
    private ?TrackingEntry $trackingEntry = null;

    public function __construct(
        private readonly ActiveTrackingEntryReader $activeTrackingEntryReader
    )
    {
    }

    public function getActiveTrackingEntry(): TrackingEntry
    {
        if ($this->trackingEntry === null) {
            $this->trackingEntry = $this->activeTrackingEntryReader->determineActiveTrackingEntry();
        }

        return $this->trackingEntry;
    }

    public function setActiveTrackingEntry(TrackingEntry $trackingEntry): self
    {
        $this->trackingEntry = $trackingEntry;

        return $this;
    }
}

<?php

declare(strict_types=1);

namespace App\Tracking\Helper;

use App\AdBot\Request\AdBotRequestInterface;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use App\Tracking\Model\TrafficType;
use App\Tracking\Request\SeaRequestInterface;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;

readonly class TrafficHelper
{
    public function __construct(
        private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper,
        private WebsiteSettingsHelper $websiteSettingsHelper,
        private AdBotRequestInterface $adBotRequest,
        private SeaRequestInterface $seaRequest,
        private FriendlyBotRequestInterface $friendlyBotRequest
    )
    {
    }

    public function isPaidTraffic(): bool
    {
        // All ad bot traffic is seen as paid traffic
        if ($this->adBotRequest->isAdBot() || $this->friendlyBotRequest->isFriendlyBot()) {
            return true;
        }

        if ($this->seaRequest->getCampaignName() !== null && $this->seaRequest->getAccountId() !== null) {
            return true;
        }

        $trackingEntry = $this->activeTrackingEntryHelper->getActiveTrackingEntry();

        // Often, partner traffic does not have a traffic source, but regular partner traffic will have a campaign name
        return $trackingEntry->trafficSource !== null
               || $trackingEntry->campaignName !== null
               || $trackingEntry->network !== null;
    }

    /**
     * Returns the campaign name or the default channel from the config. Used for traffic tag and Google Ads channel.
     */
    public function getTrackingChannel(): ?string
    {
        return $this->activeTrackingEntryHelper->getActiveTrackingEntry()->campaignName
               ?? $this->websiteSettingsHelper->getSettings()->getGoogleAdSense()->getDefaultChannel();
    }

    public function getTrafficType(): TrafficType
    {
        return $this->isPaidTraffic()
            ? TrafficType::PAID
            : TrafficType::ORGANIC;
    }
}

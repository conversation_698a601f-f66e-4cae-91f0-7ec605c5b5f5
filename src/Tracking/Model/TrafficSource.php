<?php

declare(strict_types=1);

namespace App\Tracking\Model;

enum TrafficSource: string
{
    case FACEBOOK  = 'facebook';
    case GOOGLE    = 'google';
    case MEDIAGO   = 'mediago';
    case MGID      = 'mgid';
    case MICROSOFT = 'microsoft';
    case QUORA     = 'quora';
    case SNAPCHAT  = 'snapchat';
    case TABOOLA   = 'taboola';
    case TIKTOK    = 'tiktok';
    case TWITTER   = 'twitter';
    case VISYMO    = 'visymo';
    case YAHOO     = 'yahoo';
    case ZEMANTA   = 'zemanta';

    private const string SHORT_FACEBOOK  = 'f';
    private const string SHORT_GOOGLE    = 'g';
    private const string SHORT_MEDIAGO   = 'mg';
    private const string SHORT_MGID      = 'mgid';
    private const string SHORT_MICROSOFT = 'm';
    private const string SHORT_QUORA     = 'q';
    private const string SHORT_SNAPCHAT  = 's';
    private const string SHORT_TABOOLA   = 'tb';
    private const string SHORT_TIKTOK    = 'tt';
    private const string SHORT_TWITTER   = 't';
    private const string SHORT_VISYMO    = 'v';
    private const string SHORT_YAHOO     = 'y';
    private const string SHORT_ZEMANTA   = 'z';

    public const string VALUE_DIRECT = 'direct';

    public static function tryFromShortValue(?string $shortValue): ?self
    {
        return match ($shortValue) {
            self::SHORT_FACEBOOK  => self::FACEBOOK,
            self::SHORT_GOOGLE    => self::GOOGLE,
            self::SHORT_MEDIAGO   => self::MEDIAGO,
            self::SHORT_MGID      => self::MGID,
            self::SHORT_MICROSOFT => self::MICROSOFT,
            self::SHORT_QUORA     => self::QUORA,
            self::SHORT_SNAPCHAT  => self::SNAPCHAT,
            self::SHORT_TABOOLA   => self::TABOOLA,
            self::SHORT_TIKTOK    => self::TIKTOK,
            self::SHORT_TWITTER   => self::TWITTER,
            self::SHORT_VISYMO    => self::VISYMO,
            self::SHORT_YAHOO     => self::YAHOO,
            self::SHORT_ZEMANTA   => self::ZEMANTA,
            default               => null
        };
    }

    public function getShortValue(): string
    {
        return match ($this) {
            self::FACEBOOK  => self::SHORT_FACEBOOK,
            self::GOOGLE    => self::SHORT_GOOGLE,
            self::MEDIAGO   => self::SHORT_MEDIAGO,
            self::MGID      => self::SHORT_MGID,
            self::MICROSOFT => self::SHORT_MICROSOFT,
            self::QUORA     => self::SHORT_QUORA,
            self::SNAPCHAT  => self::SHORT_SNAPCHAT,
            self::TABOOLA   => self::SHORT_TABOOLA,
            self::TIKTOK    => self::SHORT_TIKTOK,
            self::TWITTER   => self::SHORT_TWITTER,
            self::VISYMO    => self::SHORT_VISYMO,
            self::YAHOO     => self::SHORT_YAHOO,
            self::ZEMANTA   => self::SHORT_ZEMANTA,
        };
    }

    /**
     * @return string[]
     */
    public static function getShortValues(): array
    {
        return array_map(
            static fn (self $trafficSource): string => $trafficSource->getShortValue(),
            self::cases(),
        );
    }
}

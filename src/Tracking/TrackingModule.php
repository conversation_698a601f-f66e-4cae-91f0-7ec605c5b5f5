<?php

declare(strict_types=1);

namespace App\Tracking;

use App\DependencyInjection\AbstractDependencyInjectionModule;
use App\Tracking\AdditionalChannel\Provider\AdditionalChannelProviderInterface;
use Symfony\Component\Config\Definition\Builder\NodeBuilder;
use Symfony\Component\DependencyInjection\ContainerBuilder;

final class TrackingModule extends AbstractDependencyInjectionModule
{
    public static function getModuleName(): string
    {
        return 'tracking';
    }

    public function buildConfig(NodeBuilder $rootNodeChildren): void
    {
        // @formatter:off
        $moduleNodeChildren = $rootNodeChildren
            ->arrayNode(self::getModuleName())
                ->addDefaultsIfNotSet()
                ->children();

        $trackingLoggingNodeChildren = $moduleNodeChildren
            ->arrayNode('conversion_log')
                ->addDefaultsIfNotSet()
                ->children();

        $trackingLoggingNodeChildren
            ->booleanNode('file_enabled')->defaultTrue()->end()
            ->end();
        // @formatter:on
    }

    /**
     * @inheritDoc
     */
    public function buildContainer(ContainerBuilder $container, array $config): void
    {
        $container->registerForAutoconfiguration(AdditionalChannelProviderInterface::class)
            ->addTag('brand_website.tracking.additional_channel_provider');
    }
}

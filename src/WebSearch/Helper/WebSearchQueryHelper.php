<?php

declare(strict_types=1);

namespace App\WebSearch\Helper;

class WebSearchQueryHelper
{
    public function buildAdvancedSearchQuery(
        ?string $searchAll,
        ?string $searchAtLeastOne,
        ?string $searchExactPhrase,
        ?string $searchWithout,
        ?string $searchSite
    ): ?string
    {
        $query = [];

        // Add a '+' for each word
        if ($searchAll !== null) {
            $queryPart = $this->stripMultipleSpaces($searchAll);
            $queryPart = sprintf('+%s', str_replace(' ', ' +', $queryPart));

            $query[] = $queryPart;
        }

        // Same as a normal search
        if ($searchAtLeastOne !== null) {
            $query[] = $searchAtLeastOne;
        }

        // Wrap the text in quotes
        if ($searchExactPhrase !== null) {
            $query[] = sprintf('"%s"', $searchExactPhrase);
        }

        // Add a '-' for each word
        if ($searchWithout !== null) {
            $queryPart = $this->stripMultipleSpaces($searchWithout);
            $queryPart = sprintf('-%s', str_replace(' ', ' -', $queryPart));

            $query[] = $queryPart;
        }

        // Prefix with 'site:'
        if ($searchSite !== null) {
            $query[] = sprintf('site:%s', $searchSite);
        }

        return count($query) > 0 ? implode(' ', $query) : null;
    }

    /**
     * Strips negative words from the query
     * Example: `ipad air -apple` becomes `ipad air`.
     */
    public function stripNegativeSearchWords(string $query): string
    {
        $strippedQuery = array_filter(
            explode(' ', $query),
            static fn (string $word) => !str_starts_with($word, '-'),
        );

        return implode(' ', $strippedQuery);
    }

    /**
     * Strips the plus from the query
     * Example: `ipad air +apple` becomes `ipad air apple` or `ipad+air` becomes `ipad air`.
     */
    public function stripPlusChar(string $query): string
    {
        $strippedQuery = str_replace('+', ' ', $query);
        $strippedQuery = trim($strippedQuery);

        return $strippedQuery;
    }

    /**
     * Strips the `site:...` part from the query
     * Example: `apple site:amazon.com` becomes `apple`.
     */
    public function stripSiteSearch(string $query): string
    {
        $strippedQuery = (string)preg_replace('/(\ssite:[^\s]+\s)/i', ' ', sprintf(' %s ', $query));
        $strippedQuery = trim($strippedQuery);

        return $strippedQuery;
    }

    /**
     * Strips the exact phrase search quotes from the query
     * Example: `"ipad air" apple` becomes `ipad air apple`.
     */
    public function stripExactPhraseSearchQuotes(string $query): string
    {
        $strippedQuery = (string)preg_replace('/\s"([^"]*)"\s/', ' \\1 ', sprintf(' %s ', $query));
        $strippedQuery = trim($strippedQuery);

        return $strippedQuery;
    }

    public function stripMultipleSpaces(string $query): string
    {
        return (string)preg_replace('/\s+/', ' ', $query);
    }
}

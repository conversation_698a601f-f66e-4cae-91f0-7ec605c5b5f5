<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\Brand;

use App\Brand\Settings\BrandSettings;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettings;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsFactory;

readonly class WebsiteBrandConfiguration
{
    /**
     * @param mixed[] $brandConfig
     */
    public function __construct(
        private array $brandConfig
    )
    {
    }

    public function getPartnerSlug(): ?string
    {
        return $this->brandConfig[BrandSettings::KEY_PARTNER_SLUG];
    }

    public function getGoogleAdSenseContractType(): ContractType
    {
        $googleAdSenseConfig = $this->brandConfig[GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE];

        return ContractType::from($googleAdSenseConfig[GoogleAdSenseSettings::KEY_CONTRACT_TYPE]);
    }
}

<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\Brand;

use App\Http\Request\Info\RequestInfoInterface;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;

class WebsiteBrandConfigurationHelper
{
    protected WebsiteBrandConfiguration $websiteBrandConfiguration;

    public function __construct(
        private readonly RequestInfoInterface $requestInfo,
        private readonly DomainToBrandMapReader $domainToBrandMapReader,
        private readonly WebsiteBrandConfigurationFactory $websiteBrandConfigurationFactory,
        private readonly WebsiteConfigurationFileRepository $websiteConfigurationFileRepository
    )
    {
    }

    public function getConfiguration(): WebsiteBrandConfiguration
    {
        if (isset($this->websiteBrandConfiguration)) {
            return $this->websiteBrandConfiguration;
        }

        $host = $this->requestInfo->getNormalisedHost();
        $brandSlug = $this->domainToBrandMapReader->getBrandForDomain($host);
        $configurationFile = $this->websiteConfigurationFileRepository->getForBrand($brandSlug);

        $this->websiteBrandConfiguration = $this->websiteBrandConfigurationFactory->create(
            $configurationFile->getContents(),
        );

        return $this->websiteBrandConfiguration;
    }
}

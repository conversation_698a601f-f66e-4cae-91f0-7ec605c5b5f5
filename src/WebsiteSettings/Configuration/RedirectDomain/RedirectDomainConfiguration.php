<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\RedirectDomain;

class RedirectDomainConfiguration
{
    public const string KEY_REDIRECT_STRATEGY = 'redirect_strategy';

    public const string REDIRECT_STRATEGY_GEO_IP = 'geo_ip';

    /**
     * @param array<string, mixed> $configuration
     */
    public function __construct(private readonly array $configuration)
    {
    }

    public function getStrategy(): string
    {
        return $this->configuration[self::KEY_REDIRECT_STRATEGY];
    }
}

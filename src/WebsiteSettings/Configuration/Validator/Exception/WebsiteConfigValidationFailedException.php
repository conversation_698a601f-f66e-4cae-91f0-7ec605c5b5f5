<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration\Validator\Exception;

class WebsiteConfigValidationFailedException extends \RuntimeException
{
    public static function create(?\Throwable $previous = null): self
    {
        $message = 'Website config validation failed';

        if ($previous !== null) {
            $message = sprintf('%s: %s', $message, $previous->getMessage());
        }

        return new self($message, 0, $previous);
    }
}

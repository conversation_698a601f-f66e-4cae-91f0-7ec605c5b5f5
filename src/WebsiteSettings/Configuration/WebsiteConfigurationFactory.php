<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration;

use App\WebsiteSettings\Configuration\Exception\WebsiteConfigurationStructureInvalidException;

class WebsiteConfigurationFactory
{
    /**
     * @param mixed[] $config
     */
    public function create(array $config): WebsiteConfiguration
    {
        // simple validation of structure
        $validConfig = isset(
            $config[WebsiteConfiguration::KEY_ACCOUNTS],
            $config[WebsiteConfiguration::KEY_BRAND],
            $config[WebsiteConfiguration::KEY_DOMAINS],
            $config[WebsiteConfiguration::KEY_REDIRECT_DOMAINS],
            $config[WebsiteConfiguration::KEY_SPLIT_TESTS],
        );

        if ($validConfig) {
            $validConfig = !isset(
                $config[WebsiteConfiguration::KEY_ACCOUNT_CONFIG],
                $config[WebsiteConfiguration::KEY_DOMAIN_CONFIG],
            );
        }

        if (!$validConfig) {
            throw WebsiteConfigurationStructureInvalidException::create();
        }

        return new WebsiteConfiguration($config);
    }

    /**
     * @param mixed[] $config
     */
    public function createFromNormalized(array $config): WebsiteConfiguration
    {
        $validConfig = isset(
            $config[WebsiteConfiguration::KEY_ACCOUNTS],
            $config[WebsiteConfiguration::KEY_ACCOUNT_CONFIG],
            $config[WebsiteConfiguration::KEY_BRAND],
            $config[WebsiteConfiguration::KEY_DOMAINS],
            $config[WebsiteConfiguration::KEY_DOMAIN_CONFIG],
            $config[WebsiteConfiguration::KEY_REDIRECT_DOMAINS],
            $config[WebsiteConfiguration::KEY_SPLIT_TESTS],
        );

        if (!$validConfig) {
            throw WebsiteConfigurationStructureInvalidException::create();
        }

        $config = $this->convertNormalizedConfig($config);

        return new WebsiteConfiguration($config);
    }

    /**
     * @param mixed[] $config
     *
     * @return mixed[]
     */
    private function convertNormalizedConfig(array $config): array
    {
        foreach ($config[WebsiteConfiguration::KEY_ACCOUNTS] as $accountId => $accountConfig) {
            $accountConfig += $config[WebsiteConfiguration::KEY_ACCOUNT_CONFIG][$accountConfig['config']];
            unset($accountConfig['config']);
            $config[WebsiteConfiguration::KEY_ACCOUNTS][$accountId] = $accountConfig;
        }

        $domains = $config[WebsiteConfiguration::KEY_DOMAINS];
        $config[WebsiteConfiguration::KEY_DOMAINS] = [];

        foreach ($domains as $domain => $domainConfig) {
            $domainConfig += $config[WebsiteConfiguration::KEY_DOMAIN_CONFIG][$domainConfig['config']];
            unset($domainConfig['config']);
            $config[WebsiteConfiguration::KEY_DOMAINS][$domain] = $domainConfig;
        }

        unset(
            $config[WebsiteConfiguration::KEY_ACCOUNT_CONFIG],
            $config[WebsiteConfiguration::KEY_DOMAIN_CONFIG],
        );

        return $config;
    }
}

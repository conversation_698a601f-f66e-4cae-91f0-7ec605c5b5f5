<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration;

use App\WebsiteSettings\Configuration\Cache\CachedWebsiteConfigurationFileFactory;
use App\WebsiteSettings\Configuration\Exception\WebsiteConfigurationFileNotFoundException;
use Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIteratorInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

class WebsiteConfigurationFileRepository
{
    /** @var SerializedFileInterface[] */
    protected array $websiteConfigurationFiles;

    public function __construct(
        private readonly SerializedFileIteratorInterface $localBrandConfigFileIterator,
        private readonly SerializedFileIteratorInterface $nativeLocalBrandConfigFileIterator,
        private readonly CachedWebsiteConfigurationFileFactory $cachedWebsiteConfigurationFileFactory
    )
    {
    }

    private function getSerializedFileIterator(bool $useOpcache): SerializedFileIteratorInterface
    {
        return $useOpcache
            ? $this->localBrandConfigFileIterator
            : $this->nativeLocalBrandConfigFileIterator;
    }

    /**
     * @return string[]
     */
    public function getAvailableBrands(bool $useOpCache = true): array
    {
        return array_keys($this->getConfigurationFiles($useOpCache));
    }

    /**
     * @throws WebsiteConfigurationFileNotFoundException
     */
    public function getForBrand(string $brandSlug, bool $useOpCache = true): SerializedFileInterface
    {
        if (isset($this->websiteConfigurationFiles[$brandSlug])) {
            return $this->websiteConfigurationFiles[$brandSlug];
        }

        $configurationFile = $this->cachedWebsiteConfigurationFileFactory->createLocalFile($brandSlug, $useOpCache);

        if (!$configurationFile->exists()) {
            throw WebsiteConfigurationFileNotFoundException::create($brandSlug);
        }

        return $configurationFile;
    }

    /**
     * @return SerializedFileInterface[]
     */
    private function getConfigurationFiles(bool $useOpCache): array
    {
        if (isset($this->websiteConfigurationFiles)) {
            return $this->websiteConfigurationFiles;
        }

        $websiteConfigurationFiles = [];

        foreach ($this->getSerializedFileIterator($useOpCache)->iterate() as $localBrandConfigFile) {
            if (preg_match('~^[a-z0-9]+$~', $localBrandConfigFile->getBaseName()) !== 1) {
                continue;
            }

            $brandSlug = $localBrandConfigFile->getBaseName();
            $websiteConfigurationFiles[$brandSlug] = $localBrandConfigFile;
        }

        $this->websiteConfigurationFiles = $websiteConfigurationFiles;

        return $this->websiteConfigurationFiles;
    }

    public function removeBrandConfigurationFile(string $brandSlug): void
    {
        unset($this->websiteConfigurationFiles[$brandSlug]);
    }
}

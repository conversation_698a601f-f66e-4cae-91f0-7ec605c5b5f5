<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Configuration;

use App\WebsiteSettings\Configuration\Cache\CachedWebsiteConfigurationFileReader;

class WebsiteConfigurationRepository
{
    /** @var WebsiteConfiguration[] */
    protected array $fullWebsiteConfigurations = [];

    /** @var WebsiteConfiguration[] */
    protected array $partialWebsiteConfigurations = [];

    public function __construct(
        private readonly WebsiteConfigurationFileRepository $websiteConfigurationFileRepository,
        private readonly CachedWebsiteConfigurationFileReader $cachedWebsiteConfigurationFileReader,
        private readonly WebsiteConfigurationFactory $websiteConfigurationFactory
    )
    {
    }

    /**
     * @return WebsiteConfiguration[]
     */
    public function getAll(): array
    {
        $websiteConfigurations = [];

        foreach ($this->websiteConfigurationFileRepository->getAvailableBrands() as $brandSlug) {
            $websiteConfigurations[] = $this->getForBrand($brandSlug);
        }

        return $websiteConfigurations;
    }

    public function getForBrand(string $brandSlug): WebsiteConfiguration
    {
        if (isset($this->fullWebsiteConfigurations[$brandSlug])) {
            return $this->fullWebsiteConfigurations[$brandSlug];
        }

        $configurationFile = $this->websiteConfigurationFileRepository->getForBrand($brandSlug);
        $configuration = $this->cachedWebsiteConfigurationFileReader->readAllCombined($configurationFile);

        $websiteConfiguration = $this->websiteConfigurationFactory->create(
            $configuration,
        );

        return $this->fullWebsiteConfigurations[$brandSlug] = $websiteConfiguration;
    }

    public function getForBrandAndAccount(
        string $brandSlug,
        ?int $accountId
    ): WebsiteConfiguration
    {
        $configurationKey = $accountId !== null ? sprintf('%s_%u', $brandSlug, $accountId) : $brandSlug;

        if (isset($this->partialWebsiteConfigurations[$configurationKey])) {
            return $this->partialWebsiteConfigurations[$configurationKey];
        }

        $configurationFile = $this->websiteConfigurationFileRepository->getForBrand($brandSlug);
        $configuration = $this->cachedWebsiteConfigurationFileReader->read(
            serializedFile: $configurationFile,
            accountId     : $accountId,
        );

        $websiteConfiguration = $this->websiteConfigurationFactory->create(
            config: $configuration,
        );

        return $this->partialWebsiteConfigurations[$configurationKey] = $websiteConfiguration;
    }
}

<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Console;

use App\WebsiteSettings\Configuration\Cache\CachedWebsiteConfigurationFileCleaner;
use App\WebsiteSettings\Configuration\Import\Exception\ImportConfigFailedException;
use App\WebsiteSettings\Configuration\Import\WebsiteConfigurationImporter;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIteratorInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileFactory;

#[AsCommand(
    name       : 'serp:config:import',
    description: 'Import brand configurations'
)]
class ImportConfigConsole extends Command
{
    private const string ARGUMENT_CONFIG_JSON_FILE_PATH = 'config_json_file_path';

    private const string OPTION_NO_INVALIDATE_OPCACHE = 'no_invalidate_opcache';

    public function __construct(
        private readonly SerializedFileIteratorInterface $importBrandConfigFileIterator,
        private readonly WebsiteConfigurationImporter $websiteConfigurationImporter,
        private readonly WebsiteConfigurationFileRepository $websiteConfigurationFileRepository,
        private readonly CachedWebsiteConfigurationFileCleaner $cachedWebsiteConfigurationFileCleaner,
        private readonly DomainToBrandMapGenerator $domainToBrandMapGenerator,
        private readonly SerializedFileFactory $serializedFileFactory,
        private readonly LoggerInterface $logger,
        private readonly bool $cleanOldConfigs
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                self::ARGUMENT_CONFIG_JSON_FILE_PATH,
                InputArgument::OPTIONAL,
                'Only import the given absolute JSON file path',
            )
            ->addOption(
                name: self::OPTION_NO_INVALIDATE_OPCACHE,
                mode: InputOption::VALUE_NONE,
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $successCount = 0;
            $failedFiles = [];
            $availableBrands = [];
            $configJsonFilePath = $input->getArgument(self::ARGUMENT_CONFIG_JSON_FILE_PATH);
            $useOpcache = $input->getOption(self::OPTION_NO_INVALIDATE_OPCACHE) === false;

            if ($configJsonFilePath !== null) {
                $importFiles = $this->createIteratorForSingleJsonPath($configJsonFilePath);
            } else {
                $importFiles = $this->importBrandConfigFileIterator->iterate();
            }

            foreach ($importFiles as $importFile) {
                try {
                    $this->websiteConfigurationImporter->import($importFile, $useOpcache);
                    $successCount++;
                } catch (ImportConfigFailedException $exception) {
                    $failedFiles[] = $importFile->getFilePath();

                    $this->logger->critical(
                        sprintf('Caught %s while importing brand config file {file}: {message}', $exception::class),
                        [
                            'file'      => $importFile->getFilePath(),
                            'exception' => $exception,
                            'message'   => $exception->getMessage(),
                        ],
                    );
                }
            }

            $availableBrandConfigFiles = $this->importBrandConfigFileIterator->iterate();

            foreach ($availableBrandConfigFiles as $file) {
                $brandSlug = $file->getBaseName();
                $availableBrands[] = $brandSlug;
            }

            $this->cleanOldConfigs($availableBrands, $useOpcache);

            $this->domainToBrandMapGenerator->generate($useOpcache);

            if ($failedFiles !== []) {
                $failCount = count($failedFiles);
                $totalCount = $successCount + count($failedFiles);
                $output->writeln(
                    sprintf(
                        '<error>Imported %u brand config file(s): %u successful, %u failed:</error>',
                        $totalCount,
                        $successCount,
                        $failCount,
                    ),
                );

                foreach ($failedFiles as $failedFile) {
                    $output->writeln(sprintf('<error>- Failed to import: %s</error>', $failedFile));
                }

                return Command::FAILURE;
            }
        } catch (\Throwable $exception) {
            $this->logger->critical(
                sprintf('Caught %s while importing brand config files: {message}', $exception::class),
                [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                ],
            );

            return Command::FAILURE;
        }

        $output->writeln('<info>Successfully imported brand config files.</info>');

        return Command::SUCCESS;
    }

    /**
     * @param string[] $availableBrands
     */
    private function cleanOldConfigs(array $availableBrands, bool $useOpcache = true): void
    {
        if (!$this->cleanOldConfigs || $availableBrands === []) {
            return;
        }

        $obsoleteBrandConfigFiles = array_diff(
            $this->websiteConfigurationFileRepository->getAvailableBrands($useOpcache),
            $availableBrands,
        );

        foreach ($obsoleteBrandConfigFiles as $obsoleteBrandConfigFile) {
            $this->cachedWebsiteConfigurationFileCleaner->cleanAll($obsoleteBrandConfigFile, $useOpcache);
            $this->websiteConfigurationFileRepository->removeBrandConfigurationFile($obsoleteBrandConfigFile);
        }
    }

    private function createIteratorForSingleJsonPath(string $configJsonFilePath): \Generator
    {
        yield $this->serializedFileFactory->create($configJsonFilePath);
    }
}

<?php

declare(strict_types=1);

namespace App\WebsiteSettings\DebugInfoProvider;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\Info\DebugInfo;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;

class WebsiteSettingsDebugInfoProvider implements DebugInfoProviderInterface
{
    public const string KEY_WEBSITE_SETTINGS = 'website settings';

    public function __construct(
        private readonly WebsiteSettingsHelper $websiteSettingsHelper
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getDebugInfo(): array
    {
        return [
            new DebugInfo(
                self::KEY_WEBSITE_SETTINGS,
                [
                    $this->websiteSettingsHelper->getSettings()->toArrayWithoutDisabledProperties(),
                ],
            ),
        ];
    }

    public static function getDefaultPriority(): int
    {
        return 30;
    }

    /**
     * @inheritDoc
     */
    public function getKeys(): array
    {
        return [
            self::KEY_WEBSITE_SETTINGS,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\WebsiteSettings\DomainToBrandMap\Exception;

class DomainAlreadyExistsException extends \RuntimeException
{
    /**
     * @param string[] $brandSlugs
     */
    public static function withBrandsAndDomain(array $brandSlugs, string $domain): self
    {
        return new self(
            sprintf(
                'Domain %s already exists in a config, check the configs of the following brands: %s',
                $domain,
                implode(', ', $brandSlugs),
            ),
        );
    }
}

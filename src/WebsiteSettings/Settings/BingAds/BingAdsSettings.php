<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\BingAds;

use App\WebsiteSettings\Settings\Module\AbstractModuleSettings;

class BingAdsSettings extends AbstractModuleSettings
{
    public const string KEY_DYNAMIC_ADS_ENABLED = 'dynamic_ads_enabled';
    public const string KEY_APPROVAL            = 'approval';
    public const string KEY_CLARITY_ID          = 'clarity_id';
    public const string KEY_SEM_AD_UNIT_ID      = 'sem_ad_unit_id';
    public const string KEY_WEB_AD_UNIT_ID      = 'web_ad_unit_id';
    public const string KEY_DEFAULT_AD_UNIT_ID  = 'default_ad_unit_id';
    public const string KEY_FALLBACK_ENABLED    = 'fallback_enabled';

    public function __construct(
        bool $enabled,
        protected bool $dynamicAdsEnabled,
        protected bool $approval,
        protected ?string $clarityId,
        protected string $semAdUnitId,
        protected string $webAdUnitId,
        protected bool $fallbackEnabled = false
    )
    {
        $this->enabled = $enabled;
    }

    public function isDynamicAdsEnabled(): bool
    {
        return $this->dynamicAdsEnabled;
    }

    public function isApproval(): bool
    {
        return $this->approval;
    }

    public function getClarityId(): ?string
    {
        return $this->clarityId;
    }

    public function getSemAdUnitId(): string
    {
        return $this->semAdUnitId;
    }

    public function getWebAdUnitId(): string
    {
        return $this->webAdUnitId;
    }

    public function isFallbackEnabled(): bool
    {
        return $this->fallbackEnabled;
    }

    /**
     * @inheritDoc
     */
    protected function propertiesToArray(): array
    {
        return [
            self::KEY_DYNAMIC_ADS_ENABLED => $this->isDynamicAdsEnabled(),
            self::KEY_APPROVAL            => $this->isApproval(),
            self::KEY_FALLBACK_ENABLED    => $this->isFallbackEnabled(),
            self::KEY_CLARITY_ID          => $this->getClarityId(),
            self::KEY_SEM_AD_UNIT_ID      => $this->getSemAdUnitId(),
            self::KEY_WEB_AD_UNIT_ID      => $this->getWebAdUnitId(),
        ];
    }
}

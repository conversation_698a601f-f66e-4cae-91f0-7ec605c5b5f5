<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\ConversionLog;

use App\WebsiteSettings\Settings\Module\AbstractModuleSettings;

class ConversionLogSettings extends AbstractModuleSettings
{
    public const string KEY_OFFLINE_CONVERSION = 'offline_conversion';

    public function __construct(bool $enabled, protected bool $offlineConversion)
    {
        $this->enabled = $enabled;
    }

    public function isOfflineConversion(): bool
    {
        return $this->offlineConversion;
    }

    /**
     * @inheritDoc
     */
    protected function propertiesToArray(): array
    {
        return [
            self::KEY_OFFLINE_CONVERSION => $this->isOfflineConversion(),
        ];
    }
}

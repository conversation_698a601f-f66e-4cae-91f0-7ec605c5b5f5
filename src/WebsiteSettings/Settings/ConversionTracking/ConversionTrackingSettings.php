<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\ConversionTracking;

class ConversionTrackingSettings
{
    public const string KEY_ORDER_TYPE = 'order_type';

    public function __construct(
        protected ConversionTrackingOrderType $orderType
    )
    {
    }

    public function getOrderType(): ConversionTrackingOrderType
    {
        return $this->orderType;
    }

    /**
     * @return mixed[]
     */
    public function toArray(): array
    {
        return [
            self::KEY_ORDER_TYPE => $this->orderType->value,
        ];
    }
}

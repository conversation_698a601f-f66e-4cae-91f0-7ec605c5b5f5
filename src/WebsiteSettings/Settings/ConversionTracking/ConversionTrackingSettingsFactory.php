<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\ConversionTracking;

class ConversionTrackingSettingsFactory
{
    public const string KEY_CONVERSION_TRACKING = 'conversion_tracking';

    /**
     * @param mixed[]|null $accountConfig
     */
    public function create(?array $accountConfig): ConversionTrackingSettings
    {
        try {
            $conversionTrackingSettings = $accountConfig[self::KEY_CONVERSION_TRACKING] ?? [];

            if (array_key_exists(ConversionTrackingSettings::KEY_ORDER_TYPE, $conversionTrackingSettings)) {
                $orderType = ConversionTrackingOrderType::from(
                    $conversionTrackingSettings[ConversionTrackingSettings::KEY_ORDER_TYPE],
                );
            } else {
                $orderType = ConversionTrackingOrderType::DEFAULT;
            }

            return new ConversionTrackingSettings(
                $orderType,
            );
        } catch (\Throwable $exception) {
            throw InvalidConversionTrackingSettingsConfigException::create($exception);
        }
    }
}

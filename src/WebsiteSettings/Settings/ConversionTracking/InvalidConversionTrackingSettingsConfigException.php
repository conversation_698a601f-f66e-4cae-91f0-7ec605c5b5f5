<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\ConversionTracking;

use App\WebsiteSettings\Settings\Exception\InvalidWebsiteSettingsConfigException;

class InvalidConversionTrackingSettingsConfigException extends InvalidWebsiteSettingsConfigException
{
    public static function create(\Throwable $previous): self
    {
        return new self(
            sprintf('Invalid conversion tracking settings config given: %s', $previous->getMessage()),
            0,
            $previous,
        );
    }
}

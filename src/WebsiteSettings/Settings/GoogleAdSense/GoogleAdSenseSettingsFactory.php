<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\GoogleAdSense;

use App\Ads\AdProvider;
use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;
use App\WebsiteSettings\Settings\Module\AdProviderSettingsFactoryInterface;

final readonly class GoogleAdSenseSettingsFactory extends AbstractModuleSettingsFactory implements AdProviderSettingsFactoryInterface
{
    public const string KEY_GOOGLE_ADSENSE = 'google_adsense';

    public function getType(): string
    {
        return AdProvider::TYPE_GOOGLE;
    }

    /**
     * @inheritDoc
     */
    public function create(
        array $brandConfig,
        array $domainConfig,
        ?array $accountConfig
    ): GoogleAdSenseSettings
    {
        try {
            $moduleDomainEnabled = $this->isGoogleAdSenseOnDomainEnabled($domainConfig);

            if ($accountConfig === null) {
                $moduleAccountConfig = [];
                $moduleAccountEnabled = true;
            } else {
                $moduleAccountConfig = $accountConfig[self::KEY_GOOGLE_ADSENSE];
                $moduleAccountEnabled = $this->isModuleEnabled($moduleAccountConfig);
            }

            $moduleEnabled = $moduleDomainEnabled && $moduleAccountEnabled;
            $contractType = ContractType::from($brandConfig[self::KEY_GOOGLE_ADSENSE][GoogleAdSenseSettings::KEY_CONTRACT_TYPE]);

            // Google AdSense is used as fallback when an error occurs.
            // Create with all known properties from config. Even when it's disabled.
            return new GoogleAdSenseSettings(
                enabled          : $moduleEnabled,
                dynamicAdsEnabled: $moduleEnabled,
                approval         : $brandConfig[self::KEY_GOOGLE_ADSENSE][GoogleAdSenseSettings::KEY_APPROVAL],
                contractType     : $contractType,
                defaultClient    : $brandConfig[self::KEY_GOOGLE_ADSENSE][GoogleAdSenseSettings::KEY_DEFAULT_CLIENT] ?? '',
                semClient        : $moduleAccountConfig[GoogleAdSenseSettings::KEY_SEM_CLIENT] ?? null,
                webClient        : $moduleAccountConfig[GoogleAdSenseSettings::KEY_WEB_CLIENT] ?? null,
                defaultChannel   : $brandConfig[self::KEY_GOOGLE_ADSENSE][GoogleAdSenseSettings::KEY_DEFAULT_CHANNEL] ??
                                   null,
            );
        } catch (\Throwable $exception) {
            throw InvalidGoogleAdSenseSettingsConfigException::create($exception);
        }
    }

    /**
     * Check if Google AdSense is enabled on domain level
     * Google AdSense is used as fallback. Other ad provider settings could rely on this setting to be enabled or disabled.
     *
     * @param mixed[] $domainConfig
     */
    public function isGoogleAdSenseOnDomainEnabled(array $domainConfig): bool
    {
        $moduleDomainConfig = $domainConfig[self::KEY_GOOGLE_ADSENSE];

        return $this->isModuleEnabled($moduleDomainConfig);
    }
}

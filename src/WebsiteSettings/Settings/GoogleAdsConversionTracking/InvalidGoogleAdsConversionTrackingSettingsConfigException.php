<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\GoogleAdsConversionTracking;

use App\WebsiteSettings\Settings\Exception\InvalidWebsiteSettingsConfigException;

class InvalidGoogleAdsConversionTrackingSettingsConfigException extends InvalidWebsiteSettingsConfigException
{
    public static function create(\Throwable $previous): self
    {
        return new self(
            sprintf('Invalid Google Ads conversion tracking settings config given: %s', $previous->getMessage()),
            0,
            $previous,
        );
    }
}

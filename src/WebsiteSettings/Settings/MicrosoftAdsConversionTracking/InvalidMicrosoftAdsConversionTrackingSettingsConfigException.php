<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\MicrosoftAdsConversionTracking;

use App\WebsiteSettings\Settings\Exception\InvalidWebsiteSettingsConfigException;

class InvalidMicrosoftAdsConversionTrackingSettingsConfigException extends InvalidWebsiteSettingsConfigException
{
    public static function create(\Throwable $previous): self
    {
        return new self(
            sprintf('Invalid Microsoft Ads conversion tracking settings config given: %s', $previous->getMessage()),
            0,
            $previous,
        );
    }
}

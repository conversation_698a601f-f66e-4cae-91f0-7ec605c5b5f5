<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\MicrosoftAdsConversionTracking;

use App\WebsiteSettings\Settings\Module\AbstractModuleSettingsFactory;
use App\WebsiteSettings\Settings\Module\ModuleSettingsFactoryInterface;

final readonly class MicrosoftAdsConversionTrackingSettingsFactory extends AbstractModuleSettingsFactory
    implements ModuleSettingsFactoryInterface
{
    public const string KEY_MICROSOFT_ADS_CONVERSION_TRACKING = 'microsoft_ads_conversion_tracking';

    /**
     * @inheritDoc
     */
    public function create(
        array $brandConfig,
        array $domainConfig,
        ?array $accountConfig
    ): MicrosoftAdsConversionTrackingSettings
    {
        try {
            if ($accountConfig === null) {
                return $this->createDisabled();
            }

            $moduleConfig = $accountConfig[self::KEY_MICROSOFT_ADS_CONVERSION_TRACKING];

            if (!$this->isModuleEnabled($moduleConfig)) {
                return $this->createDisabled();
            }

            return $this->createEnabled($moduleConfig);
        } catch (\Throwable $exception) {
            throw InvalidMicrosoftAdsConversionTrackingSettingsConfigException::create($exception);
        }
    }

    /**
     * @param mixed[] $moduleConfig
     */
    private function createEnabled(array $moduleConfig): MicrosoftAdsConversionTrackingSettings
    {
        return new MicrosoftAdsConversionTrackingSettings(
            true,
            $moduleConfig[MicrosoftAdsConversionTrackingSettings::KEY_CONVERSION_TRACKING_ID],
            $moduleConfig[MicrosoftAdsConversionTrackingSettings::KEY_CONVERSION_TRACKING_LABEL],
        );
    }

    private function createDisabled(): MicrosoftAdsConversionTrackingSettings
    {
        return new MicrosoftAdsConversionTrackingSettings(
            false,
            0,
            null,
        );
    }
}

<?php

declare(strict_types=1);

namespace App\WebsiteSettings\Settings\Module;

interface ModuleSettingsFactoryInterface
{
    /**
     * @param mixed[]      $brandConfig
     * @param mixed[]      $domainConfig
     * @param mixed[]|null $accountConfig
     */
    public function create(
        array $brandConfig,
        array $domainConfig,
        ?array $accountConfig
    ): ModuleSettingsInterface;
}

<?php

declare(strict_types=1);

namespace Tests\Frontend;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Frontend\Framework\Helper\ExceptionHandlerHelper;
use Tests\Frontend\Framework\Helper\ScreenshotHelper;
use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverManager;
use Tests\Frontend\TestCase\TestCaseInterface;
use Tests\Frontend\TestCase\TestRunnerInterface;
use Visymo\DevelopBundle\FrontendTest\Helper\FrontendTestFilterHelper;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

abstract class AbstractFrontendTestCase extends AbstractSymfonyIntegrationTest implements FrontendTestInterface
{
    private WebDriverManager $webDriverManager;

    private ScreenshotHelper $screenshotHelper;

    private ExceptionHandlerHelper $exceptionHandlerHelper;

    private DateTimeFactory $dateTimeFactory;

    protected function setUp(): void
    {
        /** @var WebDriverManager $webDriverInstanceRegistry */
        $webDriverInstanceRegistry = self::getContainer()->get(WebDriverManager::class);
        $this->webDriverManager = $webDriverInstanceRegistry;

        /** @var ScreenshotHelper $screenshotHelper */
        $screenshotHelper = self::getContainer()->get(ScreenshotHelper::class);
        $this->screenshotHelper = $screenshotHelper;

        /** @var ExceptionHandlerHelper $exceptionHandlerHelper */
        $exceptionHandlerHelper = self::getContainer()->get(ExceptionHandlerHelper::class);
        $this->exceptionHandlerHelper = $exceptionHandlerHelper;

        /** @var DateTimeFactory $dateTimeFactory */
        $dateTimeFactory = self::getContainer()->get(DateTimeFactory::class);
        $this->dateTimeFactory = $dateTimeFactory;
    }

    /** @phpstan-ignore-next-line prevent resetting symfony container during tests by overriding tearDown() */
    protected function tearDown(): void
    {
        // prevent resetting symfony container during tests by overriding tearDown()
    }

    /**
     * @inheritDoc
     */
    abstract public static function getFrontendTestCases(): array;

    /**
     * @return mixed[]
     */
    public static function frontendDataProvider(): array
    {
        $frontendTestCapabilities = FrontendTestSettings::getDefaultCapabilities();
        $frontendTestCases = static::getFrontendTestCases();
        $frontendTestFilterHelper = new FrontendTestFilterHelper();

        $data = [];

        foreach ($frontendTestCases as $frontendTestCase) {
            $responsiveBreakpoints = $frontendTestCase->getResponsiveBreakpoints();

            foreach ($responsiveBreakpoints as $responsiveBreakpoint) {
                if ($frontendTestFilterHelper->shouldFilterResponsiveBreakpoint($responsiveBreakpoint)) {
                    continue;
                }

                $frontendTestCaseClone = clone $frontendTestCase;
                $frontendTestCaseClone->setWindowWidthOverride($responsiveBreakpoint);

                $frontendTestCases[] = $frontendTestCaseClone;
            }
        }

        foreach ($frontendTestCapabilities as $webDriverCapabilities) {
            if ($frontendTestFilterHelper->shouldFilterWebDriverCapability($webDriverCapabilities)) {
                continue;
            }

            foreach ($frontendTestCases as $frontendTestCase) {
                // only run testcase on selected platform
                if ($webDriverCapabilities->getDevice() !== $frontendTestCase->getDevice()) {
                    continue;
                }

                if ($frontendTestFilterHelper->shouldFilterTestCase($frontendTestCase)) {
                    continue;
                }

                $windowWidthOverride = $frontendTestCase->getWindowWidthOverride();

                // create testrun data
                $testName = sprintf(
                    '%s%s on %s',
                    $frontendTestCase->getFullName(),
                    $windowWidthOverride !== null ? sprintf(' responsive %s', $windowWidthOverride) : '',
                    $webDriverCapabilities->getLabel(),
                );

                $data[$testName] = [
                    'webDriverCapabilities' => $webDriverCapabilities,
                    'testCase'              => $frontendTestCase,
                ];
            }
        }

        uasort(
            $data,
            static function ($a, $b) {
                $windowWidthA = $a['testCase']->getWindowWidthOverride() ?? $a['webDriverCapabilities']->getWindowResolution()?->getWidth();
                $windowWidthB = $b['testCase']->getWindowWidthOverride() ?? $b['webDriverCapabilities']->getWindowResolution()?->getWidth();

                return $windowWidthB <=> $windowWidthA;
            },
        );

        uasort(
            $data,
            static function ($a, $b) {
                $webDriverCapabilitiesA = $a['webDriverCapabilities']->getCapabilities();
                $webDriverCapabilitiesB = $b['webDriverCapabilities']->getCapabilities();

                $platformNameMatches = $webDriverCapabilitiesA['platformName'] === $webDriverCapabilitiesB['platformName'];
                $browserNameMatches = $webDriverCapabilitiesA['browserName'] === $webDriverCapabilitiesB['browserName'];

                if ($platformNameMatches) {
                    if ($browserNameMatches) {
                        return $webDriverCapabilitiesA['browserVersion'] <=> $webDriverCapabilitiesB['browserVersion'];
                    }

                    return $webDriverCapabilitiesA['browserName'] <=> $webDriverCapabilitiesB['browserName'];
                }

                return $webDriverCapabilitiesA['platformName'] <=> $webDriverCapabilitiesB['platformName'];
            },
        );

        return $data;
    }

    #[DataProvider('frontendDataProvider')]
    public function testFrontend(
        WebDriverCapabilitiesInterface $webDriverCapabilities,
        TestCaseInterface $testCase
    ): void
    {
        $startDateTime = $this->dateTimeFactory->createNow();

        // Reset flag to prevent incorrectly marking originating or continuation test as skipped
        $this->screenshotHelper->resetFlags();

        // Load webdriver
        try {
            $webDriver = $this->webDriverManager->getWebDriver(
                $webDriverCapabilities,
                $testCase,
            );
        } catch (\Throwable $exception) {
            $this->exceptionHandlerHelper->handle($exception, 'loading webdriver');

            throw $exception;
        }

        // Run testcase
        $screenshotsWereUpdated = false;
        $screenshotsWithDifferencesFound = false;

        try {
            /** @var TestRunnerInterface $testRunner */
            $testRunner = self::getContainer()->get($testCase->getTestRunner());

            $testRunner->run(
                testCase : $testCase,
                webDriver: $webDriver,
            );

            if ($this->screenshotHelper->hasUpdatedScreenshots()) {
                // Set flag for marking test as skipped and leave test session unmarked
                $screenshotsWereUpdated = true;
            } elseif ($this->screenshotHelper->hasScreenshotsWithDifferences()) {
                $screenshotsWithDifferencesFound = true;

                // Mark session as failed
                $webDriver->markTestAsFailed();
            } else {
                // Mark session as passed
                $webDriver->markTestAsPassed();
            }
        } catch (\Throwable $exception) {
            $this->exceptionHandlerHelper->handle($exception, 'running Frontend test');

            $webDriver->markTestAsFailed();
            // output debug information
            $endDateTime = $this->dateTimeFactory->createNow(TimezoneEnum::AMSTERDAM);

            echo json_encode(
                [
                    'title'             => 'Debug info for failed test',
                    'test_url'          => $testCase->getUrl(),
                    'test_started_at'   => $startDateTime->format('d/m/Y H:i:s'),
                    'test_stopped_at'   => $endDateTime->format('d/m/Y H:i:s'),
                    'caught_exception'  => $exception::class,
                    'exception_message' => $exception->getMessage(),
                    'used_capabilities' => $webDriver->getCapabilities()->getCapabilities(),
                ],
                JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT | JSON_THROW_ON_ERROR,
            );

            throw $exception;
        }

        // Only mark test after all continuation tests were run, so that continuation tests also have the
        // chance to update any screenshots if needed
        if ($screenshotsWereUpdated) {
            self::markTestSkipped('Test marked as skipped because screenshots were updated');
        } elseif ($screenshotsWithDifferencesFound) {
            self::fail(sprintf('Test failed because screenshots with differences were found for URL: %s', $testCase->getUrl()));
        }
    }
}

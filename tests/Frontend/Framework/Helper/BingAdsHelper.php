<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use App\Http\Url\DevelopHostHelper;
use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\FrontendTestSettings;

class BingAdsHelper
{
    private const string CONTAINER_ELEMENT_TOP_ID    = 'ba-top';
    private const string CONTAINER_ELEMENT_BOTTOM_ID = 'ba-bottom';

    public function __construct(
        private readonly HtmlDocumentHelper $htmlDocumentHelper,
        private readonly DevelopHostHelper $developHostHelper
    )
    {
    }

    public function waitUntilLoaded(WebDriverInterface $webDriver, bool $hasBottomAds): void
    {
        // Wait for Bing Ads to become present
        $this->waitUntilUnitIsPresent($webDriver, self::CONTAINER_ELEMENT_TOP_ID);

        if ($hasBottomAds) {
            $this->waitUntilUnitIsPresent($webDriver, self::CONTAINER_ELEMENT_BOTTOM_ID);
        }

        // Wait a little bit more to make sure Bing Ads is shown
        usleep(500000);
    }

    private function waitUntilUnitIsPresent(WebDriverInterface $webDriver, string $containerElementId): void
    {
        // Wait until container element received CSS loaded/empty modifiers which is added by our Bing Ads JavaScript code
        $webDriver->waitUntilPresent(
            WebDriverBy::cssSelector(
                sprintf('#%1$s.ba-results--loaded, #%1$s.ba-results--empty', $containerElementId),
            ),
        );
    }

    public function expectBingAds(WebDriverInterface $webDriver, bool $bingAdsEnabled): void
    {
        $scriptElements = $webDriver->getRemote()->findElements(
            WebDriverBy::cssSelector('script[src="https://msadsscale.microsoft.com/bingads/searchads.js"]'),
        );
        $hasSearchAdsScript = count($scriptElements) === 1;
        $hasSearchAdsFunction = $this->htmlDocumentHelper->hasContentInBody(
            $webDriver,
            'searchAds(',
        );

        TestCase::assertSame(
            $bingAdsEnabled,
            $hasSearchAdsScript,
            sprintf('Bing Ads `bingads/searchads.js` is%s expected', $bingAdsEnabled ? '' : ' not'),
        );
        TestCase::assertSame(
            $bingAdsEnabled,
            $hasSearchAdsFunction,
            sprintf('Bing Ads `searchAds` JavaScript function is%s expected', $bingAdsEnabled ? '' : ' not'),
        );

        if (!$bingAdsEnabled) {
            return;
        }

        // Assert ad test setting value
        // Cannot determine exact settings in different environment, assume ad test on for develop and off for production
        $hasAdTestValue = $this->htmlDocumentHelper->hasContentInBody(
            $webDriver,
            sprintf(
                '"testMode": "%s"',
                $this->developHostHelper->hasDevVmName() ? 'On' : 'Off',
            ),
        );
        TestCase::assertTrue(
            $hasAdTestValue,
            'Bing Ads `testMode` parameter does not match',
        );

        $this->removeAdContainerCssModifiers($webDriver);
        $this->hideHasBingAdsComponent($webDriver);
    }

    /**
     * Mask the display of Bing Ads
     */
    public function maskBingAds(WebDriverInterface $webDriver, bool $hasBottomAds): void
    {
        if (!FrontendTestSettings::ENABLE_EXTERNAL_SERVICE_MASKING) {
            return;
        }

        $this->htmlDocumentHelper->maskElementById(
            $webDriver,
            self::CONTAINER_ELEMENT_TOP_ID,
            'Bing Ads',
            true,
        );

        if ($hasBottomAds) {
            $this->htmlDocumentHelper->maskElementById(
                $webDriver,
                self::CONTAINER_ELEMENT_BOTTOM_ID,
                'Bing Ads',
                true,
            );
        }
    }

    private function removeAdContainerCssModifiers(WebDriverInterface $webDriver): void
    {
        $webDriver->getRemote()->executeScript(
            <<<JS
                document.querySelectorAll('.ba-results.csa--loaded, .ba-results.csa--empty').forEach((element) =>
                    element.classList.remove('csa--loaded', 'csa--empty')
                );
            JS,
        );
    }

    private function hideHasBingAdsComponent(WebDriverInterface $webDriver): void
    {
        $webDriver->getRemote()->executeScript(
            <<<JS
                document.querySelectorAll('.component.has-bing-ads').forEach((element) =>
                    element.style.display = 'none'
                );
            JS,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

readonly class ExceptionHandlerHelper
{
    public function __construct(private LoggerInterface $logger)
    {
    }

    /**
     * @return bool wether message is handled
     */
    public function handle(\Throwable $exception, string $whileMessage, bool $autoMarkTestAsSkipped = true): bool
    {
        $exceptionMessage = $exception->getMessage();

        // Issues below are connection issues, see the docs for more info
        $ignoredMessages = [
            // This is a generic DNS or connection issue
            'Could not resolve host',
            'Operation timed out after',
            // Issues below are connection issues which specifically happens with the devices platform
            'Unable to find the session info for particular sessionId',
            'Error loading document, location is "chrome-error://chromewebdata/"',
            'Error loading document, location is "about:blank"',
        ];

        foreach ($ignoredMessages as $ignoredMessage) {
            if (str_contains($exceptionMessage, $ignoredMessage)) {
                // These messages do not have to trigger a test failure because they say nothing about the page being tested
                $this->logger->notice(
                    sprintf('Caught %s while %s: {message}', $exception::class, $whileMessage),
                    [
                        'message'   => $exceptionMessage,
                        'exception' => $exception,
                    ],
                );

                if ($autoMarkTestAsSkipped) {
                    // Prevent failing the test
                    TestCase::markTestSkipped();
                }

                return true;
            }
        }

        return false;
    }
}

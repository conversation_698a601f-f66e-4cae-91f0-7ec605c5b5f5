<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Helper;

use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\FrontendTestSettings;

class GoogleRelatedTermsHelper
{
    private const string CONTAINER_ELEMENT_ID = 'csa-related';

    public function __construct(
        private readonly HtmlDocumentHelper $htmlDocumentHelper,
        private readonly GoogleCsaContainerAffixHelperInterface $googleCsaContainerAffixHelper
    )
    {
    }

    public function waitUntilLoaded(WebDriverInterface $webDriver): void
    {
        // Wait for Google Related Terms to become visible
        $webDriver->waitUntilVisible(
            WebDriverBy::cssSelector(
                sprintf(
                    'div[id^="%s-"] iframe',
                    $this->googleCsaContainerAffixHelper->getAffixedContainer(self::CONTAINER_ELEMENT_ID),
                ),
            ),
        );
    }

    public function expectGoogleRelatedTerms(
        WebDriverInterface $webDriver,
        bool $googleRelatedTermsEnabled
    ): void
    {
        $cssSelector = WebDriverBy::cssSelector(
            sprintf(
                'div[id^="%s-"] iframe',
                $this->googleCsaContainerAffixHelper->getAffixedContainer(self::CONTAINER_ELEMENT_ID),
            ),
        );

        try {
            $googleRelatedTermsIframe = $webDriver->getRemote()->findElement($cssSelector);
        } catch (NoSuchElementException) {
            $googleRelatedTermsIframe = null;
        }

        if (!$googleRelatedTermsEnabled) {
            TestCase::assertEmpty($googleRelatedTermsIframe, 'Google Related Terms are not expected');

            return;
        }

        TestCase::assertNotEmpty($googleRelatedTermsIframe, 'Google Related Terms are expected');

        $googleCsa = '_googCsa(\'relatedsearch\'';
        $escapedString = str_replace('\'', '\\\'', $googleCsa);

        // Assert script is in header (also prevents complete source code in logs)
        $script = sprintf('return document.head.innerHTML.indexOf(\'%s\');', $escapedString);

        TestCase::assertNotEquals(
            -1,
            (int)$webDriver->getRemote()->executeScript($script),
            sprintf('Google Ads parameters do not match: %s', $googleCsa),
        );
    }

    /**
     * Mask the display of Google Related Terms
     */
    public function maskGoogleRelatedTerms(WebDriverInterface $webDriver): void
    {
        if (!FrontendTestSettings::ENABLE_EXTERNAL_SERVICE_MASKING) {
            return;
        }

        // Fully mask any GRT units
        $targetElementSelector = sprintf(
            'div[id^="%s-"]',
            $this->googleCsaContainerAffixHelper->getAffixedContainer(self::CONTAINER_ELEMENT_ID),
        );
        $this->htmlDocumentHelper->maskAllElementsByCssSelector(
            webDriver            : $webDriver,
            targetElementSelector: $targetElementSelector,
            maskLabel            : 'Google Related Terms',
        );

        // Always show related terms fallback
        $webDriver->getRemote()->executeScript(
            <<<JS
                document.querySelectorAll('[class*="related-terms--fallback"]').forEach(function (element) {
                    element.classList.remove('component--hidden');
                });
            JS,
        );
    }
}

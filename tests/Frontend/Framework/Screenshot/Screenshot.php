<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Screenshot;

readonly class Screenshot
{
    /**
     * @param ScreenshotIgnoreArea[] $ignoreAreas
     */
    public function __construct(
        private ScreenshotVariant $actualVariant,
        private ScreenshotVariant $expectedVariant,
        private ScreenshotVariant $comparisonVariant,
        private array $ignoreAreas,
        private float $allowedDifferencePercentage
    )
    {
    }

    public function getActualVariant(): ScreenshotVariant
    {
        return $this->actualVariant;
    }

    public function getExpectedVariant(): ScreenshotVariant
    {
        return $this->expectedVariant;
    }

    public function getComparisonVariant(): ScreenshotVariant
    {
        return $this->comparisonVariant;
    }

    public function hasIgnoreAreas(): bool
    {
        return $this->ignoreAreas !== [];
    }

    /**
     * @return ScreenshotIgnoreArea[]
     */
    public function getIgnoreAreas(): array
    {
        return $this->ignoreAreas;
    }

    public function getAllowedDifferencePercentage(): float
    {
        return $this->allowedDifferencePercentage;
    }

    public function updateExpectedVariant(): void
    {
        $this->getActualVariant()->getFile()->copyTo(
            $this->getExpectedVariant()->getFile()->getFilePath(),
        );
    }
}

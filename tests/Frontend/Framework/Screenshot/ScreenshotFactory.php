<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\Screenshot;

use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;
use Tests\Frontend\TestCase\TestCaseInterface;
use Visymo\Filesystem\Directory\DirectoryFactory;
use Visymo\Filesystem\File\FileFactory;

readonly class ScreenshotFactory
{
    public function __construct(
        private string $resourcesDir,
        private string $actualScreenshotsFolder,
        private string $expectedScreenshotsFolder,
        private string $comparisonScreenshotsFolder,
        private DirectoryFactory $directoryFactory,
        private FileFactory $fileFactory
    )
    {
    }

    public function create(
        TestCaseInterface $testCase,
        WebDriverCapabilitiesInterface $webDriverCapabilities,
        ?string $filenameSuffix = null,
        ?float $allowedScreenshotDifference = null
    ): Screenshot
    {
        $capabilitiesKey = $webDriverCapabilities->getLabelAsKey();
        $fileBasename = $testCase->getFullName().$filenameSuffix;

        return new Screenshot(
            $this->createScreenshotVariant(
                $this->actualScreenshotsFolder,
                $capabilitiesKey,
                $fileBasename,
            ),
            $this->createScreenshotVariant(
                $this->expectedScreenshotsFolder,
                $capabilitiesKey,
                $fileBasename,
            ),
            $this->createScreenshotVariant(
                $this->comparisonScreenshotsFolder,
                $capabilitiesKey,
                $fileBasename,
            ),
            $webDriverCapabilities->getScreenshotIgnoreAreas(),
            (float)max($allowedScreenshotDifference, $webDriverCapabilities->getAllowedScreenshotDifference()),
        );
    }

    private function createScreenshotVariant(
        string $resourceFolder,
        string $screenshotFolder,
        string $fileBasename
    ): ScreenshotVariant
    {
        // determine and create resource directory
        $absoluteResourceDirectory = sprintf('%s/%s', $this->resourcesDir, $resourceFolder);

        $this->directoryFactory->create($absoluteResourceDirectory)->create();

        // determine and create screenshot directory
        $relativeScreenshotDirectory = sprintf('%s/%s', $resourceFolder, $screenshotFolder);
        $absoluteScreenshotDirectory = sprintf('%s/%s', $this->resourcesDir, $relativeScreenshotDirectory);

        $this->directoryFactory->create($absoluteScreenshotDirectory)->create();

        // determine relative screenshot filename
        $relativeScreenshotPath = sprintf(
            '%s/%s.png',
            $relativeScreenshotDirectory,
            $fileBasename,
        );
        $absoluteScreenshotPath = sprintf('%s/%s', $this->resourcesDir, $relativeScreenshotPath);

        return new ScreenshotVariant(
            $relativeScreenshotPath,
            $this->fileFactory->create(
                $absoluteScreenshotPath,
            ),
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\Browser;

use Facebook\WebDriver\Chrome\ChromeOptions;

final class ChromeBrowser implements BrowserInterface
{
    public function getBrowser(): Browser
    {
        return Browser::CHROME;
    }

    /**
     * @inheritDoc
     */
    public function getCapabilities(): array
    {
        $chromeOptions = new ChromeOptions();

        // Enable automation prevents showing an info bar on the top of the browser
        // @see https://peter.sh/experiments/chromium-command-line-switches/#enable-automation
        $chromeOptions->setExperimentalOption('excludeSwitches', ['enable-automation']);

        return [
            ChromeOptions::CAPABILITY => $chromeOptions,
        ];
    }
}

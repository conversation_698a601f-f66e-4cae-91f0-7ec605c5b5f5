<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\LambdaTest;

use App\Generic\Device\Device;
use Tests\Frontend\Framework\Screenshot\ScreenshotIgnoreArea;
use Tests\Frontend\Framework\WebDriver\Browser\Browser;
use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverResolution;

abstract class AbstractLambdaTestCapabilities implements WebDriverCapabilitiesInterface
{
    protected const string LAMBDA_TEST_HUB_URL_TEMPLATE = 'https://%s:%<EMAIL>/wd/hub';

    protected const array DEFAULT_META_CAPABILITIES = [
        'tunnel'   => true,
        'video'    => true,
        'network'  => true, // capture network logs
        'console'  => true, // capture browser console logs
        'terminal' => true, // capture terminal logs
    ];

    /**
     * @param mixed[]                $capabilities
     * @param ScreenshotIgnoreArea[] $screenshotIgnoreAreas
     */
    protected function __construct(
        private readonly string $hubUrlTemplate,
        private string $label,
        private readonly Browser $browser,
        private readonly Device $device,
        private readonly ?WebDriverResolution $windowResolution,
        private array $capabilities,
        private readonly array $screenshotIgnoreAreas = []
    )
    {
    }

    public function setTestDetails(string $build, string $name): void
    {
        $this->capabilities['build'] = $build;
        $this->capabilities['name'] = $name;
    }

    public function getHubUrl(string $username, string $accessKey): string
    {
        return sprintf($this->hubUrlTemplate, $username, $accessKey);
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): void
    {
        $this->label = $label;
    }

    public function getBrowser(): Browser
    {
        return $this->browser;
    }

    public function getDevice(): Device
    {
        return $this->device;
    }

    public function getWindowResolution(): ?WebDriverResolution
    {
        return $this->windowResolution;
    }

    public function getLabelAsKey(): string
    {
        $key = strtolower($this->getLabel());
        $key = str_replace(' ', '-', $key);
        $key = preg_replace('~([\-]{2,})~', '-', $key);

        return (string)$key;
    }

    /**
     * @inheritDoc
     */
    public function getCapabilities(): array
    {
        return $this->capabilities;
    }

    /**
     * @inheritDoc
     */
    public function getScreenshotIgnoreAreas(): array
    {
        return $this->screenshotIgnoreAreas;
    }

    /**
     * Global allowed difference, preferably below 1% per device
     */
    public function getAllowedScreenshotDifference(): float
    {
        // Allow small difference by default, because font rendering issues occur on every platform now and then
        return 0.10;
    }
}

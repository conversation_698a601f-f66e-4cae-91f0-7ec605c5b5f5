<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\LambdaTest;

use App\Generic\Device\Device;
use Tests\Frontend\Framework\WebDriver\Browser\Browser;
use Tests\Frontend\Framework\WebDriver\WebDriverResolution;

final class LambdaTestDeviceCapabilities extends AbstractLambdaTestCapabilities
{
    public static function createForResponsiveMobile(
        string $operatingSystem,
        Browser $browser,
        string $browserVersion,
        WebDriverResolution $windowResolution
    ): self
    {
        $label = implode(
            ' ',
            [
                Device::MOBILE->value,
                $operatingSystem,
                $browser->value,
                $browserVersion,
                'mobile responsive',
            ],
        );
        $capabilities = array_merge(
            self::DEFAULT_META_CAPABILITIES,
            [
                'browserName'    => $browser->value,
                'browserVersion' => $browserVersion,
                'platformName'   => $operatingSystem,
                'resolution'     => WebDriverResolution::RESOLUTION_1280X1024->value,
            ],
        );

        // Use a small window resolution to simulate a mobile device screen
        return new self(
            hubUrlTemplate  : self::LAMBDA_TEST_HUB_URL_TEMPLATE,
            label           : $label,
            browser         : $browser,
            device          : Device::MOBILE,
            windowResolution: $windowResolution,
            capabilities    : $capabilities,
        );
    }
}

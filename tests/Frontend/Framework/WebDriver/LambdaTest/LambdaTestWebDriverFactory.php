<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver\LambdaTest;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Tests\Frontend\Framework\WebDriver\Browser\Browser;
use Tests\Frontend\Framework\WebDriver\Browser\BrowserInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverFactoryInterface;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;

class LambdaTestWebDriverFactory implements WebDriverFactoryInterface
{
    private const int CONNECTION_TIMEOUT = 300000;   // milliseconds
    private const int REQUEST_TIMEOUT    = 300000;   // milliseconds
    private const int PAGE_LOAD_TIMEOUT  = 300;      // seconds

    /** @var array<string, BrowserInterface> */
    private array $browsers = [];

    /**
     * @param \Traversable<BrowserInterface> $browsers
     */
    public function __construct(
        private readonly string $lambdaTestUsername,
        private readonly string $lambdaTestAccessKey,
        private readonly string $resourcesDir,
        iterable $browsers
    )
    {
        foreach ($browsers as $browser) {
            $this->browsers[$browser->getBrowser()->value] = $browser;
        }
    }

    public function create(WebDriverCapabilitiesInterface $webDriverCapabilities): WebDriverInterface
    {
        $desiredCapabilities = [
            ...$webDriverCapabilities->getCapabilities(),
            ...$this->getBrowserCapabilities($webDriverCapabilities->getBrowser()),
        ];

        $remoteWebDriver = RemoteWebDriver::create(
            $webDriverCapabilities->getHubUrl($this->lambdaTestUsername, $this->lambdaTestAccessKey),
            $desiredCapabilities,
            self::CONNECTION_TIMEOUT,
            self::REQUEST_TIMEOUT,
        );

        // Set timeout for page loading
        $remoteWebDriver->manage()->timeouts()->pageLoadTimeout(self::PAGE_LOAD_TIMEOUT);

        return new LambdaTestWebDriver(
            $remoteWebDriver,
            $webDriverCapabilities,
            $this->resourcesDir,
        );
    }

    /**
     * @return array<string, mixed>
     */
    private function getBrowserCapabilities(Browser $browser): array
    {
        if (isset($this->browsers[$browser->value])) {
            return $this->browsers[$browser->value]->getCapabilities();
        }

        return [];
    }
}

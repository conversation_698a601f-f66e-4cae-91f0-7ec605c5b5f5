<?php

declare(strict_types=1);

namespace Tests\Frontend\Framework\WebDriver;

use App\Generic\Device\Device;
use Tests\Frontend\Framework\Screenshot\ScreenshotIgnoreArea;
use Tests\Frontend\Framework\WebDriver\Browser\Browser;

interface WebDriverCapabilitiesInterface
{
    /**
     * Add test details to capabilities model
     */
    public function setTestDetails(string $build, string $name): void;

    public function getHubUrl(string $username, string $accessKey): string;

    public function getDevice(): Device;

    public function getWindowResolution(): ?WebDriverResolution;

    public function getLabel(): string;

    public function setLabel(string $label): void;

    public function getBrowser(): Browser;

    /**
     * Get capabilities as key to use in an URL or filename
     */
    public function getLabelAsKey(): string;

    /**
     * @return mixed[]
     */
    public function getCapabilities(): array;

    /**
     * Ignore certain areas of the screenshot that may fluctuate like time and battery status for iPhones
     *
     * @return ScreenshotIgnoreArea[]
     */
    public function getScreenshotIgnoreAreas(): array;

    /**
     * Global allowed difference, preferably below 1% per device
     */
    public function getAllowedScreenshotDifference(): float;
}

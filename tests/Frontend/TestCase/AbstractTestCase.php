<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase;

use App\Generic\Device\Device;
use Tests\Frontend\Framework\WebDriver\WebDriverCapabilitiesInterface;

abstract class AbstractTestCase implements TestCaseInterface
{
    private ?int $windowWidthOverride = null;

    /**
     * @param int[] $responsiveBreakpoints
     *
     * @see WebDriverCapabilitiesInterface for platform options
     */
    public function __construct(
        private readonly string $brandSlug,
        private readonly string $name,
        private readonly Device $device,
        private readonly string $url,
        private readonly array $responsiveBreakpoints = [],
        private readonly ?float $allowedScreenshotDifference = null,
        private readonly bool $createScreenshotFromBottomOfPage = true
    )
    {
    }

    abstract protected function getTestCaseSlug(): string;

    public function getBrandSlug(): string
    {
        return $this->brandSlug;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getFullName(): string
    {
        return sprintf(
            '%s-%s-%s-%s',
            $this->brandSlug,
            $this->getTestCaseSlug(),
            $this->name,
            $this->getDevice()->value,
        );
    }

    public function getDevice(): Device
    {
        return $this->device;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @return int[]
     */
    public function getResponsiveBreakpoints(): array
    {
        return $this->responsiveBreakpoints;
    }

    public function getAllowedScreenshotDifference(): ?float
    {
        return $this->allowedScreenshotDifference;
    }

    public function isCreateScreenshotFromBottomOfPage(): bool
    {
        return $this->createScreenshotFromBottomOfPage;
    }

    public function setWindowWidthOverride(int $windowWidthOverride): void
    {
        $this->windowWidthOverride = $windowWidthOverride;
    }

    public function getWindowWidthOverride(): ?int
    {
        return $this->windowWidthOverride;
    }
}

<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\DisplaySearchRelated;

use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Tracking\Entry\Request\TrackingEntryRequestInterface;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\Helper\GoogleAdManagerHelper;
use Tests\Frontend\Framework\Helper\GoogleRelatedTermsHelper;
use Tests\Frontend\Framework\Helper\ScreenshotHelper;
use Tests\Frontend\Framework\Helper\TestCaseAssertionHelper;
use Tests\Frontend\Framework\Helper\TestEnvironmentHelper;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\TestCase\TestCaseInterface;
use Tests\Frontend\TestCase\TestRunnerInterface;

final readonly class DisplaySearchRelatedTestRunner implements TestRunnerInterface
{
    public function __construct(
        private TestEnvironmentHelper $testEnvironmentHelper,
        private ScreenshotHelper $screenshotHelper,
        private GoogleRelatedTermsHelper $googleRelatedTermsHelper,
        private GoogleAdManagerHelper $googleAdManagerHelper,
        private TestCaseAssertionHelper $testCaseAssertionHelper
    )
    {
    }

    public function run(TestCaseInterface $testCase, WebDriverInterface $webDriver): void
    {
        if (!$testCase instanceof DisplaySearchRelatedTestCase) {
            throw new \RuntimeException(
                sprintf(
                    'This test runner only supports %s',
                    DisplaySearchRelatedTestCase::class,
                ),
            );
        }

        // Wait for processing request
        $webDriver->getUrlAndWaitUntilDocumentReady(
            $this->testEnvironmentHelper->addDevelopToUrl($testCase->getUrl()),
        );

        // Check if query is included in page title
        if ($testCase->expectsQueryInTitle) {
            TestCase::assertStringContainsStringIgnoringCase(
                $testCase->query,
                $webDriver->getRemote()->getTitle(),
                'expecting query in webpage title',
            );
        } else {
            TestCase::assertStringNotContainsStringIgnoringCase(
                $testCase->query,
                $webDriver->getRemote()->getTitle(),
                'not expecting query in webpage title',
            );
        }

        try {
            // Wait for search results (after ads are shown with a timeout)
            $webDriver->waitUntilVisible(WebDriverBy::cssSelector('[id^="delayed-container-"]'));
        } catch (NoSuchElementException) {
            // delayed container is not always present
        }

        if ($testCase->hasGoogleRelatedTerms) {
            $this->googleRelatedTermsHelper->waitUntilLoaded($webDriver);
            $this->googleRelatedTermsHelper->maskGoogleRelatedTerms($webDriver);
        }

        // Google Related Terms
        $this->googleRelatedTermsHelper->expectGoogleRelatedTerms($webDriver, $testCase->hasGoogleRelatedTerms);

        // Google AdManager
        $this->googleAdManagerHelper->expectGoogleAdManager($webDriver, $testCase->hasGoogleAdManager);

        if ($testCase->hasGoogleAdManager) {
            $this->googleAdManagerHelper->maskAdManagerAds($webDriver);
        }

        // Take and compare screenshots
        $this->screenshotHelper->createAndCompareScreenshots($testCase, $webDriver);

        if ($testCase->hasGoogleRelatedTermsWithFallback) {
            // Find first related terms default link (DSRW), this is the fallback for the Google related terms.
            $relatedTermsLinks = $webDriver->getRemote()->findElements(
                WebDriverBy::cssSelector('[class*="fallback-csa-related-"].related-terms a.related-terms__link'),
            );

            TestCase::assertNotEmpty($relatedTermsLinks, 'Expect related terms link');

            $relatedTermsLink = current($relatedTermsLinks);

            $relatedTermsUrl = (string)$relatedTermsLink->getAttribute('href');

            $this->testCaseAssertionHelper->assertNotEmptyUrlParameters(
                $relatedTermsUrl,
                [
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_ZONE,
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_LINK_INDEX,
                    TrackingEntryRequestInterface::PARAMETER_SERIALIZED_TRACKING_ENTRY,
                ],
            );
        }

        if ($testCase->hasVisymoRelatedTerms) {
            // Find first related terms search link
            // we want the second block, the first is fallback for the Google related terms.
            $relatedTermsSearchLinks = $webDriver->getRemote()->findElements(
                WebDriverBy::cssSelector(
                    '.related-terms:not([class*="fallback-csa-related-"]) .related-terms__columns a.related-terms__link',
                ),
            );

            TestCase::assertNotEmpty($relatedTermsSearchLinks, 'Expect related terms search link');

            $relatedTermsSearchLink = current($relatedTermsSearchLinks);

            $relatedTermsSearchUrl = (string)$relatedTermsSearchLink->getAttribute('href');

            $this->testCaseAssertionHelper->assertNotEmptyUrlParameters(
                $relatedTermsSearchUrl,
                [
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_ZONE,
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_LINK_INDEX,
                    TrackingEntryRequestInterface::PARAMETER_SERIALIZED_TRACKING_ENTRY,
                ],
            );
        }
    }
}

<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase;

use App\Generic\Device\Device;

interface TestCaseInterface
{
    public function getBrandSlug(): string;

    public function getName(): string;

    public function getFullName(): string;

    public function getDevice(): Device;

    public function getUrl(): string;

    /**
     * @return int[]
     */
    public function getResponsiveBreakpoints(): array;

    public function getAllowedScreenshotDifference(): ?float;

    public function isCreateScreenshotFromBottomOfPage(): bool;

    public function getTestRunner(): string;

    public function setWindowWidthOverride(int $windowWidthOverride): void;

    public function getWindowWidthOverride(): ?int;
}

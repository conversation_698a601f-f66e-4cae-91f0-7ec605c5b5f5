<?php

declare(strict_types=1);

namespace Tests\Frontend\TestCase\WebSearch;

use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Tracking\Entry\Request\TrackingEntryRequestInterface;
use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\WebDriverBy;
use PHPUnit\Framework\TestCase;
use Tests\Frontend\Framework\Helper\GoogleAdsHelper;
use Tests\Frontend\Framework\Helper\GoogleRelatedTermsHelper;
use Tests\Frontend\Framework\Helper\ScreenshotHelper;
use Tests\Frontend\Framework\Helper\TestCaseAssertionHelper;
use Tests\Frontend\Framework\Helper\TestEnvironmentHelper;
use Tests\Frontend\Framework\WebDriver\WebDriverInterface;
use Tests\Frontend\TestCase\TestCaseInterface;
use Tests\Frontend\TestCase\TestRunnerInterface;

final readonly class WebSearchTestRunner implements TestRunnerInterface
{
    public function __construct(
        private TestEnvironmentHelper $testEnvironmentHelper,
        private ScreenshotHelper $screenshotHelper,
        private GoogleAdsHelper $googleAdsHelper,
        private GoogleRelatedTermsHelper $googleRelatedTermsHelper,
        private TestCaseAssertionHelper $testCaseAssertionHelper
    )
    {
    }

    public function run(TestCaseInterface $testCase, WebDriverInterface $webDriver): void
    {
        if (!$testCase instanceof WebSearchTestCase) {
            throw new \RuntimeException(
                sprintf(
                    'This test runner only supports %s',
                    WebSearchTestCase::class,
                ),
            );
        }

        // Wait for processing request
        $webDriver->getUrlAndWaitUntilDocumentReady(
            $this->testEnvironmentHelper->addDevelopToUrl($testCase->getUrl()),
        );

        // Check if query is included in page title
        TestCase::assertStringContainsStringIgnoringCase(
            $testCase->query,
            $webDriver->getRemote()->getTitle(),
        );

        try {
            // Wait for search results (after ads are shown with a timeout)
            $webDriver->waitUntilVisible(WebDriverBy::cssSelector('[id^="delayed-container-"]'));
        } catch (NoSuchElementException) {
            // delayed container is not always present
        }

        if ($testCase->hasGoogleAds) {
            $this->googleAdsHelper->waitUntilLoaded($webDriver, true);
            $this->googleAdsHelper->maskGoogleAds($webDriver, true);
        }

        // Google Ads
        $this->googleAdsHelper->expectGoogleAds($webDriver, $testCase->hasGoogleAds);

        if ($testCase->hasGoogleRelatedTerms) {
            $this->googleRelatedTermsHelper->waitUntilLoaded($webDriver);
            $this->googleRelatedTermsHelper->maskGoogleRelatedTerms($webDriver);
        }

        // Google Related Terms
        $this->googleRelatedTermsHelper->expectGoogleRelatedTerms(
            webDriver                : $webDriver,
            googleRelatedTermsEnabled: $testCase->hasGoogleRelatedTerms,
        );

        // Take and compare screenshots
        $screenshotsAreEqual = $this->screenshotHelper->createAndCompareScreenshots($testCase, $webDriver);

        if ($testCase->hasRelated) {
            // Find first related terms link
            $relatedTermsLinks = $webDriver->getRemote()->findElements(
                WebDriverBy::cssSelector('a.related-terms__link'),
            );

            TestCase::assertNotEmpty(
                $relatedTermsLinks,
                sprintf(
                    'Expect related terms links%s',
                    !$screenshotsAreEqual ? ': could be caused by screenshot difference upload' : '',
                ),
            );

            $relatedTermsLink = current($relatedTermsLinks);

            $relatedTermsUrl = (string)$relatedTermsLink->getAttribute('href');

            $this->testCaseAssertionHelper->assertNotEmptyUrlParameters(
                $relatedTermsUrl,
                [
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_ZONE,
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_LINK_INDEX,
                    TrackingEntryRequestInterface::PARAMETER_SERIALIZED_TRACKING_ENTRY,
                ],
            );
        }
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration;

use App\BrandOverride\BrandOverrideModuleState;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\BrandOverride\Helper\BrandOverrideTestHelper;
use Tests\Integration\Helper\BrandSettingsTestHelper;
use Tests\Integration\Helper\JsonTemplateTestHelper;
use Tests\Integration\Helper\TrackingEntryTestHelper;
use Tests\Integration\Helper\WebsiteSettingsTestHelper;
use Tests\Stub\Stubs;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Loader\FilesystemLoader;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPage\Response\ContentPageResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPageCategories\Response\ContentPageCategoriesResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPageCategory\Response\ContentPageCategoryResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\SearchResponseInterface;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;

abstract class AbstractBrandWebsitesIntegrationTestCase extends AbstractSymfonyIntegrationTest
{
    private Stubs $stubs;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->stubs()->compositeSearchApiClient()
            ->setSearchResponse(
                $this->createConfiguredMock(
                    SearchResponseInterface::class,
                    [
                        'getContentPage'           => ContentPageResponseContext::createEmpty(),
                        'getContentPageCategory'   => ContentPageCategoryResponseContext::createEmpty(),
                        'getContentPageCategories' => ContentPageCategoriesResponseContext::createEmpty(),
                    ],
                ),
            );
    }

    public static function getContainer(): Container
    {
        return parent::getContainer();
    }

    public static function brandOverrideTestHelper(): BrandOverrideTestHelper
    {
        /** @var BrandOverrideTestHelper $brandOverrideTestHelper */
        $brandOverrideTestHelper = self::getContainer()->get(BrandOverrideTestHelper::class);

        return $brandOverrideTestHelper;
    }

    public static function brandOverrideModuleState(): BrandOverrideModuleState
    {
        /** @var BrandOverrideModuleState $brandOverrideModuleState */
        $brandOverrideModuleState = self::getContainer()->get(BrandOverrideModuleState::class);

        return $brandOverrideModuleState;
    }

    public static function jsonTemplateTestHelper(): JsonTemplateTestHelper
    {
        /** @var JsonTemplateTestHelper $jsonTemplateTestHelper */
        $jsonTemplateTestHelper = self::getContainer()->get(JsonTemplateTestHelper::class);

        return $jsonTemplateTestHelper;
    }

    protected static function addBrandJsonTemplateLocation(): void
    {
        /** @var Environment $twig */
        $twig = self::getContainer()->get(Environment::class);

        try {
            if ($twig->getLoader() instanceof FilesystemLoader) {
                /** @var string $projectDir */
                $projectDir = self::getContainer()->getParameter('kernel.project_dir');
                $twig->getLoader()->prependPath(
                    sprintf('%s/brands', $projectDir),
                    'themeJson',
                );
            }
        } catch (LoaderError) {
            // Ignore
        }
    }

    public static function websiteSettingsTestHelper(): WebsiteSettingsTestHelper
    {
        /** @var WebsiteSettingsTestHelper $websiteSettingsTestHelper */
        $websiteSettingsTestHelper = self::getContainer()->get(WebsiteSettingsTestHelper::class);

        return $websiteSettingsTestHelper;
    }

    public static function brandSettingsTestHelper(): BrandSettingsTestHelper
    {
        /** @var BrandSettingsTestHelper $brandSettingsTestHelper */
        $brandSettingsTestHelper = self::getContainer()->get(BrandSettingsTestHelper::class);

        return $brandSettingsTestHelper;
    }

    public static function trackingEntryTestHelper(): TrackingEntryTestHelper
    {
        /** @var TrackingEntryTestHelper $trackingEntryTestHelper */
        $trackingEntryTestHelper = self::getContainer()->get(TrackingEntryTestHelper::class);

        return $trackingEntryTestHelper;
    }

    public function stubs(): Stubs
    {
        if (!isset($this->stubs)) {
            $this->stubs = new Stubs(self::getContainer());
        }

        return $this->stubs;
    }

    /**
     * @param array<string, mixed> $parameters
     * @param array<string, mixed> $headers
     */
    public static function createRequest(
        string $path,
        array $parameters = [],
        array $headers = []
    ): Request
    {
        $url = sprintf(
            '%s%s%s',
            $path,
            $parameters !== [] ? '?' : '',
            http_build_query($parameters),
        );
        $url = self::websiteSettingsTestHelper()->prependRandomDomainToUrl($url);

        $request = Request::create(
            $url,
            Request::METHOD_GET,
            [],
            [],
            [],
            $headers,
        );
        // also manually override headers, otherwise Symfony sometimes chooses to ignore them
        foreach ($headers as $key => $value) {
            $request->headers->set($key, $value);
        }

        return $request;
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration;

use App\Http\Request\RequestInterface as BrandWebsiteRequestInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;

abstract class AbstractRequestIntegrationTestCase extends AbstractBrandWebsitesIntegrationTestCase
{
    private RouterInterface $router;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stubs()
            ->moduleSettings()
            ->getWebSearch()
            ->setEnabled()
            ->setStyleId(**********)
            ->create();

        /** @var RouterInterface $router */
        $router = self::getContainer()->get(RouterInterface::class);
        $this->router = $router;
    }

    /**
     * @param array<string, mixed> $expectedValues
     */
    protected function assertRequest(
        string $url,
        ?callable $symfonyRequestCallback,
        BrandWebsiteRequestInterface $request,
        array $expectedValues
    ): void
    {
        $this->setMainRequestWithUrl($url, $symfonyRequestCallback);

        $actualValues = $request->toArray();

        ksort($expectedValues);
        ksort($actualValues);

        self::assertSame($expectedValues, $actualValues);
    }

    private function setMainRequestWithUrl(string $url, ?callable $symfonyRequestCallback): void
    {
        $request = Request::create($url);

        if ($symfonyRequestCallback !== null) {
            $symfonyRequestCallback($request);
        }

        $this->setRequest($request);

        // Add attributes to request, extracted from the URL
        $request->attributes->add(
            $this->router->match($request->getPathInfo()),
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\App\EventSubscriber;

use App\App\EventSubscriber\AppEventSubscriber;
use App\App\Model\AppFactory;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

final class AppEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            AppEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeaders(): void
    {
        $eventMock = $this->createMock(RenderTemplateHeadersEvent::class);
        $eventSubscriber = new AppEventSubscriber(
            self::getContainer()->get(AppFactory::class),
            $this->createMock(Environment::class)
        );

        $eventSubscriber->renderTemplateHeaders($eventMock);

        $eventMock->expects($this->once());
    }
}

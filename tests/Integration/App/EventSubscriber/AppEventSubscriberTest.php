<?php

declare(strict_types=1);

namespace Tests\Integration\App\EventSubscriber;

use App\App\EventSubscriber\AppEventSubscriber;
use App\App\Model\AppFactory;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

final class AppEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            AppEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testRenderTemplateHeaders(): void
    {
        $request = self::createRequest('/');
        $this->setRequest($request);

        $event = new RenderTemplateHeadersEvent();
        $eventSubscriber = new AppEventSubscriber(
            self::getContainer()->get(AppFactory::class),
            self::getContainer()->get(Environment::class)
        );

        $eventSubscriber->renderTemplateHeaders($event);

        self::assertNotEmpty($event->getItems());
    }
}

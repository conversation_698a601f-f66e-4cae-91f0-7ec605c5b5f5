<?php

declare(strict_types=1);

namespace Tests\Integration\BingAds\EventSubscriber;

use App\BingAds\EventSubscriber\InjectBingAdsScriptsEventSubscriber;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class InjectBingAdsScriptsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectBingAdsScriptsEventSubscriber::class,
            'renderTemplateHeaders',
        );
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateFootersEvent::NAME,
            InjectBingAdsScriptsEventSubscriber::class,
            'renderTemplateFooters',
        );
    }
}

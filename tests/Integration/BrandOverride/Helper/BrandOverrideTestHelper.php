<?php

declare(strict_types=1);

namespace Tests\Integration\BrandOverride\Helper;

use App\BrandOverride\BrandOverrideModuleState;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;

final readonly class BrandOverrideTestHelper
{
    /**
     * @param array<string, mixed[]> $brandOverrideConfig
     */
    public function __construct(
        private array $brandOverrideConfig,
        private DomainToBrandMapReader $domainToBrandMapReader,
        private BrandOverrideModuleState $brandOverrideModuleState
    )
    {
    }

    /**
     * @return string[]
     */
    public function getBrands(): array
    {
        return $this->domainToBrandMapReader->getBrands();
    }

    public function getRandomBrand(): string
    {
        return array_rand(array_flip($this->getBrands()));
    }

    /**
     * @return string[]
     */
    public function getBrandsWithOverride(): array
    {
        return array_keys($this->brandOverrideConfig);
    }

    /**
     * @return string[]
     */
    public function getBrandsWithoutOverride(): array
    {
        // Exclude brands with override
        return array_diff(
            $this->domainToBrandMapReader->getBrands(),
            $this->getBrandsWithOverride(),
        );
    }

    public function hasOverrideForBrand(string $brandSlug): bool
    {
        return ($this->brandOverrideConfig[$brandSlug] ?? null) !== null;
    }

    /**
     * @return string[]
     */
    public function getBrandsWithOverrideForModule(string $moduleName): array
    {
        $brands = [];

        foreach ($this->getBrandsWithOverride() as $brandSlug) {
            $moduleConfig = $this->brandOverrideModuleState->getModuleConfigOverrideForBrand(
                $moduleName,
                $brandSlug,
            );

            if ($moduleConfig !== null) {
                $brands[] = $brandSlug;
            }
        }

        return $brands;
    }

    /**
     * @return string[]
     */
    public function getBrandsWithoutOverrideForModule(string $moduleName): array
    {
        // Exclude brands with override
        return array_diff(
            $this->domainToBrandMapReader->getBrands(),
            $this->getBrandsWithOverrideForModule($moduleName),
        );
    }
}

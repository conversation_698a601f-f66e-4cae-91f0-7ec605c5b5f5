<?php

declare(strict_types=1);

namespace Tests\Integration\ContentPage\Url;

use App\ContentPage\Url\ContentPageImageUrlGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageImage;

class ContentPageImageUrlGeneratorTest extends AbstractBrandWebsitesIntegrationTestCase
{
    /**
     * @return mixed[]
     */
    public static function generateDataProvider(): array
    {
        $contentPageImageOne = ContentPageImage::create(
            1,
            432432,
            'My image',
            'my-image',
            'm/y/i',
        );
        $contentPageImageTwo = ContentPageImage::create(
            2,
            6834,
            'My image two',
            'my-image-two',
            '2/m/y',
        );

        return [
            'format_c368x207' => [
                'contentPageImage' => $contentPageImageOne,
                'format'           => 'c368x207',
                'expectedUrl'      => 'https://imagedelivery.net/m/y/i/c368x207',
            ],
            'format_c80x80'   => [
                'contentPageImage' => $contentPageImageTwo,
                'format'           => 'c80x80',
                'expectedUrl'      => 'https://imagedelivery.net/2/m/y/c80x80',
            ],
        ];
    }

    #[DataProvider('generateDataProvider')]
    public function testGenerate(
        ContentPageImage $contentPageImage,
        string $format,
        string $expectedUrl
    ): void
    {
        /** @var ContentPageImageUrlGenerator $contentPageImageUrlGenerator */
        $contentPageImageUrlGenerator = self::getContainer()->get(ContentPageImageUrlGenerator::class);

        $actualUrl = $contentPageImageUrlGenerator->generate(
            $contentPageImage,
            $format,
        );

        self::assertSame($expectedUrl, $actualUrl);
    }
}

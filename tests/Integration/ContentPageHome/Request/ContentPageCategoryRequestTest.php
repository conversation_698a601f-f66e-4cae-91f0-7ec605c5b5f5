<?php

declare(strict_types=1);

namespace Tests\Integration\ContentPageHome\Request;

use App\ContentPageHome\Request\ContentPageCategoryRequestInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use Tests\Integration\AbstractRequestIntegrationTestCase;
use Tests\Stub\ContentPageHome\Settings\ContentPageHomeSettingsStubBuilder;

class ContentPageCategoryRequestTest extends AbstractRequestIntegrationTestCase
{
    private ContentPageHomeSettingsStubBuilder $contentPageHomeSettingsStubBuilder;

    private ContentPageCategoryRequestInterface $contentPageCategoryRequest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->contentPageHomeSettingsStubBuilder = $this->stubs()->moduleSettings()->getContentPageHome();

        /** @var ContentPageCategoryRequestInterface $contentPageCategoryRequest */
        $contentPageCategoryRequest = self::getContainer()->get(ContentPageCategoryRequestInterface::class);
        $this->contentPageCategoryRequest = $contentPageCategoryRequest;
    }

    /**
     * @return mixed[]
     */
    public static function requestDataProvider(): array
    {
        $defaultExpectedValues = [
            ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_PUBLIC_ID => null,
            ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_SLUG      => null,
        ];

        return [
            'empty'                  => [
                'contentPageHomeEnabled' => null,
                'url'                    => '/',
                'expectedValues'         => $defaultExpectedValues,
                'expectedException'      => null,
            ],
            'category page enabled'  => [
                'contentPageHomeEnabled' => true,
                'url'                    => '/category/1234-abcdef',
                'expectedValues'         => [
                    ...$defaultExpectedValues,
                    ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_PUBLIC_ID => 1234,
                    ContentPageCategoryRequestInterface::ATTRIBUTE_CATEGORY_SLUG      => 'abcdef',
                ],
                'expectedException'      => null,
            ],
            'category page disabled' => [
                'contentPageHomeEnabled' => false,
                'url'                    => '/category/1234-abcdef',
                'expectedValues'         => $defaultExpectedValues,
                'expectedException'      => ResourceNotFoundException::class,
            ],
            'invalid category page'  => [
                'contentPageHomeEnabled' => null,
                'url'                    => '/category/01234-',
                'expectedValues'         => $defaultExpectedValues,
                'expectedException'      => ResourceNotFoundException::class,
            ],
        ];
    }

    /**
     * @param mixed[] $expectedValues
     */
    #[DataProvider('requestDataProvider')]
    public function testRequest(
        ?bool $contentPageHomeEnabled,
        string $url,
        array $expectedValues,
        ?string $expectedException
    ): void
    {
        if (is_bool($contentPageHomeEnabled)) {
            $this->contentPageHomeSettingsStubBuilder
                ->setEnabled($contentPageHomeEnabled)
                ->create();
        }

        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $this->assertRequest(
            $url,
            null,
            $this->contentPageCategoryRequest,
            $expectedValues,
        );
    }
}

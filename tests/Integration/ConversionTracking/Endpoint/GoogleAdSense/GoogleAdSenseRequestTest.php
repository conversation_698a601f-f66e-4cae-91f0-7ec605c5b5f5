<?php

declare(strict_types=1);

namespace Tests\Integration\ConversionTracking\Endpoint\GoogleAdSense;

use App\ConversionTracking\Endpoint\GoogleAdSense\GoogleAdSenseRequestInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractRequestIntegrationTestCase;

class GoogleAdSenseRequestTest extends AbstractRequestIntegrationTestCase
{
    private GoogleAdSenseRequestInterface $googleAdSenseRequest;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var GoogleAdSenseRequestInterface $googleAdSenseRequest */
        $googleAdSenseRequest = self::getContainer()->get(GoogleAdSenseRequestInterface::class);
        $this->googleAdSenseRequest = $googleAdSenseRequest;
    }

    /**
     * @return mixed[]
     */
    public static function getBlockDataProvider(): array
    {
        return [
            'standard request'       => [
                'url'            => 'http://zoekie.zoekie.nl/tp/ga?block=1&ad=3',
                'expectedValues' => [
                    GoogleAdSenseRequestInterface::PARAMETER_BLOCK => 1,
                    GoogleAdSenseRequestInterface::PARAMETER_AD    => 3,
                ],
            ],
            'not a tracking request' => [
                'url'            => 'http://zoekie.zoekie.nl/ws?q=zoeken',
                'expectedValues' => [
                    GoogleAdSenseRequestInterface::PARAMETER_BLOCK => null,
                    GoogleAdSenseRequestInterface::PARAMETER_AD    => null,
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $expectedValues
     */
    #[DataProvider('getBlockDataProvider')]
    public function testGetBlock(string $url, array $expectedValues): void
    {
        $this->assertRequest(
            $url,
            null,
            $this->googleAdSenseRequest,
            $expectedValues,
        );
    }
}

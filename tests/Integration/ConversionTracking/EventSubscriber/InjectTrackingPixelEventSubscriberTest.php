<?php

declare(strict_types=1);

namespace Tests\Integration\ConversionTracking\EventSubscriber;

use App\ConversionTracking\EventSubscriber\InjectTrackingPixelEventSubscriber;
use App\Template\Event\RenderTemplateFootersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class InjectTrackingPixelEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateFootersEvent::NAME,
            InjectTrackingPixelEventSubscriber::class,
            'injectTrackingPixel',
        );
    }
}

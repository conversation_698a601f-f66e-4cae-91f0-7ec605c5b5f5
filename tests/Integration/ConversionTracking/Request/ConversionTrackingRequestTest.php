<?php

declare(strict_types=1);

namespace Tests\Integration\ConversionTracking\Request;

use App\ConversionTracking\Request\ConversionTrackingRequestInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractRequestIntegrationTestCase;

class ConversionTrackingRequestTest extends AbstractRequestIntegrationTestCase
{
    private ConversionTrackingRequestInterface $conversionTrackingRequest;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var ConversionTrackingRequestInterface $conversionTrackingRequest */
        $conversionTrackingRequest = self::getContainer()->get(ConversionTrackingRequestInterface::class);
        $this->conversionTrackingRequest = $conversionTrackingRequest;
    }

    /**
     * @return mixed[]
     */
    public static function requestDataProvider(): array
    {
        $defaultExpectedValues = [
            ConversionTrackingRequestInterface::PARAMETER_ORIGINAL_PAGEVIEW_ID => null,
            ConversionTrackingRequestInterface::PARAMETER_HTTP_HOST_CLIENT     => null,
            ConversionTrackingRequestInterface::PARAMETER_AD_STYLE_ID          => null,
            ConversionTrackingRequestInterface::PARAMETER_AD_CLIENT_ID         => null,
        ];

        return [
            'no query string'                                 => [
                'url'            => 'https://www.zapmeta.com',
                'expectedValues' => $defaultExpectedValues,
            ],
            'complete with correct URL parameter values'      => [
                'url'            => 'https://www.zapmeta.com?opvid=123abcd&hhc=https://pizza.zapmeta.com&astid=12&acid=abcd',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    ConversionTrackingRequestInterface::PARAMETER_ORIGINAL_PAGEVIEW_ID => '123abcd',
                    ConversionTrackingRequestInterface::PARAMETER_HTTP_HOST_CLIENT     => 'https://pizza.zapmeta.com',
                    ConversionTrackingRequestInterface::PARAMETER_AD_STYLE_ID          => 12,
                    ConversionTrackingRequestInterface::PARAMETER_AD_CLIENT_ID         => 'abcd',
                ],
            ],
            'complete with URL parameter value normalization' => [
                'url'            => 'https://www.zapmeta.com?opvid=%20123abcd&hhc=https://zz.zapmeta.com/bla/&astid=%20123&acid=abcd%20',
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    ConversionTrackingRequestInterface::PARAMETER_ORIGINAL_PAGEVIEW_ID => '123abcd',
                    ConversionTrackingRequestInterface::PARAMETER_HTTP_HOST_CLIENT     => 'https://zz.zapmeta.com',
                    ConversionTrackingRequestInterface::PARAMETER_AD_STYLE_ID          => 123,
                    ConversionTrackingRequestInterface::PARAMETER_AD_CLIENT_ID         => 'abcd',
                ],
            ],
            'invalid URL parameter values'                    => [
                'url'            => 'https://www.zapmeta.com?opvid=%20&hhc=iamnotanurl&std=abc&acid=%20',
                'expectedValues' => $defaultExpectedValues,
            ],
        ];
    }

    /**
     * @param mixed[] $expectedValues
     */
    #[DataProvider('requestDataProvider')]
    public function testRequest(string $url, array $expectedValues): void
    {
        $this->assertRequest(
            $url,
            null,
            $this->conversionTrackingRequest,
            $expectedValues,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\Generic\JsonSchemaValidator;

use App\Account\Settings\AccountSettings;
use App\Brand\Settings\BrandSettings;
use App\Locale\Settings\LocaleSettings;
use App\Locale\Settings\LocaleSettingsHelper;
use App\WebsiteSettings\Configuration\RedirectDomain\RedirectDomainConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Settings\BingAds\BingAdsSettings;
use App\WebsiteSettings\Settings\BingAds\BingAdsSettingsFactory;
use App\WebsiteSettings\Settings\GoogleAdsConversionTracking\GoogleAdsConversionTrackingSettingsFactory;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettings;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettingsFactory;
use App\WebsiteSettings\Settings\MicrosoftAdsConversionTracking\MicrosoftAdsConversionTrackingSettingsFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\Helper\BrandConfigTestHelper;
use Tests\Integration\WebsiteSettings\AbstractWebsiteSettingsTestCase;
use Visymo\Shared\Domain\Validator\JsonSchema\JsonSchemaValidationException;
use Visymo\Shared\Infrastructure\Bridge\Opis\OpisJsonSchemaValidatorFactory;

class JsonSchemaValidatorTest extends AbstractWebsiteSettingsTestCase
{
    /**
     * @return mixed[]
     */
    public static function configDataProvider(): array
    {
        return [
            'valid'                              => [
                'adjustConfigCallback' => null,
                'adjustJsonCallback'   => null,
                'expectedException'    => false,
            ],
            'valid empty accounts'               => [
                'adjustConfigCallback' => static function (array $config): array {
                    $config[WebsiteConfiguration::KEY_ACCOUNTS] = [];

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => false,
            ],
            'valid empty split tests'            => [
                'adjustConfigCallback' => static function (array $config): array {
                    $config[WebsiteConfiguration::KEY_SPLIT_TESTS] = [];

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => false,
            ],
            'invalid missing base properties'    => [
                'adjustConfigCallback' => static function (array $config): array {
                    unset(
                        $config[WebsiteConfiguration::KEY_BRAND],
                        $config[WebsiteConfiguration::KEY_DOMAINS],
                        $config[WebsiteConfiguration::KEY_ACCOUNTS],
                        $config[WebsiteConfiguration::KEY_SPLIT_TESTS],
                    );

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid empty objects'              => [
                'adjustConfigCallback' => static function (array $config): array {
                    $config[WebsiteConfiguration::KEY_ACCOUNTS] = [];
                    $config[WebsiteConfiguration::KEY_SPLIT_TESTS] = [];

                    return $config;
                },
                'adjustJsonCallback'   => static function (string $json): string {
                    $json = str_replace(
                        [
                            sprintf('"%s": []', WebsiteConfiguration::KEY_ACCOUNTS),
                            sprintf('"%s": []', WebsiteConfiguration::KEY_SPLIT_TESTS),
                        ],
                        [
                            sprintf('"%s": {}', WebsiteConfiguration::KEY_ACCOUNTS),
                            sprintf('"%s": {}', WebsiteConfiguration::KEY_SPLIT_TESTS),
                        ],
                        $json,
                    );

                    return $json;
                },
                'expectedException'    => true,
            ],
            'invalid brand'                      => [
                'adjustConfigCallback' => static function (array $config): array {
                    $config[WebsiteConfiguration::KEY_BRAND][BrandSettings::KEY_NAME] = '';
                    $config[WebsiteConfiguration::KEY_BRAND][BrandSettings::KEY_SLUG] = null;
                    $config[WebsiteConfiguration::KEY_BRAND][BingAdsSettingsFactory::KEY_BING_ADS] = [
                        BingAdsSettings::KEY_APPROVAL => null,
                    ];
                    $config[WebsiteConfiguration::KEY_BRAND][GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE] = [
                        GoogleAdSenseSettings::KEY_APPROVAL => null,
                    ];

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid domain'                     => [
                'adjustConfigCallback' => static function (array $config): array {
                    $domainConfig = $config[WebsiteConfiguration::KEY_DOMAINS][BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM];

                    $localeConfig = $domainConfig[LocaleSettingsHelper::KEY_LOCALES][0];
                    $localeConfig[LocaleSettings::KEY_LOCALE] = 'not a locale';

                    $domainConfig[LocaleSettingsHelper::KEY_LOCALES][0] = $localeConfig;
                    $googleAdsenseSettings = $config[WebsiteConfiguration::KEY_BRAND][GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE];
                    $googleAdsenseSettings[GoogleAdSenseSettings::KEY_DEFAULT_CHANNEL] = ['Look ma! I am an array!'];

                    $config[WebsiteConfiguration::KEY_BRAND][GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE] = $googleAdsenseSettings;
                    $config[WebsiteConfiguration::KEY_DOMAINS][BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM] = $domainConfig;

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid missing domain properties'  => [
                'adjustConfigCallback' => static function (array $config): array {
                    $domainConfig = $config[WebsiteConfiguration::KEY_DOMAINS][BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM];
                    unset(
                        $domainConfig[LocaleSettingsHelper::KEY_LOCALES],
                        $domainConfig[BingAdsSettingsFactory::KEY_BING_ADS],
                        $domainConfig[GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE],
                    );

                    $config[WebsiteConfiguration::KEY_DOMAINS][BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM] = $domainConfig;

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid domain host'                => [
                'adjustConfigCallback' => static function (array $config): array {
                    $config[WebsiteConfiguration::KEY_DOMAINS]['invalid host'] =
                        $config[WebsiteConfiguration::KEY_DOMAINS][BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM];

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid account'                    => [
                'adjustConfigCallback' => static function (array $config): array {
                    $accountConfig = $config[WebsiteConfiguration::KEY_ACCOUNTS][BrandConfigTestHelper::CONFIG_ACCOUNT_ID_3];
                    $accountConfig[AccountSettings::KEY_PAYMENT_MODE] = 'not paying';

                    $config[WebsiteConfiguration::KEY_ACCOUNTS][BrandConfigTestHelper::CONFIG_ACCOUNT_ID_3] = $accountConfig;

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid missing account properties' => [
                'adjustConfigCallback' => static function (array $config): array {
                    $accountConfig = $config[WebsiteConfiguration::KEY_ACCOUNTS][BrandConfigTestHelper::CONFIG_ACCOUNT_ID_3];
                    unset(
                        $accountConfig[BingAdsSettingsFactory::KEY_BING_ADS],
                        $accountConfig[GoogleAdSenseSettingsFactory::KEY_GOOGLE_ADSENSE],
                        $accountConfig[GoogleAdsConversionTrackingSettingsFactory::KEY_GOOGLE_ADS_CONVERSION_TRACKING],
                        $accountConfig[MicrosoftAdsConversionTrackingSettingsFactory::KEY_MICROSOFT_ADS_CONVERSION_TRACKING],
                    );

                    $config[WebsiteConfiguration::KEY_ACCOUNTS][BrandConfigTestHelper::CONFIG_ACCOUNT_ID_3] = $accountConfig;

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid redirect domain'            => [
                'adjustConfigCallback' => static function (array $config): array {
                    $config[WebsiteConfiguration::KEY_REDIRECT_DOMAINS] = [
                        'my.brand.com' => [
                            RedirectDomainConfiguration::KEY_REDIRECT_STRATEGY => 'blub',
                        ],
                    ];

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
            'invalid split test'                 => [
                'adjustConfigCallback' => static function (array $config): array {
                    $splitTestConfig = $config[WebsiteConfiguration::KEY_SPLIT_TESTS][BrandConfigTestHelper::CONFIG_SPLIT_TEST_ID_123];
                    $splitTestConfig['activation']['device'] = 'not a device';
                    $splitTestConfig['variants'][0]['variant'] = null;

                    $config[WebsiteConfiguration::KEY_SPLIT_TESTS][BrandConfigTestHelper::CONFIG_SPLIT_TEST_ID_123] = $splitTestConfig;

                    return $config;
                },
                'adjustJsonCallback'   => null,
                'expectedException'    => true,
            ],
        ];
    }

    #[DataProvider('configDataProvider')]
    public function testConfig(
        ?callable $adjustConfigCallback,
        ?callable $adjustJsonCallback,
        bool $expectedException
    ): void
    {
        if (!$expectedException) {
            $this->expectNotToPerformAssertions();
        }

        $config = $this->brandConfigTestHelper->getFoodBrandConfig();

        if ($adjustConfigCallback !== null) {
            $config = $adjustConfigCallback($config);
        }

        $json = json_encode($config, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);

        if ($adjustJsonCallback !== null) {
            $json = $adjustJsonCallback($json);
        }

        /** @var string $schemaFile */
        $schemaFile = self::getContainer()->getParameter('brand_config_json_schema_file_path');

        $fileName = str_replace(' ', '_', (string)$this->dataName());
        $expectedResponseFile = sprintf('%s/_assertions/%s.json', __DIR__, $fileName);
        $outputData = [];

        try {
            /** @var OpisJsonSchemaValidatorFactory $opisJsonSchemaValidatorFactory */
            $opisJsonSchemaValidatorFactory = self::getContainer()->get(OpisJsonSchemaValidatorFactory::class);

            $jsonSchemaValidator = $opisJsonSchemaValidatorFactory->create(
                $schemaFile,
                100,
            );

            $jsonSchemaValidator->assert($json);

            if ($expectedException) {
                self::fail('JSON validation was successful, but was expected to fail');
            }
        } catch (JsonSchemaValidationException $exception) {
            $jsonErrors = json_encode(
                $exception->getSchemaErrors(),
                JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES,
            );

            // Strip file directory, otherwise this test will fail in production.
            $jsonErrors = str_replace(__DIR__, '', $jsonErrors);
            $jsonErrors = json_decode($jsonErrors, true, 512, JSON_THROW_ON_ERROR);

            $outputData = ['exception' => $jsonErrors];
        }

        if ($expectedException) {
            $this->validateActualOutputData($outputData, $expectedResponseFile);
        }
    }
}

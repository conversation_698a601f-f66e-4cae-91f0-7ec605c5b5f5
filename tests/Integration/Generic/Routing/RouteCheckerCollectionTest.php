<?php

declare(strict_types=1);

namespace Tests\Integration\Generic\Routing;

use App\Generic\Routing\RouteCheckerCollection;
use Symfony\Component\Routing\RouterInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class RouteCheckerCollectionTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private RouteCheckerCollection $routeCheckerCollection;

    private RouterInterface $router;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var RouteCheckerCollection $routeCheckerCollection */
        $routeCheckerCollection = self::getContainer()->get(RouteCheckerCollection::class);
        $this->routeCheckerCollection = $routeCheckerCollection;

        /** @var RouterInterface $router */
        $router = self::getContainer()->get(RouterInterface::class);
        $this->router = $router;
    }

    public function testRouteConditions(): void
    {
        $routeCheckerAliases = [];

        foreach ($this->routeCheckerCollection->iterate() as $routeChecker) {
            self::assertStringStartsWith(
                RouteCheckerCollection::ROUTE_CHECKER_ALIAS_PREFIX,
                $routeChecker::getAlias(),
            );

            if (array_key_exists($routeChecker::getAlias(), $routeCheckerAliases)) {
                self::fail(sprintf('Route checker alias "%s" is duplicated', $routeChecker::getAlias()));
            }

            $routeCheckerAliases[$routeChecker::getAlias()] = false;
        }

        if ($routeCheckerAliases === []) {
            self::fail('No route checkers found');
        }

        // Assert that all route checkers are used
        foreach ($this->router->getRouteCollection()->getAliases() as $routeAlias) {
            $route = $this->router->getRouteCollection()->get($routeAlias->getId());

            if ($route === null) {
                self::fail(sprintf('Route "%s" not found', $routeAlias->getId()));
            }

            if ($route->getCondition() === '') {
                continue;
            }

            $routeCheckerAlias = $this->routeCheckerCollection->getAliasByRouteCondition($route->getCondition());

            if ($routeCheckerAlias === null) {
                self::fail(
                    sprintf(
                        'The condition "%s" of route "%s" must meet a valid route checker condition',
                        $route->getCondition(),
                        $routeAlias->getId(),
                    ),
                );
            }

            $routeChecker = $this->routeCheckerCollection->getByAlias($routeCheckerAlias);

            if ($routeChecker === null) {
                self::fail(
                    sprintf(
                        'Route checker "%s" for route "%s" not found',
                        $routeCheckerAlias,
                        $routeAlias->getId(),
                    ),
                );
            }

            $routeCheckerAliases[$routeCheckerAlias] = true;
        }

        $routeCheckerAliasesNotUsed = array_filter($routeCheckerAliases, static fn (bool $used) => !$used);

        if ($routeCheckerAliasesNotUsed !== []) {
            self::fail(
                sprintf(
                    'Route checkers "%s" are not used',
                    implode('", "', array_keys($routeCheckerAliasesNotUsed)),
                ),
            );
        }
    }
}

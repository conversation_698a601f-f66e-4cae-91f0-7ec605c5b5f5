<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\EventSubscriber;

use App\GoogleCsa\EventSubscriber\InjectGoogleCsaScriptsEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class InjectGoogleCsaScriptsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectGoogleCsaScriptsEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\EventSubscriber;

use App\GoogleCsa\EventSubscriber\InjectGoogleTagScriptEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class InjectGoogleTagScriptEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectGoogleTagScriptEventSubscriber::class,
            'injectGoogleTag',
        );
    }
}

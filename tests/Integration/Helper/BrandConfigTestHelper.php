<?php

declare(strict_types=1);

namespace Tests\Integration\Helper;

use App\WebsiteSettings\Configuration\Import\ImportWebsiteConfigurationNormalizer;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFactory;

class BrandConfigTestHelper
{
    /**
     * @see /docs/tests/integratie_tests.md
     */
    public const string FOOD_CONFIG_FILE_PATH = __DIR__.'/_config/config-api/artemis/brand-config/food.json';
    public const string FOOD_ASSETS_FILE_PATH = __DIR__.'/_config/config-api/artemis/brand-assets/food.json';

    public const string CONFIG_DOMAIN_ID_BRAND_COM = 'id.brand.com';
    public const string CONFIG_DOMAIN_WWW_BRAND_CA = 'www.brand.ca';
    public const int    CONFIG_ACCOUNT_ID_3        = 3;
    public const int    CONFIG_ACCOUNT_ID_46       = 46;
    public const int    CONFIG_ACCOUNT_ID_120      = 120;
    public const int    CONFIG_SPLIT_TEST_ID_123   = 123;

    public function __construct(
        private readonly ImportWebsiteConfigurationNormalizer $websiteConfigurationNormalizer,
        private readonly WebsiteConfigurationFactory $websiteConfigurationFactory
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function getFoodBrandConfig(): array
    {
        return $this->getConfig(self::FOOD_CONFIG_FILE_PATH);
    }

    /**
     * @return mixed[]
     */
    public function getFoodNormalizedBrandConfig(): array
    {
        return $this->websiteConfigurationNormalizer->normalizeConfig(
            $this->getFoodBrandConfig(),
        );
    }

    public function getFoodWebsiteConfiguration(): WebsiteConfiguration
    {
        return $this->websiteConfigurationFactory->createFromNormalized(
            $this->getFoodNormalizedBrandConfig(),
        );
    }

    /**
     * @return mixed[]
     */
    public function getFoodBrandAssets(): array
    {
        return $this->getConfig(self::FOOD_ASSETS_FILE_PATH);
    }

    /**
     * @return mixed[]
     *
     * @throws \JsonException
     */
    public function getConfig(string $filePath): array
    {
        return json_decode(
            (string)file_get_contents($filePath),
            true,
            512,
            JSON_THROW_ON_ERROR,
        );
    }
}

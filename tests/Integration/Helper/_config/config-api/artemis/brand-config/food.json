{"checksum": "abc", "brand": {"name": "Food", "slug": "food", "partner_slug": null, "google_adsense": {"approval": true, "contract_type": "direct", "default_channel": "izito_ca_seo", "default_client": "izito-web-iex"}, "article": {"enabled": true}, "bing_ads": {"approval": true, "default_ad_unit_id": "********"}, "cheq": {"enabled": true}, "content_page": {"enabled": true, "collection": "food", "author": {"slug": "editorial_team", "name": "Editorial Team"}, "use_brand_for_organic_results": true, "organic_result_route": "route_display_search_related"}, "content_search": {"enabled": true}, "content_page_home": {"enabled": true, "type": "home_1", "search_route": "route_display_search_related_web"}, "display_search_related": {"enabled": true, "related_fallback_enabled": false, "style_id_desktop": "3011335583", "style_id_mobile": "9385170977", "web_style_id_desktop": "3011335583", "web_style_id_mobile": "9385170977"}, "google_publisher_tag": {"enabled": true, "ad_unit_path": "food-lander"}, "google_tag_manager": {"enabled": true, "google_tag_manager_id": "GTM-5JLJ5J", "routes": ["route_display_search_related", "route_display_search_related_web"]}, "javascript_related_terms": {"enabled": false}, "json_template": {"template_variant": null}, "one_trust": {"enabled": false, "domain_script_id": "onetrust-423-fds-453"}, "pageview_conversion": {"enabled": true, "routes": ["route_display_search_related", "route_display_search_related_web"]}, "spam_click_detect": {"enabled": true, "routes": ["route_display_search_related_web"]}, "tracking": {"campaign_name_validation_enabled": true}, "web_search": {"enabled": true, "style_id_desktop": "**********", "style_id_mobile": "1002324520"}, "search": {"enabled": true, "seo_enabled": true, "style_id_desktop": "**********", "style_id_mobile": "1002324520"}, "display_search": {"enabled": false}, "monetization": {"ads_enabled": true, "related_terms_enabled": true, "display_banners_enabled": true}, "microsoft_search": {"enabled": true}, "microsoft_search_related": {"enabled": true, "style_id_desktop": "3011335583", "style_id_mobile": "9385170977"}, "info_pages": {"link_to_external_about_page": false, "link_to_visymo_publishing": true, "page_type": "content"}, "image_search": {"enabled": false}, "news_search": {"enabled": false}, "active": true}, "domains": {"id.brand.com": {"javascript_related_terms_enabled": true, "locales": [{"locale": "id_ID", "is_default": true}], "google_adsense": {"enabled": true}, "bing_ads": {"enabled": false}}, "www.brand.ca": {"javascript_related_terms_enabled": true, "locales": [{"locale": "en_CA", "is_default": true}, {"locale": "de_DE", "is_default": false}], "google_adsense": {"enabled": false}, "bing_ads": {"enabled": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>"}}}, "redirect_domains": {"www.mybrand.com": {"redirect_strategy": "geo_ip"}}, "accounts": {"3": {"name": "Google account with Google Ads", "service": "Google Ads", "campaigns_v2": [{"name": "iz_br_gc3_03"}, {"name": "iz_br_gc3_04"}, {"name": "iz_br_gc3_05", "google_adsense": {"style_id": "**********"}, "domain": "find-me.brand.com"}], "additional_channels": [], "additional_channel_prefix": "ch", "conversion_log": {"enabled": true, "offline_conversion": false}, "conversion_tracking": {"order_type": "one_per_click_id"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true, "sem_client": "izito-smh-iex", "web_client": "izito-web-iex"}, "google_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": **********, "conversion_tracking_label": "TbfSCL611AcQlv7H-QM"}, "google_ads_related_terms_conversion_tracking": {"enabled": true, "conversion_tracking_id": 9643623, "conversion_tracking_label": "abbbb1ccccc2dddd3-AZ"}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": [], "payment_mode": "CLICKS"}, "46": {"name": "Google account with no ad provider enabled", "service": "Google Ads", "campaigns_v2": [{"name": "iz_hk_gc3_04"}, {"name": "iz_hk_gc3_05"}], "additional_channels": [], "additional_channel_prefix": "ch", "conversion_log": {"enabled": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "google_ads_related_terms_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": [], "payment_mode": null}, "120": {"name": "Google account with Bing Ads", "service": "Google Ads", "campaigns_v2": [{"name": "test123"}], "additional_channels": [], "additional_channel_prefix": "ch", "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": true, "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "google_ads_related_terms_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": ["SY"], "payment_mode": "CONVERSIONS"}, "133": {"name": "Microsoft account", "service": "Microsoft Advertising", "campaigns_v2": [{"name": "iz_vn_ba_br_01"}, {"name": "iz_vn_ba_br_02"}], "additional_channels": {"iz_vn_ba_br_01": {"min": 300, "max": 399, "number_of_channels": 1}}, "additional_channel_prefix": "ch", "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "google_ads_related_terms_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": [], "payment_mode": null}, "146": {"name": "Zemanta account", "service": "<PERSON><PERSON><PERSON>", "campaigns_v2": [{"name": "test123"}], "payment_mode": null, "additional_channels": [], "additional_channel_prefix": "ch", "conversion_log": {"enabled": true, "offline_conversion": false}, "google_adsense": {"enabled": false}, "bing_ads": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "google_ads_related_terms_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": true, "conversion_tracking_id": 64641, "conversion_tracking_label": "Clickout_Conversion"}, "exclude_countries_from_conversion_tracking": [], "conversion_tracking": {"order_type": "one_per_click_id"}}}, "split_tests": {"123": {"activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": null, "routes": null}, "control_channels": {"landingpage": "ab_ta", "advertised": "ab_tb"}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"landingpage": "landing_ab_tb", "advertised": "advertised_ab_tb"}, "percentage": 30}]}}}
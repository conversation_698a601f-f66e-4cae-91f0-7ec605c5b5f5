{"attributes_bag": {"isEmpty": false, "getParameters": ["string", "empty_string", "int", "negative_int", "unsigned_int", "bool_yes", "bool_no", "bool_zero", "bool_one", "array", "empty_array", "_route"], "string": {"getString": "hello world", "getNullableString": "hello world", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "empty_string": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "int": {"getString": "5", "getNullableString": "5", "getInt": 5, "getNullableInt": 5, "getUnsignedInt": 5, "getNullableUnsignedInt": 5, "getBool": false}, "negative_int": {"getString": "-5", "getNullableString": "-5", "getInt": -5, "getNullableInt": -5, "getUnsignedInt": 0, "getBool": false}, "unsigned_int": {"getString": "10", "getNullableString": "10", "getInt": 10, "getNullableInt": 10, "getUnsignedInt": 10, "getNullableUnsignedInt": 10, "getBool": false}, "bool_yes": {"getString": "y", "getNullableString": "y", "getInt": 0, "getUnsignedInt": 0, "getBool": true, "getNullableBool": true}, "bool_no": {"getString": "n", "getNullableString": "n", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "bool_zero": {"getString": "0", "getNullableString": "0", "getInt": 0, "getNullableInt": 0, "getUnsignedInt": 0, "getNullableUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "bool_one": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true}, "array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": [1, 2]}, "empty_array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "_route": {"getString": "route_66", "getNullableString": "route_66", "getInt": 0, "getUnsignedInt": 0, "getBool": false}}, "query_bag": {"isEmpty": false, "getParameters": ["string", "empty_string", "int", "negative_int", "unsigned_int", "bool_yes", "bool_no", "bool_zero", "bool_one", "array", "empty_array"], "string": {"getString": "hello world", "getNullableString": "hello world", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "empty_string": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "int": {"getString": "5", "getNullableString": "5", "getInt": 5, "getNullableInt": 5, "getUnsignedInt": 5, "getNullableUnsignedInt": 5, "getBool": false}, "negative_int": {"getString": "-5", "getNullableString": "-5", "getInt": -5, "getNullableInt": -5, "getUnsignedInt": 0, "getBool": false}, "unsigned_int": {"getString": "10", "getNullableString": "10", "getInt": 10, "getNullableInt": 10, "getUnsignedInt": 10, "getNullableUnsignedInt": 10, "getBool": false}, "bool_yes": {"getString": "y", "getNullableString": "y", "getInt": 0, "getUnsignedInt": 0, "getBool": true, "getNullableBool": true}, "bool_no": {"getString": "n", "getNullableString": "n", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "bool_zero": {"getString": "0", "getNullableString": "0", "getInt": 0, "getNullableInt": 0, "getUnsignedInt": 0, "getNullableUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "bool_one": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true}, "array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": [1, 2]}, "empty_array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}}, "request_bag": {"isEmpty": true, "getParameters": []}, "cookies_bag": {"isEmpty": false, "getParameters": ["string", "empty_string", "int", "negative_int", "unsigned_int", "bool_yes", "bool_no", "bool_zero", "bool_one", "array", "empty_array"], "string": {"getString": "hello world", "getNullableString": "hello world", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "empty_string": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "int": {"getString": "5", "getNullableString": "5", "getInt": 5, "getNullableInt": 5, "getUnsignedInt": 5, "getNullableUnsignedInt": 5, "getBool": false}, "negative_int": {"getString": "-5", "getNullableString": "-5", "getInt": -5, "getNullableInt": -5, "getUnsignedInt": 0, "getBool": false}, "unsigned_int": {"getString": "10", "getNullableString": "10", "getInt": 10, "getNullableInt": 10, "getUnsignedInt": 10, "getNullableUnsignedInt": 10, "getBool": false}, "bool_yes": {"getString": "y", "getNullableString": "y", "getInt": 0, "getUnsignedInt": 0, "getBool": true, "getNullableBool": true}, "bool_no": {"getString": "n", "getNullableString": "n", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "bool_zero": {"getString": "0", "getNullableString": "0", "getInt": 0, "getNullableInt": 0, "getUnsignedInt": 0, "getNullableUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "bool_one": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true}, "array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": [1, 2]}, "empty_array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}}, "headers_bag": {"isEmpty": false, "getParameters": ["host", "user-agent", "accept", "accept-language", "accept-charset", "test-header-string", "test-header-empty-string", "test-header-int", "test-header-negative-int", "test-header-unsigned-int", "test-header-bool-yes", "test-header-bool-no", "test-header-bool-zero", "test-header-bool-one", "test-header-array", "test-header-empty-array", "sec-ch-ua", "sec-ch-ua-mobile", "sec-ch-ua-platform", "sec-fetch-dest", "sec-fetch-mode", "sec-fetch-site", "sec-fetch-user", "upgrade-insecure-requests"], "test-header-string": {"getString": "hello world", "getNullableString": "hello world", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["hello world"]}, "test-header-empty-string": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": [""]}, "test-header-int": {"getString": "5", "getNullableString": "5", "getInt": 5, "getNullableInt": 5, "getUnsignedInt": 5, "getNullableUnsignedInt": 5, "getBool": false, "getNullableArray": ["5"]}, "test-header-negative-int": {"getString": "-5", "getNullableString": "-5", "getInt": -5, "getNullableInt": -5, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["-5"]}, "test-header-unsigned-int": {"getString": "10", "getNullableString": "10", "getInt": 10, "getNullableInt": 10, "getUnsignedInt": 10, "getNullableUnsignedInt": 10, "getBool": false, "getNullableArray": ["10"]}, "test-header-bool-yes": {"getString": "y", "getNullableString": "y", "getInt": 0, "getUnsignedInt": 0, "getBool": true, "getNullableBool": true, "getNullableArray": ["y"]}, "test-header-bool-no": {"getString": "n", "getNullableString": "n", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableBool": false, "getNullableArray": ["n"]}, "test-header-bool-zero": {"getString": "0", "getNullableString": "0", "getInt": 0, "getNullableInt": 0, "getUnsignedInt": 0, "getNullableUnsignedInt": 0, "getBool": false, "getNullableBool": false, "getNullableArray": ["0"]}, "test-header-bool-one": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true, "getNullableArray": ["1"]}, "test-header-array": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true, "getNullableArray": [1, 2]}, "test-header-empty-array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "user-agent": {"getString": "My User Agent", "getNullableString": "My User Agent", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["My User Agent"]}, "accept-language": {"getString": "fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5", "getNullableString": "fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5"]}, "sec-ch-ua": {"getString": "\"Chromium\";v=\"104\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"104\"", "getNullableString": "\"Chromium\";v=\"104\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"104\"", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["\"Chromium\";v=\"104\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"104\""]}, "sec-ch-ua-mobile": {"getString": "?0", "getNullableString": "?0", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["?0"]}, "sec-ch-ua-platform": {"getString": "\"Windows\"", "getNullableString": "\"Windows\"", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["\"Windows\""]}, "sec-fetch-dest": {"getString": "document", "getNullableString": "document", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["document"]}, "sec-fetch-mode": {"getString": "navigate", "getNullableString": "navigate", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["navigate"]}, "sec-fetch-site": {"getString": "none", "getNullableString": "none", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["none"]}, "sec-fetch-user": {"getString": "?1", "getNullableString": "?1", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": ["?1"]}, "upgrade-insecure-requests": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true, "getNullableArray": ["1"]}, "request-time": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "request-time-float": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}}, "server_bag": {"isEmpty": false, "getParameters": ["SERVER_NAME", "SERVER_PORT", "HTTP_HOST", "HTTP_USER_AGENT", "HTTP_ACCEPT", "HTTP_ACCEPT_LANGUAGE", "HTTP_ACCEPT_CHARSET", "REMOTE_ADDR", "SCRIPT_NAME", "SCRIPT_FILENAME", "SERVER_PROTOCOL", "REQUEST_TIME", "REQUEST_TIME_FLOAT", "HTTP_test_header_string", "HTTP_test_header_empty_string", "HTTP_test_header_int", "HTTP_test_header_negative_int", "HTTP_test_header_unsigned_int", "HTTP_test_header_bool_yes", "HTTP_test_header_bool_no", "HTTP_test_header_bool_zero", "HTTP_test_header_bool_one", "HTTP_test_header_array", "HTTP_test_header_empty_array", "HTTP_user_agent", "HTTP_accept_language", "HTTP_sec_CH_UA", "HTTP_sec_CH_UA_Mobile", "HTTP_sec_CH_UA_Platform", "HTTP_Sec_Fetch_Dest", "HTTP_Sec_Fetch_Mode", "HTTP_Sec_Fetch_Site", "HTTP_Sec_Fetch_User", "HTTP_Upgrade_Insecure-Requests", "PATH_INFO", "REQUEST_METHOD", "REQUEST_URI", "QUERY_STRING"], "SERVER_NAME": {"getString": "localhost", "getNullableString": "localhost", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "SERVER_PORT": {"getString": "80", "getNullableString": "80", "getInt": 80, "getNullableInt": 80, "getUnsignedInt": 80, "getNullableUnsignedInt": 80, "getBool": false}, "HTTP_HOST": {"getString": "localhost", "getNullableString": "localhost", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_USER_AGENT": {"getString": "Symfony", "getNullableString": "Symfony", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_ACCEPT": {"getString": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "getNullableString": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_ACCEPT_LANGUAGE": {"getString": "en-us,en;q=0.5", "getNullableString": "en-us,en;q=0.5", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_ACCEPT_CHARSET": {"getString": "ISO-8859-1,utf-8;q=0.7,*;q=0.7", "getNullableString": "ISO-8859-1,utf-8;q=0.7,*;q=0.7", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "REMOTE_ADDR": {"getString": "127.0.0.1", "getNullableString": "127.0.0.1", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "SCRIPT_NAME": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "SCRIPT_FILENAME": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "SERVER_PROTOCOL": {"getString": "HTTP/1.1", "getNullableString": "HTTP/1.1", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "REQUEST_TIME": {"getString": "1234567890", "getNullableString": "1234567890", "getInt": 1234567890, "getNullableInt": 1234567890, "getUnsignedInt": 1234567890, "getNullableUnsignedInt": 1234567890, "getBool": false}, "REQUEST_TIME_FLOAT": {"getString": "1234567890.1235", "getNullableString": "1234567890.1235", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_test_header_string": {"getString": "hello world", "getNullableString": "hello world", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_test_header_empty_string": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_test_header_int": {"getString": "5", "getNullableString": "5", "getInt": 5, "getNullableInt": 5, "getUnsignedInt": 5, "getNullableUnsignedInt": 5, "getBool": false}, "HTTP_test_header_negative_int": {"getString": "-5", "getNullableString": "-5", "getInt": -5, "getNullableInt": -5, "getUnsignedInt": 0, "getBool": false}, "HTTP_test_header_unsigned_int": {"getString": "10", "getNullableString": "10", "getInt": 10, "getNullableInt": 10, "getUnsignedInt": 10, "getNullableUnsignedInt": 10, "getBool": false}, "HTTP_test_header_bool_yes": {"getString": "y", "getNullableString": "y", "getInt": 0, "getUnsignedInt": 0, "getBool": true, "getNullableBool": true}, "HTTP_test_header_bool_no": {"getString": "n", "getNullableString": "n", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "HTTP_test_header_bool_zero": {"getString": "0", "getNullableString": "0", "getInt": 0, "getNullableInt": 0, "getUnsignedInt": 0, "getNullableUnsignedInt": 0, "getBool": false, "getNullableBool": false}, "HTTP_test_header_bool_one": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true}, "HTTP_test_header_array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false, "getNullableArray": [1, 2]}, "HTTP_test_header_empty_array": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_user_agent": {"getString": "My User Agent", "getNullableString": "My User Agent", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_accept_language": {"getString": "fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5", "getNullableString": "fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_sec_CH_UA": {"getString": "\"Chromium\";v=\"104\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"104\"", "getNullableString": "\"Chromium\";v=\"104\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"104\"", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_sec_CH_UA_Mobile": {"getString": "?0", "getNullableString": "?0", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_sec_CH_UA_Platform": {"getString": "\"Windows\"", "getNullableString": "\"Windows\"", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_Sec_Fetch_Dest": {"getString": "document", "getNullableString": "document", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_Sec_Fetch_Mode": {"getString": "navigate", "getNullableString": "navigate", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_Sec_Fetch_Site": {"getString": "none", "getNullableString": "none", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_Sec_Fetch_User": {"getString": "?1", "getNullableString": "?1", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "HTTP_Upgrade_Insecure-Requests": {"getString": "1", "getNullableString": "1", "getInt": 1, "getNullableInt": 1, "getUnsignedInt": 1, "getNullableUnsignedInt": 1, "getBool": true, "getNullableBool": true}, "PATH_INFO": {"getString": "", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "REQUEST_METHOD": {"getString": "GET", "getNullableString": "GET", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "REQUEST_URI": {"getString": "/?string=hello+world&empty_string=&int=5&negative_int=-5&unsigned_int=10&bool_yes=y&bool_no=n&bool_zero=0&bool_one=1&array%5B0%5D=1&array%5B1%5D=2", "getNullableString": "/?string=hello+world&empty_string=&int=5&negative_int=-5&unsigned_int=10&bool_yes=y&bool_no=n&bool_zero=0&bool_one=1&array%5B0%5D=1&array%5B1%5D=2", "getInt": 0, "getUnsignedInt": 0, "getBool": false}, "QUERY_STRING": {"getString": "string=hello+world&empty_string=&int=5&negative_int=-5&unsigned_int=10&bool_yes=y&bool_no=n&bool_zero=0&bool_one=1&array%5B0%5D=1&array%5B1%5D=2", "getNullableString": "string=hello+world&empty_string=&int=5&negative_int=-5&unsigned_int=10&bool_yes=y&bool_no=n&bool_zero=0&bool_one=1&array%5B0%5D=1&array%5B1%5D=2", "getInt": 0, "getUnsignedInt": 0, "getBool": false}}}
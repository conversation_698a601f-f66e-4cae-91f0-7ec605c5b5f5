<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Request;

use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class RequestValidationTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private RequestValidationDataProvider $requestValidationDataProvider;

    private string $sourceNamespace;

    private string $testNamespace;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        /** @var RequestValidationDataProvider $requestValidationDataProvider */
        $requestValidationDataProvider = self::getContainer()->get(RequestValidationDataProvider::class);
        $this->requestValidationDataProvider = $requestValidationDataProvider;

        $sourceNamespace = null;

        foreach (self::getKernel()->getBundles() as $bundle) {
            if (str_contains(self::class, $bundle->getNamespace())) {
                $sourceNamespace = $bundle->getNamespace();

                break;
            }
        }

        if ($sourceNamespace === null) {
            $this->sourceNamespace = 'App';
            $this->testNamespace = 'Tests';
        } else {
            $this->sourceNamespace = $sourceNamespace;
            $this->testNamespace = sprintf('%s\Tests', $sourceNamespace);
        }
    }

    public function testRequests(): void
    {
        $requestClassNames = array_keys(
            $this->requestValidationDataProvider->requestServiceLocator->getProvidedServices(),
        );
        $requestStubsMethods = array_filter(
            get_class_methods($this->stubs()->request()),
            static fn (string $method) => preg_match('/^get.+Request|getRequestInfo$/', $method) === 1,
        );

        foreach ($requestClassNames as $requestClass) {
            $this->assertRequest($requestClass, $requestStubsMethods);
        }

        // Assert request methods are sorted alphabetically
        $actualRequestStubsMethods = $requestStubsMethods;
        natsort($requestStubsMethods);

        self::assertSame(
            $requestStubsMethods,
            $actualRequestStubsMethods,
            sprintf(
                'Request stub methods are not sorted alphabetically, expected order:%s',
                PHP_EOL.'- '.implode(PHP_EOL.'- ', $requestStubsMethods),
            ),
        );
    }

    /**
     * @param string[] $requestStubsMethods
     */
    private function assertRequest(string $requestClass, array $requestStubsMethods): void
    {
        // Force to declare request class as final
        self::assertTrue(
            $this->isClassDeclaredFinal($requestClass),
            sprintf('Request class "%s" must be declared final', $requestClass),
        );

        $requestInterfaceClass = $this->getRequestInterfaceClass($requestClass);

        // Assert request stub for requests in current bundle only
        if (str_starts_with($requestClass, $this->sourceNamespace)) {
            $this->assertRequestStub(
                $requestClass,
                $requestInterfaceClass,
                $requestStubsMethods,
            );
        }
    }

    private function getRequestInterfaceClass(string $requestClass): string
    {
        $requestClassName = explode('\\', $requestClass);
        $requestClassName = end($requestClassName);

        $requestInterfaceClassName = sprintf('%sInterface', $requestClassName);

        $requestInterfaceClassSelect = sprintf('\\%s', $requestInterfaceClassName);
        $requestInterfaceClass = null;

        $requestClassImplements = class_implements($requestClass);

        if ($requestClassImplements === false) {
            throw new \RuntimeException(
                sprintf('Request interface class "%s" not found', $requestInterfaceClassName),
            );
        }

        foreach ($requestClassImplements as $classImplement) {
            if (str_ends_with($classImplement, $requestInterfaceClassSelect)) {
                $requestInterfaceClass = $classImplement;
            }
        }

        if ($requestInterfaceClass === null) {
            throw new \RuntimeException(
                sprintf('Request interface class "%s" not found', $requestInterfaceClassName),
            );
        }

        self::assertTrue(
            interface_exists($requestInterfaceClass),
            sprintf('Request interface class "%s" does not exist', $requestInterfaceClass),
        );

        return $requestInterfaceClass;
    }

    /**
     * @param string[] $requestStubsMethods
     */
    private function assertRequestStub(
        string $requestClass,
        string $requestInterfaceClass,
        array $requestStubsMethods
    ): void
    {
        // Stub of request must be available in RequestStubs
        $requestClassName = explode('\\', $requestClass);
        $requestClassName = end($requestClassName);
        $getRequestClassMethodName = sprintf('get%s', $requestClassName);

        self::assertContainsEquals(
            $getRequestClassMethodName,
            $requestStubsMethods,
            sprintf('RequestStubs::%s does not exist', $getRequestClassMethodName),
        );

        // Interface and stub of request must exist
        $requestStubClass = str_replace(
            sprintf('%s\\', $this->sourceNamespace),
            sprintf('%s\Stub\\', $this->testNamespace),
            sprintf('%sStub', $requestClass),
        );
        $requestStubClassExists = class_exists($requestStubClass);

        self::assertTrue(
            $requestStubClassExists,
            sprintf('Request stub class "%s" does not exist', $requestStubClass),
        );

        // Force to declare request stub class as final
        self::assertTrue(
            $this->isClassDeclaredFinal($requestStubClass),
            sprintf('Request stub class "%s" must be declared final', $requestStubClass),
        );

        $requestStubImplements = class_implements($requestStubClass);

        self::assertContainsEquals(
            $requestInterfaceClass,
            $requestStubImplements,
            sprintf(
                'Request stub class "%s" should implement "%s"',
                $requestStubClass,
                $requestInterfaceClass,
            ),
        );

        /** @phpstan-ignore-next-line Variable method call not allowed; Assert that the request is an instance of the stub */
        $requestStub = $this->stubs()->request()->$getRequestClassMethodName();

        self::assertSame($requestStub::class, $requestStubClass);
    }

    private function isClassDeclaredFinal(string $class): bool
    {
        return (new \ReflectionClass($class))->isFinal();
    }
}

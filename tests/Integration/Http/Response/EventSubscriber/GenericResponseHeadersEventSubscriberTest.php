<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\GenericResponseHeadersEventSubscriber;
use App\Kernel\KernelResponseEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class GenericResponseHeadersEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::NO_REDIRECT->value,
            GenericResponseHeadersEventSubscriber::class,
            'onResponse',
        );

        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::REDIRECT->value,
            GenericResponseHeadersEventSubscriber::class,
            'onResponse',
        );
    }
}

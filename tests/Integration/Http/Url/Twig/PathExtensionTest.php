<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Url\Twig;

use App\Http\Url\PersistentUrlParametersHelper;
use App\Http\Url\PersistentUrlParametersRouter;
use App\Http\Url\Twig\PathExtension;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

class PathExtensionTest extends AbstractBrandWebsitesIntegrationTestCase
{
    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();
    }

    /**
     * @return mixed[]
     */
    public static function persistentPathDataProvider(): array
    {
        return [
            'adjust'   => [
                'url'             => '/?q=ipad+air&ss=y&twin=n&pkw=n&bkw=0&idyn=N&asid=abcd&de=t&ac=1&te=2&gclid=efgh&dummy=will be gone',
                'routeParameters' => [
                    'q'    => 'pizza hawaii',
                    'food' => 'gehaktbal',
                    'asid' => null,
                    'vid'  => 'hallo',
                    'ppid' => 'b1be7c21-90f1-4087-9054-c9e26655f43a',
                ],
                'expectedUrl'     => '/?q=pizza%20hawaii&food=gehaktbal&vid=hallo&ppid=b1be7c21-90f1-4087-9054-c9e26655f43a',
            ],
            'add only' => [
                'url'             => '/',
                'routeParameters' => [
                    'q'    => 'pizza hawaii',
                    'food' => 'gehaktbal',
                    'asid' => null,
                    'vid'  => 'hallo',
                    'ppid' => 'b1be7c21-90f1-4087-9054-c9e26655f43a',
                ],
                'expectedUrl'     => '/?q=pizza%20hawaii&food=gehaktbal&vid=hallo&ppid=b1be7c21-90f1-4087-9054-c9e26655f43a',
            ],
        ];
    }

    /**
     * @param array<string, string> $routeParameters
     */
    #[DataProvider('persistentPathDataProvider')]
    public function testPersistentPath(string $url, array $routeParameters, string $expectedUrl): void
    {
        $this->injectRequestWithWebsiteSettings($url, true);

        // run test
        /** @var PathExtension $pathExtension */
        $pathExtension = self::getContainer()->get(PathExtension::class);
        $actualUrl = $pathExtension->getPersistentPath('route_home', $routeParameters);

        // assert result
        $expectedUrlParameters = $this->getUrlParameters($expectedUrl, true);
        $actualUrlParameters = $this->getUrlParameters($actualUrl, true);

        self::assertSame($expectedUrlParameters, $actualUrlParameters);
    }

    /**
     * @param array<string, string> $routeParameters
     */
    #[DataProvider('persistentPathDataProvider')]
    public function testPersistentPathQueryString(string $url, array $routeParameters, string $expectedUrl): void
    {
        $this->injectRequestWithWebsiteSettings($url, true);

        // run test
        /** @var PathExtension $pathExtension */
        $pathExtension = self::getContainer()->get(PathExtension::class);
        $actualQueryString = $pathExtension->getPersistentPathQueryString($routeParameters);
        $actualUrl = sprintf('/?%s', $actualQueryString);

        // assert result
        $expectedUrlParameters = $this->getUrlParameters($expectedUrl, true);
        $actualUrlParameters = $this->getUrlParameters($actualUrl, true);

        self::assertSame($expectedUrlParameters, $actualUrlParameters);
    }

    /**
     * @return mixed[]
     */
    public static function persistentSearchBarDataProvider(): array
    {
        return [
            'adjust'   => [
                'url'                 => '/?q=ipad+air&twin=n&pkw=n&bkw=0&ap=1t2&gclid=efgh&dummy=I will not be there&te=0',
                'adjustUrlParameters' => [
                    'food'    => 'gehaktbal',
                    'gclid'   => null,
                    'suggest' => null,
                    'q'       => null,
                ],
                'expectedInputFields' => [
                    'q'       => null,
                    'ap'      => null,
                    'gclid'   => null,
                    'food'    => 'gehaktbal',
                    'suggest' => null,
                ],
            ],
            'add only' => [
                'url'                 => '/',
                'adjustUrlParameters' => [
                    'q'    => 'pizza hawaii',
                    'food' => 'gehaktbal',
                    'asid' => null,
                ],
                'expectedInputFields' => [
                    'food' => 'gehaktbal',
                ],
            ],
        ];
    }

    /**
     * @param array<string, string|null> $adjustUrlParameters
     * @param array<string, string|null> $expectedInputFields
     */
    #[DataProvider('persistentSearchBarDataProvider')]
    public function testPersistentPathInputFields(
        string $url,
        array $adjustUrlParameters,
        array $expectedInputFields
    ): void
    {
        $this->injectRequestWithWebsiteSettings($url, true);

        // run test
        /** @var PathExtension $pathExtension */
        $pathExtension = self::getContainer()->get(PathExtension::class);

        /** @var Environment $twigEnvironment */
        $twigEnvironment = self::getContainer()->get('twig');
        $html = $pathExtension->getPersistentPathInputFields($twigEnvironment, $adjustUrlParameters);

        // assert result
        foreach ($expectedInputFields as $parameter => $value) {
            if ($value === null) {
                $inputHtmlPart = sprintf('="%s"', $parameter);
                self::assertStringNotContainsString($inputHtmlPart, $html);
            } else {
                $inputHtml = sprintf('<input type="hidden" name="%s" value="%s">', $parameter, $value);
                self::assertStringContainsString($inputHtml, $html);
            }
        }
    }

    /**
     * @return mixed[]
     */
    public static function persistentPathUrlDataProvider(): array
    {
        $persistentUrlParameters = [
            'vid' => '1234-5678',
            'ste' => 'encoded-tracking-entry',
        ];
        $persistentPart = 'vid=1234-5678&ste=encoded-tracking-entry';

        return [
            'add nothing relative'                                => [
                'url'                     => '/path',
                'adjustParameters'        => [],
                'persistentUrlParameters' => [],
                'expectedUrl'             => '/path',
            ],
            'add nothing'                                         => [
                'url'                     => 'https://www.example.com',
                'adjustParameters'        => [],
                'persistentUrlParameters' => [],
                'expectedUrl'             => 'https://www.example.com',
            ],
            'add nothing slash'                                   => [
                'url'                     => 'https://www.example.com/',
                'adjustParameters'        => [],
                'persistentUrlParameters' => [],
                'expectedUrl'             => 'https://www.example.com/',
            ],
            'add nothing with path'                               => [
                'url'                     => 'https://www.example.com/path',
                'adjustParameters'        => [],
                'persistentUrlParameters' => [],
                'expectedUrl'             => 'https://www.example.com/path',
            ],
            'add nothing with path and slash'                     => [
                'url'                     => 'https://www.example.com/path/',
                'adjustParameters'        => [],
                'persistentUrlParameters' => [],
                'expectedUrl'             => 'https://www.example.com/path/',
            ],
            'add nothing with path and slash empty query'         => [
                'url'                     => 'https://www.example.com/path/?',
                'adjustParameters'        => [],
                'persistentUrlParameters' => [],
                'expectedUrl'             => 'https://www.example.com/path/?',
            ],
            'add persistent only relative'                        => [
                'url'                     => '/path',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => '/path?'.$persistentPart,
            ],
            'add persistent only'                                 => [
                'url'                     => 'https://www.example.com',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?'.$persistentPart,
            ],
            'add persistent only slash'                           => [
                'url'                     => 'https://www.example.com/',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?'.$persistentPart,
            ],
            'add persistent only with path'                       => [
                'url'                     => 'https://www.example.com/path',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/path?'.$persistentPart,
            ],
            'add persistent only with path and slash'             => [
                'url'                     => 'https://www.example.com/path/',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/path/?'.$persistentPart,
            ],
            'add persistent only with path and slash empty query' => [
                'url'                     => 'https://www.example.com/path/?',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/path/?'.$persistentPart,
            ],
            'add only'                                            => [
                'url'                     => 'https://www.example.com',
                'adjustParameters'        => [
                    'q'    => 'pizza hawaii',
                    'food' => 'gehaktbal',
                    'asid' => null,
                ],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?'.$persistentPart.'&q=pizza+hawaii&food=gehaktbal',
            ],
            'adjust keep and add'                                 => [
                'url'                     => 'https://www.example.com/?q=ipad+air&adjust=old&keep=stays',
                'adjustParameters'        => [
                    'q'      => null,
                    'add'    => 'extra',
                    'adjust' => 'new',
                ],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?q=ipad+air&adjust=new&keep=stays&'.$persistentPart.'&add=extra',
            ],
            'persistent overwritten'                              => [
                'url'                     => 'https://www.example.com/?q=keep&vid=exists&ste=exists',
                'adjustParameters'        => [],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?q=keep&'.$persistentPart,
            ],
            'persistent keep'                                     => [
                'url'                     => 'https://www.example.com/?vid=exists&ste=exists',
                'adjustParameters'        => [
                    'vid' => null,
                    'ste' => null,
                ],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?vid=exists&ste=exists',
            ],
            'persistent adjust'                                   => [
                'url'                     => 'https://www.example.com/?q=ipad+air&adjust=old&keep=stays',
                'adjustParameters'        => [
                    'vid' => 'adjust',
                    'ste' => 'adjust',
                ],
                'persistentUrlParameters' => $persistentUrlParameters,
                'expectedUrl'             => 'https://www.example.com/?q=ipad+air&adjust=old&keep=stays&vid=adjust&ste=adjust',
            ],
        ];
    }

    /**
     * @param array<string, string|null> $adjustParameters
     * @param array<string, string>      $persistentUrlParameters
     */
    #[DataProvider('persistentPathUrlDataProvider')]
    public function testPersistentPathUrl(
        string $url,
        array $adjustParameters,
        array $persistentUrlParameters,
        string $expectedUrl
    ): void
    {
        $this->injectRequestWithWebsiteSettings($url);

        $persistentUrlParametersRouterMock = $this->createMock(PersistentUrlParametersRouter::class);
        $persistentUrlParametersHelperMock = $this->createConfiguredMock(
            PersistentUrlParametersHelper::class,
            ['getPersistentParameters' => $persistentUrlParameters],
        );

        $pathExtension = new PathExtension(
            $persistentUrlParametersRouterMock,
            $persistentUrlParametersHelperMock,
        );
        $actualUrl = $pathExtension->getPersistentPathUrl($url, $adjustParameters);

        // Exactly the same url
        self::assertSame($expectedUrl, $actualUrl);
    }

    /**
     * @return mixed[]
     */
    private function getUrlParameters(string $url, bool $sort = false): array
    {
        $urlQuery = parse_url($url, PHP_URL_QUERY);
        parse_str(is_string($urlQuery) ? $urlQuery : '', $urlParameters);

        // The order of parameters in tests is irrelevant in most cases
        if ($sort) {
            ksort($urlParameters);
        }

        return $urlParameters;
    }

    private function injectRequestWithWebsiteSettings(string $url, bool $prependRandomHost = false): void
    {
        if ($prependRandomHost) {
            $url = self::websiteSettingsTestHelper()->prependRandomDomainToUrl($url);
        }

        $request = Request::create($url);
        $request->attributes->set('_route', 'route_home');

        // inject request in dependency container
        $this->setRequest($request);
    }
}

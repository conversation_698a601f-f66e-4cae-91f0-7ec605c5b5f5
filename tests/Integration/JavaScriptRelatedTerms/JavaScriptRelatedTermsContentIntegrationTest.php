<?php

declare(strict_types=1);

namespace Tests\Integration\JavaScriptRelatedTerms;

use App\Account\Settings\AccountSettings;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\WebsiteSettings\AbstractWebsiteSettingsTestCase;
use Tests\Stub\Assets\AssetsHelperStub;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Visymo\Shared\Domain\Locale\Locale;
use Visymo\Shared\Infrastructure\Stub\Vendor\Psr\Http\Client\HttpAsyncClientStub;
use Visymo\Shared\Infrastructure\Stub\Vendor\Psr\Http\Message\ResponseStub;
use Visymo\Shared\Infrastructure\Stub\Vendor\Psr\Http\Message\StreamStub;

class JavaScriptRelatedTermsContentIntegrationTest extends AbstractWebsiteSettingsTestCase
{
    private HttpAsyncClientStub $autosuggestApiHttpAsyncClientStub;

    private AssetsHelperStub $assetsHelperStub;

    private WebsiteConfigurationHelper $websiteConfigurationHelper;

    private ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->stubs()->localeSettingsHelper()
            ->setLocale(Locale::NL_NL);

        /** @var AssetsHelperStub $assetsHelperStub */
        $assetsHelperStub = self::getContainer()->get(AssetsHelperStub::class);
        $this->assetsHelperStub = $assetsHelperStub;

        /** @var ActiveTrackingEntryHelperInterface $activeTrackingEntryHelper */
        $activeTrackingEntryHelper = self::getContainer()->get(ActiveTrackingEntryHelperInterface::class);
        $this->activeTrackingEntryHelper = $activeTrackingEntryHelper;

        /** @var HttpAsyncClientStub $autosuggestApiHttpAsyncClientStub */
        $autosuggestApiHttpAsyncClientStub = self::getContainer()->get(
            'brand_website.autosuggest_api_client.http_async_client',
        );
        $this->autosuggestApiHttpAsyncClientStub = $autosuggestApiHttpAsyncClientStub;

        $javaScriptRelatedTermsSettings = $this->stubs()->moduleSettings()->getJavaScriptRelatedTerms();
        $javaScriptRelatedTermsSettings->setEnabledForContent(true);
        $javaScriptRelatedTermsSettings->create();

        $this->stubs()->request()
            ->getGenericRequest()
            ->setPageviewId('pageview_id');

        /** @var WebsiteConfigurationHelper $websiteConfigurationHelper */
        $websiteConfigurationHelper = self::getContainer()->get(WebsiteConfigurationHelper::class);
        $this->websiteConfigurationHelper = $websiteConfigurationHelper;
    }

    /**
     * @return mixed[]
     */
    public static function endpointResponseDataProvider(): array
    {
        return [
            'with terms'                     => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'  => 'Check it out! A nice hotel in Amsterdam',
                        'asid' => 'cmp_123_abc',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['hotel Amsterdam', 'nice view hotel', 'Museumplein Amsterdam'],
            ],
            'with gclid'                     => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'   => 'Check it out! A nice hotel in Amsterdam',
                        'asid'  => 'cmp_123_g',
                        'gclid' => 'gclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'with msclkid'                   => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'     => 'Check it out! A nice hotel in Amsterdam',
                        'asid'    => 'cmp_123_ms',
                        'msclkid' => 'msclkid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'with fbclid'                    => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'    => 'Check it out! A nice hotel in Amsterdam',
                        'asid'   => 'cmp_123_fb',
                        'fbclid' => 'fbclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'with generic click ids'         => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'   => 'Check it out! A nice hotel in Amsterdam',
                        'asid'  => 'cmp_123_gen',
                        'clid'  => 'clid123123',
                        'sclid' => 'sclid123123',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'ignore extra url parameters'    => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'   => 'Check it out! A nice hotel in Amsterdam',
                        'asid'  => 'cmp_123_abc',
                        'hello' => 'world',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'remove specific url parameters' => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'  => 'Walt Disney World® Resort is the magical!',
                        'asid' => 'cmp_123_abc',
                        'q'    => 'Hotel Disney',
                        'pg'   => '2',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'valid style id'                 => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'      => 'Walt Disney World® Resort is the magical!',
                        'asid'     => 'cmp_123_abc',
                        'style_id' => '87654321',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'invalid style id is ignored'    => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'      => 'Walt Disney World® Resort is the magical!',
                        'asid'     => 'cmp_123_abc',
                        'style_id' => '8765aaa4321',
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'custom terms'                   => [
                'request'                    =>
                    self::createRequest(
                        '/p/related-terms-content/v1',
                        [
                            'rac'   => 'Walt Disney World® Resort is the magical!',
                            'asid'  => 'cmp_123_abc',
                            'terms' => [
                                'Iron Man',
                                'Wolverine',
                            ],
                            'nc'    => 1434,
                        ],
                    ),
                'javaScriptContentAvailable' => true,
                'terms'                      => ['Mickey Mouse', 'Donald Duck', 'Disney', 'Star Wars'],
            ],
            'maximum amount of custom terms' => [
                'request'                    => self::createRequest(
                    '/p/related-terms-content/v1',
                    [
                        'rac'   => 'Walt Disney World® Resort is the magical!',
                        'asid'  => 'cmp_123_abc',
                        'terms' => [
                            'Iron Man',
                            'Wolverine',
                            'Hulk',
                            'Thor',
                            'Loki',
                            'Thanos',
                            'Groot',
                            'Odin',
                            'Drax',
                            'Star Lord',
                            'Black Widow',
                            'Spiderman',
                            'Arrow',
                            'Flash',
                            'Cyborg',
                            'Artemis',
                            'Abel',
                            'Batwoman',
                        ],
                    ],
                ),
                'javaScriptContentAvailable' => true,
                'terms'                      => [],
            ],
        ];
    }

    /**
     * @param string[] $terms
     */
    #[DataProvider('endpointResponseDataProvider')]
    public function testEndpointResponse(
        Request $request,
        bool $javaScriptContentAvailable,
        array $terms
    ): void
    {
        // inject expected settings
        $websiteSettingsStub = new WebsiteSettingsStub();
        $websiteSettingsStub->getGoogleAdSense()
            ->setEnabled(true)
            ->setDefaultClient('default_client')
            ->setSemClient('sem_client')
            ->setWebClient('web_client');

        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);

        $this->autosuggestApiHttpAsyncClientStub->setRequestResponse(
            new ResponseStub(
                Response::HTTP_OK,
                new StreamStub(
                    json_encode($terms, JSON_THROW_ON_ERROR),
                ),
            ),
        );

        if ($javaScriptContentAvailable) {
            $this->assetsHelperStub->setJavaScriptEntryFileContents(
                'JavaScriptRelatedTerms',
                (string)file_get_contents(sprintf('%s/_assets/dummy_entry.js', __DIR__)),
            );
        } else {
            $this->assetsHelperStub->setJavaScriptEntryFileContents(
                'JavaScriptRelatedTerms',
                null,
            );
        }

        $trackingEntryStubBuilder = new TrackingEntryStubBuilder();
        $trackingEntryStubBuilder->setIsEmpty(true);
        $trackingEntryStubBuilder->setAccountId(1);
        $trackingEntryStubBuilder->setCampaignName($request->query->getString('asid'));

        $this->activeTrackingEntryHelper->setActiveTrackingEntry($trackingEntryStubBuilder->create());

        $foodBrandConfig = $this->brandConfigTestHelper->getFoodBrandConfig();
        $websiteConfiguration = new WebsiteConfiguration(
            [
                ...$foodBrandConfig,
                WebsiteConfiguration::KEY_ACCOUNTS => [
                    1 => [
                        AccountSettings::KEY_CAMPAIGNS => [
                            $request->query->getString('asid') => [
                                AccountSettings::KEY_SERVICE => 'service',
                            ],
                        ],
                    ],
                ],
            ],
        );

        $this->websiteConfigurationHelper->setConfiguration($websiteConfiguration);

        // handle request
        $response = $this->handleRequest($request);
        $actualContent = (string)$response->getContent();
        $actualContent = preg_replace('/https:(.*)\/(p|dsrw|js-error)/', 'https://localhost\/$2', $actualContent);
        $actualContent = preg_replace('/(&?ste=[^&]*)",/U', '",', (string)$actualContent);
        $actualContent = preg_replace('/(&?ste=[^&]*)/', '', (string)$actualContent);

        self::assertSame(
            Response::HTTP_OK,
            $response->getStatusCode(),
            'Endpoint status code when module is enabled',
        );

        $assertionFile = $this->initGenericAssertionFile((string)$actualContent, 'js');
        $assertionFile->assertSame();
    }
}

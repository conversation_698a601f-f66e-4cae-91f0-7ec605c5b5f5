<?php

declare(strict_types=1);

namespace Tests\Integration\JavaScriptRelatedTerms\Request;

use App\JavaScriptRelatedTerms\Request\JavaScriptRelatedTermsViewRequestInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractRequestIntegrationTestCase;

class JavaScriptRelatedTermsViewRequestTest extends AbstractRequestIntegrationTestCase
{
    private JavaScriptRelatedTermsViewRequestInterface $javaScriptRelatedTermsViewRequest;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var JavaScriptRelatedTermsViewRequestInterface $javaScriptRelatedTermsViewRequest */
        $javaScriptRelatedTermsViewRequest = self::getContainer()->get(JavaScriptRelatedTermsViewRequestInterface::class);
        $this->javaScriptRelatedTermsViewRequest = $javaScriptRelatedTermsViewRequest;
    }

    /**
     * @return mixed[]
     */
    public static function urlParsingDataProvider(): array
    {
        $defaultExpectedValues = [
            JavaScriptRelatedTermsViewRequestInterface::PARAMETER_QUERY    => null,
            JavaScriptRelatedTermsViewRequestInterface::PARAMETER_URL      => null,
            JavaScriptRelatedTermsViewRequestInterface::PARAMETER_STYLE_ID => null,
            JavaScriptRelatedTermsViewRequestInterface::PARAMETER_TERMS    => null,
        ];

        return [
            'valid'                  => [
                'url'            => sprintf('/?q=ipad&url=%s&style_id=%d', urlencode('https://my.pizza.nl'), 12345678),
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_QUERY    => 'ipad',
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_URL      => 'https://my.pizza.nl',
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_STYLE_ID => 12345678,
                ],
            ],
            'invalid url value'      => [
                'url'            => sprintf('/?q=ipad&url=%s&style_id=%d', urlencode('not a url'), 12345678),
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_QUERY    => 'ipad',
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_STYLE_ID => 12345678,
                ],
            ],
            'invalid style_id value' => [
                'url'            => sprintf('/?q=ipad&url=%s&style_id=%s', urlencode('https://my.pizza.nl'), urlencode('123pizza.nl')),
                'expectedValues' => [
                    ...$defaultExpectedValues,
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_QUERY => 'ipad',
                    JavaScriptRelatedTermsViewRequestInterface::PARAMETER_URL   => 'https://my.pizza.nl',
                ],
            ],
            'empty'                  => [
                'url'            => '/',
                'expectedValues' => $defaultExpectedValues,
            ],
        ];
    }

    /**
     * @param mixed[] $expectedValues
     */
    #[DataProvider('urlParsingDataProvider')]
    public function testUrlParsing(string $url, array $expectedValues): void
    {
        $this->assertRequest(
            $url,
            null,
            $this->javaScriptRelatedTermsViewRequest,
            $expectedValues,
        );
    }
}

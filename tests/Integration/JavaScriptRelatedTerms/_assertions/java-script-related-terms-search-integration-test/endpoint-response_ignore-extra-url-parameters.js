var _vrtQuery = '';
var _vrtViewLogUrl = '';
var _vrtPageOptions = {};
var _vrtUnitOptions = {};
var _vrtGoogleLoaded = false;

function _vrtRun() {
    // Dummy code
}


_vrtIgnoredPageParams = _vrtGetIgnoredPageParams(["locale","q","tv"]);
_vrtQuery="Hotel Disney";
_vrtViewLogUrl="https:\/\/localhost\/p\/related-terms-search\/v1\/lv";
_vrtPageOptions={"pubId": "web_client", "query": "Hotel Disney", "adtest": "on", "hl": "nl", "ivt": false, "resultsPageBaseUrl": "https:\/\/localhost\/dsrw?asid=cmp_123_abc", "resultsPageQueryParam": "q", "ignoredPageParams": _vrtIgnoredPageParams};
_vrtUnitOptions={"styleId": 5136846704, "attributionSpacingBelow": 9, "colorAttribution": "#A0A0B0", "colorTitleLink": "#1A0DAB", "columnSpacing": 30, "fontSizeAttribution": "14px", "fontSizeTitle": "16px", "lineHeightTitle": "30px", "noTitleUnderline": true, "relatedSearches": 10, "rolloverLinkUnderline": true, "titleBold": true};

try {_vrtRun();} catch (error) {
    (new Image).src = 'https://localhost/js-error?error=' + encodeURIComponent(error) + '&url=' + encodeURIComponent(window.location.href);

        window.console && window.console.error && window.console.error(error);
}

var _vrtQuery = '';
var _vrtViewLogUrl = '';
var _vrtPageOptions = {};
var _vrtUnitOptions = {};
var _vrtGoogleLoaded = false;

function _vrtRun() {
    // Dummy code
}


_vrtIgnoredPageParams = _vrtGetIgnoredPageParams(["locale","q","tv","nc"]);
_vrtQuery="Hotel Disney";
_vrtViewLogUrl="https://localhost\/p\/related-terms-search\/v1\/lv";
_vrtPageOptions={"pubId": "web_client", "query": "Hotel Disney", "adtest": "on", "hl": "nl", "ivt": false, "ignoredPageParams": window._vrtIgnoredPageParams || null, "relatedSearchTargeting": "query", "resultsPageBaseUrl": "https://localhost\/dsrw?ppid=pageview_id&asid=cmp_123_g&gclid=gclid123123", "resultsPageQueryParam": "q"};
_vrtUnitOptions={"styleId": 5136846704, "relatedSearches": 10};

try {_vrtRun();} catch (error) {
    (new Image).src = 'https://localhost\/js-error?error=' + encodeURIComponent(error) + '&url=' + encodeURIComponent(window.location.href);

        window.console && window.console.error && window.console.error(error);
}

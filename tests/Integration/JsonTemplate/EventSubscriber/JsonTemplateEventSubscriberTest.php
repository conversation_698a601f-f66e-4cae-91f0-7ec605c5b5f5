<?php

declare(strict_types=1);

namespace Tests\Integration\JsonTemplate\EventSubscriber;

use App\JsonTemplate\EventSubscriber\JsonTemplateEventSubscriber;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\Template\Options\JsonTemplateOptions;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class JsonTemplateEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function setUp(): void
    {
        self::ensureKernelShutdown();
        parent::setUp();

        $request = new Request();
        $request->server->set('HTTP_HOST', 'id.brand.com');
        $this->stubs()->request()->getMainRequest()->setRequest($request);

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setSlug('testbrand');
        $brandSettingsStub->setPartnerSlug('partner');

        $websiteSettingsStub = $this->stubs()->websiteSettings();
        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        self::brandSettingsTestHelper()->injectBrandSettings($brandSettingsStub);
    }

    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateViewCreatedEvent::NAME,
            JsonTemplateEventSubscriber::class,
            'onJsonTemplateViewCreated',
        );
    }

    public function testOnJsonTemplateViewCreated(): void
    {
        $jsonTemplateViewFactory = self::getContainer()->get(JsonTemplateViewFactory::class);
        $projectDir = self::getContainer()->getParameter('kernel.project_dir');
        $jsonTemplate = new JsonTemplate(
            filePath       : $projectDir . '/resources/shared/templates_json/article/article.json',
            variant        : null,
            options        : new JsonTemplateOptions(
                                 keywordHighlight       : null,
                                 organicKeywordHighlight: null,
                                 organicLinkType        : null,
                                 pageHeadTagsType       : 'article',
                             ),
            layoutTemplate : '@theme/layout_default_components.html.twig',
            componentConfig: [],
        );
        $view = $jsonTemplateViewFactory->create(
            jsonTemplateFile: '@shared/templates_json/article/article.json',
            response        : new Response(),
        );
        $reflectionProperty = new \ReflectionProperty($view, 'jsonTemplate');
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($view, $jsonTemplate);
        $jsonTemplateEventSubscriber = new JsonTemplateEventSubscriber(
            projectDir: $projectDir,
        );
        $jsonTemplateEventSubscriber->onJsonTemplateViewCreated(
            new JsonTemplateViewCreatedEvent($view),
        );
        $expectedHeader = '/resources/shared/templates_json/article/article.json';
        $actualHeader = $view->getResponse()->headers->get('X-Log-Json_Template_File');
        self::assertEquals($expectedHeader, $actualHeader);
    }
}

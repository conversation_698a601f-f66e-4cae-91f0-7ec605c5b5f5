<?php

declare(strict_types=1);

namespace Tests\Integration\JsonTemplate\View;

use App\Brand\Settings\BrandSettingsHelper;
use App\BrandOverride\BrandOverrideModuleState;
use App\JsonTemplate\Component\ComponentIdRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequestBuilder;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use App\JsonTemplate\View\JsonTemplateViewHandler;
use App\JsonTemplate\View\JsonTemplateViewRegistry;
use App\SearchApi\Event\SearchApiRequestEvent;
use App\SearchApi\SearchApiManager;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Debug\TraceableEventDispatcher;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Integration\Helper\BrandConfigTestHelper;
use Tests\Integration\Helper\ComponentTestHelper;
use Tests\Integration\JsonTemplate\View\DataRequest\ViewDataRequestWithValidationBuilder;
use Tests\Stub\Vendor\CompositeSearchApiClient\CompositeSearchApiClientStub;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPage\Response\ContentPageResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPageCategory\Response\ContentPageCategoryResponseContext;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Context;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\SearchResponseInterface;
use Visymo\Shared\Domain\Locale\Locale;

final class JsonTemplateViewHandlerTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private BrandOverrideModuleState $brandOverrideModuleState;

    private CompositeSearchApiClientStub $compositeSearchApiClientStub;

    private ComponentTestHelper $componentTestHelper;

    private TraceableEventDispatcher $eventDispatcher;

    private JsonTemplateViewFactory $jsonTemplateViewFactory;

    private JsonTemplateViewHandler $jsonTemplateViewHandler;

    /** @var mixed[] */
    private array $compositeSearchApiRequests = [];

    protected function setUp(): void
    {
        parent::setUp();

        $moduleSettingsStubs = $this->stubs()->moduleSettings();

        // Enable content page module
        $moduleSettingsStubs
            ->getContentPage()
            ->setCollection('collection')
            ->create();

        // Enable DSR module
        $moduleSettingsStubs
            ->getDisplaySearchRelated()
            ->setEnabled(true)
            ->create();

        // Use default layout variants
        $moduleSettingsStubs
            ->getGroupComponent()
            ->create();

        $moduleSettingsStubs
            ->getBrandAssets()
            ->setBrandSlug('food')
            ->create();

        $this->stubs()
            ->request()
            ->getSearchRequest()
            ->reset();

        $this->stubs()
            ->request()
            ->getSeaRequest()
            ->setUserQuery(null);

        $this->stubs()->googleRelatedTermsContainerGenerator()->reset();

        $this->compositeSearchApiClientStub = $this->stubs()->compositeSearchApiClient();

        $localeSettingsHelper = $this->stubs()->localeSettingsHelper()
            ->setLocale(Locale::EN_US);

        /** @var BrandOverrideModuleState $brandOverrideModuleState */
        $brandOverrideModuleState = self::getContainer()->get(BrandOverrideModuleState::class);
        $this->brandOverrideModuleState = $brandOverrideModuleState;

        /** @var ViewDataRequestWithValidationBuilder $viewDataRequestWithValidationBuilder */
        $viewDataRequestWithValidationBuilder = self::getContainer()->get(ViewDataRequestWithValidationBuilder::class);
        $viewDataRequestWithValidationBuilder->enableInternalComponentValidation();

        $this->componentTestHelper = new ComponentTestHelper();

        /** @var JsonTemplateViewFactory $jsonTemplateViewFactory */
        $jsonTemplateViewFactory = self::getContainer()->get(JsonTemplateViewFactory::class);
        $this->jsonTemplateViewFactory = $jsonTemplateViewFactory;

        /** @var ViewDataRequestBuilder $viewDataRequestBuilder */
        $viewDataRequestBuilder = self::getContainer()->get(ViewDataRequestBuilder::class);

        /** @var SearchApiManager $searchApiManager */
        $searchApiManager = self::getContainer()->get(SearchApiManager::class);

        /** @var JsonTemplateViewRegistry $jsonTemplateViewRegistry */
        $jsonTemplateViewRegistry = self::getContainer()->get(JsonTemplateViewRegistry::class);

        /** @var TraceableEventDispatcher $eventDispatcher */
        $eventDispatcher = self::getContainer()->get('event_dispatcher');
        $this->eventDispatcher = $eventDispatcher;

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setName('Visymo')->setSlug('visymo');

        /** @var BrandSettingsHelper|MockObject $brandSettingsHelperStubMock */
        $brandSettingsHelperStubMock = $this->createConfiguredMock(
            BrandSettingsHelper::class,
            ['getSettings' => $brandSettingsStub],
        );

        $this->jsonTemplateViewHandler = new JsonTemplateViewHandler(
            viewDataRequestBuilder  : $viewDataRequestBuilder,
            searchApiManager        : $searchApiManager,
            jsonTemplateViewRegistry: $jsonTemplateViewRegistry,
            eventDispatcher         : $this->eventDispatcher,
            twig                    : $this->createMock(Environment::class),
            brandSettingsHelper     : $brandSettingsHelperStubMock,
            localeSettingsHelper    : $localeSettingsHelper,
        );

        ComponentIdRegistry::reset();

        self::websiteSettingsTestHelper()->injectFoodBrandWebsiteConfiguration();
        self::addBrandJsonTemplateLocation();
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        $data = [];

        // JSON templates without assertions
        self::addBrandJsonTemplateLocation();

        foreach (self::jsonTemplateTestHelper()->getJsonTemplateFiles() as $jsonTemplateFile) {
            $data[self::getTemplateDataName($jsonTemplateFile)] = [
                'jsonTemplateFile' => $jsonTemplateFile,
                'assertData'       => false,
            ];
        }

        // JSON templates with assertions
        $assertDataJsonTemplateFiles = [
            '@shared/templates_json/article/article.json',
            '@shared/templates_json/content_page_home/content_page_home_1.json',
            '@shared/templates_json/display_search_related/display_search_related.json',
            '@shared/templates_json/microsoft_search_related/microsoft_search_related_desktop.json',
            '@shared/templates_json/web_search/web_search_desktop.json',
        ];

        foreach ($assertDataJsonTemplateFiles as $jsonTemplateFile) {
            $data[self::getTemplateDataName($jsonTemplateFile)] = [
                'jsonTemplateFile' => $jsonTemplateFile,
                'assertData'       => true,
            ];
        }

        return $data;
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(
        string $jsonTemplateFile,
        bool $assertData
    ): void
    {
        if (!$assertData) {
            $this->expectNotToPerformAssertions();
        }

        $request = Request::create(
            sprintf('https://%s/', BrandConfigTestHelper::CONFIG_DOMAIN_ID_BRAND_COM),
        );
        $this->handleRequestRoute($request);
        $this->setRequest($request);

        $this->brandOverrideModuleState->setBrandSlug('food');

        $this->compositeSearchApiClientStub->setSearchResponse(
            $this->createSearchApiResponse(),
        );

        $this->eventDispatcher->addListener(
            SearchApiRequestEvent::NAME,
            $this->handleSearchApiRequest(...),
        );

        $view = $this->jsonTemplateViewFactory->create(
            jsonTemplateFile: $jsonTemplateFile,
        );
        $this->jsonTemplateViewHandler->handle($view);

        if (!$assertData) {
            return;
        }

        $data = [
            'composite_search_api_requests' => $this->compositeSearchApiRequests,
            'container'                     => $this->componentTestHelper->componentToArray(
                $view->getContainer(),
            ),
        ];

        $assertionFile = $this->initJsonAssertionFile($data);
        $assertionFile->assertSame();
    }

    private function handleSearchApiRequest(SearchApiRequestEvent $event): void
    {
        $requestData = [];

        foreach ($event->searchRequest->getContexts() as $requestContext) {
            $contextKey = $requestContext->getKey();
            $contextParameters = $requestContext->toParameters();

            if ($contextKey === Context::ORGANIC) {
                // sort setting is tied to brand
                unset($contextParameters['sort']);
            }

            if ($contextKey === Context::RELATED_TERMS) {
                // locale setting is tied to active domain
                unset($contextParameters['locale']);
            }

            if ($contextKey === Context::CONTENT_PAGE) {
                // Collection slug can be different per project/brand.
                unset($contextParameters['collection_slug']);
            }

            if ($contextKey === Context::CONTENT_PAGES) {
                // Collection slugs can be different per project/brand.
                unset($contextParameters['collection_slugs']);
            }

            $requestData[$contextKey] = $contextParameters;
        }

        $this->compositeSearchApiRequests[] = $requestData;
    }

    private static function getTemplateDataName(string $jsonTemplateFile): string
    {
        $name = [];
        $nameParts = explode(
            '/',
            str_replace('.json', '', $jsonTemplateFile),
        );

        foreach ($nameParts as $namePart) {
            if (str_starts_with($namePart, '@')) {
                continue;
            }

            if ($namePart === 'templates_json') {
                continue;
            }

            $name[] = $namePart;
        }

        return implode(' ', $name);
    }

    private function createSearchApiResponse(): SearchResponseInterface & MockObject
    {
        $contentPage = ContentPage::create(
            id                  : 1,
            publicId            : 1,
            locale              : 'en',
            collectionSlug      : 'collection',
            isHomepage          : false,
            isFallbackLocale    : false,
            isFallbackCollection: false,
            title               : 'Title',
            slug                : 'slug',
            category            : null,
            excerpt             : 'Excerpt',
            readingTime         : 1,
            publishedAt         : null,
            meta                : null,
            image               : null,
            paragraphs          : [],
            keywords            : [],
        );

        return $this->createConfiguredMock(
            SearchResponseInterface::class,
            [
                'getContentPage'         => ContentPageResponseContext::create(
                    source: 'none',
                    page  : $contentPage,
                    errors: [],
                ),
                'getContentPageCategory' => ContentPageCategoryResponseContext::createEmpty(),
            ],
        );
    }
}

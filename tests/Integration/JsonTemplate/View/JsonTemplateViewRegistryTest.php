<?php

declare(strict_types=1);

namespace Tests\Integration\JsonTemplate\View;

use App\JsonTemplate\Component\ComponentFactory;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\Template\JsonTemplateFactory;
use App\JsonTemplate\Template\Options\JsonTemplateOptions;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use App\JsonTemplate\View\JsonTemplateViewRegistry;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Debug\TraceableEventDispatcher;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsHelperStub;

final class JsonTemplateViewRegistryTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private JsonTemplateViewRegistry $jsonTemplateViewRegistry;

    private JsonTemplateFactory & MockObject $jsonTemplateFactoryMock;

    private JsonTemplateViewFactory $jsonTemplateViewFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->stubs()->request()
            ->getSearchRequest()
            ->setQuery('hello');

        $this->stubs()->request()
            ->getSeaRequest()
            ->setUserQuery('hello');

        $this->stubs()->localeSettingsHelper();

        $websiteSettingsStub = $this->stubs()->websiteSettings();

        /** @var WebsiteSettingsHelperStub $websiteSettingsHelperStub */
        $websiteSettingsHelperStub = self::getContainer()->get(WebsiteSettingsHelper::class);
        $websiteSettingsHelperStub->setWebsiteSettings($websiteSettingsStub);

        $this->setRequest(Request::create('localhost'));

        /** @var JsonTemplateViewRegistry $jsonTemplateViewRegistry */
        $jsonTemplateViewRegistry = self::getContainer()->get(JsonTemplateViewRegistry::class);
        $this->jsonTemplateViewRegistry = $jsonTemplateViewRegistry;

        $this->jsonTemplateFactoryMock = $this->createMock(JsonTemplateFactory::class);

        /** @var ComponentFactory $componentFactory */
        $componentFactory = self::getContainer()->get(ComponentFactory::class);

        /** @var TraceableEventDispatcher $eventDispatcher */
        $eventDispatcher = self::getContainer()->get('event_dispatcher');

        $this->jsonTemplateViewFactory = new JsonTemplateViewFactory(
            jsonTemplateFactory: $this->jsonTemplateFactoryMock,
            componentFactory   : $componentFactory,
            eventDispatcher    : $eventDispatcher,
        );
    }

    public function testGetJsonTemplateViewData(): void
    {
        self::assertNull($this->jsonTemplateViewRegistry->getView());

        $jsonTemplate = new JsonTemplate(
            'test.json',
            null,
            new JsonTemplateOptions(
                null,
                null,
                null,
                'search',
            ),
            'layout',
            [],
        );

        $this->jsonTemplateFactoryMock->method('create')->willReturn($jsonTemplate);

        $this->jsonTemplateViewRegistry->setView(
            $this->jsonTemplateViewFactory->create(
                jsonTemplateFile: 'test.json',
                response        : new Response(),
            ),
        );

        self::assertNotNull($this->jsonTemplateViewRegistry->getView());
    }

    public function testGetJsonTemplateWithVariantViewData(): void
    {
        self::assertNull($this->jsonTemplateViewRegistry->getView());

        $jsonTemplate = new JsonTemplate(
            'test.json',
            'foo',
            new JsonTemplateOptions(
                null,
                null,
                null,
                'search',
            ),
            'layout',
            [],
        );

        $this->jsonTemplateFactoryMock->method('create')->willReturn($jsonTemplate);

        $view = $this->jsonTemplateViewFactory->create(
            jsonTemplateFile: 'test.json',
            response        : new Response(),
        );

        self::assertSame('foo', $view->getJsonTemplate()->variant);
    }
}

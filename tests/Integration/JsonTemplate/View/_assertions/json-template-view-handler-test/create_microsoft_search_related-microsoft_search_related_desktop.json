{"composite_search_api_requests": [{"content_page": {"enabled": true, "is_homepage": null, "public_id": null, "paragraph_amount": null, "is_adult": null}, "related_terms": {"enabled": true, "amount": 6}}], "container": {"id": "container-0", "type": "container", "layout": "dsr", "mode": null, "font": null, "components": [{"id": "brand_logo-13", "type": "brand_logo", "componentSpaceModifiers": ["top"], "layout": "default", "logoDarkMode": false, "linkToHome": true}, {"id": "columns-14", "type": "columns", "layout": "default", "one": [{"id": "search_bar-15", "type": "search_bar", "componentSpaceModifiers": ["top-l"], "layout": "default", "showSearchQuery": false, "allowStartQuerySearch": true, "autofocus": false}, {"id": "has_content_page-16", "type": "has_content_page", "matchingSegment": [{"id": "content_page_header-5", "type": "content_page_header", "componentSpaceModifiers": ["top"], "layout": "default", "showReadTime": true, "showPageNumber": true}, {"id": "current_page_matches-17", "type": "current_page_matches", "page": 1, "matchingSegment": [{"id": "content_page_title-6", "type": "content_page_title", "componentSpaceModifiers": [], "layout": "default"}, {"id": "content_page_excerpt-7", "type": "content_page_excerpt", "componentSpaceModifiers": ["top", "bottom"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}], "nonMatchingSegment": [{"id": "content_page_paragraph-1", "type": "content_page_paragraph", "componentSpaceModifiers": ["top", "bottom"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}]}], "nonMatchingSegment": [{"id": "organic_results-18", "type": "organic_results", "componentSpaceModifiers": ["bottom"], "resultDescriptionMoreLink": true, "resultDisplayUrlLink": false, "resultTitleLink": false, "showResultDisplayUrl": false, "maxDescriptionLength": 500, "layout": "default"}]}, {"id": "related_terms-11", "type": "related_terms", "componentSpaceModifiers": [], "layout": "chevron", "amount": 6, "zone": "i", "route": "route_microsoft_search_related_web", "columns": 1, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}, {"id": "has_content_page-19", "type": "has_content_page", "matchingSegment": [{"id": "content_page_paragraph-2", "type": "content_page_paragraph", "componentSpaceModifiers": [], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}, {"id": "content_page_paragraph-3", "type": "content_page_paragraph", "componentSpaceModifiers": ["top", "bottom-xl"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}], "nonMatchingSegment": [{"id": "title-20", "type": "title", "componentSpaceModifiers": ["top-l"], "title": null, "titleHighlight": null, "titleTranslationId": "meta.description.display", "titleHighlightTranslationId": null, "subtitleTranslationId": null, "layout": "display"}, {"id": "organic_results-21", "type": "organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": true, "resultDisplayUrlLink": false, "resultTitleLink": false, "showResultDisplayUrl": false, "maxDescriptionLength": null, "layout": "default"}]}, {"id": "current_page_matches-22", "type": "current_page_matches", "page": 1, "matchingSegment": [{"id": "related_terms-12", "type": "related_terms", "componentSpaceModifiers": [], "layout": "chevron", "amount": 4, "zone": "i", "route": "route_microsoft_search_related_web", "columns": 1, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": []}, {"id": "has_content_page-23", "type": "has_content_page", "matchingSegment": [{"id": "content_page_paragraph-4", "type": "content_page_paragraph", "componentSpaceModifiers": ["top", "bottom"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}, {"id": "content_page_continue_reading-8", "type": "content_page_continue_reading", "componentSpaceModifiers": [], "layout": "default"}, {"id": "content_page_footer-9", "type": "content_page_footer", "componentSpaceModifiers": [], "layout": "default"}, {"id": "share_page-10", "type": "share_page", "componentSpaceModifiers": [], "layout": "default", "share": "content_page"}], "nonMatchingSegment": [{"id": "organic_error_message-24", "type": "organic_error_message"}]}], "two": [], "three": [], "mainColumn": null, "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "footer-25", "type": "footer", "layout": "default", "components": [{"id": "columns-26", "type": "columns", "layout": "default", "one": [{"id": "footer_logo-27", "type": "footer_logo", "layout": "default", "logoDarkMode": false, "logoStyleFilter": null, "hideOnDesktop": true}, {"id": "disclaimer-28", "type": "disclaimer", "componentSpaceModifiers": [], "layout": "default"}], "two": [], "three": [], "mainColumn": null, "section": "footer", "sectionVisible": true, "sectionCssProperties": ["background"]}, {"id": "footer_navigation-29", "type": "footer_navigation", "componentSpaceModifiers": [], "showAbout": true, "showContact": true, "showCopyright": true, "showDisclaimer": true, "showPrivacy": true, "layout": "dsr", "logoDarkMode": false}]}]}}
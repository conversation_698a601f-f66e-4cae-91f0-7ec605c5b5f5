{"composite_search_api_requests": [{"content_pages": {"enabled": true, "is_homepage": null, "public_ids": null, "excluded_public_ids": null, "relevant_for_public_id": null, "category_public_id": null, "has_image": null, "sort": "date_created_desc", "page_size": 10, "page": 1, "paragraph_amount": 0, "is_adult": null}, "related_terms": {"enabled": true, "amount": 12}}, {"organic": {"enabled": true, "page": 1, "page_size": 9}}], "container": {"id": "container-0", "type": "container", "layout": "default", "mode": null, "font": null, "components": [{"id": "search_header-7", "type": "search_header", "componentSpaceModifiers": [], "layout": "default", "autofocus": false, "logoDarkMode": false, "logoStyleFilter": null, "showBackgroundOnDesktop": false, "showBackgroundOnMobile": true, "showBackgroundOnTablet": true, "showHamburgerMenuOnDesktop": true, "showHamburgerMenuOnMobile": false, "showHamburgerMenuOnTablet": false, "showMainMenuMoreOnDesktop": false, "showMainMenuMoreOnMobile": true, "showMainMenuMoreOnTablet": false, "showMainMenuOnDesktop": false, "showMainMenuOnMobile": true, "showMainMenuOnTablet": true, "showSubMenuOnDesktop": false, "showSubMenuOnMobile": false, "showSubMenuOnTablet": true, "showSearchQuery": true}, {"id": "columns-8", "type": "columns", "layout": "default", "one": [{"id": "split_test_matches-9", "type": "split_test_matches", "oneOfVariants": ["innomrc"], "matchingSegment": [{"id": "pill_related_terms-10", "type": "pill_related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 8}], "nonMatchingSegment": []}, {"id": "split_test_matches-11", "type": "split_test_matches", "oneOfVariants": ["innosmrc"], "matchingSegment": [{"id": "pill_related_terms-12", "type": "pill_related_terms", "componentSpaceModifiers": [], "layout": "stacked", "amount": 8}], "nonMatchingSegment": []}, {"id": "is_ad_bot-13", "type": "is_ad_bot", "matchingSegment": [{"id": "web_search_stats_title-14", "type": "web_search_stats_title", "layout": "default", "showRandomStats": true}], "nonMatchingSegment": [{"id": "web_search_stats_title-15", "type": "web_search_stats_title", "layout": "default", "showRandomStats": false}]}, {"id": "dynamic_ads-16", "type": "dynamic_ads", "components": [{"id": "google_ads_top_unit-1", "type": "google_ads_top_unit", "amount": 4, "container": "csa-top"}]}, {"id": "split_test_matches-17", "type": "split_test_matches", "oneOfVariants": ["rtbl"], "matchingSegment": [{"id": "related_terms-18", "type": "related_terms", "componentSpaceModifiers": [], "layout": "pill", "amount": 12, "zone": "i", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": [{"id": "related_terms-5", "type": "related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 12, "zone": "i", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}]}, {"id": "is_ad_bot-19", "type": "is_ad_bot", "matchingSegment": [{"id": "dynamic_ads-20", "type": "dynamic_ads", "components": [{"id": "google_ads_bottom_unit-21", "type": "google_ads_bottom_unit", "amount": 5, "numRepeated": 4, "container": "csa-bottom"}]}, {"id": "related_terms-22", "type": "related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 12, "zone": "b", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": [{"id": "organic_results_title-23", "type": "organic_results_title", "componentSpaceModifiers": ["top"], "layout": "default", "showQuery": true}, {"id": "organic_results_with_fallback-24", "type": "organic_results_with_fallback", "resultsComponent": {"id": "content_page_results_as_organic_results-2", "type": "content_page_results_as_organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": true, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": null, "layout": "default", "linkToActiveBrand": false}, "fallbackComponent": {"id": "organic_results-3", "type": "organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": true, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": null, "layout": "default"}}, {"id": "organic_error_message-25", "type": "organic_error_message"}, {"id": "has_organic_results-26", "type": "has_organic_results", "matchingSegment": [{"id": "dynamic_ads-27", "type": "dynamic_ads", "components": [{"id": "google_ads_bottom_unit-4", "type": "google_ads_bottom_unit", "amount": 5, "numRepeated": 4, "container": "csa-bottom"}]}, {"id": "split_test_matches-28", "type": "split_test_matches", "oneOfVariants": ["innomrc", "innosmrc"], "matchingSegment": [], "nonMatchingSegment": [{"id": "split_test_matches-29", "type": "split_test_matches", "oneOfVariants": ["rtbl"], "matchingSegment": [{"id": "related_terms-30", "type": "related_terms", "componentSpaceModifiers": [], "layout": "pill", "amount": 12, "zone": "b", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}], "nonMatchingSegment": [{"id": "related_terms-6", "type": "related_terms", "componentSpaceModifiers": [], "layout": "default", "amount": 12, "zone": "b", "route": null, "columns": 2, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}]}]}, {"id": "search_bar-31", "type": "search_bar", "componentSpaceModifiers": [], "layout": "default", "showSearchQuery": true, "allowStartQuerySearch": true, "autofocus": false}], "nonMatchingSegment": []}, {"id": "organic_pagination-32", "type": "organic_pagination"}]}], "two": [], "three": [], "mainColumn": "one", "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "footer-33", "type": "footer", "layout": "default", "components": [{"id": "footer_logo-34", "type": "footer_logo", "layout": "default", "logoDarkMode": false, "logoStyleFilter": null, "hideOnDesktop": true}, {"id": "footer_navigation-35", "type": "footer_navigation", "componentSpaceModifiers": [], "showAbout": true, "showContact": true, "showCopyright": true, "showDisclaimer": true, "showPrivacy": true, "layout": "borderless", "logoDarkMode": false}]}]}}
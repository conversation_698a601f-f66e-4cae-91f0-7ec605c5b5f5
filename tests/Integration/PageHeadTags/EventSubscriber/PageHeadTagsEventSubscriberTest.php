<?php

declare(strict_types=1);

namespace Tests\Integration\PageHeadTags\EventSubscriber;

use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\PageHeadTags\EventSubscriber\PageHeadTagsEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class PageHeadTagsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            PageHeadTagsEventSubscriber::class,
            'onJsonTemplateHandled',
        );

        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            PageHeadTagsEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }

    public function testOnJsonTemplateHandledCallsInitFromView(): void
    {
        $twig = $this->createMock(\Twig\Environment::class);
        $pageHeadTagsHelper = $this->createMock(\App\PageHeadTags\Tags\PageHeadTagsHelper::class);
        $subscriber = new \App\PageHeadTags\EventSubscriber\PageHeadTagsEventSubscriber($twig, $pageHeadTagsHelper);

        $view = $this->createMock(\App\JsonTemplate\View\ViewInterface::class);
        $event = new \App\JsonTemplate\Event\JsonTemplateHandledEvent($view);

        $pageHeadTagsHelper->expects($this->once())
            ->method('initFromView')
            ->with($view);

        $subscriber->onJsonTemplateHandled($event);
    }

    public function testRenderTemplateHeadersAddsRenderedItem(): void
    {
        $twig = $this->createMock(\Twig\Environment::class);
        $pageHeadTagsHelper = $this->createMock(\App\PageHeadTags\Tags\PageHeadTagsHelper::class);
        $subscriber = new \App\PageHeadTags\EventSubscriber\PageHeadTagsEventSubscriber($twig, $pageHeadTagsHelper);

        $pageHeadTags = new \App\PageHeadTags\Tags\PageHeadTags(title: 'Test Title');
        $event = new \App\Template\Event\RenderTemplateHeadersEvent();

        $pageHeadTagsHelper->method('getPageHeadTags')->willReturn($pageHeadTags);
        $twig->method('render')
            ->with('@theme/page_head_tags/page_head_tags.html.twig', ['page_head_tags' => $pageHeadTags])
            ->willReturn('rendered_content');

        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_content', $event->getItems()[0]);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\Project\Config;

use App\Project\Config\ProjectConfigImporter;
use App\Project\Config\ProjectConfigNormalizer;
use App\Project\Exception\ProjectConfigImportFailedException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;
use Visymo\Shared\Domain\Validator\JsonSchemaValidator;

// phpcs:disable SlevomatCodingStandard.PHP.DisallowReference.DisallowedInheritingVariableByReference
class ProjectConfigImporterTest extends AbstractSymfonyIntegrationTest
{
    private ProjectConfigImporter $projectConfigImporter;

    private SerializedFileInterface & MockObject $projectConfigJsonFileMock;

    private SerializedFileInterface & MockObject $projectConfigPhpFileMock;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var JsonSchemaValidator $projectConfigJsonSchemaValidator */
        $projectConfigJsonSchemaValidator = self::getContainer()->get('brand_website.project_config.json_schema_validator');

        /** @var SerializedFileInterface & MockObject $projectConfigJsonFileMock */
        $projectConfigJsonFileMock = $this->createMock(SerializedFileInterface::class);
        $this->projectConfigJsonFileMock = $projectConfigJsonFileMock;

        /** @var SerializedFileInterface & MockObject $projectConfigPhpFileMock */
        $projectConfigPhpFileMock = $this->createMock(SerializedFileInterface::class);
        $this->projectConfigPhpFileMock = $projectConfigPhpFileMock;

        $this->projectConfigImporter = new ProjectConfigImporter(
            projectConfigJsonSchemaValidator: $projectConfigJsonSchemaValidator,
            projectConfigJsonFile           : $this->projectConfigJsonFileMock,
            projectConfigPhpFile            : $this->projectConfigPhpFileMock,
            projectConfigNormalizer         : new ProjectConfigNormalizer(),
        );
    }

    /**
     * @return mixed[]
     */
    public static function importDataProvider(): array
    {
        return [
            'valid json file'   => [
                'jsonFilePath'      => __DIR__.'/_config/valid.json',
                'expectedException' => null,
            ],
            'invalid json file' => [
                'jsonFilePath'      => __DIR__.'/_config/invalid.json',
                'expectedException' => ProjectConfigImportFailedException::class,
            ],
        ];
    }

    #[DataProvider('importDataProvider')]
    public function testImport(string $jsonFilePath, ?string $expectedException): void
    {
        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $fileContents = (string)file_get_contents($jsonFilePath);

        $this->projectConfigJsonFileMock->method('getContents')->willReturn(
            json_decode($fileContents, true, 512, JSON_THROW_ON_ERROR),
        );

        $actualConfig = [];
        $this->projectConfigPhpFileMock->method('writeContent')->willReturnCallback(
        /**
         * @param mixed[] $config
         */
            static function (array $config) use (&$actualConfig): void {
                $actualConfig = $config;
            },
        );

        $this->projectConfigImporter->import();

        $assertionFile = $this->initJsonAssertionFile($actualConfig);
        $assertionFile->assertSame();
    }
}

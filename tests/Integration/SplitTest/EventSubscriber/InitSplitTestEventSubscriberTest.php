<?php

declare(strict_types=1);

namespace Tests\Integration\SplitTest\EventSubscriber;

use App\SplitTest\EventSubscriber\InitSplitTestEventSubscriber;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class InitSplitTestEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelEvents::CONTROLLER,
            InitSplitTestEventSubscriber::class,
            'initSplitTest',
        );
    }

    public function testInitSplitTestCallsStartSplitTestOnMainRequest(): void
    {
        $splitTestActivateHelper = $this->createMock(\App\SplitTest\Activate\SplitTestActivateHelper::class);
        $splitTestActivateHelper->expects($this->once())
            ->method('startSplitTest');

        $subscriber = new \App\SplitTest\EventSubscriber\InitSplitTestEventSubscriber($splitTestActivateHelper);

        $kernel = $this->createMock(HttpKernelInterface::class);
        $request = $this->createMock(Request::class);
        $event = new ControllerEvent(
            $kernel,
            static function (): void {},
            $request,
            HttpKernelInterface::MAIN_REQUEST
        );

        $subscriber->initSplitTest($event);
    }

    public function testInitSplitTestDoesNothingOnSubRequest(): void
    {
        $splitTestActivateHelper = $this->createMock(\App\SplitTest\Activate\SplitTestActivateHelper::class);
        $splitTestActivateHelper->expects($this->never())
            ->method('startSplitTest');

        $subscriber = new \App\SplitTest\EventSubscriber\InitSplitTestEventSubscriber($splitTestActivateHelper);

        $kernel = $this->createMock(HttpKernelInterface::class);
        $request = $this->createMock(Request::class);
        $event = new ControllerEvent(
            $kernel,
            static function (): void {},
            $request,
            HttpKernelInterface::SUB_REQUEST
        );

        $subscriber->initSplitTest($event);
    }
}

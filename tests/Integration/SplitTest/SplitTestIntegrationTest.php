<?php /** @noinspection LongLine */

declare(strict_types=1);

namespace Tests\Integration\SplitTest;

use App\SplitTest\Activate\ActiveSplitTest;
use App\SplitTest\Settings\SplitTestSettings;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Request;
use Tests\Integration\WebsiteSettings\AbstractWebsiteSettingsTestCase;
use Tests\Stub\Domain\Settings\DomainSettingsStubBuilder;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;

class SplitTestIntegrationTest extends AbstractWebsiteSettingsTestCase
{
    private SplitTestExtendedReaderInterface $splitTestExtendedReader;

    protected WebsiteConfigurationHelper $websiteConfigurationHelper;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->stubs()->localeSettingsHelper();

        $this->stubs()->moduleSettings()->getWebSearch()->setEnabled()->setStyleId(**********)->create();

        /** @var DateTimeFactoryStub $dateTimeFactoryStub */
        $dateTimeFactoryStub = self::getContainer()->get(DateTimeFactory::class);
        $dateTimeFactoryStub->setDateTime(
            new \DateTime('08-05-2023 12:08:00', TimezoneEnum::UTC->toDateTimeZone()),
        );

        /** @var SplitTestExtendedReaderInterface $splitTestExtendedReader */
        $splitTestExtendedReader = self::getContainer()->get(SplitTestExtendedReaderInterface::class);
        $this->splitTestExtendedReader = $splitTestExtendedReader;

        /** @var WebsiteConfigurationHelper $websiteConfigurationHelper */
        $websiteConfigurationHelper = self::getContainer()->get(WebsiteConfigurationHelper::class);
        $this->websiteConfigurationHelper = $websiteConfigurationHelper;

        $this->stubs()->domainSettingsHelper()
            ->setSettings(
                (new DomainSettingsStubBuilder())
                    ->setHost('nl.example.org')
                    ->create(),
            );
    }

    /**
     * @return mixed[]
     */
    public static function activationDataProvider(): array
    {
        return [
            /**
             * Index page without test
             *
             * This testcase exists to verify that regular pages do not generate exceptions
             */
            'index without test'               => [
                'request'           => self::createRequest('/'),
                'splitTestSettings' => [],
            ],
            /**
             * Landingpage without test
             *
             * This testcase exists to verify that landing pages do not generate exceptions
             */
            'landingpage without test'         => [
                'request'           => self::createRequest('/ws'),
                'splitTestSettings' => [],
            ],
            /**
             * Landingpage with test activation
             *
             * This testcase exists to test that basic test activation works as expected
             */
            'landingpage with test activation' => [
                'request'           => self::createRequest(
                    path      : '/ws',
                    parameters: [
                                    'q'                  => 'Audi A4',
                                    'ac'                 => 123123,
                                    'asid'               => 'example_asisd',
                                    'de'                 => 'c',
                                    'cid'                => 565656,
                                    'aid'                => 676767,
                                    'kid'                => 787878,
                                    'nw'                 => 'g',
                                    'gclid'              => uniqid('gclid-', false),
                                    'debug_country_code' => 'NL',
                                ],
                ),
                'splitTestSettings' => [
                    1000 => [
                        'activation'       => [
                            'device'     => null,
                            'service'    => null,
                            'date_start' => '08-05-2023 00:00:00',
                            'date_end'   => '08-05-2023 23:59:59',
                            'domains'    => [
                                'nl.example.org',
                            ],
                            'routes'     => [],
                        ],
                        'control_channels' => [
                            'advertised'  => 'control_channel_advertised',
                            'landingpage' => 'control_channel_landingpage',
                        ],
                        'variants'         => [
                            [
                                'variant'          => 'variant_a',
                                'container_suffix' => null,
                                'channels'         => [
                                    'advertised'  => 'variant_a_channel_advertised',
                                    'landingpage' => 'variant_a_channel_landingpage',
                                ],
                                'percentage'       => 100,
                            ],
                        ],
                    ],
                ],
            ],
            /**
             * TrackingPixel endpoint with a tracking entry without an active test
             * The test has a route filter and should not match.
             *
             * This testcase exists for reproducing/fixing SERP-3668, where a new test was incorrectly activated
             */
            'tp without activated test'        => [
                'request'           => self::createRequest(
                    path      : '/tp/ga',
                    parameters: [
                                    'q'     => 'Audi A4',
                                    'vid'   => uniqid('vid-', false),
                                    'ste'   => self::trackingEntryTestHelper()->serialize(
                                        (new TrackingEntryStubBuilder())
                                            ->setIsEmpty(false)
                                            ->setQuery('Audi A4')
                                            ->setCreatedAt(new \DateTime('now', TimezoneEnum::UTC->toDateTimeZone()))
                                            ->create(),
                                    ),
                                    'block' => 1,
                                    'ad'    => 4,
                                ],
                ),
                'splitTestSettings' => [
                    1000 => [
                        'activation'       => [
                            'device'     => null,
                            'service'    => null,
                            'date_start' => '08-05-2023 00:00:00',
                            'date_end'   => '08-05-2023 23:59:59',
                            'domains'    => [
                                'nl.example.org',
                            ],
                            'routes'     => [
                                'default_search_landing_page',
                            ],
                        ],
                        'control_channels' => [
                            'advertised'  => 'control_channel_advertised',
                            'landingpage' => 'control_channel_landingpage',
                        ],
                        'variants'         => [
                            [
                                'variant'          => 'variant_a',
                                'container_suffix' => null,
                                'channels'         => [
                                    'advertised'  => 'variant_a_channel_advertised',
                                    'landingpage' => 'variant_a_channel_landingpage',
                                ],
                                'percentage'       => 100,
                            ],
                        ],
                    ],
                ],
            ],
            /**
             * TrackingPixel endpoint with a tracking entry with an active test
             * Although the test has a route filter, the test should still be activated because it is already activated
             * in the tracking entry.
             *
             * This testcase exists to ensure fix of SERP-3668 works as intended
             */
            'tp with already activated test'   => [
                'request'           => self::createRequest(
                    path      : '/tp/ga',
                    parameters: [
                                    'q'     => 'Audi A4',
                                    'vid'   => uniqid('vid-', false),
                                    'ste'   => self::trackingEntryTestHelper()->serialize(
                                        (new TrackingEntryStubBuilder())
                                            ->setIsEmpty(false)
                                            ->setQuery('Audi A4')
                                            ->setCreatedAt(new \DateTime('now', TimezoneEnum::UTC->toDateTimeZone()))
                                            ->setActiveSplitTest(
                                                new ActiveSplitTest(1000, 'variant_a'),
                                            )
                                            ->create(),
                                    ),
                                    'block' => 1,
                                    'ad'    => 4,
                                ],
                ),
                'splitTestSettings' => [
                    1000 => [
                        'activation'       => [
                            'device'     => null,
                            'service'    => null,
                            'date_start' => '08-05-2023 00:00:00',
                            'date_end'   => '08-05-2023 23:59:59',
                            'domains'    => [
                                'nl.example.org',
                            ],
                            'routes'     => [
                                'default_search_landing_page',
                            ],
                        ],
                        'control_channels' => [
                            'advertised'  => 'control_channel_advertised',
                            'landingpage' => 'control_channel_landingpage',
                        ],
                        'variants'         => [
                            [
                                'variant'          => 'variant_a',
                                'container_suffix' => null,
                                'channels'         => [
                                    'advertised'  => 'variant_a_channel_advertised',
                                    'landingpage' => 'variant_a_channel_landingpage',
                                ],
                                'percentage'       => 0, // percentage set to zero to ensure it is not started by accident
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @param SplitTestSettings[] $splitTestSettings
     */
    #[DataProvider('activationDataProvider')]
    public function testActivation(
        Request $request,
        array $splitTestSettings
    ): void
    {
        $websiteSettingsStub = new WebsiteSettingsStub();

        $foodBrandConfig = $this->brandConfigTestHelper->getFoodBrandConfig();
        $foodBrandAssets = $this->brandConfigTestHelper->getFoodBrandAssets();
        $websiteConfiguration = new WebsiteConfiguration(
            [
                ...$foodBrandConfig,
                WebsiteConfiguration::KEY_SPLIT_TESTS => $splitTestSettings,
            ],
        );

        $this->websiteConfigurationHelper->setConfiguration($websiteConfiguration);

        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        $this->stubs()->moduleSettings()->getBrandAssets()
            ->setConfig($foodBrandAssets['assets'])
            ->create();

        $this->handleRequest($request);

        $activeSplitTestData = [
            'id'               => $this->splitTestExtendedReader->getId(),
            'channel'          => $this->splitTestExtendedReader->getChannel(),
            'container_suffix' => $this->splitTestExtendedReader->getContainerSuffix(),
            'variant'          => $this->splitTestExtendedReader->getVariant(),
        ];

        $activeSplitTestDataFile = $this->initJsonAssertionFile($activeSplitTestData);
        $activeSplitTestDataFile->assertSame();
    }
}

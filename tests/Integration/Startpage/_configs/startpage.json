{"columns": [{"id": 1, "block": [{"id": 99353, "title": "Corona crisis", "user_block": 1, "visible": true, "color": "red", "adult": "", "no_mobile": "", "is_html": "", "block_content": [{"position": 0, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://coronadashboard.rijksoverheid.nl/", "url_visible": "", "title": "Dashboard coronavirus", "tracking_name": "dashboard", "tracking_id": "", "internal_link": 0}, {"position": 1, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://www.rijksoverheid.nl/onderwerpen/coronavirus-covid-19", "url_visible": "", "title": "Informatie rijksoverheid", "tracking_name": "", "tracking_id": "", "internal_link": 0}, {"position": 2, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://www.rivm.nl/coronavirus/covid-19/vragen-antwoorden", "url_visible": "", "title": "Veelgestelde vragen", "tracking_name": "", "tracking_id": "", "internal_link": 0}, {"position": 3, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://www.rivm.nl/nieuws/actuele-informatie-over-coronavirus", "url_visible": "", "title": "Actuele informatie RIVM", "tracking_name": "", "tracking_id": "", "internal_link": 0}, {"position": 4, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://www.rivm.nl/coronavirus/covid-19/verspreiding", "url_visible": "", "title": "Verspreiding wereldwijd", "tracking_name": "", "tracking_id": "", "internal_link": 0}]}, {"id": 99942, "title": "Zorgverzekering 2021", "user_block": 1, "visible": false, "color": "blue", "adult": "", "no_mobile": "", "is_html": "", "block_content": [{"position": 0, "active": 1, "target": "_blank", "nofollow": 0, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 1, "comment": "", "user_link": true, "url": "https://www.zorgwijzer.nl/zorgverzekering-vergelijken", "url_visible": "", "title": "Zorgverzekeringen vergelijken", "tracking_name": "<PERSON>org<PERSON><PERSON><PERSON>", "tracking_id": "", "internal_link": 0}, {"position": 1, "active": 1, "target": "_blank", "nofollow": 0, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 1, "comment": "", "user_link": true, "url": "https://www.goedkoopstezorgverzekering.nl/vergelijken/", "url_visible": "", "title": "Goedkoopste zorgverzekering", "tracking_name": "", "tracking_id": "", "internal_link": 0}, {"position": 2, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://www.consumentenbond.nl/zorgverzekering/zorgverzekering-2021", "url_visible": "", "title": "Waar op letten?", "tracking_name": "", "tracking_id": "", "internal_link": 0}]}]}, {"id": 2, "block": [{"id": 99815, "title": "<PERSON><PERSON>", "user_block": 1, "visible": true, "color": "", "adult": "", "no_mobile": "", "is_html": "", "block_content": [{"position": 0, "active": 1, "target": "_blank", "nofollow": 0, "is_new": 0, "is_tip": 1, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 0, "comment": "", "user_link": true, "url": "https://www.zoek.nl", "url_visible": "https://www.zoek.nl", "title": "Zoek.nl", "tracking_name": "zoeken/zoek.nl", "tracking_id": "", "internal_link": 0}, {"position": 1, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "https://www.detelefoongids.nl/?edsacid=l-846336-6-1", "url_visible": "https://www.detelefoongids.nl/", "title": "<PERSON><PERSON><PERSON><PERSON>", "tracking_name": "adv/telefoongids-hp-bedrijven", "tracking_id": "", "internal_link": 0}, {"position": 2, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "https://www.detelefoongids.nl/personen/6-1/?edsacid=l-846336-6-1", "url_visible": "https://www.detelefoongids.nl/", "title": "detelefoongids.nl", "tracking_name": "adv/telefoongids-hp", "tracking_id": "", "internal_link": 0}, {"position": 3, "active": 1, "target": "_blank", "nofollow": 0, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "http://www.huizenzoeker.nl/", "url_visible": "", "title": "<PERSON><PERSON>", "tracking_name": "zoe<PERSON>/huizen<PERSON><PERSON>er", "tracking_id": "", "internal_link": 0}, {"position": 4, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "http://nl.wikipedia.org/wiki/Hoofdpagina", "url_visible": "", "title": "WIkipedia", "tracking_name": "zoeken/wikipedia", "tracking_id": "", "internal_link": 0}]}]}, {"id": 3, "block": [{"id": 8318, "title": "Dagdeals", "user_block": 0, "visible": true, "color": "", "adult": "", "no_mobile": "", "is_html": "", "block_content": [{"position": 0, "active": "", "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 1, "sold": 1, "comment": "", "user_link": true, "url": "http://www.bol.com/nl/aanbiedingen/index.html?Referrer=ADVNLVIN001033TLDRO01", "url_visible": "http://www.bol.com/nl/aanbiedingen/index.html", "title": "bol.com dagaanbieding", "tracking_name": "bol.com-hp-dagaanbiedingen", "tracking_id": "", "internal_link": 0}, {"position": 1, "active": 1, "target": "default", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "https://www.vakantieveilingen.nl", "url_visible": "https://www.vakantieveilingen.nl", "title": "vaka<PERSON><PERSON><PERSON><PERSON>", "tracking_name": "vaka<PERSON><PERSON><PERSON><PERSON>", "tracking_id": 64546, "internal_link": 0}, {"position": 2, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 1, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "https://www.marktplaats.nl/", "url_visible": "https://www.marktplaats.nl/", "title": "Marktplaats - aanbieding", "tracking_name": "marktplaats - aanbieding", "tracking_id": 64548, "internal_link": 0}, {"position": 3, "active": 1, "target": "default", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "http://www.kortingscode.nl", "url_visible": "http://www.kortingscode.nl", "title": "Dagelijkse kortingscodes", "tracking_name": "dagelijkse kortingscodes", "tracking_id": 64553, "internal_link": 0}, {"position": 4, "active": 1, "target": "default", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "http://www.spotta.nl", "url_visible": "http://www.spotta.nl", "title": "Spotta - nieuwste winkelfolders", "tracking_name": "spotta - nieuwste winkelfolders", "tracking_id": 64555, "internal_link": 0}]}]}, {"id": 4, "block": [{"id": 99993, "title": "Vinden.nl", "user_block": 0, "visible": true, "color": "", "adult": "", "no_mobile": "", "is_html": "", "block_content": [{"position": 0, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "https://www.vinden.nl/info/advertising", "url_visible": "", "title": "adverteren op deze pagina", "tracking_name": "vinden.nl/adverteren", "tracking_id": "", "internal_link": 0}, {"position": 1, "active": 1, "target": "_blank", "nofollow": 1, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 0, "comment": "", "user_link": true, "url": "http://www.vinden.nl/info/power_of", "url_visible": "/info/power_of", "title": "alles over vinden.nl", "tracking_name": "allesover", "tracking_id": "", "internal_link": 0}]}, {"id": 99323, "title": "Spaarprogramma", "user_block": 0, "visible": true, "color": "blue", "adult": "", "no_mobile": "", "is_html": 1, "html": "%3Cli%3E%0A%3Cdiv%20id=%22EuroClixdiv%22%20style=%22display:%20block;%22%3E%3C/div%3E%0A%3Cshow_if_mobile_device%3E%0A%3Ciframe%20name=%22EuroClixiframe%22%20id=%22EuroClixiframe%22%20src=%22/spaarprogramma/status?de=m&amp;template=homepage%22%20style=%22width:%201px;%20height:%201px;%20display:%20none;%20visibility:%20hidden;%22%3E%3C/iframe%3E%0A%3C/show_if_mobile_device%3E%0A%3Cshow_if_no_mobile_device%3E%0A%3Ciframe%20name=%22EuroClixiframe%22%20id=%22EuroClixiframe%22%20src=%22/spaarprogramma/status?de=c&amp;template=homepage%22%20style=%22width:%201px;%20height:%201px;%20display:%20none;%20visibility:%20hidden;%22%3E%3C/iframe%3E%0A%3C/show_if_no_mobile_device%3E%0A%3Cdiv%20id=%22EuroClixLogindiv%22%20style=%22margin-bottom:%20-18px;%20display:%20block;%22%3E%3C/div%3E%0A%3C/li%3E"}, {"id": 99709, "title": "Favoriete sites", "user_block": 0, "visible": true, "color": "green", "adult": "", "no_mobile": "", "is_html": 1, "html": "%3Cscript%20language=%22javascript%22%20type=%22text/javascript%22%3Efavoriete();%3C/script%3E%0A%3Cli%3E%3Ca%20href=%22http://www.vinden.nl/favo%22%20target=%22_self%22%20rel=%22nofollow%22%3Etoevoegen%20/%20wijzigen%3C/a%3E%3C/li%3E"}, {"id": 8075, "title": "Vacatures en Opleidingen", "user_block": 0, "visible": true, "color": "", "adult": "", "no_mobile": "", "is_html": "", "block_content": [{"position": 0, "active": 1, "target": "_blank", "nofollow": 0, "is_new": 0, "is_tip": 0, "is_extra": 0, "extra_text": "", "force_new_line": 0, "sold": 1, "comment": "", "user_link": true, "url": "https://www.emerce.nl/opleidingen/", "url_visible": "https://www.emerce.nl/opleidingen/", "title": "<PERSON><PERSON><PERSON>", "tracking_name": "emerce opleidingen", "tracking_id": "", "internal_link": 0}]}]}]}
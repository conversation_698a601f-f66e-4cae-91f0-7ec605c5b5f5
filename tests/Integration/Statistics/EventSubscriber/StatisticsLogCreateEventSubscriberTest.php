<?php

declare(strict_types=1);

namespace Tests\Integration\Statistics\EventSubscriber;

use App\BingAds\EventSubscriber\BingAdsStatisticsLogEventSubscriber;
use App\CookieConsent\EventSubscriber\CookieConsentStatisticsLogEventSubscriber;
use App\GoogleCsa\EventSubscriber\GoogleAdSenseStatisticsLogEventSubscriber;
use App\GooglePublisherTag\EventSubscriber\GoogleAdManagerStatisticsLogEventSubscriber;
use App\Statistics\EventSubscriber\MainStatisticsLogEventSubscriber;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class StatisticsLogCreateEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private const array STATISTICS_LOG_CREATE_EVENT_LISTENERS = [
        BingAdsStatisticsLogEventSubscriber::class,
        CookieConsentStatisticsLogEventSubscriber::class,
        GoogleAdManagerStatisticsLogEventSubscriber::class,
        GoogleAdSenseStatisticsLogEventSubscriber::class,
        MainStatisticsLogEventSubscriber::class,
    ];

    public function testStatisticsLogCreateEventListeners(): void
    {
        /** @var EventDispatcher $dispatcher */
        $dispatcher = self::getContainer()->get('event_dispatcher');
        $eventListeners = $dispatcher->getListeners(StatisticsLogCreateEvent::NAME);
        /** @var string $projectDir */
        $projectDir = self::getContainer()->getParameter('kernel.project_dir');
        $eventListenerClasses = [];

        foreach ($eventListeners as $eventListener) {
            if (!is_array($eventListener)) {
                continue;
            }

            $eventListenerClass = $eventListener[0]::class;
            $eventListenerReflectionClass = new \ReflectionClass($eventListener[0]);
            $eventListenerFilePath = $eventListenerReflectionClass->getFileName();

            if ($eventListenerFilePath === false) {
                continue;
            }

            $eventListenerFilePath = str_replace($projectDir, '', $eventListenerFilePath);
            $eventListenerFilePath = ltrim($eventListenerFilePath, '/');

            if (str_starts_with($eventListenerFilePath, 'vendor/')) {
                continue;
            }

            if (!in_array($eventListenerClass, self::STATISTICS_LOG_CREATE_EVENT_LISTENERS, true)) {
                self::fail(
                    sprintf(
                        'Event listener "%s" is not expected for event "%s"',
                        $eventListenerClass,
                        StatisticsLogCreateEvent::NAME,
                    ),
                );
            }

            $eventListenerClasses[$eventListenerClass] = true;
        }

        self::assertCount(count($eventListenerClasses), self::STATISTICS_LOG_CREATE_EVENT_LISTENERS);
    }
}

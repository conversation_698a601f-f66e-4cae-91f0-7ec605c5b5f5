<?php

declare(strict_types=1);

namespace Tests\Integration\Template;

use Symfony\Component\Console\Input\StringInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Loader\FilesystemLoader;
use Visymo\Shared\Infrastructure\Bridge\Symfony\Console\Application;

class TwigTemplateLinterTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testThemeTemplatePaths(): void
    {
        /** @var FilesystemLoader $fileSystemLoader */
        $fileSystemLoader = self::getContainer()->get('twig.loader.native_filesystem');

        $themeTemplatePaths = $fileSystemLoader->getPaths('theme');

        $application = new Application(self::getKernel());
        $application->setAutoExit(false);

        foreach ($themeTemplatePaths as $themeTemplatePath) {
            // Lint the path using the Symfony linter on the active brand's kernel
            $output = new BufferedOutput();
            $exitCode = $application->run(
                new StringInput(sprintf('lint:twig %s -e test', escapeshellarg($themeTemplatePath))),
                $output,
            );

            self::assertSame(
                0,
                $exitCode,
                $output->fetch(),
            );
        }
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Entry\Serializer;

use App\AdBot\Request\AdBotRequestInterface;
use App\Generic\Device\Device;
use App\Tracking\Entry\Factory\CommandLineTrackingEntryFactory;
use App\Tracking\Entry\Serializer\TrackingEntryArraySerializer;
use App\Tracking\Helper\ActiveTrackingEntryHelper;
use App\Tracking\Validator\CampaignNameValidator;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Validator\CampaignNameValidatorStub;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\TestCaseDataProviderHelpers;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class TrackingEntryArraySerializerTest extends AbstractBrandWebsitesIntegrationTestCase
{
    private SearchRequestStub $searchRequestStub;

    private TrackingEntryArraySerializer $trackingEntryArraySerializer;

    private DateTimeFactory $dateTimeFactory;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->searchRequestStub = $this->stubs()->request()->getSearchRequest()
            ->setPage(1);

        /** @var TrackingEntryArraySerializer $trackingEntryArraySerializer */
        $trackingEntryArraySerializer = self::getContainer()->get(TrackingEntryArraySerializer::class);
        $this->trackingEntryArraySerializer = $trackingEntryArraySerializer;

        /** @var DateTimeFactory $dateTimeFactory */
        $dateTimeFactory = self::getContainer()->get(DateTimeFactory::class);
        $this->dateTimeFactory = $dateTimeFactory;

        // Allow all campaign names
        /** @var CampaignNameValidatorStub $campaignNameValidatorStub */
        $campaignNameValidatorStub = self::getContainer()->get(CampaignNameValidator::class);
        $campaignNameValidatorStub->setFixedResult(true);
    }

    /**
     * @return mixed[]
     */
    public static function completeDataProvider(): array
    {
        return TestCaseDataProviderHelpers::initDataProviderCases(
            __DIR__.'/TrackingEntryArraySerializerTest',
            self::class,
        );
    }

    #[DataProvider('completeDataProvider')]
    public function testComplete(
        AbstractTrackingEntryArraySerializerCase $testCase
    ): void
    {
        $trackingEntry = $testCase->getTrackingEntry();
        $actualData = $this->trackingEntryArraySerializer->serialize($trackingEntry);

        // The order is irrelevant
        ksort($actualData);

        // Assertions
        $assertionFile = $this->initJsonAssertionFile($actualData, $testCase->getAssertionFilePath());
        $assertionFile->assertSame();

        self::assertFalse($trackingEntry->isEmpty);

        // Test deserialization
        $deserializeData = [
            ...$actualData,
            ...$testCase->additionalData,
        ];
        $deserializedTrackingEntry = $this->trackingEntryArraySerializer->deserialize($deserializeData);

        $expectedDeserializationData = $actualData;
        $actualDeserializationData = $this->trackingEntryArraySerializer->serialize($deserializedTrackingEntry);
        ksort($actualDeserializationData);

        self::assertSame($expectedDeserializationData, $actualDeserializationData);
    }

    public function testEmpty(): void
    {
        $trackingEntryFactory = new CommandLineTrackingEntryFactory(
            $this->dateTimeFactory,
        );

        $emptyTrackingEntry = $trackingEntryFactory->create();

        self::assertTrue($emptyTrackingEntry->isEmpty);
        self::assertSame([], $this->trackingEntryArraySerializer->serialize($emptyTrackingEntry));
    }

    /**
     * @return mixed[]
     */
    public static function getDeviceDataProvider(): array
    {
        // phpcs:disable Generic.Files.LineLength.MaxExceeded
        return [
            'device computer from request'         => [
                'deviceParameter' => Device::DESKTOP,
                'userAgent'       => null,
                'isAdBot'         => false,
                'expectedDevice'  => Device::DESKTOP,
            ],
            'device computer from DeviceDetection' => [
                'deviceParameter' => null,
                'userAgent'       => null,
                'isAdBot'         => false,
                'expectedDevice'  => Device::DESKTOP,
            ],
            'device tablet from request'           => [
                'deviceParameter' => Device::TABLET,
                'userAgent'       => null,
                'isAdBot'         => false,
                'expectedDevice'  => Device::TABLET,
            ],
            'device tablet from DeviceDetection'   => [
                'deviceParameter' => null,
                'userAgent'       => 'Mozilla/5.0 (iPad; CPU OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53',
                'isAdBot'         => false,
                'expectedDevice'  => Device::TABLET,
            ],
            'device mobile from request'           => [
                'deviceParameter' => Device::MOBILE,
                'userAgent'       => null,
                'isAdBot'         => false,
                'expectedDevice'  => Device::MOBILE,
            ],
            'device mobile from DeviceDetection'   => [
                'deviceParameter' => null,
                'userAgent'       => 'Mozilla/5.0 (Linux; Android 10.0; S21+Ultra Build/MRA58K; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/55.0.2883.91 Mobile Safari/537.36',
                'isAdBot'         => false,
                'expectedDevice'  => Device::MOBILE,
            ],
            'device computer from bing ad bot'     => [
                'deviceParameter' => null,
                'userAgent'       => 'Mozilla/5.0 (compatible; adidxbot/2.0; +http://www.bing.com/bingbot.htm)',
                'isAdBot'         => true,
                'expectedDevice'  => Device::DESKTOP,
            ],
            'device mobile from bing ad bot'       => [
                'deviceParameter' => null,
                'userAgent'       => implode(
                    ' ',
                    [
                        'Mozilla/5.0 (Windows Phone 8.1; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 530)',
                        'like Gecko (compatible; adidxbot/2.0; +http://www.bing.com/bingbot.htm)',
                    ],
                ),
                'isAdBot'         => true,
                'expectedDevice'  => Device::MOBILE,
            ],
            'device computer from google ad bot'   => [
                'deviceParameter' => null,
                'userAgent'       => 'AdsBot-Google (+http://www.google.com/adsbot.html)',
                'isAdBot'         => true,
                'expectedDevice'  => Device::DESKTOP,
            ],
            'device mobile from google ad bot'     => [
                'deviceParameter' => null,
                'userAgent'       => implode(
                    ' ',
                    [
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko)',
                        'Version/9.0 Mobile/13B143 Safari/601.1',
                        '(compatible; AdsBot-Google-Mobile; +http://www.google.com/mobile/adsbot.html)',
                    ],
                ),
                'isAdBot'         => true,
                'expectedDevice'  => Device::MOBILE,
            ],
        ];
    }

    #[DataProvider('getDeviceDataProvider')]
    public function testGetDevice(
        ?Device $deviceParameter,
        ?string $userAgent,
        bool $isAdBot,
        Device $expectedDevice
    ): void
    {
        $this->searchRequestStub
            ->setIsLandingPage(true)
            ->setQuery('example-search');

        $request = AbstractBrandWebsitesIntegrationTestCase::createRequest(
            '/ws',
            [
                'q'  => 'example-search',
                'de' => $deviceParameter?->getShortValue(),
            ],
            [
                'User-Agent'                                           => $userAgent,
                AdBotRequestInterface::HEADER_X_LOADBALANCER_IS_AD_BOT => (string)(int)$isAdBot,
            ],
        );
        $this->handleRequest($request);

        /** @var ActiveTrackingEntryHelper $activeTrackingEntryHelper */
        $activeTrackingEntryHelper = self::getContainer()->get(ActiveTrackingEntryHelper::class);

        $trackingEntry = $activeTrackingEntryHelper->getActiveTrackingEntry();

        self::assertSame($expectedDevice->value, $trackingEntry->device->value);
        self::assertSame($isAdBot, $trackingEntry->isEmpty);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Entry\Serializer\TrackingEntryArraySerializerTest;

use App\Generic\Device\Device;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\TrafficSource;
use Tests\Integration\Tracking\Entry\Serializer\AbstractTrackingEntryArraySerializerCase;

class FacebookWithClickId extends AbstractTrackingEntryArraySerializerCase
{
    public function __construct()
    {
        parent::__construct(
            device       : Device::TABLET,
            trafficSource: TrafficSource::FACEBOOK,
            clickId      : new ClickId('facebook_click_123', ClickIdSource::FACEBOOK_CLICK_ID),
        );
    }
}

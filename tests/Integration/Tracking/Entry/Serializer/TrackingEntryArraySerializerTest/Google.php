<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Entry\Serializer\TrackingEntryArraySerializerTest;

use App\Generic\Device\Device;
use App\SplitTest\Activate\ActiveSplitTest;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficSource;
use Tests\Integration\Tracking\Entry\Serializer\AbstractTrackingEntryArraySerializerCase;

class Google extends AbstractTrackingEntryArraySerializerCase
{
    public function __construct()
    {
        parent::__construct(
            campaignId        : 1,
            campaignName      : 'sw_01',
            device            : Device::TABLET,
            network           : Network::GOOGLE_SEARCH,
            accountId         : 45,
            adGroupId         : 56,
            keywordId         : 346,
            trafficSource     : TrafficSource::GOOGLE,
            additionalChannels: ['ch1', 'ch2', 'ch3'],
            activeSplitTest   : new ActiveSplitTest(123, null),
            styleId           : **********,
        );
    }
}

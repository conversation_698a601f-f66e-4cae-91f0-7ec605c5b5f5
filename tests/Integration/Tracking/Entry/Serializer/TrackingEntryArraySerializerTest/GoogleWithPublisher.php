<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Entry\Serializer\TrackingEntryArraySerializerTest;

use App\Tracking\Model\TrafficSource;
use Tests\Integration\Tracking\Entry\Serializer\AbstractTrackingEntryArraySerializerCase;

class GoogleWithPublisher extends AbstractTrackingEntryArraySerializerCase
{
    public function __construct()
    {
        parent::__construct(
            trafficSource: TrafficSource::GOOGLE,
            publisher    : 'publisher_123',
        );
    }
}

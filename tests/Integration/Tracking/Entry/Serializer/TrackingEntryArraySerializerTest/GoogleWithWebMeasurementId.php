<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Entry\Serializer\TrackingEntryArraySerializerTest;

use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\TrafficSource;
use Tests\Integration\Tracking\Entry\Serializer\AbstractTrackingEntryArraySerializerCase;

class GoogleWithWebMeasurementId extends AbstractTrackingEntryArraySerializerCase
{
    public function __construct()
    {
        parent::__construct(
            trafficSource: TrafficSource::GOOGLE,
            clickId      : new ClickId('google_web_123', ClickIdSource::GOOGLE_WEB_MEASUREMENT_ID),
        );
    }
}

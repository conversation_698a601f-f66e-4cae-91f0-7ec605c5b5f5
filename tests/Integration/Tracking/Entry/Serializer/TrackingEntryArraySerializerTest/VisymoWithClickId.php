<?php

declare(strict_types=1);

namespace Tests\Integration\Tracking\Entry\Serializer\TrackingEntryArraySerializerTest;

use App\Generic\Device\Device;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\TrafficSource;
use Tests\Integration\Tracking\Entry\Serializer\AbstractTrackingEntryArraySerializerCase;

class VisymoWithClickId extends AbstractTrackingEntryArraySerializerCase
{
    public function __construct()
    {
        parent::__construct(
            query        : 'crm software',
            device       : Device::TABLET,
            trafficSource: TrafficSource::VISYMO,
            clickId      : new ClickId('visymo_click_123', ClickIdSource::VISYMO_CLICK_ID),
        );
    }
}

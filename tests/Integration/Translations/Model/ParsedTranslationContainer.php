<?php

declare(strict_types=1);

namespace Tests\Integration\Translations\Model;

class ParsedTranslationContainer
{
    /** @var ParsedTranslationLocale[] */
    private array $locales = [];

    /**
     * @param ParsedTranslationLocale[] $locales
     */
    public function __construct(private readonly string $name, array $locales = [])
    {
        foreach ($locales as $localeCode) {
            $this->addLocale($localeCode);
        }
    }

    public function hasLocale(string $localeCode): bool
    {
        return array_key_exists($localeCode, $this->locales);
    }

    public function getLocale(string $localeCode): ParsedTranslationLocale
    {
        if (!$this->hasLocale($localeCode)) {
            throw new \RuntimeException(sprintf('Locale "%s" was not registered', $localeCode));
        }

        return $this->locales[$localeCode];
    }

    public function addLocale(ParsedTranslationLocale $locale): void
    {
        $this->locales[$locale->getCode()] = $locale;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return ParsedTranslationLocale[]
     */
    public function getLocales(): array
    {
        return $this->locales;
    }
}

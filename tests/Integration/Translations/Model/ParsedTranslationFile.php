<?php

declare(strict_types=1);

namespace Tests\Integration\Translations\Model;

class ParsedTranslationFile
{
    /** @var ParsedTranslationUnit[] */
    private array $units = [];

    /**
     * @param ParsedTranslationUnit[] $units
     */
    public function __construct(private readonly string $filePath, array $units)
    {
        foreach ($units as $unit) {
            $this->addUnit($unit);
        }
    }

    public function addUnit(ParsedTranslationUnit $unit): void
    {
        if (array_key_exists($unit->getId(), $this->units)) {
            throw new \RuntimeException(sprintf('trans-unit key "%s" already exists in %s', $unit->getId(), $this->getFilePath()));
        }

        $this->units[$unit->getId()] = $unit;
    }

    public function getFilePath(): string
    {
        return $this->filePath;
    }

    /**
     * @return ParsedTranslationUnit[]
     */
    public function getUnits(): array
    {
        return $this->units;
    }

    /**
     * @return string[]
     */
    public function getUnitIds(): array
    {
        return array_values(
            array_map(
                static fn (ParsedTranslationUnit $parsedTranslationUnit): string => $parsedTranslationUnit->getId(),
                $this->getUnits(),
            ),
        );
    }
}

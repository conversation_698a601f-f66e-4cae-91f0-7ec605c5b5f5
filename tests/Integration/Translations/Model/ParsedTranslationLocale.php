<?php

declare(strict_types=1);

namespace Tests\Integration\Translations\Model;

class ParsedTranslationLocale
{
    /** @var ParsedTranslationFile[] */
    private array $translationFiles = [];

    /** @var ParsedTranslationUnit[] */
    private array $mergedTranslationsUnitsCache;

    /**
     * @param ParsedTranslationFile[] $translationFiles
     */
    public function __construct(private readonly string $code, array $translationFiles = [])
    {
        foreach ($translationFiles as $translationFile) {
            $this->addTranslationFile($translationFile);
        }
    }

    public function addTranslationFile(ParsedTranslationFile $translationFile): void
    {
        $this->translationFiles[] = $translationFile;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * @return ParsedTranslationFile[]
     */
    public function getTranslationFiles(): array
    {
        return $this->translationFiles;
    }

    /**
     * @return ParsedTranslationUnit[]
     */
    public function getMergedTranslationUnits(): array
    {
        if (!isset($this->mergedTranslationsUnitsCache)) {
            $mergedUnits = [];

            foreach ($this->getTranslationFiles() as $translationFile) {
                /** @noinspection SlowArrayOperationsInLoopInspection */
                $mergedUnits = array_merge(
                    $mergedUnits,
                    $translationFile->getUnits(),
                );
            }

            $this->mergedTranslationsUnitsCache = $mergedUnits;
        }

        return $this->mergedTranslationsUnitsCache;
    }

    /**
     * @return string[]
     */
    public function getMergedTranslationUnitIds(): array
    {
        return array_values(
            array_map(
                static fn (ParsedTranslationUnit $parsedTranslationUnit): string => $parsedTranslationUnit->getId(),
                $this->getMergedTranslationUnits(),
            ),
        );
    }
}

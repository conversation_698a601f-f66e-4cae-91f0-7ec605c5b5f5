<?php

declare(strict_types=1);

namespace Tests\Integration\WebsiteSettings\Configuration\Import;

use App\Config\Helper\ConfigFileHelperInterface;
use App\WebsiteSettings\Configuration\Cache\CachedWebsiteConfigurationFileReader;
use App\WebsiteSettings\Configuration\Import\ImportWebsiteConfigurationNormalizer;
use App\WebsiteSettings\Configuration\Import\WebsiteConfigurationImporter;
use Tests\Integration\Helper\BrandConfigTestHelper;
use Tests\Integration\WebsiteSettings\AbstractWebsiteSettingsTestCase;
use Tests\Stub\Config\Helper\ConfigFileHelperStub;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\Filesystem\SerializedFile\Type\NativeJsonFileFactory;
use Visymo\Filesystem\SerializedFile\Type\NativePhpIncludeFileFactory;

class WebsiteConfigurationImporterTest extends AbstractWebsiteSettingsTestCase
{
    private ConfigFileHelperStub $configFileHelperStub;

    private WebsiteConfigurationImporter $websiteConfigurationImporter;

    private ImportWebsiteConfigurationNormalizer $importWebsiteConfigurationNormalizer;

    private CachedWebsiteConfigurationFileReader $cachedWebsiteConfigurationFileReader;

    private NativePhpIncludeFileFactory $nativePhpIncludeFileFactory;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var ConfigFileHelperStub $configFileHelperStub */
        $configFileHelperStub = self::getContainer()->get(ConfigFileHelperInterface::class);
        $this->configFileHelperStub = $configFileHelperStub;
        $this->configFileHelperStub->setLocalConfigPath(__DIR__.'/_config');

        /** @var WebsiteConfigurationImporter $websiteConfigurationImporter */
        $websiteConfigurationImporter = self::getContainer()->get(WebsiteConfigurationImporter::class);
        $this->websiteConfigurationImporter = $websiteConfigurationImporter;

        /** @var ImportWebsiteConfigurationNormalizer $importWebsiteConfigurationNormalizer */
        $importWebsiteConfigurationNormalizer = self::getContainer()->get(ImportWebsiteConfigurationNormalizer::class);
        $this->importWebsiteConfigurationNormalizer = $importWebsiteConfigurationNormalizer;

        /** @var CachedWebsiteConfigurationFileReader $cachedWebsiteConfigurationFileReader */
        $cachedWebsiteConfigurationFileReader = self::getContainer()->get(CachedWebsiteConfigurationFileReader::class);
        $this->cachedWebsiteConfigurationFileReader = $cachedWebsiteConfigurationFileReader;

        /** @var NativePhpIncludeFileFactory $nativePhpIncludeFileFactory */
        $nativePhpIncludeFileFactory = self::getContainer()->get(NativePhpIncludeFileFactory::class);
        $this->nativePhpIncludeFileFactory = $nativePhpIncludeFileFactory;
    }

    public function testBrandConfig(): void
    {
        /** @var NativeJsonFileFactory $nativeJsonFileFactory */
        $nativeJsonFileFactory = self::getContainer()->get(NativeJsonFileFactory::class);
        $configFile = $nativeJsonFileFactory->create(BrandConfigTestHelper::FOOD_CONFIG_FILE_PATH);
        $this->importConfigFile($configFile);
    }

    private function importConfigFile(SerializedFileInterface $configFile): void
    {
        $sourceConfig = $configFile->getContents();
        $brandSlug = $sourceConfig['brand']['slug'];

        $brandPhpConfigFilePath = $this->configFileHelperStub->getBrandConfigPhpFilePath($configFile->getBaseName());

        $this->deleteBrandConfigFiles($brandSlug);

        $this->websiteConfigurationImporter->import($configFile);

        /**
         * Assert that imported files exist
         */
        self::assertFileExists($brandPhpConfigFilePath);

        /**
         * Assert that recombined config still contains all account/campaign data
         */
        $actualConfigReCombined = $this->cachedWebsiteConfigurationFileReader->readAllCombined(
            $this->nativePhpIncludeFileFactory->create(
                $brandPhpConfigFilePath,
            ),
        );

        foreach ($sourceConfig['accounts'] as $expectedAccountId => $expectedAccountConfig) {
            self::assertArrayHasKey(
                $expectedAccountId,
                $actualConfigReCombined['accounts'],
                sprintf('asserting that account-id exists for brand %s', $brandSlug),
            );

            // Normalize
            $expectedAccountConfig = $this->importWebsiteConfigurationNormalizer->normalizeAccountConfig(
                $expectedAccountId,
                $expectedAccountConfig,
            );

            // Assert account data still matches
            self::assertEquals(
                $expectedAccountConfig,
                $actualConfigReCombined['accounts'][$expectedAccountId],
                sprintf('asserting that account config matches for brand %s', $brandSlug),
            );
        }

        // Delete generated PHP files
        $this->deleteBrandConfigFiles($brandSlug);
    }

    private function deleteBrandConfigFiles(string $brandSlug): void
    {
        $directoryIterator = new \DirectoryIterator($this->configFileHelperStub->getBrandConfigPhpPath());

        /** @var \DirectoryIterator $file */
        foreach ($directoryIterator as $file) {
            if ($file->isDot() || $file->isDir()) {
                continue;
            }

            if (str_starts_with($file->getFilename(), $brandSlug)) {
                @unlink($file->getPathname());
            }
        }
    }
}

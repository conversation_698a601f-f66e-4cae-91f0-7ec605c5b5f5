{"domain:id.brand.com__account:3": {"conversion_log": {"enabled": true, "offline_conversion": false}, "conversion_tracking": {"order_type": "one_per_click_id"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true, "approval": true, "contract_type": "direct", "default_client": "izito-web-iex", "sem_client": "izito-smh-iex", "web_client": "izito-web-iex", "default_channel": "izito_ca_seo"}, "google_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": **********, "conversion_tracking_label": "TbfSCL611AcQlv7H-QM"}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__account:3": {"conversion_log": {"enabled": true, "offline_conversion": false}, "conversion_tracking": {"order_type": "one_per_click_id"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": false, "approval": true, "fallback_enabled": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": **********, "conversion_tracking_label": "TbfSCL611AcQlv7H-QM"}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__account:46": {"conversion_log": {"enabled": false}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__account:46": {"conversion_log": {"enabled": false}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": false, "approval": true, "fallback_enabled": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__account:120": {"conversion_log": {"enabled": true, "offline_conversion": true}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__account:120": {"conversion_log": {"enabled": true, "offline_conversion": true}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": true, "approval": true, "fallback_enabled": false, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__account:133": {"conversion_log": {"enabled": true, "offline_conversion": true}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}}, "domain:www.brand.ca__account:133": {"conversion_log": {"enabled": true, "offline_conversion": true}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": false, "approval": true, "fallback_enabled": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}}, "domain:id.brand.com__account:146": {"conversion_log": {"enabled": true, "offline_conversion": false}, "conversion_tracking": {"order_type": "one_per_click_id"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__account:146": {"conversion_log": {"enabled": true, "offline_conversion": false}, "conversion_tracking": {"order_type": "one_per_click_id"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": false, "approval": true, "fallback_enabled": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__account:0": {"conversion_log": {"enabled": false}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true, "approval": true, "contract_type": "direct", "default_client": "izito-web-iex", "sem_client": null, "web_client": null, "default_channel": "izito_ca_seo"}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__account:0": {"conversion_log": {"enabled": false}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": true, "approval": true, "fallback_enabled": false, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "********", "web_ad_unit_id": "********"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}}
{"domain:id.brand.com__locale:id_ID__account:3": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "id.brand.com", "google_analytics_id": null, "image_search": true, "news_search": true}, "locale": {"locale": "id_ID", "related_terms_locale": "id_ID", "is_default": true}, "account": {"id": 3, "name": "Google account with Google Ads", "service": "Google Ads", "campaigns": ["iz_br_gc3_03", "iz_br_gc3_04"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true, "approval": true, "style_id_desktop": **********, "style_id_tablet": **********, "style_id_mobile": **********, "microsoft_ads_traffic_style_id_desktop": **********, "microsoft_ads_traffic_style_id_tablet": **********, "microsoft_ads_traffic_style_id_mobile": **********, "default_client": "izito-web-iex", "sem_client": "izito-smh-iex", "web_client": "izito-web-iex", "default_channel": "izito_ca_seo"}, "google_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": **********, "conversion_tracking_label": "TbfSCL611AcQlv7H-QM"}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:en_CA__account:3": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "en_CA", "related_terms_locale": "en_CA", "is_default": true}, "account": {"id": 3, "name": "Google account with Google Ads", "service": "Google Ads", "campaigns": ["iz_br_gc3_03", "iz_br_gc3_04"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": **********, "conversion_tracking_label": "TbfSCL611AcQlv7H-QM"}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:de_DE__account:3": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "de_DE", "related_terms_locale": "de_DE", "is_default": false}, "account": {"id": 3, "name": "Google account with Google Ads", "service": "Google Ads", "campaigns": ["iz_br_gc3_03", "iz_br_gc3_04"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": **********, "conversion_tracking_label": "TbfSCL611AcQlv7H-QM"}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__locale:id_ID__account:46": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "id.brand.com", "google_analytics_id": null, "image_search": true, "video_search": true, "wiki_search": true, "news_search": true}, "locale": {"locale": "id_ID", "related_terms_locale": "id_ID", "is_default": true}, "account": {"id": 46, "name": "Google account with no ad provider enabled", "service": "Google Ads", "campaigns": ["iz_hk_gc3_04", "iz_hk_gc3_05"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:en_CA__account:46": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "en_CA", "related_terms_locale": "en_CA", "is_default": true}, "account": {"id": 46, "name": "Google account with no ad provider enabled", "service": "Google Ads", "campaigns": ["iz_hk_gc3_04", "iz_hk_gc3_05"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:de_DE__account:46": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "de_DE", "related_terms_locale": "de_DE", "is_default": false}, "account": {"id": 46, "name": "Google account with no ad provider enabled", "service": "Google Ads", "campaigns": ["iz_hk_gc3_04", "iz_hk_gc3_05"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__locale:id_ID__account:120": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "id.brand.com", "google_analytics_id": null, "image_search": true, "video_search": true, "wiki_search": true, "news_search": true}, "locale": {"locale": "id_ID", "related_terms_locale": "id_ID", "is_default": true}, "account": {"id": 120, "name": "Google account with Bing Ads", "service": "Google Ads", "campaigns": ["test123"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:en_CA__account:120": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "en_CA", "related_terms_locale": "en_CA", "is_default": true}, "account": {"id": 120, "name": "Google account with Bing Ads", "service": "Google Ads", "campaigns": ["test123"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": true, "approval": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": ********, "web_ad_unit_id": ********}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:de_DE__account:120": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "de_DE", "related_terms_locale": "de_DE", "is_default": false}, "account": {"id": 120, "name": "Google account with Bing Ads", "service": "Google Ads", "campaigns": ["test123"], "additional_channel_prefix": "ch", "additional_channels": []}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": true, "approval": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": ********, "web_ad_unit_id": ********}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:id.brand.com__locale:id_ID__account:133": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "id.brand.com", "google_analytics_id": null, "image_search": true, "video_search": true, "wiki_search": true, "news_search": true}, "locale": {"locale": "id_ID", "related_terms_locale": "id_ID", "is_default": true}, "account": {"id": 133, "name": "Microsoft account", "service": "Microsoft Advertising", "campaigns": ["iz_vn_ba_br_01", "iz_vn_ba_br_02"], "additional_channel_prefix": "ch", "additional_channels": {"iz_vn_ba_br_01": {"campaign": "iz_vn_ba_br_01", "min": 300, "max": 399, "number_of_channels": 1}}}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}}, "domain:www.brand.ca__locale:en_CA__account:133": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "en_CA", "related_terms_locale": "en_CA", "is_default": true}, "account": {"id": 133, "name": "Microsoft account", "service": "Microsoft Advertising", "campaigns": ["iz_vn_ba_br_01", "iz_vn_ba_br_02"], "additional_channel_prefix": "ch", "additional_channels": {"iz_vn_ba_br_01": {"campaign": "iz_vn_ba_br_01", "min": 300, "max": 399, "number_of_channels": 1}}}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}}, "domain:www.brand.ca__locale:de_DE__account:133": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "google_analytics_id": "G-123", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "de_DE", "related_terms_locale": "de_DE", "is_default": false}, "account": {"id": 133, "name": "Microsoft account", "service": "Microsoft Advertising", "campaigns": ["iz_vn_ba_br_01", "iz_vn_ba_br_02"], "additional_channel_prefix": "ch", "additional_channels": {"iz_vn_ba_br_01": {"campaign": "iz_vn_ba_br_01", "min": 300, "max": 399, "number_of_channels": 1}}}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}}, "domain:id.brand.com__locale:id_ID__account:0": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "id.brand.com", "image_search": true, "video_search": true, "wiki_search": true, "news_search": true}, "locale": {"locale": "id_ID", "related_terms_locale": "id_ID", "is_default": true}, "account": null, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": false}, "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true, "approval": true, "style_id_desktop": **********, "style_id_tablet": **********, "style_id_mobile": **********, "microsoft_ads_traffic_style_id_desktop": **********, "microsoft_ads_traffic_style_id_tablet": **********, "microsoft_ads_traffic_style_id_mobile": **********, "default_client": "izito-web-iex", "sem_client": null, "web_client": null}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:en_CA__account:0": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "en_CA", "related_terms_locale": "en_CA", "is_default": true}, "account": null, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": false}, "bing_ads": {"enabled": true, "approval": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": ********, "web_ad_unit_id": ********}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}, "domain:www.brand.ca__locale:de_DE__account:0": {"brand": {"name": "Test Brand", "slug": "testbrand", "conversion_pixel_url": "https://my.conversion.url"}, "domain": {"scheme": "https", "host": "www.brand.ca", "image_search": false, "video_search": false, "wiki_search": false, "news_search": false}, "locale": {"locale": "de_DE", "related_terms_locale": "de_DE", "is_default": false}, "account": null, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "Desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": false}, "bing_ads": {"enabled": true, "approval": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": ********, "web_ad_unit_id": ********}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}}}
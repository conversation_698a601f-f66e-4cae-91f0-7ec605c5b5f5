{"website_settings": {"domain": {"scheme": "https", "host": "www.brand.ca", "image_search": false, "news_search": false, "javascript_related_terms_enabled": true}, "locale": {"locale": "en_CA", "is_default": true}, "account": {"id": 133, "name": "Microsoft account", "service": "Microsoft Advertising", "payment_mode": null, "additional_channel_prefix": "ch", "exclude_countries_from_conversion_tracking": []}, "campaign": {"name": "iz_vn_ba_br_01", "domain": null, "additional_channel": {"min": 300, "max": 399, "number_of_channels": 1}, "google_adsense": null}, "split_tests": [{"id": 123, "control_channels": {"advertised": "ab_tb", "landingpage": "ab_ta"}, "activation": {"device": "desktop", "service": null, "date_start": "2020-01-01 00:00:00", "date_end": "2020-01-02 00:00:00", "domains": [], "routes": []}, "variants": [{"variant": "abcdef", "container_suffix": "1234", "channels": {"advertised": "advertised_ab_tb", "landingpage": "landing_ab_tb"}, "percentage": 30}]}], "conversion_log": {"enabled": true, "offline_conversion": true}, "conversion_tracking": {"order_type": "default"}, "bing_ads": {"enabled": true, "dynamic_ads_enabled": false, "approval": true, "fallback_enabled": true, "clarity_id": "b<PERSON><PERSON><PERSON><PERSON>", "sem_ad_unit_id": "11726713", "web_ad_unit_id": "11726713"}, "google_adsense": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "google_ads_related_terms_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 4012332, "conversion_tracking_label": null}, "zemanta_conversion_tracking": {"enabled": false}}, "log": []}
<?php

declare(strict_types=1);

namespace Tests\Stub\Account\Provider;

use App\Account\Provider\FallbackAccountIdProviderInterface;

final class FallbackAccountIdProviderStub implements FallbackAccountIdProviderInterface
{
    private ?int $fallbackAccountId = null;

    public function getFallbackAccountId(): ?int
    {
        return $this->fallbackAccountId;
    }

    public function setFallbackAccountId(?int $fallbackAccountId): self
    {
        $this->fallbackAccountId = $fallbackAccountId;

        return $this;
    }
}

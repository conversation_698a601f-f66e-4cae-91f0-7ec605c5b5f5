<?php

declare(strict_types=1);

namespace Tests\Stub\Account\Settings;

use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettings;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

class AccountSettingsStubBuilder
{
    private int $id;

    private string $name;

    private AccountService $service;

    private ?string $paymentMode;

    /** @var string[] */
    private array $excludeCountriesFromConversionTracking;

    public function __construct()
    {
        $this->id = random_int(1, 9);
        $this->name = sprintf('Random account %s', $this->id);
        $this->service = AccountService::GOOGLE_ADS;
        $this->paymentMode = TestStubRandomizer::pickItem(
            array_merge([null], AccountSettings::PAYMENT_MODES),
        );
        $this->excludeCountriesFromConversionTracking = TestStubRandomizer::pickItems(
            ['DE', 'ES', 'FR', 'GB', 'US'],
            random_int(0, 5),
        );
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function setService(AccountService $service): self
    {
        $this->service = $service;

        return $this;
    }

    public function setPaymentMode(?string $paymentMode): self
    {
        if ($paymentMode !== null && !in_array($paymentMode, AccountSettings::PAYMENT_MODES, true)) {
            throw new \InvalidArgumentException(
                sprintf('Invalid payment mode "%s" given', $paymentMode),
            );
        }

        $this->paymentMode = $paymentMode;

        return $this;
    }

    /**
     * @param string[] $excludeCountriesFromConversionTracking
     */
    public function setExcludeCountriesFromConversionTracking(array $excludeCountriesFromConversionTracking): self
    {
        $this->excludeCountriesFromConversionTracking = $excludeCountriesFromConversionTracking;

        return $this;
    }

    public function create(): AccountSettings
    {
        return new AccountSettings(
            id                                    : $this->id,
            name                                  : $this->name,
            service                               : $this->service,
            paymentMode                           : $this->paymentMode,
            excludeCountriesFromConversionTracking: $this->excludeCountriesFromConversionTracking,
        );
    }
}

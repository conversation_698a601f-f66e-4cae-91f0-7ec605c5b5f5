<?php

declare(strict_types=1);

namespace Tests\Stub\AdBot\Request;

use App\AdBot\Bot\AdBot;
use App\AdBot\Request\AdBotRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class AdBotRequestStub implements AdBotRequestInterface
{
    private bool $isAdBot;

    private ?AdBot $adBot;

    public function __construct()
    {
        $this->isAdBot = TestStubRandomizer::pickBoolean();

        if (!$this->isAdBot || TestStubRandomizer::createInt(0, 2) === 1) {
            $this->adBot = null;
        } else {
            $this->adBot = TestStubRandomizer::pickItem(AdBot::cases());
        }
    }

    public function isAdBot(): bool
    {
        return $this->isAdBot;
    }

    public function setIsAdBot(bool $isAdBot): self
    {
        $this->isAdBot = $isAdBot;

        return $this;
    }

    public function getAdBot(): ?AdBot
    {
        return $this->isAdBot() ? $this->adBot : null;
    }

    public function setAdBot(?AdBot $adBot): self
    {
        $this->adBot = $adBot;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_IS_AD_BOT => $this->isAdBot(),
            self::KEY_AD_BOT    => $this->getAdBot()?->value,
        ];
    }
}

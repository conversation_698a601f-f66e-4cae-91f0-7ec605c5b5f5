<?php

declare(strict_types=1);

namespace Tests\Stub\Assets;

use App\Assets\AssetsHelper;

class AssetsHelperStub extends AssetsHelper
{
    /** @var array<string|null> */
    private array $javaScriptEntryFileContents = [];

    public function getJavaScriptEntryFileContents(string $entryName): ?string
    {
        if (array_key_exists($entryName, $this->javaScriptEntryFileContents)) {
            return $this->javaScriptEntryFileContents[$entryName];
        }

        return parent::getJavaScriptEntryFileContents($entryName);
    }

    public function setJavaScriptEntryFileContents(string $entryName, ?string $contents): void
    {
        $this->javaScriptEntryFileContents[$entryName] = $contents;
    }
}

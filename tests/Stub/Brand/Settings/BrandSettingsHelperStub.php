<?php

declare(strict_types=1);

namespace Tests\Stub\Brand\Settings;

use App\Brand\Settings\BrandSettings;
use App\Brand\Settings\BrandSettingsHelper;

class BrandSettingsHelperStub extends BrandSettingsHelper
{
    public function setSettings(BrandSettings $brandSettings): self
    {
        $this->brandSettings = $brandSettings;

        return $this;
    }

    public function resetSettings(): void
    {
        unset($this->brandSettings);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\BrandAssets\Settings;

use App\BrandAssets\Settings\BrandAssetsSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method BrandAssetsSettings create()
 */
final class BrandAssetsSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    /** @var mixed[] */
    private array $config = [];

    private string $brandSlug = 'brand-slug';

    /**
     * @param mixed[] $config
     */
    public function setConfig(array $config): self
    {
        $this->config = $config;

        return $this;
    }

    public function setBrandSlug(string $brandSlug): self
    {
        $this->brandSlug = $brandSlug;

        return $this;
    }

    protected function createSettings(): BrandAssetsSettings
    {
        return new BrandAssetsSettings(
            $this->config,
            $this->brandSlug,
        );
    }
}

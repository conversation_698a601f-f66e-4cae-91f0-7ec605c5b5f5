<?php

declare(strict_types=1);

namespace Tests\Stub\ContentPage\Request;

use App\ContentPage\Request\ContentPageRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class ContentPageRequestStub implements ContentPageRequestInterface
{
    private ?int $publicId;

    private ?int $previousPublicId;

    private ?string $slug;

    private bool $isPreviousFromFallbackCollection;

    public function __construct()
    {
        $this->publicId = TestStubRandomizer::createNullableInt();
        $this->previousPublicId = TestStubRandomizer::createNullableInt();
        $this->isPreviousFromFallbackCollection = TestStubRandomizer::pickBoolean();
    }

    public function getPublicId(): ?int
    {
        return $this->publicId;
    }

    public function setPublicId(?int $publicId): self
    {
        $this->publicId = $publicId;

        return $this;
    }

    public function getPreviousPublicId(): ?int
    {
        return $this->previousPublicId;
    }

    public function setPreviousPublicId(?int $previousPublicId): self
    {
        $this->previousPublicId = $previousPublicId;

        return $this;
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function setSlug(?string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function isPreviousFromFallbackCollection(): bool
    {
        return $this->isPreviousFromFallbackCollection;
    }

    public function setIsPreviousFromFallbackCollection(bool $isPreviousFromFallbackCollection): self
    {
        $this->isPreviousFromFallbackCollection = $isPreviousFromFallbackCollection;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_CONTENT_PAGE_PUBLIC_ID                    => $this->getPublicId(),
            self::PARAMETER_PREVIOUS_CONTENT_PAGE_PUBLIC_ID           => $this->getPreviousPublicId(),
            self::ATTRIBUTE_CONTENT_PAGE_SLUG                         => $this->getSlug(),
            self::PARAMETER_PREVIOUS_CONTENT_PAGE_FALLBACK_COLLECTION => $this->isPreviousFromFallbackCollection(),
        ];
    }
}

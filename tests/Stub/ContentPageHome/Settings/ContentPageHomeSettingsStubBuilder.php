<?php

declare(strict_types=1);

namespace Tests\Stub\ContentPageHome\Settings;

use App\ContentPageHome\Settings\ContentPageHomeSettings;
use App\ContentPageHome\Type\ContentPageHomeType;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method ContentPageHomeSettings create()
 */
final class ContentPageHomeSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private ?ContentPageHomeType $type = null;

    private ?string $searchRoute = null;

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setType(?ContentPageHomeType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function setSearchRoute(?string $searchRoute): self
    {
        $this->searchRoute = $searchRoute;

        return $this;
    }

    protected function createSettings(): ContentPageHomeSettings
    {
        if ($this->enabled) {
            return new ContentPageHomeSettings(
                enabled    : $this->enabled,
                type       : $this->type,
                searchRoute: $this->searchRoute ?? 'route_search',
            );
        }

        return new ContentPageHomeSettings(
            enabled    : false,
            type       : null,
            searchRoute: null,
        );
    }
}

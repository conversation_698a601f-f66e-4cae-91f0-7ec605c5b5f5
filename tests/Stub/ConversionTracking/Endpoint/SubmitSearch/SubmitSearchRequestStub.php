<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\Endpoint\SubmitSearch;

use App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchRequestFlag;
use App\ConversionTracking\Endpoint\SubmitSearch\SubmitSearchRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class SubmitSearchRequestStub implements SubmitSearchRequestInterface
{
    private bool $hasPreventConversionLoggingFlag;

    public function __construct()
    {
        $this->hasPreventConversionLoggingFlag = TestStubRandomizer::pickBoolean();
    }

    public function hasPreventConversionLoggingFlag(): bool
    {
        return $this->hasPreventConversionLoggingFlag;
    }

    public function setHasPreventConversionLoggingFlag(bool $hasPreventConversionLoggingFlag): self
    {
        $this->hasPreventConversionLoggingFlag = $hasPreventConversionLoggingFlag;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            SubmitSearchRequestFlag::PREVENT_CONVERSION_LOGGING => $this->hasPreventConversionLoggingFlag(),
        ];
    }
}

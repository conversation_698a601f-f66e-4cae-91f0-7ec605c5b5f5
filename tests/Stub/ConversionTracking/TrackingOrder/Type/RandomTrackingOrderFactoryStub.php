<?php

declare(strict_types=1);

namespace Tests\Stub\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\TrackingOrder\Type\RandomTrackingOrder;
use App\ConversionTracking\TrackingOrder\Type\RandomTrackingOrderFactory;

class RandomTrackingOrderFactoryStub extends RandomTrackingOrderFactory
{
    private string $randomTrackingOrderId;

    public function create(): RandomTrackingOrder
    {
        $orderId = $this->randomTrackingOrderId ?? uuid_create(UUID_TYPE_RANDOM);

        return new RandomTrackingOrder(
            $orderId,
        );
    }

    public function setRandomTrackingOrderId(string $randomTrackingOrderId): self
    {
        $this->randomTrackingOrderId = $randomTrackingOrderId;

        return $this;
    }
}

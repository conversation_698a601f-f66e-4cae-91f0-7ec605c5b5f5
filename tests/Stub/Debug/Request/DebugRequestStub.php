<?php

declare(strict_types=1);

namespace Tests\Stub\Debug\Request;

use App\Ads\AdProvider;
use App\ContentPageHome\Type\ContentPageHomeType;
use App\Debug\Request\DebugRequestInterface;
use Visymo\Shared\Domain\Locale\Region;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class DebugRequestStub implements DebugRequestInterface
{
    private ?string $contentPageCollection;

    private bool $debugInfo;

    private bool $disableBingAds;

    private bool $disableContentPage;

    private bool $disableContentPages;

    private bool $disableGoogleAds;

    private bool $disableProfiler;

    private bool $enableCheq;

    private bool $enableModule;

    private bool $forceBotSearch;

    private bool $forceCacheRefresh;

    private bool $forceMockSearch;

    private bool $throwException;

    private bool $isAdBot;

    private bool $isFriendlyBot;

    private bool $isCustomError;

    private bool $rateLimitExceeded;

    private bool $showFixedStats;

    private bool $showGoogleTestAd;

    private bool $showUnpublishedContentPages;

    private ?int $forceStyleId;

    private ?int $statusCode;

    private ?string $countryCode;

    private ?string $forcePrimaryAdsType;

    private ?string $preferCsapiServer;

    private ?string $splitTestVariant;

    private ?string $forceCsaContainerPrefix;

    private ?string $forceCsaContainerSuffix;

    private ?string $userIp;

    private ?string $contentPageHomeType;

    private ?string $templateOverride;

    public function __construct()
    {
        $this->contentPageCollection = TestStubRandomizer::createNullableString('finance');
        $this->debugInfo = TestStubRandomizer::pickBoolean();
        $this->disableBingAds = TestStubRandomizer::pickBoolean();
        $this->disableContentPage = TestStubRandomizer::pickBoolean();
        $this->disableContentPages = TestStubRandomizer::pickBoolean();
        $this->disableGoogleAds = TestStubRandomizer::pickBoolean();
        $this->disableProfiler = TestStubRandomizer::pickBoolean();
        $this->enableCheq = TestStubRandomizer::pickBoolean();
        $this->enableModule = TestStubRandomizer::pickBoolean();
        $this->forceBotSearch = TestStubRandomizer::pickBoolean();
        $this->forceCacheRefresh = TestStubRandomizer::pickBoolean();
        $this->forceMockSearch = TestStubRandomizer::pickBoolean();
        $this->throwException = TestStubRandomizer::pickBoolean();
        $this->isAdBot = TestStubRandomizer::pickBoolean();
        $this->isFriendlyBot = TestStubRandomizer::pickBoolean();
        $this->isCustomError = TestStubRandomizer::pickBoolean();
        $this->rateLimitExceeded = TestStubRandomizer::pickBoolean();
        $this->showFixedStats = TestStubRandomizer::pickBoolean();
        $this->showGoogleTestAd = TestStubRandomizer::pickBoolean();
        $this->showUnpublishedContentPages = TestStubRandomizer::pickBoolean();
        $this->forceStyleId = TestStubRandomizer::createNullableInt(5543, **********);
        $this->statusCode = TestStubRandomizer::createNullableInt(200, 503);
        $this->countryCode = TestStubRandomizer::pickBoolean()
            ? null
            : array_rand(array_flip(Region::SUPPORTED_REGIONS));
        $this->forcePrimaryAdsType = TestStubRandomizer::pickBoolean()
            ? null
            : array_rand(array_flip(AdProvider::TYPES));
        $this->preferCsapiServer = TestStubRandomizer::pickBoolean()
            ? null
            : sprintf('csapi%u', random_int(1, 5));
        $this->splitTestVariant = TestStubRandomizer::pickBoolean()
            ? null
            : sprintf('variant-%u', random_int(1, 5));
        $this->forceCsaContainerPrefix = TestStubRandomizer::pickBoolean()
            ? null
            : 'prefix';
        $this->forceCsaContainerSuffix = TestStubRandomizer::pickBoolean()
            ? null
            : 'suffix';
        $this->userIp = TestStubRandomizer::pickBoolean()
            ? null
            : sprintf(
                '%u.%u.%u.%u',
                random_int(1, 255),
                random_int(1, 255),
                random_int(1, 255),
                random_int(1, 255),
            );
        $this->contentPageHomeType = TestStubRandomizer::pickNullableItem(
            array_column(ContentPageHomeType::cases(), 'value'),
        );
        $this->templateOverride = TestStubRandomizer::createNullableString();
    }

    public function debugInfo(): bool
    {
        return $this->debugInfo;
    }

    public function setDebugInfo(bool $debugInfo = true): self
    {
        $this->debugInfo = $debugInfo;

        return $this;
    }

    public function disableBingAds(): bool
    {
        return $this->disableBingAds;
    }

    public function setDisableBingAds(bool $disableBingAds = true): self
    {
        $this->disableBingAds = $disableBingAds;

        return $this;
    }

    public function disableContentPage(): bool
    {
        return $this->disableContentPage;
    }

    public function setDisableContentPage(bool $disableContentPage = true): self
    {
        $this->disableContentPage = $disableContentPage;

        return $this;
    }

    public function disableContentPages(): bool
    {
        return $this->disableContentPages;
    }

    public function setDisableContentPages(bool $disableContentPages = true): self
    {
        $this->disableContentPages = $disableContentPages;

        return $this;
    }

    public function disableGoogleAds(): bool
    {
        return $this->disableGoogleAds;
    }

    public function setDisableGoogleAds(bool $disableGoogleAds = true): self
    {
        $this->disableGoogleAds = $disableGoogleAds;

        return $this;
    }

    public function disableProfiler(): bool
    {
        return $this->disableProfiler;
    }

    public function setDisableProfiler(bool $disableProfiler = true): self
    {
        $this->disableProfiler = $disableProfiler;

        return $this;
    }

    public function enableCheq(): bool
    {
        return $this->enableCheq;
    }

    public function setEnableCheq(bool $enableCheq = true): self
    {
        $this->enableCheq = $enableCheq;

        return $this;
    }

    public function enableModule(): bool
    {
        return $this->enableModule;
    }

    public function setEnableModule(bool $enableModule = true): self
    {
        $this->enableModule = $enableModule;

        return $this;
    }

    public function forceBotSearch(): bool
    {
        return $this->forceBotSearch;
    }

    public function setForceBotSearch(bool $forceBotSearch = true): self
    {
        $this->forceBotSearch = $forceBotSearch;

        return $this;
    }

    public function forceCacheRefresh(): bool
    {
        return $this->forceCacheRefresh;
    }

    public function setForceCacheRefresh(bool $forceCacheRefresh = true): self
    {
        $this->forceCacheRefresh = $forceCacheRefresh;

        return $this;
    }

    public function forceMockSearch(): bool
    {
        return $this->forceMockSearch;
    }

    public function setForceMockSearch(bool $forceMockSearch = true): self
    {
        $this->forceMockSearch = $forceMockSearch;

        return $this;
    }

    public function throwException(): bool
    {
        return $this->throwException;
    }

    public function setThrowException(bool $throwException = true): self
    {
        $this->throwException = $throwException;

        return $this;
    }

    public function forcePrimaryAdsType(): ?string
    {
        return $this->forcePrimaryAdsType;
    }

    public function setForcePrimaryAdsType(?string $forcePrimaryAdsType): self
    {
        $this->forcePrimaryAdsType = $forcePrimaryAdsType;

        return $this;
    }

    public function forceStyleId(): ?int
    {
        return $this->forceStyleId;
    }

    public function setForceStyleId(?int $forceStyleId): self
    {
        $this->forceStyleId = $forceStyleId;

        return $this;
    }

    public function getContentPageCollection(): ?string
    {
        return $this->contentPageCollection;
    }

    public function setContentPageCollection(?string $contentPageCollection): self
    {
        $this->contentPageCollection = $contentPageCollection;

        return $this;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(?string $countryCode): self
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    public function getPreferCsapiServer(): ?string
    {
        return $this->preferCsapiServer;
    }

    public function setPreferCsapiServer(?string $preferCsapiServer): self
    {
        $this->preferCsapiServer = $preferCsapiServer;

        return $this;
    }

    public function getSplitTestVariant(): ?string
    {
        return $this->splitTestVariant;
    }

    public function setSplitTestVariant(?string $splitTestVariant): self
    {
        $this->splitTestVariant = $splitTestVariant;

        return $this;
    }

    public function getDebugForceCsaContainerPrefix(): ?string
    {
        return $this->forceCsaContainerPrefix;
    }

    public function setDebugForceCsaContainerPrefix(?string $forceCsaContainerPrefix): self
    {
        $this->forceCsaContainerPrefix = $forceCsaContainerPrefix;

        return $this;
    }

    public function getDebugForceCsaContainerSuffix(): ?string
    {
        return $this->forceCsaContainerSuffix;
    }

    public function setDebugForceCsaContainerSuffix(?string $forceCsaContainerSuffix): self
    {
        $this->forceCsaContainerSuffix = $forceCsaContainerSuffix;

        return $this;
    }

    public function getStatusCode(): ?int
    {
        return $this->statusCode;
    }

    public function setStatusCode(?int $statusCode): self
    {
        $this->statusCode = $statusCode;

        return $this;
    }

    public function getUserIp(): ?string
    {
        return $this->userIp;
    }

    public function setUserIp(?string $userIp): self
    {
        $this->userIp = $userIp;

        return $this;
    }

    public function isAdBot(): bool
    {
        return $this->isAdBot;
    }

    public function setIsAdBot(bool $isAdBot = true): self
    {
        $this->isAdBot = $isAdBot;

        return $this;
    }

    public function isFriendlyBot(): bool
    {
        return $this->isFriendlyBot;
    }

    public function setIsFriendlyBot(bool $isFriendlyBot = true): self
    {
        $this->isFriendlyBot = $isFriendlyBot;

        return $this;
    }

    public function isCustomError(): bool
    {
        return $this->isCustomError;
    }

    public function setIsCustomError(bool $isCustomError = true): self
    {
        $this->isCustomError = $isCustomError;

        return $this;
    }

    public function rateLimitExceeded(): bool
    {
        return $this->rateLimitExceeded;
    }

    public function setRateLimitExceeded(bool $rateLimitExceeded = true): self
    {
        $this->rateLimitExceeded = $rateLimitExceeded;

        return $this;
    }

    public function showFixedStats(): bool
    {
        return $this->showFixedStats;
    }

    public function setShowFixedStats(bool $showFixedStats = true): self
    {
        $this->showFixedStats = $showFixedStats;

        return $this;
    }

    public function showGoogleTestAd(): bool
    {
        return $this->showGoogleTestAd;
    }

    public function setShowGoogleTestAd(bool $showGoogleTestAd = true): self
    {
        $this->showGoogleTestAd = $showGoogleTestAd;

        return $this;
    }

    public function showUnpublishedContentPages(): bool
    {
        return $this->showUnpublishedContentPages;
    }

    public function setShowUnpublishedContentPages(bool $showUnpublishedContentPages = true): self
    {
        $this->showUnpublishedContentPages = $showUnpublishedContentPages;

        return $this;
    }

    public function getContentPageHomeType(): ?string
    {
        return $this->contentPageHomeType;
    }

    public function setContentPageHomeType(?string $contentPageHomeType): self
    {
        $this->contentPageHomeType = $contentPageHomeType;

        return $this;
    }

    public function setTemplateOverride(?string $templateOverride): self
    {
        $this->templateOverride = $templateOverride;

        return $this;
    }

    public function getTemplateOverride(): ?string
    {
        return $this->templateOverride;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_DEBUG_COUNTRY_CODE                   => $this->getCountryCode(),
            self::PARAMETER_DEBUG_DISABLE_BING_ADS               => $this->disableBingAds(),
            self::PARAMETER_DEBUG_DISABLE_CONTENT_PAGE           => $this->disableContentPage(),
            self::PARAMETER_DEBUG_DISABLE_CONTENT_PAGES          => $this->disableContentPages(),
            self::PARAMETER_DEBUG_DISABLE_GOOGLE_ADS             => $this->disableGoogleAds(),
            self::PARAMETER_DEBUG_DISABLE_PROFILER               => $this->disableProfiler(),
            self::PARAMETER_DEBUG_ENABLE_CHEQ                    => $this->enableCheq(),
            self::PARAMETER_DEBUG_ENABLE_MODULE                  => $this->enableModule(),
            self::PARAMETER_DEBUG_FORCE_BOT_SEARCH               => $this->forceBotSearch(),
            self::PARAMETER_DEBUG_FORCE_CACHE_REFRESH            => $this->forceCacheRefresh(),
            self::PARAMETER_DEBUG_FORCE_MOCK_SEARCH              => $this->forceMockSearch(),
            self::PARAMETER_DEBUG_FORCE_PRIMARY_ADS_TYPE         => $this->forcePrimaryAdsType(),
            self::PARAMETER_DEBUG_FORCE_STYLE_ID                 => $this->forceStyleId(),
            self::PARAMETER_DEBUG_INFO                           => $this->debugInfo(),
            self::PARAMETER_DEBUG_IS_AD_BOT                      => $this->isAdBot(),
            self::PARAMETER_DEBUG_IS_FRIENDLY_BOT                => $this->isFriendlyBot(),
            self::PARAMETER_DEBUG_IS_CUSTOM_ERROR                => $this->isCustomError(),
            self::PARAMETER_DEBUG_PREFER_CSAPI_SERVER            => $this->getPreferCsapiServer(),
            self::PARAMETER_DEBUG_RATE_LIMIT_EXCEEDED            => $this->rateLimitExceeded(),
            self::PARAMETER_DEBUG_SHOW_FIXED_STATS               => $this->showFixedStats(),
            self::PARAMETER_DEBUG_SHOW_GOOGLE_TEST_AD            => $this->showGoogleTestAd(),
            self::PARAMETER_DEBUG_SHOW_UNPUBLISHED_CONTENT_PAGES => $this->showUnpublishedContentPages(),
            self::PARAMETER_DEBUG_SPLIT_TEST_VARIANT             => $this->getSplitTestVariant(),
            self::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_PREFIX     => $this->getDebugForceCsaContainerPrefix(),
            self::PARAMETER_DEBUG_FORCE_CSA_CONTAINER_SUFFIX     => $this->getDebugForceCsaContainerSuffix(),
            self::PARAMETER_DEBUG_STATUS_CODE                    => $this->getStatusCode(),
            self::PARAMETER_DEBUG_USER_IP                        => $this->getUserIp(),
            self::PARAMETER_DEBUG_CONTENT_PAGE_HOME_TYPE         => $this->getContentPageHomeType(),
            self::PARAMETER_DEBUG_TEMPLATE_OVERRIDE              => $this->getTemplateOverride(),
        ];
    }
}

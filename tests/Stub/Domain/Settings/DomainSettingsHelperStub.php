<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Settings;

use App\Domain\Settings\DomainSettings;
use App\Domain\Settings\DomainSettingsHelperInterface;

final class DomainSettingsHelperStub implements DomainSettingsHelperInterface
{
    private DomainSettings $domainSettings;

    public function setSettings(DomainSettings $domainSettings): self
    {
        $this->domainSettings = $domainSettings;

        return $this;
    }

    public function getSettings(): DomainSettings
    {
        return $this->domainSettings;
    }
}

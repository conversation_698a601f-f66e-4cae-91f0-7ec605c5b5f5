<?php

declare(strict_types=1);

namespace Tests\Stub\FriendlyBot\Request;

use App\FriendlyBot\Bot\FriendlyBot;
use App\FriendlyBot\Request\FriendlyBotRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class FriendlyBotRequestStub implements FriendlyBotRequestInterface
{
    private bool $isFriendlyBot;

    private ?FriendlyBot $friendlyBot;

    public function __construct()
    {
        $this->isFriendlyBot = TestStubRandomizer::pickBoolean();

        if (!$this->isFriendlyBot || TestStubRandomizer::createInt(0, 2) === 1) {
            $this->friendlyBot = null;
        } else {
            $this->friendlyBot = TestStubRandomizer::pickItem(FriendlyBot::cases());
        }
    }

    public function isFriendlyBot(): bool
    {
        return $this->isFriendlyBot;
    }

    public function setIsFriendlyBot(bool $isFriendlyBot = true): self
    {
        $this->isFriendlyBot = $isFriendlyBot;

        return $this;
    }

    public function getFriendlyBot(): ?FriendlyBot
    {
        return $this->isFriendlyBot() ? $this->friendlyBot : null;
    }

    public function setFriendlyBot(?FriendlyBot $friendlyBot): self
    {
        $this->friendlyBot = $friendlyBot;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_IS_FRIENDLY_BOT => $this->isFriendlyBot(),
            self::KEY_FRIENDLY_BOT    => $this->getFriendlyBot()?->value,
        ];
    }
}

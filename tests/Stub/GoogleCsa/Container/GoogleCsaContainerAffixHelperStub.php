<?php

declare(strict_types=1);

namespace Tests\Stub\GoogleCsa\Container;

use App\GoogleCsa\Container\GoogleCsaContainerAffixHelperInterface;

final class GoogleCsaContainerAffixHelperStub implements GoogleCsaContainerAffixHelperInterface
{
    private ?string $containerSuffix = null;

    private ?string $containerPrefix = null;

    public function getContainerSuffix(): ?string
    {
        return $this->containerSuffix;
    }

    public function setContainerSuffix(?string $containerSuffix): self
    {
        $this->containerSuffix = $containerSuffix;

        return $this;
    }

    public function getContainerPrefix(): ?string
    {
        return $this->containerPrefix;
    }

    public function setContainerPrefix(?string $containerPrefix): self
    {
        $this->containerPrefix = $containerPrefix;

        return $this;
    }

    public function getAffixedContainer(string $container, ?string $containerSuffix = null): string
    {
        $additionalContainerSuffix = $this->getContainerSuffix();
        $containerPrefix = $this->getContainerPrefix();

        if ($additionalContainerSuffix !== null) {
            $containerSuffix = $containerSuffix !== null
                ? sprintf(
                    '%s-%s',
                    $containerSuffix,
                    $additionalContainerSuffix,
                )
                : $additionalContainerSuffix;
        }

        $containerWithSuffix = $containerSuffix === null
            ? $container
            : sprintf('%s-%s', $container, $containerSuffix);

        return $containerPrefix !== null
            ? sprintf('%s-%s', $containerPrefix, $containerWithSuffix)
            : $containerWithSuffix;
    }
}

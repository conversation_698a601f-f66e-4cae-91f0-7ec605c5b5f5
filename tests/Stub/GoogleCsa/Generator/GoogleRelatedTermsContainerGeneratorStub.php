<?php

declare(strict_types=1);

namespace Tests\Stub\GoogleCsa\Generator;

use App\GoogleCsa\Generator\GoogleRelatedTermsContainerGeneratorInterface;

final class GoogleRelatedTermsContainerGeneratorStub implements GoogleRelatedTermsContainerGeneratorInterface
{
    private static int $containerCount = 0;

    public function generateContainer(?string $suffix): string
    {
        self::$containerCount++;

        if ($suffix !== null) {
            return sprintf(self::CONTAINER_SUFFIXED_TEMPLATE, $suffix, self::$containerCount);
        }

        return sprintf(self::CONTAINER_TEMPLATE, self::$containerCount);
    }

    public function reset(): void
    {
        self::$containerCount = 0;
    }
}

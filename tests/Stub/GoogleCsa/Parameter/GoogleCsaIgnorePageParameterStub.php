<?php

declare(strict_types=1);

namespace Tests\Stub\GoogleCsa\Parameter;

use App\GoogleCsa\Parameter\GoogleCsaIgnorePageParameterInterface;

final class GoogleCsaIgnorePageParameterStub implements GoogleCsaIgnorePageParameterInterface
{
    /** @var string[] */
    private array $ignoreParams = [];

    /**
     * @inheritDoc
     */
    public function getIgnoreParams(): array
    {
        return $this->ignoreParams;
    }

    /**
     * @param string[] $ignoreParams
     */
    public function setIgnoreParams(array $ignoreParams): self
    {
        $this->ignoreParams = $ignoreParams;

        return $this;
    }
}

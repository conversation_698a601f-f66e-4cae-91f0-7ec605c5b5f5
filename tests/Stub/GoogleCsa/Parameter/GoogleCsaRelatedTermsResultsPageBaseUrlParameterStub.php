<?php

declare(strict_types=1);

namespace Tests\Stub\GoogleCsa\Parameter;

use App\GoogleCsa\Parameter\GoogleCsaRelatedTermsResultsPageBaseUrlParameterInterface;

final class GoogleCsaRelatedTermsResultsPageBaseUrlParameterStub implements
    GoogleCsaRelatedTermsResultsPageBaseUrlParameterInterface
{
    private string $resultsPageBaseUrl;

    public function getResultsPageBaseUrl(?string $relatedTermsRoute): string
    {
        return $this->resultsPageBaseUrl;
    }

    public function setResultsPageBaseUrl(string $resultsPageBaseUrl): self
    {
        $this->resultsPageBaseUrl = $resultsPageBaseUrl;

        return $this;
    }
}

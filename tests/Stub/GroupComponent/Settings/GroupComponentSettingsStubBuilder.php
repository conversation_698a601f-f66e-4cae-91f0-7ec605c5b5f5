<?php

declare(strict_types=1);

namespace Tests\Stub\GroupComponent\Settings;

use App\Component\Generic\Group\GroupLayout;
use App\GroupComponent\Settings\GroupComponentSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method GroupComponentSettings create()
 */
final class GroupComponentSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    /** @var array<string, string> */
    private array $layoutVariant = [];

    public function setLayoutVariant(GroupLayout $layout, string $variant): self
    {
        $this->layoutVariant[$layout->value] = $variant;

        return $this;
    }

    protected function createSettings(): GroupComponentSettings
    {
        return new GroupComponentSettings(
            layoutVariant: $this->layoutVariant,
        );
    }
}

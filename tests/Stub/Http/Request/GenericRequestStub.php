<?php

declare(strict_types=1);

namespace Tests\Stub\Http\Request;

use App\Http\Request\GenericRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class GenericRequestStub implements GenericRequestInterface
{
    private ?string $visitId;

    private ?string $pageviewId;

    private ?int $appTs;

    public function __construct()
    {
        $this->visitId = TestStubRandomizer::pickBoolean() ? null : uuid_create(UUID_TYPE_RANDOM);
        $this->pageviewId = TestStubRandomizer::pickBoolean() ? null : uuid_create(UUID_TYPE_RANDOM);
        $this->appTs = TestStubRandomizer::pickBoolean() ? null : TestStubRandomizer::createInt();
    }

    public function getVisitId(): ?string
    {
        return $this->visitId;
    }

    public function setVisitId(?string $visitId): self
    {
        $this->visitId = $visitId;

        return $this;
    }

    public function getPageviewId(): ?string
    {
        return $this->pageviewId;
    }

    public function setPageviewId(?string $pageviewId): self
    {
        $this->pageviewId = $pageviewId;

        return $this;
    }

    public function getAppTs(): ?int
    {
        return $this->appTs;
    }

    public function setAppTs(?int $appTs): self
    {
        $this->appTs = $appTs;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_PAGEVIEW_ID    => $this->getPageviewId(),
            self::PARAMETER_VISIT_ID => $this->getVisitId(),
            self::PARAMETER_APP_TS   => $this->getAppTs(),
        ];
    }
}

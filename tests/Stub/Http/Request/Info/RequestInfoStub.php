<?php

declare(strict_types=1);

namespace Tests\Stub\Http\Request\Info;

use App\Http\Request\Info\RequestInfoInterface;
use Visymo\Shared\Domain\Locale\Locale;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class RequestInfoStub implements RequestInfoInterface
{
    private string $url;

    private string $relativeUrl;

    private string $route;

    private string $locale;

    private ?string $userIp;

    private string $userAgent;

    /** @var array<string, string> */
    private array $userAgentClientHints;

    private string $acceptLanguage;

    private string $host;

    private string $normalisedHost;

    private bool $hasUrlParameters;

    private ?string $tlsVersion;

    private ?string $referer;

    private string $pathInfo;

    private string $method;

    private string $scheme;

    private string $protocol;

    public function __construct()
    {
        $this->url = TestStubRandomizer::createString('https://www.zapmeta.com?q=query');
        $this->relativeUrl = TestStubRandomizer::createString('/search?q=query');
        $this->route = TestStubRandomizer::createString('route');
        $this->locale = TestStubRandomizer::pickItem(Locale::SUPPORTED_LOCALES);
        $this->userIp = TestStubRandomizer::pickBoolean()
            ? null
            : sprintf(
                '%u.%u.%u.%u',
                random_int(0, 255),
                random_int(0, 255),
                random_int(0, 255),
                random_int(0, 255),
            );
        $this->userAgent = TestStubRandomizer::createString('user-agent');
        $this->userAgentClientHints = [];

        $acceptLanguages = ['nl-NL', 'nl;q=0.9', 'en-US;q=0.8', 'en;q=0.7'];
        shuffle($acceptLanguages);
        $this->acceptLanguage = implode(',', $acceptLanguages);

        $this->host = 'https://www.zapmeta.com';
        $this->normalisedHost = $this->host;
        $this->hasUrlParameters = true;
        $this->tlsVersion = TestStubRandomizer::createNullableString('TLSv');
        $this->referer = TestStubRandomizer::createNullableString('referer');
        $this->pathInfo = TestStubRandomizer::createString('pathinfo');
        $this->method = TestStubRandomizer::createString('method');
        $this->scheme = TestStubRandomizer::createString('scheme');
        $this->protocol = TestStubRandomizer::createString('protocol');
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getRelativeUrl(): string
    {
        return $this->relativeUrl;
    }

    public function setRelativeUrl(string $relativeUrl): self
    {
        $this->relativeUrl = $relativeUrl;

        return $this;
    }

    public function getRoute(): string
    {
        return $this->route;
    }

    public function setRoute(string $route): self
    {
        $this->route = $route;

        return $this;
    }

    public function isRoute(string $route): bool
    {
        return $this->getRoute() === $route;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    public function getUserIp(): ?string
    {
        return $this->userIp;
    }

    public function setUserIp(?string $userIp): self
    {
        $this->userIp = $userIp;

        return $this;
    }

    public function getUserAgent(): string
    {
        return $this->userAgent;
    }

    public function setUserAgent(string $userAgent): self
    {
        $this->userAgent = $userAgent;

        return $this;
    }

    /**
     * @return array<string, string>
     */
    public function getUserAgentClientHints(): array
    {
        return $this->userAgentClientHints;
    }

    /**
     * @param array<string, string> $userAgentClientHints
     */
    public function setUserAgentClientHints(array $userAgentClientHints): self
    {
        $this->userAgentClientHints = $userAgentClientHints;

        return $this;
    }

    public function getAcceptLanguage(): string
    {
        return $this->acceptLanguage;
    }

    public function setAcceptLanguage(string $acceptLanguage): self
    {
        $this->acceptLanguage = $acceptLanguage;

        return $this;
    }

    public function getHost(): string
    {
        return $this->host;
    }

    public function setHost(string $host): self
    {
        $this->host = $host;

        return $this;
    }

    public function getNormalisedHost(): string
    {
        return $this->normalisedHost;
    }

    public function setNormalisedHost(string $normalisedHost): self
    {
        $this->normalisedHost = $normalisedHost;

        return $this;
    }

    public function hasUrlParameters(): bool
    {
        return $this->hasUrlParameters;
    }

    public function setHasUrlParameters(bool $hasUrlParameters = true): self
    {
        $this->hasUrlParameters = $hasUrlParameters;

        return $this;
    }

    public function getTlsVersion(): ?string
    {
        return $this->tlsVersion;
    }

    public function setTlsVersion(?string $tlsVersion): self
    {
        $this->tlsVersion = $tlsVersion;

        return $this;
    }

    public function getReferer(): ?string
    {
        return $this->referer;
    }

    public function setReferer(?string $referer): self
    {
        $this->referer = $referer;

        return $this;
    }

    public function getPathInfo(): string
    {
        return $this->pathInfo;
    }

    public function setPathInfo(string $pathInfo): self
    {
        $this->pathInfo = $pathInfo;

        return $this;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function setMethod(string $method): self
    {
        $this->method = $method;

        return $this;
    }

    public function getScheme(): string
    {
        return $this->scheme;
    }

    public function setScheme(string $scheme): self
    {
        $this->scheme = $scheme;

        return $this;
    }

    public function getProtocol(): string
    {
        return $this->protocol;
    }

    public function setProtocol(string $protocol): self
    {
        $this->protocol = $protocol;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_URL                     => $this->getUrl(),
            self::KEY_RELATIVE_URL            => $this->getRelativeUrl(),
            self::KEY_ROUTE                   => $this->getRoute(),
            self::KEY_LOCALE                  => $this->getLocale(),
            self::KEY_USER_IP                 => $this->getUserIp(),
            self::KEY_USER_AGENT              => $this->getUserAgent(),
            self::KEY_USER_AGENT_CLIENT_HINTS => $this->getUserAgentClientHints(),
            self::KEY_ACCEPT_LANGUAGE         => $this->getAcceptLanguage(),
            self::KEY_HOST                    => $this->getHost(),
            self::KEY_NORMALISED_HOST         => $this->getNormalisedHost(),
            self::KEY_HAS_URL_PARAMETERS      => $this->hasUrlParameters(),
            self::KEY_TLS_VERSION             => $this->getTlsVersion(),
            self::KEY_REFERER                 => $this->getReferer(),
            self::KEY_PATH_INFO               => $this->getPathInfo(),
            self::KEY_METHOD                  => $this->getMethod(),
            self::KEY_SCHEME                  => $this->getScheme(),
            self::KEY_PROTOCOL                => $this->getProtocol(),
        ];
    }
}

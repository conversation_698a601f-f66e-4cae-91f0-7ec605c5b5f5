<?php

declare(strict_types=1);

namespace Tests\Stub\Http\Request\LoadBalancer;

use App\Http\Request\LoadBalancer\LoadBalancerRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class LoadBalancerRequestStub implements LoadBalancerRequestInterface
{
    private bool $isSfoRequest;

    public function __construct()
    {
        $this->isSfoRequest = TestStubRandomizer::pickBoolean();
    }

    public function isSfo(): bool
    {
        return $this->isSfoRequest;
    }

    /**
     * @return array<string, bool>
     */
    public function toArray(): array
    {
        return [
            self::KEY_SFO => $this->isSfo(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\InfoPages\Settings;

use App\InfoPages\Page\PageType;
use App\InfoPages\Settings\InfoPagesSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method InfoPagesSettings create()
 */
final class InfoPagesSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private PageType $pageType = PageType::SEARCH;

    private bool $linkToExternalAboutPage = false;

    private bool $linkToVisymoPublishing = false;

    public function setLinkToExternalAboutPage(bool $linkToExternalAboutPage): self
    {
        $this->linkToExternalAboutPage = $linkToExternalAboutPage;

        return $this;
    }

    public function setLinkToVisymoPublishing(bool $linkToVisymoPublishing): self
    {
        $this->linkToVisymoPublishing = $linkToVisymoPublishing;

        return $this;
    }

    protected function createSettings(): InfoPagesSettings
    {
        return new InfoPagesSettings(
            pageType               : $this->pageType,
            linkToExternalAboutPage: $this->linkToExternalAboutPage,
            linkToVisymoPublishing : $this->linkToVisymoPublishing,
        );
    }
}

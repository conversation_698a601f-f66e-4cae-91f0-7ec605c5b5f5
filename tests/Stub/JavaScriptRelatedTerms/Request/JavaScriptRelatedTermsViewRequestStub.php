<?php

declare(strict_types=1);

namespace Tests\Stub\JavaScriptRelatedTerms\Request;

use App\JavaScriptRelatedTerms\Request\JavaScriptRelatedTermsViewRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class JavaScriptRelatedTermsViewRequestStub implements JavaScriptRelatedTermsViewRequestInterface
{
    private string $query;

    private string $url;

    private ?int $styleId;

    /** @var string[] */
    private ?array $relatedTerms;

    public function __construct()
    {
        $this->query = TestStubRandomizer::createString('q');
        $this->url = TestStubRandomizer::createUrl();
        $this->styleId = TestStubRandomizer::createNullableInt();
        $this->relatedTerms = TestStubRandomizer::createCollection(
            static fn () => TestStubRandomizer::createString('related'),
            random_int(0, 12),
        );
    }

    public function getQuery(): string
    {
        return $this->query;
    }

    public function setQuery(string $query): self
    {
        $this->query = $query;

        return $this;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function getStyleId(): ?int
    {
        return $this->styleId;
    }

    public function setStyleId(?int $styleId): self
    {
        $this->styleId = $styleId;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getRelatedTerms(): ?array
    {
        return $this->relatedTerms;
    }

    /**
     * @param string[]|null $relatedTerms
     */
    public function setRelatedTerms(?array $relatedTerms): self
    {
        $this->relatedTerms = $relatedTerms;

        return $this;
    }

    /**
     * @return array<string, array<string>|int|string|null>
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_QUERY    => $this->getQuery(),
            self::PARAMETER_URL      => $this->getUrl(),
            self::PARAMETER_STYLE_ID => $this->getStyleId(),
            self::PARAMETER_TERMS    => $this->getRelatedTerms(),
        ];
    }
}

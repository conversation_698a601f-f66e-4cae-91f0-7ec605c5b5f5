<?php

declare(strict_types=1);

namespace Tests\Stub\JsonTemplate\View\Data;

use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\ContentPage\Response\ContentPageResponseContext;

final class ViewDataRegistryStub extends ViewDataRegistry
{
    public function setContentPage(?ContentPageResponseContext $contentPageResponseContext): void
    {
        $this->dataRegistry->set(
            ViewDataProperty::CONTENT_PAGE,
            static fn () => $contentPageResponseContext,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\Locale\Request;

use App\Locale\Request\LocaleRequestInterface;
use Visymo\Shared\Domain\Locale\Locale;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class LocaleRequestStub implements LocaleRequestInterface
{
    private ?string $locale;

    public function __construct()
    {
        $this->locale = TestStubRandomizer::pickItem(
            [
                null,
                ...Locale::SUPPORTED_LOCALES,
            ],
        );
    }

    public function getLocale(): ?string
    {
        return $this->locale;
    }

    public function setLocale(?string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getUrlParameters(): array
    {
        return $this->toArray();
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_LOCALE => $this->getLocale(),
        ];
    }
}

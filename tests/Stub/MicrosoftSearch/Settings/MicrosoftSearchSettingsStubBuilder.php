<?php

declare(strict_types=1);

namespace Tests\Stub\MicrosoftSearch\Settings;

use App\MicrosoftSearch\Settings\MicrosoftSearchSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method MicrosoftSearchSettings create()
 */
final class MicrosoftSearchSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    public function setEnabled(bool $enabled = true): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    protected function createSettings(): MicrosoftSearchSettings
    {
        return new MicrosoftSearchSettings(
            enabled: $this->enabled,
        );
    }
}

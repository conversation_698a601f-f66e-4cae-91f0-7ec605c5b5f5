<?php

declare(strict_types=1);

namespace Tests\Stub\MicrosoftSearchRelated\Settings;

use App\MicrosoftSearchRelated\Settings\MicrosoftSearchRelatedSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method MicrosoftSearchRelatedSettings create()
 */
final class MicrosoftSearchRelatedSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private ?int $styleIdDesktop = null;

    private ?int $styleIdMobile = null;

    public function setEnabled(bool $enabled = true): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setStyleIdDesktop(?int $styleIdDesktop): self
    {
        $this->styleIdDesktop = $styleIdDesktop;

        return $this;
    }

    public function setStyleIdMobile(?int $styleIdMobile): self
    {
        $this->styleIdMobile = $styleIdMobile;

        return $this;
    }

    protected function createSettings(): MicrosoftSearchRelatedSettings
    {
        return new MicrosoftSearchRelatedSettings(
            enabled       : $this->enabled,
            styleIdDesktop: $this->styleIdDesktop,
            styleIdMobile : $this->styleIdMobile,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\ModuleSettings;

use App\ModuleSettings\ModuleSettingsInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

abstract class AbstractModuleSettingsStubBuilder
{
    final public function __construct(
        private readonly ?ContainerInterface $container = null
    )
    {
    }

    final public function create(): ModuleSettingsInterface
    {
        $settings = $this->createSettings();
        $this->container?->set($settings::class, $settings);

        return $settings;
    }

    abstract protected function createSettings(): ModuleSettingsInterface;
}

<?php

declare(strict_types=1);

namespace Tests\Stub\Office\Request;

use App\Office\Request\OfficeRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class OfficeRequestStub implements OfficeRequestInterface
{
    private bool $isOffice;

    public function __construct()
    {
        $this->isOffice = TestStubRandomizer::pickBoolean();
    }

    public function isOffice(): bool
    {
        return $this->isOffice;
    }

    public function setIsOffice(bool $isOffice): self
    {
        $this->isOffice = $isOffice;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_IS_OFFICE => $this->isOffice(),
        ];
    }
}

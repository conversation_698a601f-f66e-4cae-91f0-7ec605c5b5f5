<?php

declare(strict_types=1);

namespace Tests\Stub\Search\Settings;

use App\Search\Settings\SearchSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method SearchSettings create()
 */
final class SearchSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private bool $seoEnabled = false;

    private ?int $styleIdDesktop = null;

    private ?int $styleIdMobile = null;

    public function setEnabled(bool $enabled = true): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setSeoEnabled(bool $seoEnabled = true): self
    {
        $this->seoEnabled = $seoEnabled;

        return $this;
    }

    public function setStyleIdDesktop(int $styleIdDesktop): self
    {
        $this->styleIdDesktop = $styleIdDesktop;

        return $this;
    }

    public function setStyleIdMobile(int $styleIdMobile): self
    {
        $this->styleIdMobile = $styleIdMobile;

        return $this;
    }

    public function setStyleId(int $styleId): self
    {
        $this->styleIdDesktop = $styleId;
        $this->styleIdMobile = $styleId;

        return $this;
    }

    protected function createSettings(): SearchSettings
    {
        if ($this->enabled && ($this->styleIdDesktop === null || $this->styleIdMobile === null)) {
            throw new \InvalidArgumentException('Style ID must be set when module is enabled');
        }

        return new SearchSettings(
            enabled       : $this->enabled,
            seoEnabled    : $this->seoEnabled,
            styleIdDesktop: $this->styleIdDesktop,
            styleIdMobile : $this->styleIdMobile,
        );
    }
}

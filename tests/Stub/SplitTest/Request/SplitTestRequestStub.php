<?php

declare(strict_types=1);

namespace Tests\Stub\SplitTest\Request;

use App\SplitTest\Request\SplitTestRequestFlag;
use App\SplitTest\Request\SplitTestRequestInterface;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class SplitTestRequestStub implements SplitTestRequestInterface
{
    private bool $alwaysMatchRouteFlag;

    public function __construct()
    {
        $this->alwaysMatchRouteFlag = TestStubRandomizer::pickBoolean();
    }

    public function hasAlwaysMatchRouteFlag(): bool
    {
        return $this->alwaysMatchRouteFlag;
    }

    public function setAlwaysMatchRouteFlag(bool $alwaysMatchRouteFlag = true): self
    {
        $this->alwaysMatchRouteFlag = $alwaysMatchRouteFlag;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            SplitTestRequestFlag::ALWAYS_MATCH_ROUTE => $this->hasAlwaysMatchRouteFlag(),
        ];
    }
}

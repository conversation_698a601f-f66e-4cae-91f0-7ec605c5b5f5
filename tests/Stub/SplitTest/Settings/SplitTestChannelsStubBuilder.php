<?php

declare(strict_types=1);

namespace Tests\Stub\SplitTest\Settings;

use App\SplitTest\Settings\SplitTestChannels;

class SplitTestChannelsStubBuilder
{
    private string $advertised;

    private string $landingPage;

    public function __construct()
    {
        $this->advertised = sprintf('control-channel-advertised-%s', mt_rand());
        $this->landingPage = sprintf('control-channel-landingpage-%s', mt_rand());
    }

    public function setAdvertised(string $advertised): self
    {
        $this->advertised = $advertised;

        return $this;
    }

    public function setLandingPage(string $landingpage): self
    {
        $this->landingPage = $landingpage;

        return $this;
    }

    public function create(): SplitTestChannels
    {
        return new SplitTestChannels(
            advertised : $this->advertised,
            landingPage: $this->landingPage,
        );
    }
}

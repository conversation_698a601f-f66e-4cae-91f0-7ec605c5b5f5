<?php

declare(strict_types=1);

namespace Tests\Stub\SplitTest\Settings;

use App\SplitTest\Settings\SplitTestActivation;
use App\SplitTest\Settings\SplitTestChannels;
use App\SplitTest\Settings\SplitTestSettings;
use App\SplitTest\Settings\SplitTestVariant;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

class SplitTestSettingsStubBuilder
{
    /** @var SplitTestVariant[] */
    private array $variants;

    private int $id;

    private SplitTestActivation $activation;

    private SplitTestChannels $controlChannels;

    public function __construct()
    {
        $this->id = mt_rand();
        $this->activation = (new SplitTestActivationStubBuilder())->create();
        $this->controlChannels = (new SplitTestChannelsStubBuilder())->create();
        $this->variants = TestStubRandomizer::createCollection(
            static fn () => (new SplitTestVariantStubBuilder())->create(),
            random_int(0, 3),
        );
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function setActivation(SplitTestActivation $activation): self
    {
        $this->activation = $activation;

        return $this;
    }

    public function setControlChannels(SplitTestChannels $controlChannels): self
    {
        $this->controlChannels = $controlChannels;

        return $this;
    }

    public function clearVariants(): self
    {
        $this->variants = [];

        return $this;
    }

    /**
     * @param SplitTestVariant[] $variants
     *
     * @return $this
     */
    public function setVariants(array $variants): self
    {
        $this->clearVariants();

        foreach ($variants as $variant) {
            $this->addVariant($variant);
        }

        return $this;
    }

    private function addVariant(SplitTestVariant $variant): void
    {
        $this->variants[$variant->getVariant()] = $variant;
    }

    public function create(): SplitTestSettings
    {
        return new SplitTestSettings(
            $this->id,
            $this->activation,
            $this->controlChannels,
            $this->variants,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\SplitTest\Settings;

use App\SplitTest\Settings\SplitTestChannels;
use App\SplitTest\Settings\SplitTestVariant;

class SplitTestVariantStubBuilder
{
    private string $variant;

    private ?string $containerSuffix;

    private SplitTestChannels $channels;

    private int $percentage;

    public function __construct()
    {
        $this->variant = sprintf('variant_%s', mt_rand());
        $this->containerSuffix = sprintf('container_suffix_%s', mt_rand());
        $this->channels = (new SplitTestChannelsStubBuilder())->create();
        $this->percentage = random_int(0, 20);
    }

    public function setVariant(string $variant): self
    {
        $this->variant = $variant;

        return $this;
    }

    public function setContainerSuffix(?string $containerSuffix): self
    {
        $this->containerSuffix = $containerSuffix;

        return $this;
    }

    public function setChannels(SplitTestChannels $channels): self
    {
        $this->channels = $channels;

        return $this;
    }

    public function setPercentage(int $percentage): self
    {
        $this->percentage = $percentage;

        return $this;
    }

    public function create(): SplitTestVariant
    {
        return new SplitTestVariant(
            variant        : $this->variant,
            containerSuffix: $this->containerSuffix,
            channels       : $this->channels,
            percentage     : $this->percentage,
        );
    }
}

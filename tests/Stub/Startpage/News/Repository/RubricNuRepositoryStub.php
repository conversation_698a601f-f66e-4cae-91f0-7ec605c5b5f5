<?php

declare(strict_types=1);

namespace Tests\Stub\Startpage\News\Repository;

use App\Startpage\News\Model\NewsItem;
use App\Startpage\News\Model\Rubric;
use App\Startpage\News\Repository\RubricRepositoryInterface;

class RubricNuRepositoryStub implements RubricRepositoryInterface
{
    private Rubric $rubric;

    public function setRubric(Rubric $rubric): self
    {
        $this->rubric = $rubric;

        return $this;
    }

    public function findByName(string $name): Rubric
    {
        if (!isset($this->rubric)) {
            $url = 'https://nu.nl';
            $this->rubric = new Rubric('Nu.nl', $url, $this->createNewsItems($url));
        }

        return $this->rubric;
    }

    /**
     * @return NewsItem[]
     */
    private function createNewsItems(string $url): array
    {
        $newsItems = [];
        $dateTimeZone = new \DateTimeZone('UTC');

        for ($i = 0; $i < 10; $i++) {
            $newsItems[] = new NewsItem(
                sprintf('Item %u', $i),
                sprintf('%s/link/%u', $url, $i),
                sprintf('%s/m/%u.jpg', $url, $i),
                new \DateTime(sprintf('-%u days', $i), $dateTimeZone),
            );
        }

        return $newsItems;
    }
}

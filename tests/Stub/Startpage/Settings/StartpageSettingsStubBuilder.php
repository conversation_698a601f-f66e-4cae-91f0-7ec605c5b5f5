<?php

declare(strict_types=1);

namespace Tests\Stub\Startpage\Settings;

use App\Startpage\Settings\StartpageSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method StartpageSettings create()
 */
final class StartpageSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private bool $enabledForRequest = false;

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setEnabledForRequest(bool $enabledForRequest): self
    {
        $this->enabledForRequest = $enabledForRequest;

        return $this;
    }

    protected function createSettings(): StartpageSettings
    {
        return new StartpageSettings(
            enabled          : $this->enabled,
            enabledForRequest: $this->enabledForRequest,
        );
    }
}

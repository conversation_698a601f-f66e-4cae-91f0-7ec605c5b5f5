<?php

declare(strict_types=1);

namespace Tests\Stub\Tracking\Entry;

use App\Generic\Device\Device;
use App\SplitTest\Activate\ActiveSplitTest;
use App\Tracking\Entry\TrackingEntry;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\Network;
use App\Tracking\Model\TrafficSource;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;

final class TrackingEntryStubBuilder
{
    private bool $isEmpty;

    private ?string $query;

    private ?int $campaignId;

    private ?string $campaignName;

    private Device $device;

    private ?Network $network;

    private ?int $accountId;

    private ?int $adGroupId;

    private ?int $keywordId;

    private ?TrafficSource $trafficSource;

    private ?ClickId $clickId;

    private ?string $googleLocationId;

    private ?string $genericSecondaryClickId;

    private ?string $publisher;

    /** @var array<string> */
    private array $additionalChannels;

    private ?ActiveSplitTest $activeSplitTest;

    private \DateTime $createdAt;

    private ?string $originRoute;

    private ?int $styleId;

    private ?string $customId;

    private ?string $conversionRoute;

    /** @var callable|null */
    private $onCreateCallback;

    public function __construct()
    {
        $this->reset();
    }

    public function reset(): self
    {
        $this->isEmpty = TestStubRandomizer::pickBoolean();
        $this->query = TestStubRandomizer::pickItem([null, 'pizza', 'hawaii']);
        $this->campaignId = TestStubRandomizer::pickItem([null, random_int(12345, 98765)]);
        $this->campaignName = TestStubRandomizer::pickItem([null, 'campaign_pizza', 'campaign_good', 'campaign_bad']);
        $this->device = TestStubRandomizer::pickItem(Device::cases());
        $this->network = TestStubRandomizer::pickNullableItem(Network::cases());
        $this->clickId = new ClickId('test', ClickIdSource::GOOGLE_CLICK_ID);
        $this->googleLocationId = TestStubRandomizer::pickItem([null, '165', '123', '456']);
        $this->accountId = TestStubRandomizer::pickItem([null, random_int(12345, 98765)]);
        $this->adGroupId = TestStubRandomizer::pickItem([null, random_int(12345, 98765)]);
        $this->keywordId = TestStubRandomizer::pickItem([null, random_int(12345, 98765)]);
        $this->trafficSource = TestStubRandomizer::pickItem(array_merge([null], TrafficSource::cases()));
        $this->genericSecondaryClickId = TestStubRandomizer::pickItem([null, sprintf('sclid_%s', mt_rand())]);
        $this->publisher = TestStubRandomizer::pickItem([null, sprintf('publisher_%s', mt_rand())]);

        $this->additionalChannels = TestStubRandomizer::createCollection(
            static fn () => sprintf('kwc_%s', mt_rand()),
            random_int(0, 3),
        );

        // phpcs:disable Visymo.DateTime.DateTime.MissingTimezoneArgument
        $this->createdAt = new \DateTime(
            date('Y-m-d H:i:s', time() - mt_rand()),
            TimezoneEnum::UTC->toDateTimeZone(),
        );
        $this->activeSplitTest = TestStubRandomizer::pickItem(
            [
                null,
                new ActiveSplitTest(
                    random_int(1, 50),
                    TestStubRandomizer::pickItem([null, 'pizza']),
                ),
            ],
        );
        $this->originRoute = TestStubRandomizer::pickItem([null, 'my_route']);
        $this->styleId = TestStubRandomizer::pickItem([null, random_int(1, 50)]);
        $this->customId = TestStubRandomizer::pickItem([null, 'custom_id']);
        $this->conversionRoute = TestStubRandomizer::pickItem([null, 'conversion_route']);

        return $this;
    }

    public function clear(): self
    {
        $this->isEmpty = false;
        $this->query = null;
        $this->campaignId = null;
        $this->campaignName = null;
        $this->device = Device::DESKTOP;
        $this->network = null;
        $this->clickId = null;
        $this->googleLocationId = null;
        $this->accountId = null;
        $this->adGroupId = null;
        $this->keywordId = null;
        $this->trafficSource = null;
        $this->genericSecondaryClickId = null;
        $this->publisher = null;
        $this->additionalChannels = [];
        $this->createdAt = (new DateTimeFactoryStub())->create('now');
        $this->activeSplitTest = null;
        $this->originRoute = null;
        $this->styleId = null;
        $this->customId = null;
        $this->conversionRoute = null;

        return $this;
    }

    public function setIsEmpty(bool $isEmpty): self
    {
        $this->isEmpty = $isEmpty;

        return $this;
    }

    public function setQuery(?string $query): self
    {
        $this->query = $query;

        return $this;
    }

    public function setCampaignId(?int $campaignId): self
    {
        $this->campaignId = $campaignId;

        return $this;
    }

    public function setCampaignName(?string $campaignName): self
    {
        $this->campaignName = $campaignName;

        return $this;
    }

    public function setDevice(Device $device): self
    {
        $this->device = $device;

        return $this;
    }

    public function setNetwork(?Network $network): self
    {
        $this->network = $network;

        return $this;
    }

    public function setAccountId(?int $accountId): self
    {
        $this->accountId = $accountId;

        return $this;
    }

    public function setAdGroupId(?int $adGroupId): self
    {
        $this->adGroupId = $adGroupId;

        return $this;
    }

    public function setKeywordId(?int $keywordId): self
    {
        $this->keywordId = $keywordId;

        return $this;
    }

    public function setTrafficSource(?TrafficSource $trafficSource): self
    {
        $this->trafficSource = $trafficSource;

        return $this;
    }

    public function setClickId(?ClickId $clickId): self
    {
        $this->clickId = $clickId;

        return $this;
    }

    public function setGoogleLocationId(?string $googleLocationId): self
    {
        $this->googleLocationId = $googleLocationId;

        return $this;
    }

    public function setGenericSecondaryClickId(?string $genericSecondaryClickId): self
    {
        $this->genericSecondaryClickId = $genericSecondaryClickId;

        return $this;
    }

    public function setPublisher(?string $publisher): self
    {
        $this->publisher = $publisher;

        return $this;
    }

    /**
     * @param array<string> $additionalChannels
     */
    public function setAdditionalChannels(array $additionalChannels): self
    {
        $this->additionalChannels = $additionalChannels;

        return $this;
    }

    public function setActiveSplitTest(?ActiveSplitTest $activeSplitTest): self
    {
        $this->activeSplitTest = $activeSplitTest;

        return $this;
    }

    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function setOriginRoute(?string $originRoute): self
    {
        $this->originRoute = $originRoute;

        return $this;
    }

    public function setStyleId(?int $styleId): self
    {
        $this->styleId = $styleId;

        return $this;
    }

    public function clearClickIds(): self
    {
        $this->clickId = null;
        $this->genericSecondaryClickId = null;

        return $this;
    }

    public function setOnCreateCallback(?callable $onCreateCallback): self
    {
        $this->onCreateCallback = $onCreateCallback;

        return $this;
    }

    public function setCustomId(?string $customId): self
    {
        $this->customId = $customId;

        return $this;
    }

    public function setConversionRoute(?string $conversionRoute): self
    {
        $this->conversionRoute = $conversionRoute;

        return $this;
    }

    public function create(): TrackingEntry
    {
        $trackingEntry = new TrackingEntry(
            isEmpty                : $this->isEmpty,
            query                  : $this->query,
            campaignId             : $this->campaignId,
            campaignName           : $this->campaignName,
            device                 : $this->device,
            network                : $this->network,
            accountId              : $this->accountId,
            adGroupId              : $this->adGroupId,
            keywordId              : $this->keywordId,
            trafficSource          : $this->trafficSource,
            clickId                : $this->clickId,
            googleLocationId       : $this->googleLocationId,
            genericSecondaryClickId: $this->genericSecondaryClickId,
            publisher              : $this->publisher,
            additionalChannels     : $this->additionalChannels,
            activeSplitTest        : $this->activeSplitTest,
            createdAt              : $this->createdAt,
            originRoute            : $this->originRoute,
            styleId                : $this->styleId,
            customId               : $this->customId,
            conversionRoute        : $this->conversionRoute,
        );

        $onCreateCallback = $this->onCreateCallback;

        if (is_callable($onCreateCallback)) {
            $onCreateCallback($trackingEntry);
        }

        return $trackingEntry;
    }
}

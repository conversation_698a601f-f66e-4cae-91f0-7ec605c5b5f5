<?php

declare(strict_types=1);

namespace Tests\Stub\Tracking\Validator;

use App\Tracking\Validator\CampaignNameValidator;

class CampaignNameValidatorStub extends CampaignNameValidator
{
    private ?bool $fixedResult = null;

    public function setFixedResult(bool $fixedResult): self
    {
        $this->fixedResult = $fixedResult;

        return $this;
    }

    public function isValid(?string $campaignName, ?int $accountId): bool
    {
        return $this->fixedResult ?? parent::isValid($campaignName, $accountId);
    }
}

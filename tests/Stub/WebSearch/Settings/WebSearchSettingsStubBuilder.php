<?php

declare(strict_types=1);

namespace Tests\Stub\WebSearch\Settings;

use App\WebSearch\Settings\WebSearchSettings;
use Tests\Stub\ModuleSettings\AbstractModuleSettingsStubBuilder;

/**
 * @method WebSearchSettings create()
 */
final class WebSearchSettingsStubBuilder extends AbstractModuleSettingsStubBuilder
{
    private bool $enabled = false;

    private ?int $styleIdDesktop = null;

    private ?int $styleIdMobile = null;

    public function setEnabled(bool $enabled = true): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setStyleIdDesktop(int $styleIdDesktop): self
    {
        $this->styleIdDesktop = $styleIdDesktop;

        return $this;
    }

    public function setStyleIdMobile(int $styleIdMobile): self
    {
        $this->styleIdMobile = $styleIdMobile;

        return $this;
    }

    public function setStyleId(int $styleId): self
    {
        $this->styleIdDesktop = $styleId;
        $this->styleIdMobile = $styleId;

        return $this;
    }

    protected function createSettings(): WebSearchSettings
    {
        if ($this->enabled && ($this->styleIdDesktop === null || $this->styleIdMobile === null)) {
            throw new \InvalidArgumentException('Style ID must be set when module is enabled');
        }

        return new WebSearchSettings(
            enabled       : $this->enabled,
            styleIdDesktop: $this->styleIdDesktop,
            styleIdMobile : $this->styleIdMobile,
        );
    }
}

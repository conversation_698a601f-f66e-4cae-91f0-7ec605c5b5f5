<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\Configuration\BrandConfiguration;

use App\WebsiteSettings\Configuration\Brand\WebsiteBrandConfiguration;
use App\WebsiteSettings\Configuration\Brand\WebsiteBrandConfigurationHelper;

class WebsiteBrandConfigurationHelperStub extends WebsiteBrandConfigurationHelper
{
    public function setWebsiteBrandConfiguration(WebsiteBrandConfiguration $websiteBrandConfiguration): self
    {
        $this->websiteBrandConfiguration = $websiteBrandConfiguration;

        return $this;
    }
}

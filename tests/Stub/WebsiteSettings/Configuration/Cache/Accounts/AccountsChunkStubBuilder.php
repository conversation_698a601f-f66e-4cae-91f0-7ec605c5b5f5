<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\Configuration\Cache\Accounts;

use App\WebsiteSettings\Configuration\Cache\Accounts\AccountsChunk;

class AccountsChunkStubBuilder
{
    private int $accountIdFirst;

    private int $accountIdLast;

    private int $amountOfAccounts;

    private int $amountOfCampaigns;

    private string $filename;

    public function __construct()
    {
        $this->accountIdFirst = random_int(0, 99999);
        $this->accountIdLast = random_int(0, 99999);
        $this->amountOfAccounts = random_int(0, 100);
        $this->amountOfCampaigns = random_int(0, 500);
        $this->filename = uniqid('filename-', false);
    }

    public function withAccountIdFirst(int $accountIdFirst): self
    {
        $this->accountIdFirst = $accountIdFirst;

        return $this;
    }

    public function withAccountIdLast(int $accountIdLast): self
    {
        $this->accountIdLast = $accountIdLast;

        return $this;
    }

    public function build(): AccountsChunk
    {
        return new AccountsChunk(
            accountIdFirst   : $this->accountIdFirst,
            accountIdLast    : $this->accountIdLast,
            amountOfAccounts : $this->amountOfAccounts,
            amountOfCampaigns: $this->amountOfCampaigns,
            filename         : $this->filename,
        );
    }
}

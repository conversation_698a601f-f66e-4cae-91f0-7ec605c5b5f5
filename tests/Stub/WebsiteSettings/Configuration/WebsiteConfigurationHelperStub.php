<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\Configuration;

use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationHelper;
use Symfony\Contracts\Service\Attribute\Required;
use Tests\Stub\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReaderStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsHelperStub;

class WebsiteConfigurationHelperStub extends WebsiteConfigurationHelper
{
    private WebsiteSettingsHelperStub $websiteSettingsHelperStub;

    #[Required]
    public function setStubDependencies(WebsiteSettingsHelperStub $websiteSettingsHelperStub): void
    {
        $this->websiteSettingsHelperStub = $websiteSettingsHelperStub;
    }

    public function setWebsiteConfiguration(WebsiteConfiguration $websiteConfiguration): self
    {
        $this->websiteConfiguration = $websiteConfiguration;
        $this->updateDomainToBrandMapReader();

        $this->websiteSettingsHelperStub->resetWebsiteSettings();

        return $this;
    }

    private function updateDomainToBrandMapReader(): void
    {
        if (!$this->domainToBrandMapReader instanceof DomainToBrandMapReaderStub) {
            throw new \RuntimeException('DomainToBrandMapReader must be an instance of DomainToBrandMapReaderStub');
        }

        $brandSlug = $this->websiteConfiguration->getBrandSlug();
        $domainToBrandMap = [];

        foreach ($this->websiteConfiguration->getAvailableDomains() as $domain) {
            $domainToBrandMap[$domain] = $brandSlug;
        }

        $this->domainToBrandMapReader->setDomainToBrandMap($domainToBrandMap);
    }
}

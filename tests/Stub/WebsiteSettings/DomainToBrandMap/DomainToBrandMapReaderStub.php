<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\DomainToBrandMap;

use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapReader;

class DomainToBrandMapReaderStub extends DomainToBrandMapReader
{
    /**
     * @param array<string, string> $domainToBrandMap
     */
    public function setDomainToBrandMap(array $domainToBrandMap): self
    {
        $this->domainToBrandMap = $domainToBrandMap;

        return $this;
    }
}

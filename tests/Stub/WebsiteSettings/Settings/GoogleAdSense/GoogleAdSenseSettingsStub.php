<?php

declare(strict_types=1);

namespace Tests\Stub\WebsiteSettings\Settings\GoogleAdSense;

use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\GoogleAdSense\GoogleAdSenseSettings;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

class GoogleAdSenseSettingsStub extends GoogleAdSenseSettings
{
    public function __construct()
    {
        $enabled = TestStubRandomizer::pickBoolean();

        parent::__construct(
            enabled          : $enabled,
            dynamicAdsEnabled: $enabled && TestStubRandomizer::pickBoolean(),
            approval         : TestStubRandomizer::pickBoolean(),
            contractType     : ContractType::from(TestStubRandomizer::pickItem(['online', 'direct'])),
            defaultClient    : sprintf('random-default-client-%s', mt_rand()),
            semClient        : TestStubRandomizer::pickItem([null, sprintf('random-sem-client-%s', mt_rand())]),
            webClient        : TestStubRandomizer::pickItem([null, sprintf('random-web-client-%s', mt_rand())]),
            defaultChannel   : TestStubRandomizer::pickItem([null, sprintf('default_channel_%s', mt_rand())]),
        );
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        if (!$enabled) {
            $this->setDynamicAdsEnabled(false);
        }

        return $this;
    }

    public function setContractType(ContractType $contractType): self
    {
        $this->contractType = $contractType;

        return $this;
    }

    public function setDynamicAdsEnabled(bool $dynamicAdsEnabled): self
    {
        $this->dynamicAdsEnabled = $dynamicAdsEnabled;

        return $this;
    }

    public function setApproval(bool $approval): self
    {
        $this->approval = $approval;

        return $this;
    }

    public function setDefaultClient(string $defaultClient): self
    {
        $this->defaultClient = $defaultClient;

        return $this;
    }

    public function setSemClient(?string $semClient): self
    {
        $this->semClient = $semClient;

        return $this;
    }

    public function setWebClient(?string $webClient): self
    {
        $this->webClient = $webClient;

        return $this;
    }

    public function setDefaultChannel(?string $defaultChannel): self
    {
        $this->defaultChannel = $defaultChannel;

        return $this;
    }
}

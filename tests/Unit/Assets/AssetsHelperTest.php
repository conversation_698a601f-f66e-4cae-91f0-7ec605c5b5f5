<?php

declare(strict_types=1);

namespace Tests\Unit\Assets;

use App\Assets\AssetsHelper;
use App\Assets\DebugAssetsHelper;
use App\Assets\Directory\BrandBuildFolder;
use App\Assets\Directory\SharedBuildFolder;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Tests\Stub\BrandAssets\Settings\BrandAssetsSettingsStubBuilder;

class AssetsHelperTest extends TestCase
{
    private MockObject & LoggerInterface $loggerMock;

    private AssetsHelper $assetsHelper;

    protected function setUp(): void
    {
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $debugAssetsHelperMock = $this->createMock(DebugAssetsHelper::class);

        $brandAssetsSettingsStubBuilder = new BrandAssetsSettingsStubBuilder();
        $brandAssetsSettingsStubBuilder
            ->setBrandSlug('brand');

        $brandBuildFolder = new BrandBuildFolder(
            $brandAssetsSettingsStubBuilder->create(),
            __DIR__,
        );
        $sharedBuildFolder = new SharedBuildFolder(
            __DIR__,
        );
        $this->assetsHelper = new AssetsHelper(
            $this->loggerMock,
            $debugAssetsHelperMock,
            $brandBuildFolder,
            $sharedBuildFolder,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getComponentCssFileContentsDataProvider(): array
    {
        return [
            'pagination component'                     => [
                'componentStyleName' => 'pagination',
                'rightToLeft'        => false,
                'expectedToExists'   => true,
            ],
            'pagination component RTL does not exists' => [
                'componentStyleName' => 'pagination',
                'rightToLeft'        => true,
                'expectedToExists'   => false,
            ],
            'organic component does not exists'        => [
                'componentStyleName' => 'organic',
                'rightToLeft'        => false,
                'expectedToExists'   => false,
            ],
        ];
    }

    #[DataProvider('getComponentCssFileContentsDataProvider')]
    public function testGetComponentCssFileContents(
        string $componentStyleName,
        bool $rightToLeft,
        bool $expectedToExists
    ): void
    {
        $this->assertAssets(
            getFileContents : fn () => $this->assetsHelper->getComponentCssFileContents($componentStyleName, $rightToLeft),
            expectedToExists: $expectedToExists,
            expectedToLog   : !$expectedToExists,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getEntryCssFileContentsDataProvider(): array
    {
        return [
            'base entry exists'               => [
                'entryStyleName'   => 'Base',
                'rightToLeft'      => false,
                'expectedToExists' => true,
            ],
            'base entry RTL exists'           => [
                'entryStyleName'   => 'Base',
                'rightToLeft'      => true,
                'expectedToExists' => true,
            ],
            'world entry does not exists'     => [
                'entryStyleName'   => 'World',
                'rightToLeft'      => false,
                'expectedToExists' => false,
            ],
            'world entry RTL does not exists' => [
                'entryStyleName'   => 'World',
                'rightToLeft'      => true,
                'expectedToExists' => false,
            ],
            'hello entry does not exists'     => [
                'entryStyleName'   => 'Hello',
                'rightToLeft'      => false,
                'expectedToExists' => false,
            ],
            'hello entry RTL does not exists' => [
                'entryStyleName'   => 'Hello',
                'rightToLeft'      => true,
                'expectedToExists' => true,
            ],
        ];
    }

    #[DataProvider('getEntryCssFileContentsDataProvider')]
    public function testGetEntryCssFileContents(
        string $entryStyleName,
        bool $rightToLeft,
        bool $expectedToExists
    ): void
    {
        $this->assertAssets(
            getFileContents : fn () => $this->assetsHelper->getEntryCssFileContents($entryStyleName, $rightToLeft),
            expectedToExists: $expectedToExists,
            expectedToLog   : !$expectedToExists,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getBrandEntryCssFileContentsDataProvider(): array
    {
        return [
            'shared entry, not brand entry'    => [
                'entryStyleName'   => 'Base',
                'rightToLeft'      => false,
                'expectedToExists' => false,
            ],
            'rtl only does not exists for ltr' => [
                'entryStyleName'   => 'RightToLeftOnly',
                'rightToLeft'      => false,
                'expectedToExists' => false,
            ],
            'rtl only'                         => [
                'entryStyleName'   => 'RightToLeftOnly',
                'rightToLeft'      => true,
                'expectedToExists' => true,
            ],
        ];
    }

    #[DataProvider('getBrandEntryCssFileContentsDataProvider')]
    public function testGetBrandEntryCssFileContents(
        string $entryStyleName,
        bool $rightToLeft,
        bool $expectedToExists
    ): void
    {
        $this->assertAssets(
            getFileContents : fn () => $this->assetsHelper->getBrandEntryCssFileContents($entryStyleName, $rightToLeft),
            expectedToExists: $expectedToExists,
            expectedToLog   : false,
        );
    }

    private function assertAssets(callable $getFileContents, bool $expectedToExists, bool $expectedToLog): void
    {
        if ($expectedToLog) {
            $this->loggerMock->expects($this->once())->method('alert')
                ->with(
                    self::callback(
                        static fn (string $message): bool => str_contains($message, 'Could not find assets file'),
                    ),
                );
        } else {
            $this->loggerMock->expects($this->never())->method('alert');
        }

        $fileContents = $getFileContents();
        $actualToExists = $fileContents !== null;

        self::assertSame($expectedToExists, $actualToExists);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Component\Web\OrganicResults\StatisticsProvider;

use App\Component\Web\OrganicResults\StatisticsProvider\OrganicResultsStatisticsProvider;
use App\Component\Web\OrganicResults\StatisticsProvider\OrganicResultsStatisticsResolver;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tests\Unit\Statistics\Provider\AbstractStatisticsProviderTestCase;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

class OrganicResultsStatisticsProviderTest extends AbstractStatisticsProviderTestCase
{
    private OrganicResultsStatisticsResolver $organicResultsStatisticsResolver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organicResultsStatisticsResolver = new OrganicResultsStatisticsResolver(
            new SymfonyOptionsResolverBridge(new OptionsResolver()),
            $this->genericRequestMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function payloadDataProvider(): array
    {
        $pageviewId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'complete' => [
                'payload'            => [
                    'key'  => 'or',
                    'pvid' => $pageviewId,
                    'a'    => 10,
                ],
                'expectedStatistics' => [
                    'visit_id'        => self::$visitId,
                    'pageview_id'     => $pageviewId,
                    'organic_results' => [
                        'amount_shown' => 10,
                    ],
                ],
            ],
            'extra'    => [
                'payload'            => [
                    'extra' => true,
                    'key'   => 'or',
                    'pvid'  => $pageviewId,
                    'a'     => 5,
                ],
                'expectedStatistics' => [
                    'visit_id'        => self::$visitId,
                    'pageview_id'     => $pageviewId,
                    'organic_results' => [
                        'amount_shown' => 5,
                    ],
                ],
            ],
            'bad'      => [
                'payload'            => [
                    'key'  => 'or',
                    'pvid' => $pageviewId,
                    'bad'  => true,
                ],
                'expectedStatistics' => null,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $payload
     * @param mixed[]|null         $expectedStatistics
     */
    #[DataProvider('payloadDataProvider')]
    public function testGetFromPayload(array $payload, ?array $expectedStatistics): void
    {
        if ($expectedStatistics === null) {
            $this->expectException(InvalidOptionException::class);
        }

        $organicResultsStatisticsProvider = new OrganicResultsStatisticsProvider($this->organicResultsStatisticsResolver);
        self::assertSame('organic_results', $organicResultsStatisticsProvider::getContextKey());
        self::assertSame('or', $organicResultsStatisticsProvider::getPayloadKey());

        $actualStatistics = $organicResultsStatisticsProvider->getFromPayload($payload);

        self::assertArrayHasKey('response_timestamp', $actualStatistics['organic_results']);

        unset($actualStatistics['organic_results']['response_timestamp']);

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPage\EventSubscriber;

use App\ContentPage\EventSubscriber\ContentPageEventSubscriber;
use App\ContentPage\Request\ContentPageRequestInterface;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use PHPUnit\Framework\TestCase;

class ContentPageEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsPublicId(): void
    {
        $contentPageRequest = $this->createMock(ContentPageRequestInterface::class);
        $contentPageRequest->method('getPublicId')->willReturn(123);
        $contentPageRequest->method('getPreviousPublicId')->willReturn(null);

        $contentPage = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['setPublicId'])
            ->getMock();
        $contentPage->expects($this->once())
            ->method('setPublicId')
            ->with(123);

        $dataRequest = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['contentPage'])
            ->getMock();
        $dataRequest->method('contentPage')->willReturn($contentPage);

        $view = $this->getMockBuilder(\App\JsonTemplate\View\JsonTemplateView::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getDataRequest'])
            ->getMock();
        $view->method('getDataRequest')->willReturn($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPageEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);
    }
}


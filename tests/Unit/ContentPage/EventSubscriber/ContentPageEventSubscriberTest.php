<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPage\EventSubscriber;

use App\ContentPage\EventSubscriber\ContentPageEventSubscriber;
use App\ContentPage\Request\ContentPageRequestInterface;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use PHPUnit\Framework\TestCase;
use Tests\Stub\JsonTemplate\View\ViewStub;

class ContentPageEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsPublicId(): void
    {
        $contentPageRequest = $this->createMock(ContentPageRequestInterface::class);
        $contentPageRequest->method('getPublicId')->willReturn(123);
        $contentPageRequest->method('getPreviousPublicId')->willReturn(null);

        $view = new ViewStub();

        // Mock the contentPage method to return a mock that we can verify
        $contentPageDataRequest = $this->createMock(\stdClass::class);
        $contentPageDataRequest->expects($this->once())
            ->method('setPublicId')
            ->with(123);

        // Create a mock data request that returns our content page mock
        $dataRequest = $this->getMockBuilder(\stdClass::class)
            ->onlyMethods([])
            ->addMethods(['contentPage'])
            ->getMock();
        $dataRequest->method('contentPage')->willReturn($contentPageDataRequest);

        $view->setDataRequest($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPageEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);
    }
}


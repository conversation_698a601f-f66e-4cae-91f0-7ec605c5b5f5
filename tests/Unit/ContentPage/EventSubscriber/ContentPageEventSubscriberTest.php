<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPage\EventSubscriber;

use App\ContentPage\EventSubscriber\ContentPageEventSubscriber;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use PHPUnit\Framework\TestCase;
use Tests\Stub\ContentPage\Request\ContentPageRequestStub;

class ContentPageEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsPublicId(): void
    {
        $contentPageRequest = new ContentPageRequestStub();
        $contentPageRequest->setPublicId(123);
        $contentPageRequest->setPreviousPublicId(null);

        // Create a real ViewDataRequest instance
        $dataRequest = new ViewDataRequest();

        // Create a mock view that returns our real data request
        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPageEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);

        // Verify that the public ID was set on the content page data request
        self::assertSame(123, $dataRequest->contentPage()->getPublicId());
    }
}
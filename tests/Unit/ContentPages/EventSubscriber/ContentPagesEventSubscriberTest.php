<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPages\EventSubscriber;

use App\ContentPage\Request\ContentPageRequestInterface;
use App\ContentPages\EventSubscriber\ContentPagesEventSubscriber;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use PHPUnit\Framework\TestCase;

class ContentPagesEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedAddsExcludedPublicId(): void
    {
        $contentPageRequest = $this->createMock(ContentPageRequestInterface::class);
        $contentPageRequest->method('getPublicId')->willReturn(123);
        $contentPageRequest->method('getPreviousPublicId')->willReturn(null);

        $contentPages = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['addExcludedPublicIds'])
            ->getMock();
        $contentPages->expects($this->once())
            ->method('addExcludedPublicIds')
            ->with([123]);

        $dataRequest = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['contentPages'])
            ->getMock();
        $dataRequest->method('contentPages')->willReturn($contentPages);

        $view = $this->getMockBuilder(\App\JsonTemplate\View\JsonTemplateView::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['getDataRequest'])
            ->getMock();
        $view->method('getDataRequest')->willReturn($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPagesEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);
    }
}


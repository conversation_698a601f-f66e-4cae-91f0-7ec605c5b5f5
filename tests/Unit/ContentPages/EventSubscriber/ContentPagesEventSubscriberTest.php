<?php

declare(strict_types=1);

namespace Tests\Unit\ContentPages\EventSubscriber;

use App\ContentPages\EventSubscriber\ContentPagesEventSubscriber;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use PHPUnit\Framework\TestCase;
use Tests\Stub\ContentPage\Request\ContentPageRequestStub;

class ContentPagesEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedAddsExcludedPublicId(): void
    {
        $contentPageRequest = new ContentPageRequestStub();
        $contentPageRequest->setPublicId(123);
        $contentPageRequest->setPreviousPublicId(null);

        // Create a real ViewDataRequest instance
        $dataRequest = new ViewDataRequest();

        // Create a mock view that returns our real data request
        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new ContentPagesEventSubscriber($contentPageRequest);
        $subscriber->onJsonTemplateViewCreated($event);

        // Verify that the excluded public IDs were added
        self::assertSame([123], $dataRequest->contentPages()->getExcludedPublicIds());
    }
}

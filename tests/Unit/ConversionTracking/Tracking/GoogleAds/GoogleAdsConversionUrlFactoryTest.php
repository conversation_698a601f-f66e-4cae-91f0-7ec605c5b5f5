<?php /** @noinspection LongLine */

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\Tracking\GoogleAds;

use App\ConversionTracking\Tracking\GoogleAds\GoogleAdsConversionUrlFactory;
use App\ConversionTracking\Tracking\GoogleAds\GoogleAdsConversionUrlHelper;
use App\Tracking\Model\ClickId\Factory\ClickIdFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

/**
 * @phpcs:disable Generic.Files.LineLength.MaxExceeded
 */
class GoogleAdsConversionUrlFactoryTest extends PhpUnitTestCase
{
    private GoogleAdsConversionUrlFactory $googleAdsConversionUrlFactory;

    private ClickIdFactory $clickIdFactory;

    protected function setUp(): void
    {
        $this->googleAdsConversionUrlFactory = new GoogleAdsConversionUrlFactory(
            new GoogleAdsConversionUrlHelper(),
        );

        $this->clickIdFactory = new ClickIdFactory();
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return [
            'default google click source' => [
                'conversionTrackingId'    => 1234,
                'conversionTrackingLabel' => 'tracking-label',
                'orderId'                 => '232323',
                'combinedClickIdSource'   => 'gclid',
                'combinedClickId'         => 'combined-click-id',
                'expectedUrl'             => 'https://www.googleadservices.com/pagead/conversion/1234/?guid=ON&script=0&label=tracking-label&oid=232323&gclaw=combined-click-id',
            ],
            'without any optional fields' => [
                'conversionTrackingId'    => 0,
                'conversionTrackingLabel' => null,
                'orderId'                 => null,
                'combinedClickIdSource'   => null,
                'combinedClickId'         => null,
                'expectedUrl'             => 'https://www.googleadservices.com/pagead/conversion/0/?guid=ON&script=0',
            ],
            'google app click source'     => [
                'conversionTrackingId'    => 1234,
                'conversionTrackingLabel' => 'tracking-label',
                'orderId'                 => '232323',
                'combinedClickIdSource'   => 'gbraid',
                'combinedClickId'         => 'combined-click-id',
                'expectedUrl'             => 'https://www.googleadservices.com/pagead/conversion/1234/?guid=ON&script=0&label=tracking-label&oid=232323&gclgb=combined-click-id',
            ],
            'google web click source'     => [
                'conversionTrackingId'    => 1234,
                'conversionTrackingLabel' => 'tracking-label',
                'orderId'                 => '232323',
                'combinedClickIdSource'   => 'wbraid',
                'combinedClickId'         => 'combined-click-id',
                'expectedUrl'             => 'https://www.googleadservices.com/pagead/conversion/1234/?guid=ON&script=0&label=tracking-label&oid=232323&gclgb=combined-click-id',
            ],
            'microsoft click source'      => [
                'conversionTrackingId'    => 1111,
                'conversionTrackingLabel' => 'mslabel',
                'orderId'                 => '444',
                'combinedClickIdSource'   => 'msclkid',
                'combinedClickId'         => 'ms-click-123',
                'expectedUrl'             => 'https://www.googleadservices.com/pagead/conversion/1111/?guid=ON&script=0&label=mslabel&oid=444',
            ],
        ];
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(
        int $conversionTrackingId,
        ?string $conversionTrackingLabel,
        ?string $orderId,
        ?string $combinedClickIdSource,
        ?string $combinedClickId,
        string $expectedUrl
    ): void
    {
        $clickId = $this->clickIdFactory->create((string)$combinedClickId, (string)$combinedClickIdSource);

        $actualUrl = $this->googleAdsConversionUrlFactory->create(
            conversionTrackingId   : $conversionTrackingId,
            conversionTrackingLabel: $conversionTrackingLabel,
            orderId                : $orderId,
            clickId                : $clickId,
        );

        self::assertSame(
            $expectedUrl,
            $actualUrl,
        );
    }
}

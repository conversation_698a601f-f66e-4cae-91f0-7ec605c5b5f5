<?php

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\Tracking\HttpClient;

use App\ConversionTracking\Settings\ConversionTrackingSettings;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingHttpClient;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingHttpClientException;
use GuzzleHttp\ClientInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

class ConversionTrackingHttpClientTest extends PhpUnitTestCase
{
    private ClientInterface & MockObject $guzzleClientMock;

    private RequestInfoStub $requestInfoStub;

    private LoggerInterface & MockObject $loggerMock;

    protected function setUp(): void
    {
        $this->guzzleClientMock = $this->createMock(ClientInterface::class);
        $this->requestInfoStub = new RequestInfoStub();
        $this->loggerMock = $this->createMock(LoggerInterface::class);
    }

    /**
     * @return mixed[]
     */
    public static function sendRequestDataProvider(): array
    {
        return [
            'successful tracking request'        => [
                'conversionUrl'      => 'https://unit-test.visymo.com/serp',
                'responseStatusCode' => 200,
                'expectedResult'     => true,
                'expectedErrorLog'   => false,
            ],
            'tracking request with response 404' => [
                'conversionUrl'      => 'https://unit-test.visymo.com/derp',
                'responseStatusCode' => 404,
                'expectedResult'     => false,
                'expectedErrorLog'   => true,
            ],
            'tracking request with response 500' => [
                'conversionUrl'      => 'https://unit-test.visymo.com/500',
                'responseStatusCode' => 500,
                'expectedResult'     => false,
                'expectedErrorLog'   => true,
            ],
        ];
    }

    #[DataProvider('sendRequestDataProvider')]
    public function testSendRequest(
        string $conversionUrl,
        int $responseStatusCode,
        bool $expectedResult,
        bool $expectedErrorLog
    ): void
    {
        $userIp = (string)random_int(0, 255);
        $userAgent = uniqid('user-agent-', true);
        $acceptLanguage = uniqid('accept-language-', true);

        $this->requestInfoStub->setUserIp($userIp);
        $this->requestInfoStub->setUserAgent($userAgent);
        $this->requestInfoStub->setAcceptLanguage($acceptLanguage);

        /** @var ResponseInterface & MockObject $responseMock */
        $responseMock = $this->createConfiguredMock(
            ResponseInterface::class,
            [
                'getStatusCode' => $responseStatusCode,
            ],
        );

        $this->guzzleClientMock
            ->expects(self::once())
            ->method('request')
            ->with(
                'GET',
                $conversionUrl,
                [
                    'allow_redirects' => true,
                    'headers'         => [
                        'X-Forwarded-For' => $userIp,
                        'User-Agent'      => $userAgent,
                        'Accept-Language' => $acceptLanguage,
                    ],
                ],
            )
            ->willReturn($responseMock);

        $this->loggerMock
            ->expects($expectedErrorLog ? self::once() : self::never())
            ->method('notice');

        $conversionTrackingHttpClient = new ConversionTrackingHttpClient(
            new ConversionTrackingSettings(false),
            $this->guzzleClientMock,
            $this->requestInfoStub,
            $this->loggerMock,
        );

        $actualResult = $conversionTrackingHttpClient->sendRequest(
            $conversionUrl,
        );

        self::assertSame(
            $expectedResult,
            $actualResult,
        );
    }

    public function testHttpException(): void
    {
        $this->guzzleClientMock
            ->method('request')
            ->willThrowException(new \RuntimeException('test exception'));

        $this->loggerMock
            ->expects(self::once())
            ->method('notice')
            ->willReturnCallback(
                static function (string $message, array $context): void {
                    PhpUnitTestCase::assertSame(
                        ConversionTrackingHttpClientException::class,
                        $context['exception']::class,
                    );
                },
            );

        $conversionTrackingHttpClient = new ConversionTrackingHttpClient(
            new ConversionTrackingSettings(false),
            $this->guzzleClientMock,
            $this->requestInfoStub,
            $this->loggerMock,
        );

        $conversionTrackingHttpClient->sendRequest(
            'https://unit-test.visymo.com',
        );
    }

    public function testDebugMode(): void
    {
        $this->guzzleClientMock
            ->expects(self::never())
            ->method('request');

        $this->loggerMock
            ->expects(self::once())
            ->method('warning');

        $conversionTrackingHttpClient = new ConversionTrackingHttpClient(
            new ConversionTrackingSettings(true),
            $this->guzzleClientMock,
            $this->requestInfoStub,
            $this->loggerMock,
        );

        $conversionTrackingHttpClient->sendRequest(
            'https://unit-test.visymo.com',
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\Tracking\HttpClient;

use App\ConversionTracking\Endpoint\EndpointResponse;
use App\ConversionTracking\Endpoint\EndpointResponseInterface;
use App\ConversionTracking\Settings\ConversionTrackingSettings;
use App\ConversionTracking\Tracking\HttpClient\ConversionTrackingResponseFactory;
use App\Generic\Response\UncachedPixelResponse;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

class ConversionTrackingResponseFactoryTest extends PhpUnitTestCase
{
    private LoggerInterface & MockObject $loggerMock;

    protected function setUp(): void
    {
        $this->loggerMock = $this->createMock(LoggerInterface::class);
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return [
            'with tracking url'    => [
                'endpointResponse' => new EndpointResponse('https://unit-test.visymo.com/serp'),
                'expectedResponse' => new RedirectResponse('https://unit-test.visymo.com/serp'),
            ],
            'without tracking url' => [
                'endpointResponse' => new EndpointResponse(),
                'expectedResponse' => new UncachedPixelResponse(),
            ],
        ];
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(
        EndpointResponseInterface $endpointResponse,
        Response $expectedResponse
    ): void
    {
        $conversionTrackingResponseFactory = new ConversionTrackingResponseFactory(
            new ConversionTrackingSettings(false),
            $this->loggerMock,
        );

        $actualResponse = $conversionTrackingResponseFactory->create(
            $endpointResponse,
        );

        // date value can not be verified for Symfony responses
        if ($expectedResponse->getDate() !== null) {
            self::assertNotNull($actualResponse->getDate());

            $actualResponse->setDate(
                $expectedResponse->getDate(),
            );
        }

        self::assertEquals(
            $expectedResponse,
            $actualResponse,
        );
    }

    public function testDebugMode(): void
    {
        $this->loggerMock
            ->expects(self::once())
            ->method('warning');

        $conversionTrackingResponseFactory = new ConversionTrackingResponseFactory(
            new ConversionTrackingSettings(true),
            $this->loggerMock,
        );

        $actualResponse = $conversionTrackingResponseFactory->create(
            new EndpointResponse(
                'https://unit-test.visymo.com',
            ),
        );

        self::assertEquals(
            new UncachedPixelResponse(),
            $actualResponse,
        );
    }
}

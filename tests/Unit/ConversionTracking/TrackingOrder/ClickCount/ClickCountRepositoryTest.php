<?php

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\TrackingOrder\ClickCount;

use App\ConversionTracking\TrackingOrder\ClickCount\ClickCountKeyFactory;
use App\ConversionTracking\TrackingOrder\ClickCount\ClickCountRepository;
use App\ConversionTracking\TrackingOrder\Type\OnePerAdV2TrackingOrder;
use App\Redis\RedisClient;
use Monolog\Test\TestCase;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;

class ClickCountRepositoryTest extends TestCase
{
    private RedisClient & MockObject $redisClientMock;

    private ClickCountKeyFactory & MockObject $clickCountKeyFactoryMock;

    private LoggerInterface & MockObject $loggerMock;

    private ClickCountRepository $clickCountRepository;

    protected function setUp(): void
    {
        $this->redisClientMock = $this->getMockBuilder(RedisClient::class)
            ->disableOriginalConstructor()
            ->disableOriginalClone()
            ->getMock();

        $this->clickCountKeyFactoryMock = $this->createMock(ClickCountKeyFactory::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->clickCountRepository = new ClickCountRepository(
            $this->redisClientMock,
            $this->clickCountKeyFactoryMock,
            $this->loggerMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getClickCountDataProvider(): array
    {
        return [
            'with click count 1'      => [
                'hasClickCountKey'    => true,
                'clickCountValue'     => 1,
                'expectedReturnValue' => 1,
            ],
            'with click count 2'      => [
                'hasClickCountKey'    => true,
                'clickCountValue'     => 2,
                'expectedReturnValue' => 2,
            ],
            'without click count key' => [
                'hasClickCountKey'    => false,
                'clickCountValue'     => 1,
                'expectedReturnValue' => 0,
            ],
            'without click count'     => [
                'hasClickCountKey'    => true,
                'clickCountValue'     => false,
                'expectedReturnValue' => 0,
            ],
        ];
    }

    #[DataProvider('getClickCountDataProvider')]
    public function testGetClickCount(
        bool $hasClickCountKey,
        int|bool $clickCountValue,
        int $expectedReturnValue
    ): void
    {
        $trackingOrder = new OnePerAdV2TrackingOrder('testid');
        $clickCountKey = $hasClickCountKey ? uniqid('click_count_key', true) : null;

        $this->clickCountKeyFactoryMock
            ->method('create')
            ->willReturn($clickCountKey);

        $this->redisClientMock
            ->expects($hasClickCountKey ? self::once() : self::never())
            ->method('__call')
            ->with('get', [$clickCountKey])
            ->willReturn($clickCountValue);

        self::assertSame(
            $expectedReturnValue,
            $this->clickCountRepository->getClickCount($trackingOrder),
        );
    }

    public function testGetClickCountWithRedisException(): void
    {
        $trackingOrder = new OnePerAdV2TrackingOrder('testid');
        $clickCountKey = uniqid('click_count_key', true);

        $this->clickCountKeyFactoryMock
            ->method('create')
            ->willReturn($clickCountKey);

        $this->redisClientMock
            ->method('__call')
            ->willThrowException(new \RuntimeException('This exception should be caught'));

        $this->loggerMock->expects(self::once())->method('error');

        $this->clickCountRepository->getClickCount($trackingOrder);
    }

    /**
     * @return mixed[]
     */
    public static function increaseClickCountDataProvider(): array
    {
        return [
            'with click count 1'      => [
                'hasClickCountKey'    => true,
                'clickCountValue'     => 1,
                'expectedReturnValue' => 1,
            ],
            'with click count 2'      => [
                'hasClickCountKey'    => true,
                'clickCountValue'     => 2,
                'expectedReturnValue' => 2,
            ],
            'without click count key' => [
                'hasClickCountKey'    => false,
                'clickCountValue'     => null,
                'expectedReturnValue' => 1,
            ],
            'without click count'     => [
                'hasClickCountKey'    => true,
                'clickCountValue'     => 1,
                'expectedReturnValue' => 1,
            ],
        ];
    }

    #[DataProvider('increaseClickCountDataProvider')]
    public function testIncreaseClickCount(
        bool $hasClickCountKey,
        ?int $clickCountValue,
        int $expectedReturnValue
    ): void
    {
        $trackingOrder = new OnePerAdV2TrackingOrder('testid');
        $clickCountKey = $hasClickCountKey ? uniqid('click_count_key', true) : null;

        $this->clickCountKeyFactoryMock
            ->method('create')
            ->willReturn($clickCountKey);

        $this->redisClientMock
            ->expects($hasClickCountKey ? self::exactly(2) : self::never())
            ->method('__call')
            ->with(
                self::callback(
                    static fn (string $name): bool => in_array($name, ['incr', 'expire'], true),
                ),
            )
            ->willReturn((int)$clickCountValue);

        self::assertSame(
            $expectedReturnValue,
            $this->clickCountRepository->increaseClickCount($trackingOrder),
        );
    }

    public function testIncreaseClickCountWithRedisException(): void
    {
        $trackingOrder = new OnePerAdV2TrackingOrder('testid');
        $clickCountKey = uniqid('click_count_key', true);

        $this->clickCountKeyFactoryMock
            ->method('create')
            ->willReturn($clickCountKey);

        $this->redisClientMock
            ->method('__call')
            ->willThrowException(new \RuntimeException('This exception should be caught'));

        $this->redisClientMock
            ->expects(self::once())
            ->method('__call');

        $this->loggerMock->expects(self::once())->method('error');

        $this->clickCountRepository->increaseClickCount($trackingOrder);
    }
}

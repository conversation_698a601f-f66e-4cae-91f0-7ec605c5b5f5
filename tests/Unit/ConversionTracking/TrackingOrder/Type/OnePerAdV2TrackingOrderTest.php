<?php

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\TrackingOrder\Type\OnePerAdV2TrackingOrder;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class OnePerAdV2TrackingOrderTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return [
            'click count 1' => [
                'clickCount'                       => 1,
                'expectedSupportsOnlineConversion' => true,
            ],
            'click count 2' => [
                'clickCount'                       => 2,
                'expectedSupportsOnlineConversion' => true,
            ],
            'click count 3' => [
                'clickCount'                       => 3,
                'expectedSupportsOnlineConversion' => false,
            ],
        ];
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(
        int $clickCount,
        bool $expectedSupportsOnlineConversion
    ): void
    {
        $trackingOrder = new OnePerAdV2TrackingOrder(
            uniqid('id', true),
        );

        self::assertSame(
            $expectedSupportsOnlineConversion,
            $trackingOrder->supportsOnlineConversion($clickCount),
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\TrackingOrder\TrackingOrderInterface;
use App\ConversionTracking\TrackingOrder\Type\OnePerPageviewIdTrackingOrder;
use App\ConversionTracking\TrackingOrder\Type\OnePerPageviewIdTrackingOrderFactory;
use App\Generic\Generator\Sha256HashGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\ConversionTracking\Request\ConversionTrackingRequestStub;

class OnePerPageviewIdTrackingOrderFactoryTest extends TestCase
{
    private ConversionTrackingRequestStub $conversionTrackingRequestStub;

    private OnePerPageviewIdTrackingOrderFactory $onePerPageviewIdTrackingOrderFactory;

    protected function setUp(): void
    {
        $this->conversionTrackingRequestStub = new ConversionTrackingRequestStub();
        $this->onePerPageviewIdTrackingOrderFactory = new OnePerPageviewIdTrackingOrderFactory(
            $this->conversionTrackingRequestStub,
            new Sha256HashGenerator(),
        );
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return [
            'normal ad click'              => [
                'originalPageviewId'    => '17cd4207-c516-45ef-b223-264e7b768fef',
                'expectedTrackingOrder' => new OnePerPageviewIdTrackingOrder(
                    '0b15699402a9c0449e565d310b61d3eed66e19b1914ed9ed7adb959d8e1555e2',
                ),
            ],
            'another ad click'             => [
                'originalPageviewId'    => '17EL1337-c516-45ef-b223-264e7b768fef',
                'expectedTrackingOrder' => new OnePerPageviewIdTrackingOrder(
                    '3104e0d138d6690be6f385d66c3b36a000af6cec55f6e82316e8378cd3f05210',
                ),
            ],
            'missing original_pageview_id' => [
                'originalPageviewId'    => null,
                'expectedTrackingOrder' => null,
            ],
        ];
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(
        ?string $originalPageviewId,
        ?TrackingOrderInterface $expectedTrackingOrder
    ): void
    {
        $this->conversionTrackingRequestStub->setOriginalPageviewId($originalPageviewId);

        // Assert create()
        $actualTrackingOrder = $this->onePerPageviewIdTrackingOrderFactory->create();

        if ($expectedTrackingOrder === null) {
            // When empty
            self::assertNull($actualTrackingOrder);
        } else {
            // When not empty
            self::assertNotNull($actualTrackingOrder);
            self::assertSame($expectedTrackingOrder->getId(), $actualTrackingOrder->getId());
            self::assertSame($expectedTrackingOrder->getType(), $actualTrackingOrder->getType());
        }
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\ConversionTracking\TrackingOrder\Type;

use App\ConversionTracking\TrackingOrder\Type\RandomTrackingOrderFactory;
use PHPUnit\Framework\TestCase;

class RandomTrackingOrderFactoryTest extends TestCase
{
    public function testCreate(): void
    {
        $factory = new RandomTrackingOrderFactory();
        $trackingOrder = $factory->create();
        $trackingOrderId = $trackingOrder->getId();

        self::assertTrue(uuid_is_valid($trackingOrderId));
        self::assertSame('random', $trackingOrder->getType());

        // Additional check because used uuid_create() and uuid_is_valid() are both Symfony polyfills
        self::assertMatchesRegularExpression(
            '/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i',
            $trackingOrderId,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\CookieConsent\EventSubscriber;

use App\CookieConsent\EventSubscriber\CookieConsentStatisticsLogEventSubscriber;
use App\CookieConsent\Helper\CookieConsentHelper;
use App\OneTrust\Helper\OneTrustHelper;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class CookieConsentStatisticsLogEventSubscriberTest extends TestCase
{
    private CookieConsentHelper & MockObject $cookieConsentHelperMock;

    private OneTrustHelper & MockObject $oneTrustHelperMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cookieConsentHelperMock = $this->createMock(CookieConsentHelper::class);
        $this->oneTrustHelperMock = $this->createMock(OneTrustHelper::class);
    }

    /**
     * @return mixed[]
     */
    public static function statisticsLogCreateDataProvider(): array
    {
        return [
            'no consent'                 => [
                'hasConsent'         => false,
                'oneTrustEnabled'    => false,
                'expectedStatistics' => [],
            ],
            'consent'                    => [
                'hasConsent'         => true,
                'oneTrustEnabled'    => false,
                'expectedStatistics' => [
                    'cookie_consent' => [
                        'consent' => true,
                    ],
                ],
            ],
            'consent & onetrust enabled' => [
                'hasConsent'         => true,
                'oneTrustEnabled'    => true,
                'expectedStatistics' => [],
            ],
        ];
    }

    /**
     * @param mixed[] $expectedStatistics
     */
    #[DataProvider('statisticsLogCreateDataProvider')]
    public function testOnStatisticsLogCreate(bool $hasConsent, bool $oneTrustEnabled, array $expectedStatistics): void
    {
        $this->cookieConsentHelperMock->expects($this->once())->method('consent')->willReturn($hasConsent);

        if ($hasConsent) {
            $this->oneTrustHelperMock->expects($this->once())->method('isEnabled')->willReturn($oneTrustEnabled);
        }

        $event = new StatisticsLogCreateEvent();
        $cookieConsentStatisticsLogEventSubscriber = new CookieConsentStatisticsLogEventSubscriber(
            cookieConsentHelper: $this->cookieConsentHelperMock,
            oneTrustHelper     : $this->oneTrustHelperMock,
        );
        $cookieConsentStatisticsLogEventSubscriber->onStatisticsLogCreate($event);

        $actualStatistics = $event->getStatistics();

        if ($expectedStatistics !== []) {
            self::assertNotNull($actualStatistics['cookie_consent']['request_timestamp']);
            unset($actualStatistics['cookie_consent']['request_timestamp']);
        }

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\CookieConsent\StatisticsProvider;

use App\CookieConsent\StatisticsProvider\CookieConsentStatisticsProvider;
use App\CookieConsent\StatisticsProvider\CookieConsentStatisticsResolver;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tests\Unit\Statistics\Provider\AbstractStatisticsProviderTestCase;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

class CookieConsentStatisticsProviderTest extends AbstractStatisticsProviderTestCase
{
    private CookieConsentStatisticsResolver $cookieConsentStatisticsResolver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cookieConsentStatisticsResolver = new CookieConsentStatisticsResolver(
            new SymfonyOptionsResolverBridge(new OptionsResolver()),
            $this->genericRequestMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function payloadDataProvider(): array
    {
        $pageviewId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'complete' => [
                'payload'            => [
                    'key'  => 'cc',
                    'pvid' => $pageviewId,
                    'c'    => true,
                ],
                'expectedStatistics' => [
                    'visit_id'       => self::$visitId,
                    'pageview_id'    => $pageviewId,
                    'cookie_consent' => [
                        'consent' => true,
                    ],
                ],
            ],
            'extra'    => [
                'payload'            => [
                    'extra'    => true,
                    'key'      => 'cc',
                    'visit_id' => self::$visitId,
                    'pvid'     => $pageviewId,
                    'c'        => true,
                ],
                'expectedStatistics' => [
                    'visit_id'       => self::$visitId,
                    'pageview_id'    => $pageviewId,
                    'cookie_consent' => [
                        'consent' => true,
                    ],
                ],
            ],
            'bad'      => [
                'payload'            => [
                    'key'      => 'cc',
                    'visit_id' => self::$visitId,
                    'pvid'     => $pageviewId,
                    'bad'      => true,
                ],
                'expectedStatistics' => null,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $payload
     * @param mixed[]|null         $expectedStatistics
     */
    #[DataProvider('payloadDataProvider')]
    public function testGetFromPayload(array $payload, ?array $expectedStatistics): void
    {
        if ($expectedStatistics === null) {
            $this->expectException(InvalidOptionException::class);
        }

        $cookieConsentStatisticsProvider = new CookieConsentStatisticsProvider($this->cookieConsentStatisticsResolver);
        self::assertSame('cookie_consent', $cookieConsentStatisticsProvider::getContextKey());
        self::assertSame('cc', $cookieConsentStatisticsProvider::getPayloadKey());

        $actualStatistics = $cookieConsentStatisticsProvider->getFromPayload($payload);

        self::assertArrayHasKey('response_timestamp', $actualStatistics['cookie_consent']);

        unset($actualStatistics['cookie_consent']['response_timestamp']);

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

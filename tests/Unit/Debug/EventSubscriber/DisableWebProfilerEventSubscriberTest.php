<?php

declare(strict_types=1);

namespace Tests\Unit\Debug\EventSubscriber;

use App\Debug\EventSubscriber\DisableWebProfilerEventSubscriber;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpKernel\Profiler\Profiler;
use Tests\Stub\Debug\Request\DebugRequestStub;

class DisableWebProfilerEventSubscriberTest extends TestCase
{
    private DebugRequestStub $debugRequestStub;

    protected function setUp(): void
    {
        $this->debugRequestStub = new DebugRequestStub();
    }

    public function testDisableWebProfilerWithoutProfiler(): void
    {
        $this->expectNotToPerformAssertions();
        $disableWebProfilerEventSubscriber = new DisableWebProfilerEventSubscriber(
            debugRequest: $this->debugRequestStub,
            profiler    : null,
        );

        $disableWebProfilerEventSubscriber->onKernelRequest();
    }

    public function testDisableWebProfilerWithDebugDisableProfiler(): void
    {
        $this->debugRequestStub->setDisableProfiler();
        $profilerMock = $this->createMock(Profiler::class);
        $profilerMock->expects($this->once())
            ->method('disable');

        $disableWebProfilerEventSubscriber = new DisableWebProfilerEventSubscriber(
            debugRequest: $this->debugRequestStub,
            profiler    : $profilerMock,
        );

        $disableWebProfilerEventSubscriber->onKernelRequest();
    }

    public function testDisableWebProfilerWithoutDebugDisableProfiler(): void
    {
        $this->debugRequestStub->setDisableProfiler(false);
        $profilerMock = $this->createMock(Profiler::class);
        $profilerMock->expects($this->never())
            ->method('disable');

        $disableWebProfilerEventSubscriber = new DisableWebProfilerEventSubscriber(
            debugRequest: $this->debugRequestStub,
            profiler    : $profilerMock,
        );

        $disableWebProfilerEventSubscriber->onKernelRequest();
    }
}

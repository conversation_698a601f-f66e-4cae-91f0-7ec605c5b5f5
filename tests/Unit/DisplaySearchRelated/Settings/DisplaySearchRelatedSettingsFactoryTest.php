<?php

declare(strict_types=1);

namespace Tests\Unit\DisplaySearchRelated\Settings;

use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettingsFactory;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class DisplaySearchRelatedSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private DisplaySearchRelatedSettingsFactory $displaySearchRelatedSettingsFactory;

    private DebugRequestStub $debugRequestStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->debugRequestStub = new DebugRequestStub();

        $this->displaySearchRelatedSettingsFactory = new DisplaySearchRelatedSettingsFactory(
            $this->websiteConfigurationHelperMock,
            $this->debugRequestStub,
        );
    }

    public function testDisabled(): void
    {
        $this->setBrandConfig(
            [
                'display_search_related' => [
                    'enabled' => false,
                ],
            ],
        );
        $this->debugRequestStub->setEnableModule(false);

        $displaySearchRelatedSettings = $this->displaySearchRelatedSettingsFactory->create();

        self::assertFalse($displaySearchRelatedSettings->enabled);
    }

    public function testEnabled(): void
    {
        $styleIdsConfig = [
            'style_id_desktop'     => 1000000001,
            'style_id_mobile'      => 1000000002,
            'web_style_id_desktop' => 1000000004,
            'web_style_id_mobile'  => 1000000005,
        ];

        $this->setBrandConfig(
            [
                'display_search_related' => [
                    'enabled'                  => true,
                    'related_fallback_enabled' => true,
                    ...$styleIdsConfig,
                ],
            ],
        );
        $this->debugRequestStub->setEnableModule(false);

        $displaySearchRelatedSettings = $this->displaySearchRelatedSettingsFactory->create();

        self::assertTrue($displaySearchRelatedSettings->enabled);
        self::assertTrue($displaySearchRelatedSettings->relatedFallbackEnabled);
        self::assertSame(
            array_values($styleIdsConfig),
            [
                $displaySearchRelatedSettings->styleIdDesktop,
                $displaySearchRelatedSettings->styleIdMobile,
                $displaySearchRelatedSettings->webStyleIdDesktop,
                $displaySearchRelatedSettings->webStyleIdMobile,
            ],
        );
    }
}

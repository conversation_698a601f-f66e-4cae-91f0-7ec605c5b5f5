<?php

declare(strict_types=1);

namespace Tests\Unit\Generic\Url;

use App\Generic\Url\VisitIdParametersProvider;
use App\Http\Request\GenericRequestInterface;
use App\Http\Url\PersistentUrlParametersPageType;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class VisitIdParametersProviderTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        $randomVisitId = (string)mt_rand();

        return [
            'empty'     => [
                'visitId'               => null,
                'expectedUrlParameters' => [],
            ],
            'not empty' => [
                'visitId'               => $randomVisitId,
                'expectedUrlParameters' => [
                    GenericRequestInterface::PARAMETER_VISIT_ID => $randomVisitId,
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(?string $visitId, array $expectedUrlParameters): void
    {
        $genericRequestMock = $this->createConfiguredMock(
            GenericRequestInterface::class,
            [
                'getVisitId' => $visitId,
            ],
        );

        $genericRequestMock->expects(self::once())->method('getVisitId');

        $provider = new VisitIdParametersProvider($genericRequestMock);

        // Assert the output stays the same each time and the request is accessed once for information
        self::assertSame(
            $expectedUrlParameters,
            $provider->getPersistentUrlParameters(
                PersistentUrlParametersPageType::DEFAULT,
            ),
        );
        self::assertSame(
            $expectedUrlParameters,
            $provider->getPersistentUrlParameters(
                PersistentUrlParametersPageType::DEFAULT,
            ),
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\GeoIp;

use App\GeoIp\GeoIp2CountryHelper;
use App\GeoIp\Reader\GeoIp2CountryReader;
use App\GeoIp\Reader\GeoIp2CountryReaderFactory;
use GeoIp2\Model\Country as CountryModel;
use GeoIp2\Record\Country as CountryRecord;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\AdBot\Request\AdBotRequestStub;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Http\Request\Info\RequestInfoStub;

class GeoIp2CountryHelperTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function ipCountryDataProvider(): array
    {
        return [
            'no ip'      => [
                'userIp'              => null,
                'countryIsoCode'      => 'NL',
                'adBot'               => false,
                'debugCountryCode'    => null,
                'expectedCountryCode' => null,
            ],
            'no country' => [
                'userIp'              => '127.0.0.1',
                'countryIsoCode'      => null,
                'adBot'               => false,
                'debugCountryCode'    => null,
                'expectedCountryCode' => null,
            ],
            'normal'     => [
                'userIp'              => '127.0.0.1',
                'countryIsoCode'      => 'NL',
                'adBot'               => false,
                'debugCountryCode'    => null,
                'expectedCountryCode' => 'NL',
            ],
            'ad bot'     => [
                'userIp'              => '127.0.0.1',
                'countryIsoCode'      => 'NL',
                'adBot'               => true,
                'debugCountryCode'    => null,
                'expectedCountryCode' => null,
            ],
            'debug'      => [
                'userIp'              => '127.0.0.1',
                'countryIsoCode'      => 'NL',
                'adBot'               => false,
                'debugCountryCode'    => 'DE',
                'expectedCountryCode' => 'DE',
            ],
        ];
    }

    #[DataProvider('ipCountryDataProvider')]
    public function testVisitorCountryCode(
        ?string $userIp,
        ?string $countryIsoCode,
        bool $adBot,
        ?string $debugCountryCode,
        ?string $expectedCountryCode
    ): void
    {
        $requestInfoStub = (new RequestInfoStub())
            ->setUserIp($userIp);

        $adBotRequestStub = (new AdBotRequestStub())
            ->setIsAdBot($adBot);

        $debugRequestStub = (new DebugRequestStub())
            ->setCountryCode($debugCountryCode);

        $countryRecordMock = $this->createMock(CountryRecord::class);
        $countryRecordMock->expects(self::atMost(2))->method('__get')
            ->willReturn($countryIsoCode);

        $countryModelMock = $this->createMock(CountryModel::class);
        $countryModelMock->expects(self::atMost(2))->method('__get')
            ->willReturn($countryRecordMock);

        $geoIpCountryReaderMock = $this->createMock(GeoIp2CountryReader::class);
        $geoIpCountryReaderMock->expects(self::atMost(1))->method('country')
            ->willReturn($countryIsoCode !== null ? $countryModelMock : null);

        $geoIpCountryReaderFactoryMock = $this->createMock(GeoIp2CountryReaderFactory::class);
        $geoIpCountryReaderFactoryMock->expects(self::atMost(1))->method('create')
            ->willReturn($geoIpCountryReaderMock);

        $geoIpCountryHelper = new GeoIp2CountryHelper(
            $adBotRequestStub,
            $debugRequestStub,
            $requestInfoStub,
            $geoIpCountryReaderFactoryMock,
        );

        self::assertSame($expectedCountryCode, $geoIpCountryHelper->getVisitorCountryCode());

        // Value should be cached on additional call
        self::assertSame($expectedCountryCode, $geoIpCountryHelper->getVisitorCountryCode());
    }
}

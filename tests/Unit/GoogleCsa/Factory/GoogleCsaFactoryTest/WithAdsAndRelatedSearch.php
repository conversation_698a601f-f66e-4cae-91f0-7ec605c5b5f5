<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Factory\GoogleCsaFactoryTest;

use App\GoogleCsa\Request\GoogleCsaAdsRequest;
use App\GoogleCsa\Request\GoogleCsaRelatedSearchRequest;
use App\GoogleCsa\Request\GoogleCsaRequest;
use Visymo\GoogleCsa\Enum\AdSafe;
use Visymo\GoogleCsa\RelatedSearch\Unit\RelatedSearchUnit;

class WithAdsAndRelatedSearch extends AbstractGoogleCsaFactoryCase
{
    public function getRequest(): GoogleCsaRequest
    {
        $adsRequest = new GoogleCsaAdsRequest(
            numRepeated     : 3,
            page            : 1,
            styleId         : 1867873333,
            topAmount       : 3,
            topContainer    : 'csa-top',
            bottomAmount    : 4,
            bottomContainer : 'csa-bottom',
            addClickTrackUrl: true,
        );

        $relatedSearchUnits = [
            new RelatedSearchUnit(
                container      : 'related-1',
                styleId        : 1454543543,
                relatedSearches: 12,
            ),
            new RelatedSearchUnit(
                container      : 'related-2',
                styleId        : 1454543543,
                relatedSearches: 6,
            ),
        ];

        $relatedSearchRequest = new GoogleCsaRelatedSearchRequest(
            route             : 'route_display_search_related',
            units             : $relatedSearchUnits,
            forContent        : true,
            referrerAdCreative: 'Very nice! Click here!',
            terms             : ['term1', 'term2'],
        );

        return new GoogleCsaRequest(
            query        : 'hello world',
            adSafe       : AdSafe::LOW,
            channels     : ['channel1', 'channel2'],
            ads          : $adsRequest,
            relatedSearch: $relatedSearchRequest,
        );
    }
}

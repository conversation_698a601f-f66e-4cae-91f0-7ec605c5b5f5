<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Factory\GoogleCsaFactoryTest;

use App\GoogleCsa\Request\GoogleCsaRelatedSearchRequest;
use App\GoogleCsa\Request\GoogleCsaRequest;
use Visymo\GoogleCsa\Enum\AdSafe;
use Visymo\GoogleCsa\RelatedSearch\Unit\RelatedSearchUnit;

class WithRelatedSearchForSearch extends AbstractGoogleCsaFactoryCase
{
    public function getRequest(): GoogleCsaRequest
    {
        $relatedSearchUnits = [
            new RelatedSearchUnit(
                container      : 'related-1',
                styleId        : 1454543543,
                relatedSearches: 6,
            ),
            new RelatedSearchUnit(
                container      : 'related-2',
                styleId        : 1454543543,
                relatedSearches: 10,
            ),
        ];

        $relatedSearchRequest = new GoogleCsaRelatedSearchRequest(
            route             : 'route_display_search_related',
            units             : $relatedSearchUnits,
            forContent        : false,
            referrerAdCreative: null,
            terms             : [],
        );

        return new GoogleCsaRequest(
            query        : 'Nice summer!',
            adSafe       : AdSafe::HIGH,
            channels     : ['channel1', 'ab_tb'],
            ads          : null,
            relatedSearch: $relatedSearchRequest,
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Helper;

use App\Generic\Url\UrlHelper;
use App\GoogleCsa\Helper\GoogleCsaClickTrackUrlHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Visymo\GoogleCsa\Ads\Unit\AdUnit;
use Visymo\GoogleCsa\Ads\Unit\AdUnitInterface;

final class GoogleCsaClickTrackUrlHelperTest extends TestCase
{
    private GoogleCsaClickTrackUrlHelper $googleCsaClickTrackUrlHelper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->googleCsaClickTrackUrlHelper = new GoogleCsaClickTrackUrlHelper(
            new UrlHelper(),
        );
    }

    /**
     * @return mixed[]
     */
    public static function unitDataProvider(): array
    {
        return [
            'no click track URLs' => [
                'unitCallback'           => static function (AdUnitInterface $unit): void {
                    $unit->setStyleId(12345);
                },
                'expectedClickTrackUrls' => [],
            ],
            'add style ID'        => [
                'unitCallback'           => static function (AdUnitInterface $unit): void {
                    $unit
                        ->setStyleId(12345)
                        ->addClickTrackUrl('https://www.visymo.com/click1?hello=world')
                        ->addClickTrackUrl('https://www.visymo.com/click2?hello=world');
                },
                'expectedClickTrackUrls' => [
                    'https://www.visymo.com/click1?hello=world&astid=12345',
                    'https://www.visymo.com/click2?hello=world&astid=12345',
                ],
            ],
            'no style ID'         => [
                'unitCallback'           => static function (AdUnitInterface $unit): void {
                    $unit
                        ->setStyleId(null)
                        ->addClickTrackUrl('https://www.visymo.com/click1?hello=world')
                        ->addClickTrackUrl('https://www.visymo.com/click2?hello=world');
                },
                'expectedClickTrackUrls' => [
                    'https://www.visymo.com/click1?hello=world',
                    'https://www.visymo.com/click2?hello=world',
                ],
            ],
            'adjust to style ID'  => [
                'unitCallback'           => static function (AdUnitInterface $unit): void {
                    $unit
                        ->setStyleId(5438754)
                        ->addClickTrackUrl('https://www.visymo.com/click1?hello=world&astid=12345')
                        ->addClickTrackUrl('https://www.visymo.com/click2?hello=world');
                },
                'expectedClickTrackUrls' => [
                    'https://www.visymo.com/click1?hello=world&astid=5438754',
                    'https://www.visymo.com/click2?hello=world&astid=5438754',
                ],
            ],
            'remove style ID'     => [
                'unitCallback'           => static function (AdUnitInterface $unit): void {
                    $unit
                        ->setStyleId(null)
                        ->addClickTrackUrl('https://www.visymo.com/click1?hello=world&astid=12345')
                        ->addClickTrackUrl('https://www.visymo.com/click2?hello=world&astid=99944&hi=there');
                },
                'expectedClickTrackUrls' => [
                    'https://www.visymo.com/click1?hello=world',
                    'https://www.visymo.com/click2?hello=world&hi=there',
                ],
            ],
        ];
    }

    /**
     * @param string[] $expectedClickTrackUrls
     */
    #[DataProvider('unitDataProvider')]
    public function testGetClickTrackUrl(
        callable $unitCallback,
        array $expectedClickTrackUrls
    ): void
    {
        $unit = new AdUnit('csa-top', null, 3, true);
        $unitCallback($unit);

        $this->googleCsaClickTrackUrlHelper->updateUnitClickTrackUrls($unit);

        self::assertSame(
            $expectedClickTrackUrls,
            $unit->getClickTrackUrls(),
        );
    }
}

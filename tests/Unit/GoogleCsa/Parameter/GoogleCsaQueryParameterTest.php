<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Parameter;

use App\GoogleCsa\Parameter\GoogleCsaQueryParameter;
use App\WebSearch\Helper\WebSearchQueryHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Search\Request\SearchRequestStub;

final class GoogleCsaQueryParameterTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function queryDataProvider(): array
    {
        return [
            'empty'          => [
                'query'         => null,
                'expectedQuery' => null,
            ],
            'single word'    => [
                'query'         => 'Pizza',
                'expectedQuery' => 'Pizza',
            ],
            'multiple words' => [
                'query'         => 'Pizza HaWaii',
                'expectedQuery' => 'Pizza HaWaii',
            ],
        ];
    }

    #[DataProvider('queryDataProvider')]
    public function testGoogleTestAd(?string $query, ?string $expectedQuery): void
    {
        $searchRequestStub = new SearchRequestStub();
        $searchRequestStub->setQuery($query);

        $googleCsaQueryParameter = new GoogleCsaQueryParameter(
            $searchRequestStub,
            new WebSearchQueryHelper(),
        );

        self::assertSame(
            $expectedQuery,
            $googleCsaQueryParameter->getQuery(),
        );
    }
}

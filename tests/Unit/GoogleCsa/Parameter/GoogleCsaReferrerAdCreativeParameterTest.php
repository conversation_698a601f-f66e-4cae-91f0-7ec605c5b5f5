<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Parameter;

use App\GoogleCsa\Parameter\GoogleCsaReferrerAdCreativeParameter;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;

final class GoogleCsaReferrerAdCreativeParameterTest extends TestCase
{
    private SearchRequestStub $searchRequestStub;

    private SeaRequestStub $seaRequestStub;

    private GoogleCsaReferrerAdCreativeParameter $googleCsaDisplayToSearchParameter;

    protected function setUp(): void
    {
        parent::setUp();

        $this->searchRequestStub = new SearchRequestStub();
        $this->seaRequestStub = new SeaRequestStub();

        $this->googleCsaDisplayToSearchParameter = new GoogleCsaReferrerAdCreativeParameter(
            $this->seaRequestStub,
            $this->searchRequestStub,
        );
    }

    /**
     * @return mixed[]
     */
    public static function referrerAdCreativeForContentDataProvider(): array
    {
        return [
            'empty null'          => [
                'queryAsString'                        => '',
                'referrerAdCreativeForContent'         => null,
                'expectedReferrerAdCreativeForContent' => 'Search for ',
            ],
            'query with null'     => [
                'queryAsString'                        => 'pizza',
                'referrerAdCreativeForContent'         => null,
                'expectedReferrerAdCreativeForContent' => 'Search for pizza',
            ],
            'query and parameter' => [
                'queryAsString'                        => 'pizza',
                'referrerAdCreativeForContent'         => 'Best pizza in town!',
                'expectedReferrerAdCreativeForContent' => 'Best pizza in town!',
            ],
        ];
    }

    #[DataProvider('referrerAdCreativeForContentDataProvider')]
    public function testGetReferrerAdCreativeForContent(
        string $queryAsString,
        ?string $referrerAdCreativeForContent,
        ?string $expectedReferrerAdCreativeForContent
    ): void
    {
        $this->searchRequestStub->setQuery($queryAsString);
        $this->seaRequestStub->setReferrerAdCreative($referrerAdCreativeForContent);

        $actualReferrerAdCreativeForContent = $this->googleCsaDisplayToSearchParameter
            ->getReferrerAdCreativeForContent();

        self::assertSame($expectedReferrerAdCreativeForContent, $actualReferrerAdCreativeForContent);
    }
}

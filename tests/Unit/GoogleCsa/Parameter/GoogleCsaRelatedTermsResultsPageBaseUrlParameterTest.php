<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\Parameter;

use App\Campaign\Settings\CampaignSettingsHelper;
use App\GoogleCsa\Parameter\GoogleCsaRelatedTermsResultsPageBaseUrlParameter;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersRouter;
use App\Tracking\Helper\TrafficHelper;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Campaign\Settings\CampaignSettingsStubBuilder;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;

class GoogleCsaRelatedTermsResultsPageBaseUrlParameterTest extends TestCase
{
    private WebsiteSettingsHelper & MockObject $websiteSettingsHelperMock;

    private TrafficHelper & MockObject $trafficHelperMock;

    private PersistentUrlParametersRouter & MockObject $persistentUrlParametersRouterMock;

    protected function setUp(): void
    {
        $this->websiteSettingsHelperMock = $this->createConfiguredMock(
            WebsiteSettingsHelper::class,
            [
                'getSettings' => new WebsiteSettingsStub(),
            ],
        );

        $this->trafficHelperMock = $this->createConfiguredMock(
            TrafficHelper::class,
            [
                'getTrackingChannel' => 'channel',
            ],
        );

        $this->persistentUrlParametersRouterMock = $this->createMock(PersistentUrlParametersRouter::class);
    }

    /**
     * @return mixed[]
     */
    public static function getResultsPageBaseUrlDataProvider(): array
    {
        return [
            'with related terms route, direct contract, paid traffic, no campaign domain'      => [
                'relatedTermsRoute'          => 'related_terms_route',
                'contractType'               => ContractType::DIRECT,
                'isPaidTraffic'              => true,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'related_terms_route',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'with related terms route, online contract, paid traffic, no campaign domain'      => [
                'relatedTermsRoute'          => 'related_terms_route',
                'contractType'               => ContractType::ONLINE,
                'isPaidTraffic'              => true,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'related_terms_route',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'with related terms route, direct contract, direct traffic, no campaign domain'    => [
                'relatedTermsRoute'          => 'related_terms_route',
                'contractType'               => ContractType::DIRECT,
                'isPaidTraffic'              => false,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'related_terms_route',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'with related terms route, online contract, direct traffic, no campaign domain'    => [
                'relatedTermsRoute'          => 'related_terms_route',
                'contractType'               => ContractType::ONLINE,
                'isPaidTraffic'              => false,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'related_terms_route',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'without related terms route, direct contract, paid traffic, no campaign domain'   => [
                'relatedTermsRoute'          => null,
                'contractType'               => ContractType::DIRECT,
                'isPaidTraffic'              => true,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'route_web_search_advertised',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'without related terms route, online contract, paid traffic, no campaign domain'   => [
                'relatedTermsRoute'          => null,
                'contractType'               => ContractType::ONLINE,
                'isPaidTraffic'              => true,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'route_web_search_advertised',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'without related terms route, direct contract, direct traffic, no campaign domain' => [
                'relatedTermsRoute'          => null,
                'contractType'               => ContractType::DIRECT,
                'isPaidTraffic'              => false,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'route_search',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'without related terms route, online contract, direct traffic, no campaign domain' => [
                'relatedTermsRoute'          => null,
                'contractType'               => ContractType::ONLINE,
                'isPaidTraffic'              => false,
                'campaignDomain'             => null,
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'route_search',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'without related terms route, direct contract, paid traffic, campaign domain'      => [
                'relatedTermsRoute'          => null,
                'contractType'               => ContractType::DIRECT,
                'isPaidTraffic'              => true,
                'campaignDomain'             => 'analyze-unit.test.com',
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'route_web_search_advertised',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'without related terms route, online contract, paid traffic, campaign domain'      => [
                'relatedTermsRoute'          => null,
                'contractType'               => ContractType::ONLINE,
                'isPaidTraffic'              => true,
                'campaignDomain'             => 'analyze-unit.test.com',
                'expectedRouterFunctionCall' => 'generateForDomain',
                'expectedParams'             => [
                    'domain'           => 'analyze-unit.test.com',
                    'route'            => 'route_web_search_advertised',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                ],
            ],
            'with related terms route, direct contract, paid traffic, campaign domain'         => [
                'relatedTermsRoute'          => 'related_terms_route',
                'contractType'               => ContractType::DIRECT,
                'isPaidTraffic'              => true,
                'campaignDomain'             => 'analyze-unit.test.com',
                'expectedRouterFunctionCall' => 'generate',
                'expectedParams'             => [
                    'route'            => 'related_terms_route',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                    'absolute_url'     => true,
                ],
            ],
            'with related terms route, online contract, paid traffic, campaign domain'         => [
                'relatedTermsRoute'          => 'related_terms_route',
                'contractType'               => ContractType::ONLINE,
                'isPaidTraffic'              => true,
                'campaignDomain'             => 'analyze-unit.test.com',
                'expectedRouterFunctionCall' => 'generateForDomain',
                'expectedParams'             => [
                    'domain'           => 'analyze-unit.test.com',
                    'route'            => 'related_terms_route',
                    'route_parameters' => [],
                    'page_type'        => PersistentUrlParametersPageType::RELATED_TERMS,
                ],
            ],
        ];
    }

    /**
     * @param array<string, mixed> $expectedParams
     * @param non-empty-string     $expectedRouterFunctionCall
     */
    #[DataProvider('getResultsPageBaseUrlDataProvider')]
    public function testGetResultsPageBaseUrl(
        ?string $relatedTermsRoute,
        ContractType $contractType,
        bool $isPaidTraffic,
        ?string $campaignDomain,
        string $expectedRouterFunctionCall,
        array $expectedParams
    ): void
    {
        /** @var WebsiteSettingsStub $websiteSettings */
        $websiteSettings = $this->websiteSettingsHelperMock->getSettings();
        $campaignSettings = (new CampaignSettingsStubBuilder())->setDomain($campaignDomain)->create();
        $websiteSettings->getGoogleAdSense()->setContractType($contractType);

        $campaignSettingsHelperMock = $this->createConfiguredMock(
            CampaignSettingsHelper::class,
            [
                'getSettings' => $campaignSettings,
            ],
        );

        $expectedParams = array_values($expectedParams);

        $this->trafficHelperMock->method('isPaidTraffic')->willReturn($isPaidTraffic);
        $this->persistentUrlParametersRouterMock
            ->expects($this->once())
            ->method($expectedRouterFunctionCall)
            ->with(...$expectedParams);

        (new GoogleCsaRelatedTermsResultsPageBaseUrlParameter(
            $this->websiteSettingsHelperMock,
            $this->trafficHelperMock,
            $this->persistentUrlParametersRouterMock,
            $campaignSettingsHelperMock,
        ))->getResultsPageBaseUrl($relatedTermsRoute);
    }
}

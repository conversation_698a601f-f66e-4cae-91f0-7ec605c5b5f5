<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StatisticsProvider;

use App\GoogleCsa\StatisticsProvider\GoogleAdSenseStatisticsProvider;
use App\GoogleCsa\StatisticsProvider\GoogleAdSenseStatisticsResolver;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tests\Unit\Statistics\Provider\AbstractStatisticsProviderTestCase;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

class GoogleAdSenseStatisticsProviderTest extends AbstractStatisticsProviderTestCase
{
    private GoogleAdSenseStatisticsResolver $googleAdSenseStatisticsResolver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->googleAdSenseStatisticsResolver = new GoogleAdSenseStatisticsResolver(
            new SymfonyOptionsResolverBridge(new OptionsResolver()),
            $this->genericRequestMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function payloadDataProvider(): array
    {
        $pageviewId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'ads'      => [
                'payload'            => [
                    'key'  => 'ga',
                    'pvid' => $pageviewId,
                    'hl'   => true,
                    'as'   => 5,
                    'lt'   => 2,
                    'tas'  => 1,
                    'tar'  => 2,
                ],
                'expectedStatistics' => [
                    'visit_id'       => self::$visitId,
                    'pageview_id'    => $pageviewId,
                    'google_adsense' => [
                        'has_loaded'           => true,
                        'amount_shown'         => 5,
                        'load_time'            => 2,
                        'top_amount_shown'     => 1,
                        'top_amount_requested' => 2,
                        'has_shopping_ads'     => false,
                    ],
                ],
            ],
            'shopping' => [
                'payload'            => [
                    'key'  => 'ga',
                    'pvid' => $pageviewId,
                    'hl'   => true,
                    'as'   => 6,
                    'lt'   => 123,
                    'tas'  => 10,
                    'tar'  => 3,
                ],
                'expectedStatistics' => [
                    'visit_id'       => self::$visitId,
                    'pageview_id'    => $pageviewId,
                    'google_adsense' => [
                        'has_loaded'           => true,
                        'amount_shown'         => 6,
                        'load_time'            => 123,
                        'top_amount_shown'     => 10,
                        'top_amount_requested' => 3,
                        'has_shopping_ads'     => true,
                    ],
                ],
            ],
            'extra'    => [
                'payload'            => [
                    'extra' => true,
                    'key'   => 'ga',
                    'pvid'  => $pageviewId,
                    'hl'    => true,
                    'as'    => 6,
                    'lt'    => 5,
                    'tas'   => 4,
                    'tar'   => 3,
                ],
                'expectedStatistics' => [
                    'visit_id'       => self::$visitId,
                    'pageview_id'    => $pageviewId,
                    'google_adsense' => [
                        'has_loaded'           => true,
                        'amount_shown'         => 6,
                        'load_time'            => 5,
                        'top_amount_shown'     => 4,
                        'top_amount_requested' => 3,
                        'has_shopping_ads'     => true,
                    ],
                ],
            ],
            'bad'      => [
                'payload'            => [
                    'key'  => 'ga',
                    'pvid' => $pageviewId,
                    'bad'  => true,
                ],
                'expectedStatistics' => null,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $payload
     * @param mixed[]|null         $expectedStatistics
     */
    #[DataProvider('payloadDataProvider')]
    public function testGetFromPayload(array $payload, ?array $expectedStatistics): void
    {
        if ($expectedStatistics === null) {
            $this->expectException(InvalidOptionException::class);
        }

        $googleAdSenseStatisticsProvider = new GoogleAdSenseStatisticsProvider($this->googleAdSenseStatisticsResolver);
        self::assertSame('google_adsense', $googleAdSenseStatisticsProvider::getContextKey());
        self::assertSame('ga', $googleAdSenseStatisticsProvider::getPayloadKey());

        $actualStatistics = $googleAdSenseStatisticsProvider->getFromPayload($payload);

        self::assertArrayHasKey('response_timestamp', $actualStatistics['google_adsense']);

        unset($actualStatistics['google_adsense']['response_timestamp']);

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StyleId;

use App\GoogleCsa\StyleId\GoogleCsaStyleIdParameter;
use App\GoogleCsa\StyleId\GoogleCsaStyleIdProviderInterface;
use PHPUnit\Framework\TestCase;
use Visymo\Shared\Infrastructure\Stub\Domain\Logger\MemoryLoggerStub;

final class GoogleCsaStyleIdParameterTest extends TestCase
{
    public function testLogsInvalidStyleId(): void
    {
        $invalidStyleId = 33;
        $invalidStyleIdProvider = $this->createConfiguredMock(
            GoogleCsaStyleIdProviderInterface::class,
            [
                'getStyleId'      => $invalidStyleId,
                'validateStyleId' => true,
            ],
        );

        $memoryLogger = new MemoryLoggerStub();

        $googleCsaStyleIdParameter = new GoogleCsaStyleIdParameter(
            [
                $invalidStyleIdProvider,
            ],
            $memoryLogger,
        );

        self::assertSame($invalidStyleId, $googleCsaStyleIdParameter->getStyleId());

        $logMessages = [];
        foreach ($memoryLogger->getNormalizedLogs() as $logs) {
            foreach ($logs as $log) {
                $logMessages[] = $log['message'];
            }
        }

        $logMessages = implode(' ', $logMessages);

        self::assertStringContainsString('Invalid style ID', $logMessages);
    }
}

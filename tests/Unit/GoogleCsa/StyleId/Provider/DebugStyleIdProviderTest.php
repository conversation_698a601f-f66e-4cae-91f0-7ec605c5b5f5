<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StyleId\Provider;

use App\GoogleCsa\StyleId\Provider\DebugStyleIdProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Debug\Request\DebugRequestStub;

final class DebugStyleIdProviderTest extends TestCase
{
    private DebugRequestStub $debugRequestStub;

    private DebugStyleIdProvider $debugStyleIdProvider;

    protected function setUp(): void
    {
        parent::setUp();

        $this->debugRequestStub = new DebugRequestStub();
        $this->debugStyleIdProvider = new DebugStyleIdProvider(
            $this->debugRequestStub,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getStyleIdDataProvider(): array
    {
        return [
            'none'   => [
                'styleId' => null,
            ],
            'random' => [
                'styleId' => random_int(**********, **********),
            ],
        ];
    }

    #[dataProvider('getStyleIdDataProvider')]
    public function testGetStyleId(?int $styleId): void
    {
        $this->debugRequestStub->setForceStyleId($styleId);

        self::assertSame(
            $styleId,
            $this->debugStyleIdProvider->getStyleId(),
        );
    }
}

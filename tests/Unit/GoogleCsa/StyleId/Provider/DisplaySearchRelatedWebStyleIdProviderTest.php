<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StyleId\Provider;

use App\Campaign\Settings\CampaignSettingsHelper;
use App\Campaign\Settings\GoogleAdSense;
use App\Generic\Device\Device;
use App\GoogleCsa\StyleId\Provider\DisplaySearchRelatedWebStyleIdProvider;
use App\WebsiteSettings\Settings\GoogleAdSense\ContractType;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Campaign\Settings\CampaignSettingsStubBuilder;
use Tests\Stub\DisplaySearchRelated\Settings\DisplaySearchRelatedSettingsStubBuilder;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;

final class DisplaySearchRelatedWebStyleIdProviderTest extends TestCase
{
    private DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder;

    private WebsiteSettingsStub $websiteSettingStubs;

    private CampaignSettingsStubBuilder $campaignSettingsStubBuilder;

    private SearchRequestStub $searchRequestStub;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    private WebsiteSettingsHelper & MockObject $websiteSettingsHelperMock;

    private CampaignSettingsHelper & MockObject $campaignSettingsHelperMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->displaySearchRelatedSettingsStubBuilder = new DisplaySearchRelatedSettingsStubBuilder();
        $this->displaySearchRelatedSettingsStubBuilder->setEnabled(true);

        $this->websiteSettingStubs = new WebsiteSettingsStub();
        $this->websiteSettingStubs->getGoogleAdSense()->setContractType(
            ContractType::DIRECT,
        );
        $this->campaignSettingsStubBuilder = new CampaignSettingsStubBuilder();
        $this->campaignSettingsStubBuilder->setGoogleAdSense(null);
        $this->searchRequestStub = new SearchRequestStub();

        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
        $this->websiteSettingsHelperMock = $this->createConfiguredMock(
            WebsiteSettingsHelper::class,
            [
                'getSettings' => $this->websiteSettingStubs,
            ],
        );
    }

    /**
     * @return mixed[]
     */
    public static function getStyleIdDataProvider(): array
    {
        return [
            'not DSRW endpoint'                          => [
                'expectedStyleId' => null,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    WebsiteSettingsStub $websiteSettingStubs,
                    CampaignSettingsStubBuilder $campaignSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setIsLandingPage(false);
                },
            ],
            'by campaign because of online contract'     => [
                'expectedStyleId' => 555,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    WebsiteSettingsStub $websiteSettingStubs,
                    CampaignSettingsStubBuilder $campaignSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $websiteSettingStubs->getGoogleAdSense()
                        ->setContractType(ContractType::ONLINE);
                    $campaignSettingsStubBuilder->setGoogleAdSense(
                        new GoogleAdSense(555),
                    );
                    $trackingEntryStubBuilder->setDevice(Device::MOBILE);
                    $searchRequestStub->setDisplaySearchRelatedWeb();
                },
            ],
            'mobile by display search related settings'  => [
                'expectedStyleId' => 777,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    WebsiteSettingsStub $websiteSettingStubs,
                    CampaignSettingsStubBuilder $campaignSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $displaySearchRelatedSettingsStubBuilder->setWebStyleIdMobile(777);
                    $trackingEntryStubBuilder->setDevice(Device::MOBILE);
                    $searchRequestStub->setDisplaySearchRelatedWeb();
                },
            ],
            'desktop by display search related settings' => [
                'expectedStyleId' => 777,
                'setTestCallback' => static function (
                    DisplaySearchRelatedSettingsStubBuilder $displaySearchRelatedSettingsStubBuilder,
                    WebsiteSettingsStub $websiteSettingStubs,
                    CampaignSettingsStubBuilder $campaignSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $displaySearchRelatedSettingsStubBuilder->setWebStyleIdDesktop(777);
                    $trackingEntryStubBuilder->setDevice(Device::DESKTOP);
                    $searchRequestStub->setDisplaySearchRelatedWeb();
                },
            ],
        ];
    }

    #[dataProvider('getStyleIdDataProvider')]
    public function testGetStyleId(?int $expectedStyleId, callable $setTestCallback): void
    {
        $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('pizza');

        $setTestCallback(
            $this->displaySearchRelatedSettingsStubBuilder,
            $this->websiteSettingStubs,
            $this->campaignSettingsStubBuilder,
            $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder(),
            $this->searchRequestStub,
        );

        $this->campaignSettingsHelperMock = $this->createConfiguredMock(
            CampaignSettingsHelper::class,
            [
                'getSettings' => $this->campaignSettingsStubBuilder->create(),
            ],
        );

        $displaySearchRelatedSettings = $this->displaySearchRelatedSettingsStubBuilder->create();

        $styleIdProvider = new DisplaySearchRelatedWebStyleIdProvider(
            displaySearchRelatedSettings: $displaySearchRelatedSettings,
            websiteSettingsHelper       : $this->websiteSettingsHelperMock,
            activeTrackingEntryHelper   : $this->activeTrackingEntryHelperStub,
            searchRequest               : $this->searchRequestStub,
            campaignSettingsHelper      : $this->campaignSettingsHelperMock,
        );

        self::assertSame(
            $expectedStyleId,
            $styleIdProvider->getStyleId(),
        );
    }
}

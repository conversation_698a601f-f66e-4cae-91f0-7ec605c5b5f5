<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StyleId\Provider;

use App\Generic\Device\Device;
use App\GoogleCsa\StyleId\Provider\SearchStyleIdProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Search\Settings\SearchSettingsStubBuilder;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;

class SearchStyleIdProviderTest extends TestCase
{
    private SearchSettingsStubBuilder $searchSettingsStubBuilder;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->searchSettingsStubBuilder = new SearchSettingsStubBuilder();
        $this->searchSettingsStubBuilder
            ->setEnabled()
            ->setStyleIdDesktop(**********)
            ->setStyleIdMobile(**********);

        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();
    }

    /**
     * @return mixed[]
     */
    public static function getStyleIdDataProvider(): array
    {
        return [
            'not search endpoint and not seo page' => [
                'expectedStyleId' => null,
                'setTestCallback' => static function (
                    SearchSettingsStubBuilder $searchSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    RequestInfoStub $requestInfoStub,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $requestInfoStub->setRoute('route_home');
                    $searchRequestStub->setIsSeoPage(false);
                },
            ],
            'seo page'                             => [
                'expectedStyleId' => 86543567,
                'setTestCallback' => static function (
                    SearchSettingsStubBuilder $searchSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    RequestInfoStub $requestInfoStub,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchSettingsStubBuilder->setStyleIdMobile(86543567);
                    $trackingEntryStubBuilder->setDevice(Device::MOBILE);
                    $requestInfoStub->setRoute('route_vinden_seo_landing_page_zr');
                    $searchRequestStub->setIsSeoPage();
                },
            ],
            'mobile by search settings'            => [
                'expectedStyleId' => 777,
                'setTestCallback' => static function (
                    SearchSettingsStubBuilder $searchSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    RequestInfoStub $requestInfoStub,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchSettingsStubBuilder->setStyleIdMobile(777);
                    $trackingEntryStubBuilder->setDevice(Device::MOBILE);
                    $requestInfoStub->setRoute('route_search');
                },
            ],
            'desktop by search settings'           => [
                'expectedStyleId' => 4444,
                'setTestCallback' => static function (
                    SearchSettingsStubBuilder $searchSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    RequestInfoStub $requestInfoStub,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchSettingsStubBuilder->setStyleIdDesktop(4444);
                    $trackingEntryStubBuilder->setDevice(Device::DESKTOP);
                    $requestInfoStub->setRoute('route_search');
                },
            ],
            'keyword ideas search'                 => [
                'expectedStyleId' => **********,
                'setTestCallback' => static function (
                    SearchSettingsStubBuilder $searchSettingsStubBuilder,
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    RequestInfoStub $requestInfoStub,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchSettingsStubBuilder->setStyleIdDesktop(**********);
                    $trackingEntryStubBuilder->setDevice(Device::DESKTOP);
                    $requestInfoStub->setRoute('route_keyword_ideas_search');
                },
            ],
        ];
    }

    #[dataProvider('getStyleIdDataProvider')]
    public function testGetStyleId(?int $expectedStyleId, callable $setTestCallback): void
    {
        $requestInfoStub = new RequestInfoStub();
        $searchRequestStub = new SearchRequestStub();

        $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('pizza');

        $setTestCallback(
            $this->searchSettingsStubBuilder,
            $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder(),
            $requestInfoStub,
            $searchRequestStub,
        );

        $styleIdProvider = new SearchStyleIdProvider(
            searchSettings           : $this->searchSettingsStubBuilder->create(),
            activeTrackingEntryHelper: $this->activeTrackingEntryHelperStub,
            requestInfo              : $requestInfoStub,
            searchRequest            : $searchRequestStub,
        );

        self::assertSame(
            $expectedStyleId,
            $styleIdProvider->getStyleId(),
        );
    }
}

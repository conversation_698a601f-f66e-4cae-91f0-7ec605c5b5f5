<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleCsa\StyleId\Provider;

use App\GoogleCsa\StyleId\Provider\TrackingEntryStyleIdProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;
use Tests\Stub\Tracking\Helper\ActiveTrackingEntryHelperStub;

final class TrackingEntryStyleIdProviderTest extends TestCase
{
    private SearchRequestStub $searchRequestStub;

    private ActiveTrackingEntryHelperStub $activeTrackingEntryHelperStub;

    private TrackingEntryStyleIdProvider $trackingEntryStyleIdProvider;

    protected function setUp(): void
    {
        parent::setUp();

        $this->searchRequestStub = new SearchRequestStub();
        $this->activeTrackingEntryHelperStub = new ActiveTrackingEntryHelperStub();

        $this->trackingEntryStyleIdProvider = new TrackingEntryStyleIdProvider(
            activeTrackingEntryHelper: $this->activeTrackingEntryHelperStub,
            searchRequest            : $this->searchRequestStub,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getStyleIdDataProvider(): array
    {
        return [
            'none'                         => [
                'expectedStyleId' => null,
                'setTestCallback' => static function (
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setIsLandingPage(false);
                },
            ],
            'web search'                   => [
                'expectedStyleId' => 123,
                'setTestCallback' => static function (
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setWebSearch();
                    $trackingEntryStubBuilder->setStyleId(123);
                },
            ],
            'web search advertised'        => [
                'expectedStyleId' => 567,
                'setTestCallback' => static function (
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setWebSearchAdvertised();
                    $trackingEntryStubBuilder->setStyleId(567);
                },
            ],
            'display search related web'   => [
                'expectedStyleId' => 890,
                'setTestCallback' => static function (
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setDisplaySearchRelatedWeb();
                    $trackingEntryStubBuilder->setStyleId(890);
                },
            ],
            'microsoft search related web' => [
                'expectedStyleId' => null,
                'setTestCallback' => static function (
                    TrackingEntryStubBuilder $trackingEntryStubBuilder,
                    SearchRequestStub $searchRequestStub
                ): void {
                    $searchRequestStub->setMicrosoftSearchRelatedWeb();
                    $trackingEntryStubBuilder->setStyleId(1234);
                },
            ],
        ];
    }

    #[dataProvider('getStyleIdDataProvider')]
    public function testGetStyleId(?int $expectedStyleId, callable $setTestCallback): void
    {
        $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder()
            ->setIsEmpty(false)
            ->setQuery('pizza');

        $setTestCallback(
            $this->activeTrackingEntryHelperStub->getTrackingEntryStubBuilder(),
            $this->searchRequestStub,
        );

        self::assertSame(
            $expectedStyleId,
            $this->trackingEntryStyleIdProvider->getStyleId(),
        );
    }
}

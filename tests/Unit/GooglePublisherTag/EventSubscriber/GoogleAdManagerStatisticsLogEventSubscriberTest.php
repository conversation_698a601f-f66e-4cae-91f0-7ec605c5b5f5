<?php

declare(strict_types=1);

namespace Tests\Unit\GooglePublisherTag\EventSubscriber;

use App\GooglePublisherTag\EventSubscriber\GoogleAdManagerStatisticsLogEventSubscriber;
use App\GooglePublisherTag\Slot\Slot;
use App\GooglePublisherTag\Slot\SlotSize;
use App\GooglePublisherTag\Tag\GoogleTagRenderHelper;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class GoogleAdManagerStatisticsLogEventSubscriberTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function statisticsLogCreateDataProvider(): array
    {
        return [
            'disabled'          => [
                'googleTagRendererEnabled' => false,
                'renderedSlotIds'          => [],
                'slots'                    => [],
                'expectedStatistics'       => [],
            ],
            'no slots'          => [
                'googleTagRendererEnabled' => true,
                'renderedSlotIds'          => [],
                'slots'                    => [],
                'expectedStatistics'       => [],
            ],
            'no rendered slots' => [
                'googleTagRendererEnabled' => true,
                'renderedSlotIds'          => [],
                'slots'                    => [
                    new Slot('/**********/slot-1', 'slot-1', [new SlotSize(600, 500)]),
                    new Slot('/**********/slot-2', 'slot-2', [new SlotSize(600, 500)]),
                ],
                'expectedStatistics'       => [],
            ],
            'rendered slots'    => [
                'googleTagRendererEnabled' => true,
                'renderedSlotIds'          => ['slot-1'],
                'slots'                    => [
                    new Slot('/**********/slot-1', 'slot-1', [new SlotSize(600, 500)]),
                    new Slot('/**********/slot-2', 'slot-2', [new SlotSize(600, 500)]),
                ],
                'expectedStatistics'       => [
                    'google_ad_manager' => [
                        'ads' => [
                            'slot-1' => [
                                'slot_id'      => 'slot-1',
                                'ad_unit_path' => '/**********/slot-1',
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @param string[] $renderedSlotIds
     * @param Slot[]   $slots
     * @param mixed[]  $expectedStatistics
     */
    #[DataProvider('statisticsLogCreateDataProvider')]
    public function testOnStatisticsLogCreate(
        bool $googleTagRendererEnabled,
        array $renderedSlotIds,
        array $slots,
        array $expectedStatistics
    ): void
    {
        $googleTagRenderHelperMock = $this->createMock(GoogleTagRenderHelper::class);
        $googleTagRenderHelperMock->method('isEnabled')->willReturn($googleTagRendererEnabled);
        $googleTagRenderHelperMock->method('hasSlots')->willReturn($slots !== []);
        $googleTagRenderHelperMock->renderedSlotIds = $renderedSlotIds;
        $googleTagRenderHelperMock->multiRequestSlots = $slots;

        $event = new StatisticsLogCreateEvent();
        $googleAdManagerStatisticsLogEventSubscriber = new GoogleAdManagerStatisticsLogEventSubscriber(
            googleTagRenderHelper: $googleTagRenderHelperMock,
        );
        $googleAdManagerStatisticsLogEventSubscriber->onStatisticsLogCreate($event);

        $actualStatistics = $event->getStatistics();

        if ($expectedStatistics !== []) {
            self::assertNotNull($actualStatistics['google_ad_manager']['request_timestamp']);
            unset($actualStatistics['google_ad_manager']['request_timestamp']);
        }

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

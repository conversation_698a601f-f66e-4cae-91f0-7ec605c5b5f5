<?php

declare(strict_types=1);

namespace Tests\Unit\GoogleTagManager\Settings;

use App\Brand\Settings\BrandSettingsHelper;
use App\GoogleTagManager\Settings\GoogleTagManagerSettingsFactory;
use App\Tracking\Helper\TrafficHelper;
use App\WebsiteSettings\Settings\WebsiteSettingsHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\Stub\Brand\Settings\BrandSettingsStub;
use Tests\Stub\Http\Request\Info\RequestInfoStub;
use Tests\Stub\WebsiteSettings\Settings\WebsiteSettingsStub;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class GoogleTagManagerSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private WebsiteSettingsStub $websiteSettingsStub;

    private BrandSettingsStub $brandSettingsStub;

    private TrafficHelper & MockObject $trafficHelperMock;

    private RequestInfoStub $requestInfoStub;

    private GoogleTagManagerSettingsFactory $googleTagManagerSettingsFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->websiteSettingsStub = new WebsiteSettingsStub();
        $websiteSettingsHelperMock = $this->createConfiguredMock(
            WebsiteSettingsHelper::class,
            [
                'getSettings' => $this->websiteSettingsStub,
            ],
        );
        $this->trafficHelperMock = $this->createMock(
            TrafficHelper::class,
        );
        $this->requestInfoStub = new RequestInfoStub();

        $this->brandSettingsStub = new BrandSettingsStub();
        $brandSettingsHelperMock = $this->createConfiguredMock(
            BrandSettingsHelper::class,
            [
                'getSettings' => $this->brandSettingsStub,
            ],
        );

        $this->googleTagManagerSettingsFactory = new GoogleTagManagerSettingsFactory(
            websiteConfigurationHelper: $this->websiteConfigurationHelperMock,
            websiteSettingsHelper     : $websiteSettingsHelperMock,
            trafficHelper             : $this->trafficHelperMock,
            requestInfo               : $this->requestInfoStub,
            brandSettingsHelper       : $brandSettingsHelperMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return [
            'disabled'               => [
                'brandConfig'                => [
                    'google_tag_manager' => [
                        'enabled' => false,
                    ],
                ],
                'brandSlug'                  => 'zapmeta',
                'paidTraffic'                => false,
                'stubsCallback'              => static function (
                    WebsiteSettingsStub $websiteSettingsStub,
                    RequestInfoStub $requestInfoStub
                ): void {
                    $requestInfoStub->setRoute('route_web_search');
                },
                'expectedEnabled'            => false,
                'expectedEnabledForRequest'  => false,
                'expectedGoogleTagManagerId' => null,
            ],
            'enabled for route'      => [
                'brandConfig'                => [
                    'google_tag_manager' => [
                        'enabled'               => true,
                        'google_tag_manager_id' => 'GTA-123456',
                        'routes'                => [
                            'route_web_search',
                        ],
                    ],
                ],
                'brandSlug'                  => 'zapmeta',
                'paidTraffic'                => true,
                'stubsCallback'              => static function (
                    WebsiteSettingsStub $websiteSettingsStub,
                    RequestInfoStub $requestInfoStub
                ): void {
                    $websiteSettingsStub->getGoogleAdsConversionTracking()->setEnabled();

                    $requestInfoStub->setRoute('route_web_search');
                },
                'expectedEnabled'            => true,
                'expectedEnabledForRequest'  => true,
                'expectedGoogleTagManagerId' => 'GTA-123456',
            ],
            'disabled by route'      => [
                'brandConfig'                => [
                    'google_tag_manager' => [
                        'enabled'               => true,
                        'google_tag_manager_id' => 'GTA-123333',
                        'routes'                => [
                            'route_display_search_related',
                        ],
                    ],
                ],
                'brandSlug'                  => 'zapmeta',
                'paidTraffic'                => true,
                'stubsCallback'              => static function (
                    WebsiteSettingsStub $websiteSettingsStub,
                    RequestInfoStub $requestInfoStub
                ): void {
                    $websiteSettingsStub->getGoogleAdsConversionTracking()->setEnabled();

                    $requestInfoStub->setRoute('route_web_search_advertised');
                },
                'expectedEnabled'            => true,
                'expectedEnabledForRequest'  => false,
                'expectedGoogleTagManagerId' => 'GTA-123333',
            ],
            'disabled for proadvisr' => [
                'brandConfig'                => [
                    'google_tag_manager' => [
                        'enabled'               => true,
                        'google_tag_manager_id' => 'GTM-123456',
                        'routes'                => [
                            'route_display_search_related',
                        ],
                    ],
                ],
                'brandSlug'                  => 'proadvisr',
                'paidTraffic'                => false,
                'stubsCallback'              => static function (
                    WebsiteSettingsStub $websiteSettingsStub,
                    RequestInfoStub $requestInfoStub
                ): void {
                    $websiteSettingsStub->getGoogleAdsConversionTracking()->setEnabled();

                    $requestInfoStub->setRoute('route_display_search_related');
                },
                'expectedEnabled'            => false,
                'expectedEnabledForRequest'  => false,
                'expectedGoogleTagManagerId' => 'GTM-123456',
            ],
        ];
    }

    /**
     * @param mixed[] $brandConfig
     */
    #[DataProvider('createDataProvider')]
    public function testCreate(
        array $brandConfig,
        string $brandSlug,
        bool $paidTraffic,
        callable $stubsCallback,
        bool $expectedEnabled,
        bool $expectedEnabledForRequest,
        ?string $expectedGoogleTagManagerId
    ): void
    {
        $this->setBrandConfig($brandConfig);
        $this->brandSettingsStub->setSlug($brandSlug);
        $this->trafficHelperMock->method('isPaidTraffic')->willReturn($paidTraffic);

        $stubsCallback($this->websiteSettingsStub, $this->requestInfoStub);

        $googleTagManagerSettings = $this->googleTagManagerSettingsFactory->create();

        self::assertSame($expectedEnabled, $googleTagManagerSettings->enabled);
        self::assertSame($expectedEnabledForRequest, $googleTagManagerSettings->enabledForRequest);
        self::assertSame($expectedGoogleTagManagerId, $googleTagManagerSettings->googleTagManagerId);
    }
}

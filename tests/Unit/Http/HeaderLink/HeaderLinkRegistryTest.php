<?php

declare(strict_types=1);

namespace Tests\Unit\Http\HeaderLink;

use App\Http\Response\HeaderLink\HeaderLink;
use App\Http\Response\HeaderLink\HeaderLinkAs;
use App\Http\Response\HeaderLink\HeaderLinkCrossOrigin;
use App\Http\Response\HeaderLink\HeaderLinkFetchPriority;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\Http\Response\HeaderLink\HeaderLinkRel;
use Symfony\Component\HttpFoundation\Response;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

class HeaderLinkRegistryTest extends PhpUnitTestCase
{
    private HeaderLinkRegistry $headerLinkRegistry;

    private Response $response;

    protected function setUp(): void
    {
        parent::setUp();

        $this->headerLinkRegistry = new HeaderLinkRegistry();
        $this->response = new Response();
    }

    public function testEmpty(): void
    {
        $this->headerLinkRegistry->addToResponse($this->response);

        static::assertNull($this->response->headers->get('Link'));
    }

    public function testAddToResponse(): void
    {
        $this->headerLinkRegistry
            ->add(
                new HeaderLink(
                    url          : 'https://www.google.com/adsense/search/ads.js',
                    rel          : HeaderLinkRel::PRELOAD,
                    as           : HeaderLinkAs::SCRIPT,
                    fetchPriority: HeaderLinkFetchPriority::HIGH,
                ),
            )
            ->add(
                new HeaderLink(
                    url: 'https://syndicatedsearch.goog',
                    rel: HeaderLinkRel::PRECONNECT,
                ),
            )
            ->add(
                new HeaderLink(
                    url        : 'https://www.example.com',
                    rel        : HeaderLinkRel::PRECONNECT,
                    crossOrigin: HeaderLinkCrossOrigin::ANONYMOUS,
                ),
            );
        $this->headerLinkRegistry->addToResponse($this->response);

        $assertionFile = $this->initGenericAssertionFile(
            (string)$this->response->headers->get('Link'),
        );
        $assertionFile->assertSame();
    }
}

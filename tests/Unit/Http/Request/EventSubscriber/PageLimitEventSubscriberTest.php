<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Request\EventSubscriber;

use App\Http\Request\EventSubscriber\PageLimitEventSubscriber;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tests\Stub\Search\Request\SearchRequestStub;

class PageLimitEventSubscriberTest extends TestCase
{
    private SearchRequestStub $searchRequestStub;

    private PageLimitEventSubscriber $pageLimitEventSubscriber;

    protected function setUp(): void
    {
        $this->searchRequestStub = new SearchRequestStub();

        $this->pageLimitEventSubscriber = new PageLimitEventSubscriber(
            searchRequest: $this->searchRequestStub,
        );
    }

    public function testOnKernelRequestWhenPageIsAllowed(): void
    {
        $this->expectNotToPerformAssertions();
        $this->searchRequestStub->setPage(5);

        $this->pageLimitEventSubscriber->onKernelRequest();
    }

    public function testOnKernelRequestWhenPageIsNotAllowed(): void
    {
        $this->expectException(NotFoundHttpException::class);
        $this->searchRequestStub->setPage(10);

        $this->pageLimitEventSubscriber->onKernelRequest();
    }
}

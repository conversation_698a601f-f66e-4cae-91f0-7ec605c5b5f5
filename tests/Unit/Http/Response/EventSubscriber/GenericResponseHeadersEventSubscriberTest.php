<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\GenericResponseHeadersEventSubscriber;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Stub\Http\Request\GenericRequestStub;
use Tests\Stub\Http\Request\Info\RequestInfoStub;

class GenericResponseHeadersEventSubscriberTest extends TestCase
{
    public function testOnResponse(): void
    {
        $requestInfo = new RequestInfoStub();
        $requestInfo->setLocale('en_US');

        $genericRequest = new GenericRequestStub();
        $genericRequest->setPageviewId('pageview-123')
            ->setVisitId('visit-456');

        $subscriber = new GenericResponseHeadersEventSubscriber(
            $requestInfo,
            $genericRequest,
            new HeaderLinkRegistry(),
        );

        $response = new Response();
        $responseEvent = $this->createResponseEvent($response);

        $subscriber->onResponse($responseEvent);

        self::assertSame('en_US', $response->headers->get('X-Log-Locale'));
        self::assertSame('pageview-123', $response->headers->get('X-Log-Pageview_Id'));
        self::assertSame('visit-456', $response->headers->get('X-Log-Visit_Id'));
    }

    private function createResponseEvent(Response $response): ResponseEvent
    {
        return new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );
    }
}

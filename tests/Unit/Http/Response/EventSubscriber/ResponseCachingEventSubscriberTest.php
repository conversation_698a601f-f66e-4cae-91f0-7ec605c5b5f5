<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\ResponseCachingEventSubscriber;
use App\Http\Response\ResponseCachingHelper;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class ResponseCachingEventSubscriberTest extends TestCase
{
    private EventDispatcherInterface & MockObject $eventDispatcherMock;

    private DateTimeFactory $dateTimeFactory;

    private ResponseCachingHelper $responseCachingHelper;

    private ResponseCachingEventSubscriber $subscriber;

    protected function setUp(): void
    {
        $this->eventDispatcherMock = $this->createMock(EventDispatcherInterface::class);
        $this->dateTimeFactory = new DateTimeFactory();
        $this->responseCachingHelper = new ResponseCachingHelper($this->eventDispatcherMock, $this->dateTimeFactory);

        $this->subscriber = new ResponseCachingEventSubscriber(
            $this->responseCachingHelper,
        );
    }

    public function testEnableResponseCachingWhenCachingStarted(): void
    {
        $this->responseCachingHelper->startResponseCaching(60);

        $response = new Response();
        $response->headers->set('X-Log-Test', 'test');
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->enableResponseCaching($responseEvent);

        self::assertNotNull($response->getExpires());
        self::assertEquals(60, $response->getMaxAge());
        self::assertTrue($response->headers->getCacheControlDirective('public'));

        // Verify that X-Log headers were removed
        self::assertFalse($response->headers->has('X-Log-Test'));
    }

    public function testEnableResponseCachingWhenCachingNotStarted(): void
    {
        $response = new Response();
        $response->headers->set('X-Log-Test', 'test');
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->enableResponseCaching($responseEvent);

        self::assertNull($response->getExpires());
        self::assertNull($response->getMaxAge());
        self::assertFalse($response->headers->hasCacheControlDirective('public'));

        self::assertTrue($response->headers->has('X-Log-Test'));
    }

    private function createResponseEvent(Response $response): ResponseEvent
    {
        return new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );
    }
}

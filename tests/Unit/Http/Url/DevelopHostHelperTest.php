<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Url;

use App\Http\Url\DevelopHostHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class DevelopHostHelperTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function modifyHostDataProvider(): array
    {
        return [
            'dev with user host .com'       => [
                'devVmName'                  => 'user',
                'host'                       => 'www.zapmeta.com.user.ldev.nl',
                'expectedWithDevelopHost'    => 'www.zapmeta.com.user.ldev.nl',
                'expectedWithoutDevelopHost' => 'www.zapmeta.com',
            ],
            'dev with user host .de'        => [
                'devVmName'                  => 'harrie',
                'host'                       => 'www.zapmeta.de.harrie.ldev.nl',
                'expectedWithDevelopHost'    => 'www.zapmeta.de.harrie.ldev.nl',
                'expectedWithoutDevelopHost' => 'www.zapmeta.de',
            ],
            'dev without user develop host' => [
                // This situation should not exist in real life
                'devVmName'                  => 'henk',
                'host'                       => 'www.zapmeta.com',
                'expectedWithDevelopHost'    => 'www.zapmeta.com.henk.ldev.nl',
                'expectedWithoutDevelopHost' => 'www.zapmeta.com',
            ],
            'prod'                          => [
                'devVmName'                  => '',
                'host'                       => 'www.zapmeta.com',
                'expectedWithDevelopHost'    => 'www.zapmeta.com',
                'expectedWithoutDevelopHost' => 'www.zapmeta.com',
            ],
            'prod with user develop host'   => [
                // This situation should not exist in real life. No changes to host
                'devVmName'                  => null,
                'host'                       => 'www.zapmeta.com.user.ldev.nl',
                'expectedWithDevelopHost'    => 'www.zapmeta.com.user.ldev.nl',
                'expectedWithoutDevelopHost' => 'www.zapmeta.com.user.ldev.nl',
            ],
        ];
    }

    #[DataProvider('modifyHostDataProvider')]
    public function testModifyHost(
        ?string $devVmName,
        string $host,
        string $expectedWithDevelopHost,
        string $expectedWithoutDevelopHost
    ): void
    {
        $developerHostHelper = new DevelopHostHelper($devVmName);

        $actualWithDevelopHost = $developerHostHelper->addDevelopToHost($host);
        $actualWithoutDevelopHost = $developerHostHelper->removeDevelopFromHost($host);

        self::assertSame($expectedWithDevelopHost, $actualWithDevelopHost);
        self::assertSame($actualWithoutDevelopHost, $expectedWithoutDevelopHost);
        self::assertSame(
            str_contains($expectedWithDevelopHost, '.ldev.nl'),
            $developerHostHelper->isDevelopHost($expectedWithDevelopHost),
        );
    }
}

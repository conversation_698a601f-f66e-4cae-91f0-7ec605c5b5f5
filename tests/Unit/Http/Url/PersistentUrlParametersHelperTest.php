<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Url;

use App\Http\Url\PersistentUrlParametersHelper;
use App\Http\Url\PersistentUrlParametersPageType;
use App\Http\Url\PersistentUrlParametersProviderInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PersistentUrlParametersHelperTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function getPersistentParametersDataProvider(): array
    {
        return [
            'empty'               => [
                'parameterProviderMockArguments' => [],
                'enabled'                        => true,
                'expectedArray'                  => [],
            ],
            'merge parameters'    => [
                'parameterProviderMockArguments' => [
                    [
                        'getPersistentUrlParameters' => [
                            'persistent_1' => '1',
                            'persistent_2' => '2',
                        ],
                    ],
                    [
                        'getPersistentUrlParameters' => [
                            'persistent_3' => '3',
                        ],
                    ],
                ],
                'enabled'                        => true,
                'expectedArray'                  => [
                    'persistent_1' => '1',
                    'persistent_2' => '2',
                    'persistent_3' => '3',
                ],
            ],
            'disabled'            => [
                'parameterProviderMockArguments' => [
                    [
                        'getPersistentUrlParameters' => [
                            'persistent_1' => '1',
                            'persistent_2' => '2',
                        ],
                    ],
                    [
                        'getPersistentUrlParameters' => [
                            'persistent_3' => '3',
                        ],
                    ],
                ],
                'enabled'                        => false,
                'expectedArray'                  => [],
            ],
            'override parameters' => [
                'parameterProviderMockArguments' => [
                    [
                        'getPersistentUrlParameters' => [
                            'persistent_1' => '1',
                            'persistent_2' => '2',
                        ],
                    ],
                    [
                        'getPersistentUrlParameters' => [
                            'persistent_2' => '3',
                        ],
                    ],
                ],
                'enabled'                        => true,
                'expectedArray'                  => [
                    'persistent_1' => '1',
                    'persistent_2' => '3',
                ],
            ],
        ];
    }

    /**
     * @param mixed[]               $parameterProviderMockArguments
     * @param array<string, string> $expectedArray
     */
    #[DataProvider('getPersistentParametersDataProvider')]
    public function testGetPersistentParameters(
        array $parameterProviderMockArguments,
        bool $enabled,
        array $expectedArray
    ): void
    {
        $parameterProviderMocks = [];

        foreach ($parameterProviderMockArguments as $parameterProviderMockArgument) {
            $parameterProviderMocks[] = $this->createConfiguredMock(
                PersistentUrlParametersProviderInterface::class,
                $parameterProviderMockArgument,
            );
        }

        $persistentUrlParametersHelper = new PersistentUrlParametersHelper($parameterProviderMocks);

        if (!$enabled) {
            $persistentUrlParametersHelper->disable();
        }

        $array = $persistentUrlParametersHelper->getPersistentParameters(PersistentUrlParametersPageType::DEFAULT);
        self::assertSame($expectedArray, $array);
    }

    /**
     * @return mixed[]
     */
    public static function pageTypesDataProvider(): array
    {
        $data = [];

        foreach (PersistentUrlParametersPageType::cases() as $pageType) {
            $data[$pageType->value] = [
                'pageType' => $pageType,
            ];
        }

        return $data;
    }

    #[DataProvider('pageTypesDataProvider')]
    public function testPersistentParametersPageTypes(PersistentUrlParametersPageType $pageType): void
    {
        $persistentUrlParametersHelper = new PersistentUrlParametersHelper([]);
        $array = $persistentUrlParametersHelper->getPersistentParameters($pageType);
        self::assertEmpty($array);
    }
}

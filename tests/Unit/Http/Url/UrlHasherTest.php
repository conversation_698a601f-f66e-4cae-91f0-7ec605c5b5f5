<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Url;

use App\Http\Url\UrlHasher;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UrlHasherTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function hashDataProvider(): array
    {
        return [
            [
                'url'          => 'https://www.google.com/',
                'expectedHash' => '4d71a34d60f1deaf9d8a5ae5ad25b8f6',
            ],
            [
                'url'          => 'http://www.google.com/',
                'expectedHash' => 'd4f7f487b4939e6206a5d9ac685ca20f',
            ],
            [
                'url'          => 'https://www.visymo.com/copyright?ref=seekweb',
                'expectedHash' => '5aa99c779964887c4f0dfdf54a8baa00',
            ],
        ];
    }

    #[DataProvider('hashDataProvider')]
    public function testHash(string $url, string $expectedHash): void
    {
        $hasher = new UrlHasher();

        self::assertSame(
            $expectedHash,
            $hasher->hash($url),
        );

        self::assertTrue(
            $hasher->isValid($url, $expectedHash),
        );
    }

    public function testThatSaltIsApplied(): void
    {
        $hasher = new UrlHasher();

        self::assertFalse(
            $hasher->isValid('https://www.visymo.com', md5('https://www.visymo.com')),
        );
    }
}

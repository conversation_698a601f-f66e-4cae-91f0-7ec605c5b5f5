<?php

declare(strict_types=1);

namespace Tests\Unit\InfoPages\Settings;

use App\InfoPages\Page\PageType;
use App\InfoPages\Settings\InfoPagesSettingsFactory;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class InfoPagesSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private InfoPagesSettingsFactory $infoPagesSettingsFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->infoPagesSettingsFactory = new InfoPagesSettingsFactory(
            $this->websiteConfigurationHelperMock,
        );
    }

    public function testEmpty(): void
    {
        $this->setBrandConfig([]);

        $infoPageSettings = $this->infoPagesSettingsFactory->create();

        self::assertSame(PageType::SEARCH, $infoPageSettings->pageType);
        self::assertFalse($infoPageSettings->linkToExternalAboutPage);
        self::assertFalse($infoPageSettings->linkToVisymoPublishing);
    }

    public function testCreate(): void
    {
        $this->setBrandConfig(
            [
                'info_pages' => [
                    'page_type'                   => PageType::CONTENT->value,
                    'link_to_external_about_page' => true,
                    'link_to_visymo_publishing'   => true,
                ],
            ],
        );

        $infoPageSettings = $this->infoPagesSettingsFactory->create();

        self::assertSame(PageType::CONTENT, $infoPageSettings->pageType);
        self::assertTrue($infoPageSettings->linkToExternalAboutPage);
        self::assertTrue($infoPageSettings->linkToVisymoPublishing);
    }
}

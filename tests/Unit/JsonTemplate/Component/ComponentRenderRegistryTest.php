<?php

declare(strict_types=1);

namespace Tests\Unit\JsonTemplate\Component;

use App\Component\Content\ContentPageParagraph\ContentPageParagraphComponent;
use App\Component\Content\ContentPageParagraph\ContentPageParagraphLayout;
use App\Component\Content\ContentPageTitle\ContentPageTitleComponent;
use App\Component\Content\ContentPageTitle\ContentPageTitleLayout;
use App\JsonTemplate\Component\ComponentRenderRegistry;
use PHPUnit\Framework\TestCase;

class ComponentRenderRegistryTest extends TestCase
{
    private ComponentRenderRegistry $componentRenderRegistry;

    protected function setUp(): void
    {
        parent::setUp();

        $this->componentRenderRegistry = new ComponentRenderRegistry();
    }

    public function testComponentRender(): void
    {
        $components = [
            new ContentPageTitleComponent(ContentPageTitleLayout::DEFAULT, []),
            new ContentPageParagraphComponent(
                null,
                0,
                false,
                ContentPageParagraphLayout::DEFAULT,
                [],
            ),
        ];

        foreach ($components as $component) {
            $this->componentRenderRegistry->registerComponentHeaderRender($component);
            self::assertTrue($this->componentRenderRegistry->isComponentHeaderRendered($component));
        }
    }

    public function testComponentStyleRender(): void
    {
        self::assertFalse($this->componentRenderRegistry->isComponentStyleRendered('pizza'));

        $this->componentRenderRegistry->registerComponentStyleRender('pizza');
        $this->componentRenderRegistry->registerComponentStyleRender('hawaii');

        self::assertTrue($this->componentRenderRegistry->isComponentStyleRendered('pizza'));
        self::assertFalse($this->componentRenderRegistry->isComponentStyleRendered('salami'));
    }

    public function testComponentScriptRender(): void
    {
        self::assertFalse($this->componentRenderRegistry->isComponentJavaScriptRendered('pizza'));

        $this->componentRenderRegistry->registerComponentJavaScriptRender('pizza');
        $this->componentRenderRegistry->registerComponentJavaScriptRender('hawaii');

        self::assertTrue($this->componentRenderRegistry->isComponentJavaScriptRendered('pizza'));
        self::assertFalse($this->componentRenderRegistry->isComponentJavaScriptRendered('salami'));
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\JsonTemplate\EventSubscriber;

use App\JsonTemplate\EventSubscriber\JsonTemplateEventSubscriber;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\Template\Options\JsonTemplateOptions;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class JsonTemplateEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsHeader(): void
    {
        $projectDir = '/var/www/html/brand-websites';
        $filePath = $projectDir . '/resources/shared/templates_json/article/article.json';
        $expectedHeader = '/resources/shared/templates_json/article/article.json';

        $jsonTemplate = new JsonTemplate(
            filePath       : $filePath,
            variant        : null,
            options        : new JsonTemplateOptions(
                                 keywordHighlight       : null,
                                 organicKeywordHighlight: null,
                                 organicLinkType        : null,
                                 pageHeadTagsType       : 'article',
                             ),
            layoutTemplate : '@theme/layout_default_components.html.twig',
            componentConfig: [],
        );

        $response = new Response();
        $view = $this->createMock(ViewInterface::class);
        $view->method('getResponse')->willReturn($response);
        $view->method('getJsonTemplate')->willReturn($jsonTemplate);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new JsonTemplateEventSubscriber($projectDir);
        $subscriber->onJsonTemplateViewCreated($event);

        $this->assertSame($expectedHeader, $response->headers->get('X-Log-Json_Template_File'));
    }
}


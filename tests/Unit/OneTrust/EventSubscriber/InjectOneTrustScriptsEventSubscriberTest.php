<?php

declare(strict_types=1);

namespace Tests\Unit\OneTrust\EventSubscriber;

use App\OneTrust\EventSubscriber\InjectOneTrustScriptsEventSubscriber;
use App\OneTrust\Helper\OneTrustHelper;
use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

final class InjectOneTrustScriptsEventSubscriberTest extends TestCase
{
    public function testRenderTemplateHeadersAddsItemWhenEnabled(): void
    {
        $oneTrustHelper = $this->createMock(OneTrustHelper::class);
        $splitTestReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $twig = $this->createMock(Environment::class);

        $oneTrustHelper->method('isEnabled')->willReturn(true);
        $oneTrustHelper->method('getDomainScriptId')->willReturn('domain-script-id');
        $splitTestReader->method('isVariantActive')->willReturnMap([
            ['enc', false],
            ['encwi', false],
        ]);
        $twig->method('render')->willReturn('rendered');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectOneTrustScriptsEventSubscriber($oneTrustHelper, $splitTestReader, $twig);
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered', $event->getItems()[0]);
    }

    public function testRenderTemplateHeadersDoesNothingWhenDisabled(): void
    {
        $oneTrustHelper = $this->createMock(OneTrustHelper::class);
        $splitTestReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $twig = $this->createMock(Environment::class);

        $oneTrustHelper->method('isEnabled')->willReturn(false);

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectOneTrustScriptsEventSubscriber($oneTrustHelper, $splitTestReader, $twig);
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }
}
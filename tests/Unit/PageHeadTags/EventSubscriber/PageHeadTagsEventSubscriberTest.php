<?php

declare(strict_types=1);

namespace Tests\Unit\PageHeadTags\EventSubscriber;

use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\ViewInterface;
use App\PageHeadTags\EventSubscriber\PageHeadTagsEventSubscriber;
use App\PageHeadTags\Tags\PageHeadTags;
use App\PageHeadTags\Tags\PageHeadTagsHelper;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

final class PageHeadTagsEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateHandledCallsInitFromView(): void
    {
        $twig = $this->createMock(Environment::class);
        $pageHeadTagsHelper = $this->createMock(PageHeadTagsHelper::class);
        $view = $this->createMock(ViewInterface::class);

        $event = new JsonTemplateHandledEvent($view);

        $pageHeadTagsHelper->expects($this->once())
            ->method('initFromView')
            ->with($view);

        $subscriber = new PageHeadTagsEventSubscriber($twig, $pageHeadTagsHelper);
        $subscriber->onJsonTemplateHandled($event);
    }

    public function testRenderTemplateHeadersAddsRenderedItem(): void
    {
        $twig = $this->createMock(Environment::class);
        $pageHeadTagsHelper = $this->createMock(PageHeadTagsHelper::class);
        $pageHeadTags = new PageHeadTags(title: 'Test Title');

        $pageHeadTagsHelper->method('getPageHeadTags')->willReturn($pageHeadTags);
        $twig->method('render')
            ->with('@theme/page_head_tags/page_head_tags.html.twig', ['page_head_tags' => $pageHeadTags])
            ->willReturn('rendered_content');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new PageHeadTagsEventSubscriber($twig, $pageHeadTagsHelper);
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_content', $event->getItems()[0]);
    }
}
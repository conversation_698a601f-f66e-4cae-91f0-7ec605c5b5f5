<?php

declare(strict_types=1);

namespace Tests\Unit\PageviewConversion\EventSubscriber;

use App\PageviewConversion\EventSubscriber\InjectPageviewConversionEventSubscriber;
use App\PageviewConversion\Settings\PageviewConversionSettings;
use App\Template\Event\RenderTemplateFootersEvent;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class InjectPageviewConversionEventSubscriberTest extends TestCase
{
    public function testInjectPageviewConversionAddsItemWhenEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $searchRequest = $this->createMock(\App\Search\Request\SearchRequestInterface::class);
        $urlGenerator = $this->createMock(\App\PageviewConversion\Url\PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled: true,
            enabledForRequest: true
        );
        $searchRequest->method('isLandingPage')->willReturn(true);
        $urlGenerator->method('generateLanding')->willReturn('url');
        $twig->method('render')->willReturn('rendered');

        $event = $this->createMock(RenderTemplateFootersEvent::class);
        $event->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectPageviewConversionEventSubscriber($twig, $searchRequest, $urlGenerator, $settings);
        $subscriber->injectPageviewConversion($event);
    }

    public function testInjectPageviewConversionDoesNothingIfNotEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $searchRequest = $this->createMock(\App\Search\Request\SearchRequestInterface::class);
        $urlGenerator = $this->createMock(\App\PageviewConversion\Url\PageviewConversionUrlGenerator::class);
        $settings = new PageviewConversionSettings(
            enabled: true,
            enabledForRequest: false
        );
        $event = $this->createMock(RenderTemplateFootersEvent::class);
        $event->expects($this->never())->method('addItem');

        $subscriber = new InjectPageviewConversionEventSubscriber($twig, $searchRequest, $urlGenerator, $settings);
        $subscriber->injectPageviewConversion($event);
    }
}


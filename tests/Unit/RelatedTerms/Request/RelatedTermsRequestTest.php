<?php

declare(strict_types=1);

namespace Tests\Unit\RelatedTerms\Request;

use App\Http\Url\PersistentUrlParametersPageType;
use App\RelatedTerms\RelatedTermsUrlParametersProvider;
use App\RelatedTerms\Request\RelatedTermsRequest;
use App\RelatedTerms\Request\RelatedTermsRequestInterface;
use App\Search\Query\SearchQueryNormalizer;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Unit\Http\Request\AbstractRequestTestCase;

class RelatedTermsRequestTest extends AbstractRequestTestCase
{
    /**
     * @return mixed[]
     */
    public static function urlDataProvider(): array
    {
        return [
            'complete'               => [
                'url'                   => '/?arq=ipad+air&rkb=i&rkln=4&terms=test,test,test2,test3,test2,test3&dummy=gone',
                'expectedUrlParameters' => [
                    RelatedTermsRequestInterface::PARAMETER_ALTERNATE_RELATED_QUERY  => 'ipad air',
                    RelatedTermsRequestInterface::PARAMETER_TERMS                    => ['test', 'test2', 'test3'],
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_ZONE       => 'i',
                    RelatedTermsRequestInterface::PARAMETER_RELATED_TERMS_LINK_INDEX => 4,
                ],
            ],
            'empty values'           => [
                'url'                   => '/?arq=%20%20%20&terms=%20%20%20&rkb&rkln=0',
                'expectedUrlParameters' => [],
            ],
            'invalid values'         => [
                'url'                   => '/?arq=query&rkb=invalid&rkln=invalid',
                'expectedUrlParameters' => [
                    RelatedTermsRequestInterface::PARAMETER_ALTERNATE_RELATED_QUERY => 'query',
                ],
            ],
            'html5 entity and emoji' => [
                'url'                   => '/?arq=query+%26apos%3B😎&terms=query+%26apos%3B😎',
                'expectedUrlParameters' => [
                    RelatedTermsRequestInterface::PARAMETER_ALTERNATE_RELATED_QUERY => 'query \'😎',
                    RelatedTermsRequestInterface::PARAMETER_TERMS                   => ['query \'😎'],
                ],
            ],
            'none'                   => [
                'url'                   => '/',
                'expectedUrlParameters' => [],
            ],
        ];
    }

    /**
     * @param array<string, mixed> $expectedUrlParameters
     */
    #[DataProvider('urlDataProvider')]
    public function testUrlParameters(string $url, array $expectedUrlParameters): void
    {
        $this->setMainRequestWithUrl($url);

        $expectedAlternateRelatedQuery = $expectedUrlParameters[RelatedTermsRequestInterface::PARAMETER_ALTERNATE_RELATED_QUERY] ?? null;
        $searchRequestStub = (new SearchRequestStub())->setIsLandingPage(true);

        $relatedTermsRequest = new RelatedTermsRequest(
            requestManager           : $this->requestManager,
            requestPropertyNormalizer: $this->requestPropertyNormalizer,
            searchQueryNormalizer    : new SearchQueryNormalizer(),
        );

        $actualUrlParameters = $relatedTermsRequest->getUrlParameters();

        // The order of parameters is irrelevant
        ksort($expectedUrlParameters);
        ksort($actualUrlParameters);

        self::assertSame($expectedUrlParameters, $actualUrlParameters);

        $parametersProvider = new RelatedTermsUrlParametersProvider($relatedTermsRequest, $searchRequestStub);
        $actualPersistentUrlParameters = $parametersProvider->getPersistentUrlParameters(
            PersistentUrlParametersPageType::RELATED_TERMS,
        );
        $expectedPersistentUrlParameters = array_filter(
            [
                RelatedTermsRequestInterface::PARAMETER_ALTERNATE_RELATED_QUERY => $expectedAlternateRelatedQuery,
            ],
            static fn ($value) => $value !== null,
        );

        self::assertSame($expectedPersistentUrlParameters, $actualPersistentUrlParameters);
    }
}

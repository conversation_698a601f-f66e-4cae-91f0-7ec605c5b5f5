<?php

declare(strict_types=1);

namespace Tests\Unit\Search\EventSubscriber;

use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use App\Search\EventSubscriber\SearchEventSubscriber;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;

class SearchEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsQuery(): void
    {
        $seaRequest = new SeaRequestStub();
        $seaRequest->setUserQuery('sea-query');

        $searchRequest = new SearchRequestStub();
        $searchRequest->setQuery('search-query');

        $blocker = $this->createMock(TrademarkInfringementResultBlocker::class);
        $blocker->method('blockResults')->willReturn(false);

        // Create real instances since ViewDataRequest is final
        $dataRequest = new ViewDataRequest();
        $dataRegistry = new ViewDataRegistry();

        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);
        $view->method('getDataRegistry')->willReturn($dataRegistry);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new SearchEventSubscriber($seaRequest, $searchRequest, $blocker);
        $subscriber->onJsonTemplateViewCreated($event);

        // Verify that the query was set on both data request and data registry
        self::assertSame('sea-query', $dataRequest->getQuery());
        self::assertSame('sea-query', $dataRegistry->getQuery());
    }
}

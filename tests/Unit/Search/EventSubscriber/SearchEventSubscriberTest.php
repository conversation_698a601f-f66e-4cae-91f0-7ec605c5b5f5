<?php

declare(strict_types=1);

namespace Tests\Unit\Search\EventSubscriber;

use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\ViewInterface;
use App\Search\EventSubscriber\SearchEventSubscriber;
use PHPUnit\Framework\TestCase;

class SearchEventSubscriberTest extends TestCase
{
    public function testOnJsonTemplateViewCreatedSetsQuery(): void
    {
        $seaRequest = $this->createMock(\App\Tracking\Request\SeaRequestInterface::class);
        $searchRequest = $this->createMock(\App\Search\Request\SearchRequestInterface::class);
        $blocker = $this->createMock(\App\TrademarkInfringement\TrademarkInfringementResultBlocker::class);
        $blocker->method('blockResults')->willReturn(false);
        $seaRequest->method('getUserQuery')->willReturn('sea-query');
        $searchRequest->method('getQuery')->willReturn('search-query');

        // Create real instances since ViewDataRequest is final
        $dataRequest = new ViewDataRequest();
        $dataRegistry = new ViewDataRegistry();

        $view = $this->createMock(ViewInterface::class);
        $view->method('getDataRequest')->willReturn($dataRequest);
        $view->method('getDataRegistry')->willReturn($dataRegistry);

        $event = new JsonTemplateViewCreatedEvent($view);
        $subscriber = new SearchEventSubscriber($seaRequest, $searchRequest, $blocker);
        $subscriber->onJsonTemplateViewCreated($event);

        // Verify that the query was set on both data request and data registry
        self::assertSame('sea-query', $dataRequest->getQuery());
        self::assertSame('sea-query', $dataRegistry->getQuery());
    }
}


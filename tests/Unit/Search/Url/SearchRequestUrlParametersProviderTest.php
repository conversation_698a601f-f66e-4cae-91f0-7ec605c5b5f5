<?php

declare(strict_types=1);

namespace Tests\Unit\Search\Url;

use App\Http\Url\PersistentUrlParametersPageType;
use App\Search\Request\SearchRequestInterface;
use App\Search\Url\SearchUrlParametersProvider;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Search\Request\SearchRequestStub;

class SearchRequestUrlParametersProviderTest extends TestCase
{
    private TrademarkInfringementResultBlocker & MockObject $trademarkInfringementResultBlocker;

    public function setUp(): void
    {
        $this->trademarkInfringementResultBlocker = $this->createMock(
            TrademarkInfringementResultBlocker::class,
        );
    }

    /**
     * @return mixed[]
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        return [
            'empty'                   => [
                'searchRequestStub'     => (new SearchRequestStub())
                    ->setQuery(null),
                'expectedUrlParameters' => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [],
                ],
                'blockResult'           => false,
            ],
            'not empty'               => [
                'searchRequestStub'     => (new SearchRequestStub())
                    ->setQuery('ipad air'),
                'expectedUrlParameters' => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [
                        SearchRequestInterface::PARAMETER_QUERY => 'ipad air',
                    ],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [
                        SearchRequestInterface::PARAMETER_QUERY => 'ipad air',
                    ],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [],
                ],
                'blockResult'           => false,
            ],
            'empty with block result' => [
                'searchRequestStub'     => (new SearchRequestStub())
                    ->setQuery('ohama steaks'),
                'expectedUrlParameters' => [
                    PersistentUrlParametersPageType::DEFAULT->value             => [],
                    PersistentUrlParametersPageType::NEW_SEARCH->value          => [],
                    PersistentUrlParametersPageType::CONVERSION_TRACKING->value => [],
                    PersistentUrlParametersPageType::RELATED_TERMS->value       => [],
                ],
                'blockResult'           => true,
            ],
        ];
    }

    /**
     * @param mixed[] $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(
        SearchRequestStub $searchRequestStub,
        array $expectedUrlParameters,
        bool $blockResult
    ): void
    {
        $provider = new SearchUrlParametersProvider(
            $searchRequestStub,
            $this->trademarkInfringementResultBlocker,
        );
        $this->trademarkInfringementResultBlocker->method('blockResults')->willReturn($blockResult);

        foreach (PersistentUrlParametersPageType::cases() as $pageType) {
            // Assert parameters are set for page type
            self::assertArrayHasKey(
                $pageType->value,
                $expectedUrlParameters,
                sprintf('Expected parameters set for page type %s', $pageType->value),
            );
            // Assert the output stays the same each time and the request is accessed once for information
            self::assertSame(
                $expectedUrlParameters[$pageType->value],
                $provider->getPersistentUrlParameters($pageType),
                sprintf('Expected parameters for page type %s', $pageType->value),
            );
            self::assertSame(
                $expectedUrlParameters[$pageType->value],
                $provider->getPersistentUrlParameters($pageType),
                sprintf('Second time expected parameters for page type %s', $pageType->value),
            );
        }
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\SearchHistory\EventSubscriber;

use App\JsonTemplate\Event\JsonTemplateSearchSubmittedEvent;
use App\SearchHistory\EventSubscriber\BuildSearchHistoryEventSubscriber;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class BuildSearchHistoryEventSubscriberTest extends TestCase
{
    public function testAddDefaultSearchQueryAddsQueryWhenAllowed(): void
    {
        $searchHistoryHelper = $this->createMock(\App\SearchHistory\Helper\SearchHistoryHelper::class);
        $preferencesHelper = $this->createMock(\App\Preferences\Helper\PreferencesHelper::class);
        $adBotRequest = $this->createMock(\App\AdBot\Request\AdBotRequestInterface::class);

        $adBotRequest->method('isAdBot')->willReturn(false);
        $preferencesHelper->method('getPreviousSearches')->willReturn(true);
        $response = new Response();
        $searchHistoryHelper->expects($this->once())
            ->method('addQuery')
            ->with('query', $response);

        $event = new JsonTemplateSearchSubmittedEvent('query', $response);
        $subscriber = new BuildSearchHistoryEventSubscriber($searchHistoryHelper, $preferencesHelper, $adBotRequest);
        $subscriber->addDefaultSearchQuery($event);
    }

    public function testAddDefaultSearchQueryDoesNothingForAdBot(): void
    {
        $searchHistoryHelper = $this->createMock(\App\SearchHistory\Helper\SearchHistoryHelper::class);
        $preferencesHelper = $this->createMock(\App\Preferences\Helper\PreferencesHelper::class);
        $adBotRequest = $this->createMock(\App\AdBot\Request\AdBotRequestInterface::class);

        $adBotRequest->method('isAdBot')->willReturn(true);
        $searchHistoryHelper->expects($this->never())->method('addQuery');

        $event = new JsonTemplateSearchSubmittedEvent('query', new Response());
        $subscriber = new BuildSearchHistoryEventSubscriber($searchHistoryHelper, $preferencesHelper, $adBotRequest);
        $subscriber->addDefaultSearchQuery($event);
    }

    public function testAddDefaultSearchQueryDoesNothingIfHistoryNotEnabled(): void
    {
        $searchHistoryHelper = $this->createMock(\App\SearchHistory\Helper\SearchHistoryHelper::class);
        $preferencesHelper = $this->createMock(\App\Preferences\Helper\PreferencesHelper::class);
        $adBotRequest = $this->createMock(\App\AdBot\Request\AdBotRequestInterface::class);

        $adBotRequest->method('isAdBot')->willReturn(false);
        $preferencesHelper->method('getPreviousSearches')->willReturn(false);
        $searchHistoryHelper->expects($this->never())->method('addQuery');

        $event = new JsonTemplateSearchSubmittedEvent('query', new Response());
        $subscriber = new BuildSearchHistoryEventSubscriber($searchHistoryHelper, $preferencesHelper, $adBotRequest);
        $subscriber->addDefaultSearchQuery($event);
    }
}


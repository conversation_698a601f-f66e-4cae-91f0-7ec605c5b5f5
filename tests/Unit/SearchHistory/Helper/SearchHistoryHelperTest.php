<?php

declare(strict_types=1);

namespace Tests\Unit\SearchHistory\Helper;

use App\SearchHistory\Helper\SearchHistoryHelper;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class SearchHistoryHelperTest extends TestCase
{
    private MockObject & RequestStack $requestStackMock;

    private MockObject & LoggerInterface $loggerMock;

    protected function setUp(): void
    {
        $this->requestStackMock = $this->createMock(RequestStack::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
    }

    /**
     * @return mixed[]
     */
    public static function cookieValueDataProvider(): array
    {
        return [
            'valid, empty array'        => [
                'cookieContent' => '[]',
                'expectedJson'  => '[]',
                'loggerWarning' => null,
            ],
            'valid, with values'        => [
                'cookieContent' => '["hello world", [], {}, "test"]',
                'expectedJson'  => '["hello world","test"]',
                'loggerWarning' => null,
            ],
            'valid, null value'         => [
                'cookieContent' => null,
                'expectedJson'  => '[]',
                'loggerWarning' => null,
            ],
            'valid, json object'        => [
                'cookieContent' => '{}',
                'expectedJson'  => '[]',
                'loggerWarning' => null,
            ],
            'valid, empty string value' => [
                'cookieContent' => '',
                'expectedJson'  => '[]',
                'loggerWarning' => null,
            ],
            'invalid, xml content'      => [
                'cookieContent' => '<xml>hello</xml>',
                'expectedJson'  => '[]',
                'loggerWarning' => 'Caught JsonException',
            ],
        ];
    }

    #[DataProvider('cookieValueDataProvider')]
    public function testCookieValue(?string $cookieContent, string $expectedJson, ?string $loggerWarning): void
    {
        $request = new Request();

        if ($cookieContent !== null) {
            $request->cookies->set('hist', $cookieContent);
        }

        $this->requestStackMock->method('getMainRequest')->willReturn($request);

        if ($loggerWarning === null) {
            $this->loggerMock->expects(self::never())->method('notice');
        } else {
            $this->loggerMock->expects(self::atLeastOnce())->method('notice')->with(
                ...[self::stringContains($loggerWarning)],
            );
        }

        $searchHistoryHelper = new SearchHistoryHelper(
            $this->requestStackMock,
            $this->loggerMock,
            new DateTimeFactory(),
        );
        self::assertSame($expectedJson, $searchHistoryHelper->getJson());
    }

    /**
     * @return mixed[]
     */
    public static function addQueryDataProvider(): array
    {
        return [
            'slice'      => [
                'cookieValues'    => range(0, 20),
                'addQueries'      => ['21', '3'],
                'expectedHistory' => ['3', '21', '0', '1', '2', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'],
            ],
            'duplicates' => [
                'cookieValues'    => ['Hello', 'hello', 'HeLLo World', 'hi'],
                'addQueries'      => ['hello world'],
                'expectedHistory' => ['hello world', 'Hello', 'hello', 'hi'],
            ],
        ];
    }

    /**
     * @param array<int,string> $cookieValues
     * @param string[]          $addQueries
     * @param string[]          $expectedHistory
     */
    #[DataProvider('addQueryDataProvider')]
    public function testAddQuery(array $cookieValues, array $addQueries, array $expectedHistory): void
    {
        $request = new Request();
        $request->cookies->set('hist', json_encode($cookieValues, JSON_THROW_ON_ERROR));
        $this->requestStackMock->method('getMainRequest')->willReturn($request);
        $response = new Response();

        $searchHistoryHelper = new SearchHistoryHelper($this->requestStackMock, $this->loggerMock, new DateTimeFactory());

        foreach ($addQueries as $query) {
            $searchHistoryHelper->addQuery($query, $response);
        }

        self::assertSame($expectedHistory, $searchHistoryHelper->getHistory());
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\SpamClickDetect\EventSubscriber;

use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\SpamClickDetect\EventSubscriber\InjectAdClickSpamDetectEventSubscriber;
use App\SpamClickDetect\Settings\SpamClickDetectSettings;
use App\Template\Event\RenderTemplateHeadersEvent;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class InjectAdClickSpamDetectEventSubscriberTest extends TestCase
{
    public function testRenderTemplateHeadersAddsItemWhenEnabledAndHasUnits(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $twig = $this->createMock(Environment::class);
        $settings = new SpamClickDetectSettings(
            enabled: true,
            enabledForRequest: true
        );
        $adClickCounterHelper = $this->createMock(\App\ConversionTracking\Helper\AdClickCounterHelper::class);
        $googleCsa = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['ads'])
            ->getMock();
        $ads = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['hasUnits'])
            ->getMock();
        $ads->method('hasUnits')->willReturn(true);
        $googleCsa->method('ads')->willReturn($ads);
        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $adClickCounterHelper->method('getGenericAdClickCount')->willReturn(5);
        $twig->method('render')->willReturn('rendered');

        $event = $this->createMock(RenderTemplateHeadersEvent::class);
        $event->expects($this->once())->method('addItem')->with('rendered');

        $subscriber = new InjectAdClickSpamDetectEventSubscriber(
            $googleCsaRegistry,
            $twig,
            $settings,
            $adClickCounterHelper
        );
        $subscriber->renderTemplateHeaders($event);
    }

    public function testRenderTemplateHeadersDoesNothingIfNotEnabled(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $twig = $this->createMock(Environment::class);
        $settings = new SpamClickDetectSettings(
            enabled: true,
            enabledForRequest: false
        );
        $adClickCounterHelper = $this->createMock(\App\ConversionTracking\Helper\AdClickCounterHelper::class);
        $event = $this->createMock(RenderTemplateHeadersEvent::class);
        $event->expects($this->never())->method('addItem');

        $subscriber = new InjectAdClickSpamDetectEventSubscriber(
            $googleCsaRegistry,
            $twig,
            $settings,
            $adClickCounterHelper
        );
        $subscriber->renderTemplateHeaders($event);
    }

    public function testRenderTemplateHeadersDoesNothingIfNoUnits(): void
    {
        $googleCsaRegistry = new GoogleCsaRegistry();
        $twig = $this->createMock(Environment::class);
        $settings = new SpamClickDetectSettings(
            enabled: true,
            enabledForRequest: true
        );
        $adClickCounterHelper = $this->createMock(\App\ConversionTracking\Helper\AdClickCounterHelper::class);
        $googleCsa = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['ads'])
            ->getMock();
        $ads = $this->getMockBuilder(\stdClass::class)
            ->addMethods(['hasUnits'])
            ->getMock();
        $ads->method('hasUnits')->willReturn(false);
        $googleCsa->method('ads')->willReturn($ads);
        $googleCsaRegistry->setGoogleCsa($googleCsa);
        $event = $this->createMock(RenderTemplateHeadersEvent::class);
        $event->expects($this->never())->method('addItem');

        $subscriber = new InjectAdClickSpamDetectEventSubscriber(
            $googleCsaRegistry,
            $twig,
            $settings,
            $adClickCounterHelper
        );
        $subscriber->renderTemplateHeaders($event);
    }
}


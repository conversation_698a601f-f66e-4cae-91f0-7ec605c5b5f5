<?php

declare(strict_types=1);

namespace Tests\Unit\Statistics\EventSubscriber;

use App\Statistics\EventSubscriber\CreateStatisticsLogEventSubscriber;
use App\Statistics\Helper\StatisticsLogEnabledHelper;
use App\Statistics\Helper\StatisticsLogHelper;
use App\Statistics\Provider\Event\StatisticsLogCreateEvent;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

class CreateStatisticsLogEventSubscriberTest extends PhpUnitTestCase
{
    public function testCreateStatisticsLogEventSubscriber(): void
    {
        $kernel = $this->createMock(HttpKernelInterface::class);
        $eventDispatcherMock = $this->createMock(EventDispatcherInterface::class);
        $statisticsLogEnabledHelper = $this->createMock(StatisticsLogEnabledHelper::class);
        $statisticsLogEnabledHelper->method('isLogCreateEnabled')->willReturn(true);
        $statisticsLogHelper = $this->createMock(StatisticsLogHelper::class);
        $statisticsLogHelper->expects($this->once())
            ->method('createStatisticsLog')
            ->with([]);
        $request = new Request();
        $request->setMethod('HEAD');
        $response = new Response();

        $responseEvent = new ResponseEvent(
            $kernel,
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );

        $eventDispatcherMock->expects($this->once())
            ->method('dispatch')
            ->with(
                self::isInstanceOf(StatisticsLogCreateEvent::class),
                self::identicalTo(StatisticsLogCreateEvent::NAME),
            );

        $createStatisticsLogEventSubscriber = new CreateStatisticsLogEventSubscriber(
            statisticsLogEnabledHelper: $statisticsLogEnabledHelper,
            statisticsLogHelper       : $statisticsLogHelper,
            eventDispatcher           : $eventDispatcherMock,
        );

        $createStatisticsLogEventSubscriber->onKernelResponse($responseEvent);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Statistics\Helper;

use App\FriendlyBot\Bot\FriendlyBot;
use App\Statistics\Helper\StatisticsLogEnabledHelper;
use App\Statistics\Helper\StatisticsRequestFlag;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Stub\AdBot\Request\AdBotRequestStub;
use Tests\Stub\FriendlyBot\Request\FriendlyBotRequestStub;
use Tests\Stub\Http\Request\GenericRequestStub;
use Tests\Stub\Search\Request\SearchRequestStub;
use Tests\Unit\Http\Request\AbstractRequestTestCase;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;

class StatisticsLogEnabledHelperTest extends AbstractRequestTestCase
{
    private AdBotRequestStub $adBotRequestStub;

    private FriendlyBotRequestStub $friendlyBotRequestStub;

    private GenericRequestStub $genericRequestStub;

    private DateTimeFactoryStub $dateTimeFactoryStub;

    private SearchRequestStub $searchRequestStub;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adBotRequestStub = new AdBotRequestStub();
        $this->friendlyBotRequestStub = new FriendlyBotRequestStub();
        $this->genericRequestStub = new GenericRequestStub();
        $this->dateTimeFactoryStub = new DateTimeFactoryStub();
        $this->searchRequestStub = new SearchRequestStub();
    }

    /**
     * @return mixed[]
     */
    public static function isLogCreateEnabledDataProvider(): array
    {
        return [
            'ad bot with query and route'    => [
                'isAdBot'        => true,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'hasQuery'       => true,
                'logEnabledFlag' => false,
                'route'          => 'route_search',
                'expectedResult' => false,
            ],
            'user without query and route'   => [
                'isAdBot'        => false,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'hasQuery'       => false,
                'logEnabledFlag' => false,
                'route'          => 'route_search',
                'expectedResult' => false,
            ],
            'user without query wrong route' => [
                'isAdBot'        => false,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'hasQuery'       => false,
                'logEnabledFlag' => false,
                'route'          => 'route_info_pages_about',
                'expectedResult' => false,
            ],
            'user with query and route'      => [
                'isAdBot'        => false,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'hasQuery'       => true,
                'logEnabledFlag' => true,
                'route'          => 'route_search',
                'expectedResult' => true,
            ],
            'generic friendly bot'           => [
                'isAdBot'        => false,
                'isFriendlyBot'  => true,
                'friendlyBot'    => FriendlyBot::GENERIC,
                'hasQuery'       => true,
                'logEnabledFlag' => true,
                'route'          => 'route_search',
                'expectedResult' => false,
            ],
            'apify friendly bot'             => [
                'isAdBot'        => false,
                'isFriendlyBot'  => true,
                'friendlyBot'    => FriendlyBot::APIFY,
                'hasQuery'       => true,
                'logEnabledFlag' => true,
                'route'          => 'route_search',
                'expectedResult' => true,
            ],
        ];
    }

    #[DataProvider('isLogCreateEnabledDataProvider')]
    public function testIsLogCreateEnabled(
        bool $isAdBot,
        bool $isFriendlyBot,
        ?FriendlyBot $friendlyBot,
        bool $hasQuery,
        bool $logEnabledFlag,
        string $route,
        bool $expectedResult
    ): void
    {
        $request = $this->setMainRequestWithUrl('/', $isAdBot, $route);
        $request->attributes->add(
            [
                StatisticsRequestFlag::LOG_ENABLED => $logEnabledFlag,
            ],
        );

        $this->adBotRequestStub->setIsAdBot($isAdBot);

        $this->friendlyBotRequestStub->setIsFriendlyBot($isFriendlyBot);
        $this->friendlyBotRequestStub->setFriendlyBot($friendlyBot);

        $this->searchRequestStub->setQuery($hasQuery ? 'query' : null);

        $statisticsLogEnabledHelper = new StatisticsLogEnabledHelper(
            adBotRequest      : $this->adBotRequestStub,
            genericRequest    : $this->genericRequestStub,
            searchRequest     : $this->searchRequestStub,
            dateTimeFactory   : $this->dateTimeFactoryStub,
            requestManager    : $this->requestManager,
            friendlyBotRequest: $this->friendlyBotRequestStub,
        );

        self::assertSame($expectedResult, $statisticsLogEnabledHelper->isLogCreateEnabled());
    }

    /**
     * @return mixed[]
     */
    public static function isLogUpdateEnabledDataProvider(): array
    {
        return [
            'ad bot'                  => [
                'isAdBot'        => true,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'isOldUpdate'    => false,
                'expectedResult' => false,
            ],
            'user with old update'    => [
                'isAdBot'        => false,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'isOldUpdate'    => true,
                'expectedResult' => false,
            ],
            'user with recent update' => [
                'isAdBot'        => false,
                'isFriendlyBot'  => false,
                'friendlyBot'    => null,
                'isOldUpdate'    => false,
                'expectedResult' => true,
            ],
            'generic friendly bot'    => [
                'isAdBot'        => false,
                'isFriendlyBot'  => true,
                'friendlyBot'    => FriendlyBot::GENERIC,
                'isOldUpdate'    => false,
                'expectedResult' => false,
            ],
            'apify friendly bot'      => [
                'isAdBot'        => false,
                'isFriendlyBot'  => true,
                'friendlyBot'    => FriendlyBot::APIFY,
                'isOldUpdate'    => false,
                'expectedResult' => true,
            ],
        ];
    }

    #[DataProvider('isLogUpdateEnabledDataProvider')]
    public function testIsLogUpdateEnabled(
        bool $isAdBot,
        bool $isFriendlyBot,
        ?FriendlyBot $friendlyBot,
        bool $isOldUpdate,
        bool $expectedResult
    ): void
    {
        $dateTime = new \DateTime('2000-01-23 12:34:56', TimezoneEnum::UTC->toDateTimeZone());
        $appTimestampOffsetMinutes = $isOldUpdate ? 10 : 2;
        $appTimestamp = $dateTime->getTimestamp() - $appTimestampOffsetMinutes * 60;
        $this->dateTimeFactoryStub->setDateTime($dateTime);
        $this->setMainRequestWithUrl(sprintf('/?app_ts=%u', $appTimestamp), $isAdBot);

        $this->adBotRequestStub->setIsAdBot($isAdBot);
        $this->friendlyBotRequestStub->setIsFriendlyBot($isFriendlyBot);
        $this->friendlyBotRequestStub->setFriendlyBot($friendlyBot);
        $this->genericRequestStub->setAppTs($appTimestamp);

        $statisticsLogEnabledHelper = new StatisticsLogEnabledHelper(
            adBotRequest      : $this->adBotRequestStub,
            genericRequest    : $this->genericRequestStub,
            searchRequest     : $this->searchRequestStub,
            dateTimeFactory   : $this->dateTimeFactoryStub,
            requestManager    : $this->requestManager,
            friendlyBotRequest: $this->friendlyBotRequestStub,
        );

        self::assertSame($expectedResult, $statisticsLogEnabledHelper->isLogUpdateEnabled());
    }
}

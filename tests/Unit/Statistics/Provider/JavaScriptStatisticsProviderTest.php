<?php

declare(strict_types=1);

namespace Tests\Unit\Statistics\Provider;

use App\Statistics\Provider\JavaScriptStatisticsProvider;
use App\Statistics\Provider\JavaScriptStatisticsResolver;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

class JavaScriptStatisticsProviderTest extends AbstractStatisticsProviderTestCase
{
    private JavaScriptStatisticsResolver $javaScriptStatisticsResolver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->javaScriptStatisticsResolver = new JavaScriptStatisticsResolver(
            new SymfonyOptionsResolverBridge(new OptionsResolver()),
            $this->genericRequestMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function payloadDataProvider(): array
    {
        $pageviewId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'is_enabled'   => [
                'payload'            => [
                    'key'  => 'j',
                    'pvid' => $pageviewId,
                    'e'    => true,
                    'nw'   => [],
                ],
                'expectedStatistics' => [
                    'visit_id'    => self::$visitId,
                    'pageview_id' => $pageviewId,
                    'javascript'  => [
                        'is_enabled' => true,
                        'network'    => [],
                    ],
                ],
            ],
            'extra'        => [
                'payload'            => [
                    'extra' => true,
                    'key'   => 'j',
                    'pvid'  => $pageviewId,
                    'e'     => true,
                    'nw'    => [
                        'net' => '4g',
                    ],
                ],
                'expectedStatistics' => [
                    'visit_id'    => self::$visitId,
                    'pageview_id' => $pageviewId,
                    'javascript'  => [
                        'is_enabled' => true,
                        'network'    => [
                            'effective_type' => '4g',
                        ],
                    ],
                ],
            ],
            'bad'          => [
                'payload'            => [
                    'key'  => 'j',
                    'pvid' => $pageviewId,
                    'bad'  => true,
                ],
                'expectedStatistics' => null,
            ],
            'bad disabled' => [
                'payload'            => [
                    'key'      => 'j',
                    'visit_id' => self::$visitId,
                    'pvid'     => $pageviewId,
                    'e'        => false,
                ],
                'expectedStatistics' => null,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $payload
     * @param mixed[]|null         $expectedStatistics
     */
    #[DataProvider('payloadDataProvider')]
    public function testGetFromPayload(array $payload, ?array $expectedStatistics): void
    {
        if ($expectedStatistics === null) {
            $this->expectException(InvalidOptionException::class);
        }

        $javaScriptStatisticsProvider = new JavaScriptStatisticsProvider($this->javaScriptStatisticsResolver);
        self::assertSame('javascript', $javaScriptStatisticsProvider::getContextKey());
        self::assertSame('j', $javaScriptStatisticsProvider::getPayloadKey());

        $actualStatistics = $javaScriptStatisticsProvider->getFromPayload($payload);

        self::assertArrayHasKey('response_timestamp', $actualStatistics['javascript']);

        unset($actualStatistics['javascript']['response_timestamp']);

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Template\DelayedContainer\StatisticsProvider;

use App\Template\DelayedContainer\StatisticsProvider\DelayedContainerStatisticsProvider;
use App\Template\DelayedContainer\StatisticsProvider\DelayedContainerStatisticsResolver;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tests\Unit\Statistics\Provider\AbstractStatisticsProviderTestCase;
use Visymo\Shared\Domain\OptionsResolver\InvalidOptionException;
use Visymo\Shared\Infrastructure\Bridge\Symfony\OptionsResolver\SymfonyOptionsResolverBridge;

class DelayedContainerStatisticsProviderTest extends AbstractStatisticsProviderTestCase
{
    private DelayedContainerStatisticsResolver $delayedContainerStatisticsResolver;

    protected function setUp(): void
    {
        parent::setUp();

        $this->delayedContainerStatisticsResolver = new DelayedContainerStatisticsResolver(
            new SymfonyOptionsResolverBridge(new OptionsResolver()),
            $this->genericRequestMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function payloadDataProvider(): array
    {
        $pageviewId = uuid_create(UUID_TYPE_RANDOM);

        return [
            'ads'             => [
                'payload'            => [
                    'key'  => 'dc',
                    'pvid' => $pageviewId,
                    'fs'   => 'ads',
                ],
                'expectedStatistics' => [
                    'visit_id'          => self::$visitId,
                    'pageview_id'       => $pageviewId,
                    'delayed_container' => [
                        'first_shown' => 'ads',
                    ],
                ],
            ],
            'delayed_content' => [
                'payload'            => [
                    'key'  => 'dc',
                    'pvid' => $pageviewId,
                    'fs'   => 'delayed_content',
                ],
                'expectedStatistics' => [
                    'visit_id'          => self::$visitId,
                    'pageview_id'       => $pageviewId,
                    'delayed_container' => [
                        'first_shown' => 'delayed_content',
                    ],
                ],
            ],
            'extra'           => [
                'payload'            => [
                    'extra' => true,
                    'key'   => 'dc',
                    'pvid'  => $pageviewId,
                    'fs'    => 'delayed_content',
                ],
                'expectedStatistics' => [
                    'visit_id'          => self::$visitId,
                    'pageview_id'       => $pageviewId,
                    'delayed_container' => [
                        'first_shown' => 'delayed_content',
                    ],
                ],
            ],
            'bad'             => [
                'payload'            => [
                    'key'  => 'dc',
                    'pvid' => $pageviewId,
                    'bad'  => true,
                ],
                'expectedStatistics' => null,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $payload
     * @param mixed[]|null         $expectedStatistics
     */
    #[DataProvider('payloadDataProvider')]
    public function testGetFromPayload(array $payload, ?array $expectedStatistics): void
    {
        if ($expectedStatistics === null) {
            $this->expectException(InvalidOptionException::class);
        }

        $delayedContainerStatisticsProvider = new DelayedContainerStatisticsProvider($this->delayedContainerStatisticsResolver);
        self::assertSame('delayed_container', $delayedContainerStatisticsProvider::getContextKey());
        self::assertSame('dc', $delayedContainerStatisticsProvider::getPayloadKey());

        $actualStatistics = $delayedContainerStatisticsProvider->getFromPayload($payload);

        self::assertArrayHasKey('response_timestamp', $actualStatistics['delayed_container']);

        unset($actualStatistics['delayed_container']['response_timestamp']);

        self::assertSame($expectedStatistics, $actualStatistics);
    }
}

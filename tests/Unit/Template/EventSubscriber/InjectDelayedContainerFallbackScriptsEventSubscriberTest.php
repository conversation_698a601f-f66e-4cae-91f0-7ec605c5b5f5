<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\Template\DelayedContainer\DelayedContainerHelper;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use App\Template\EventSubscriber\InjectDelayedContainerFallbackScriptsEventSubscriber;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

final class InjectDelayedContainerFallbackScriptsEventSubscriberTest extends TestCase
{
    public function testRenderScriptsAddsItemWhenNotRenderedBefore(): void
    {
        $delayedContainerHelper = $this->createMock(DelayedContainerHelper::class);
        $twig = $this->createMock(Environment::class);

        $delayedContainerHelper->method('isDelayedContainerRequired')->willReturn(false);
        $twig->method('render')
            ->with('@theme/delayed_container/delayed_container_scripts.html.twig', ['show' => true])
            ->willReturn('rendered_scripts');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber($delayedContainerHelper, $twig);
        $subscriber->renderScripts($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_scripts', $event->getItems()[0]);
    }

    public function testRenderScriptsDoesNothingWhenAlreadyRendered(): void
    {
        $delayedContainerHelper = $this->createMock(DelayedContainerHelper::class);
        $twig = $this->createMock(Environment::class);

        $event1 = new RenderTemplateHeadersEvent();
        $event2 = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber($delayedContainerHelper, $twig);

        $subscriber->renderScripts($event1);
        $subscriber->renderScripts($event2);

        self::assertEmpty($event2->getItems());
    }

    public function testRenderTemplateHeadersAddsItemWhenRequired(): void
    {
        $delayedContainerHelper = $this->createMock(DelayedContainerHelper::class);
        $twig = $this->createMock(Environment::class);

        $delayedContainerHelper->method('isDelayedContainerRequired')->willReturn(true);
        $twig->method('render')
            ->with('@theme/delayed_container/delayed_container_header.html.twig')
            ->willReturn('rendered_header');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber($delayedContainerHelper, $twig);
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_header', $event->getItems()[0]);
    }

    public function testRenderTemplateHeadersDoesNothingWhenNotRequired(): void
    {
        $delayedContainerHelper = $this->createMock(DelayedContainerHelper::class);
        $twig = $this->createMock(Environment::class);

        $delayedContainerHelper->method('isDelayedContainerRequired')->willReturn(false);

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber($delayedContainerHelper, $twig);
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateFootersAddsItemWhenRequired(): void
    {
        $delayedContainerHelper = $this->createMock(DelayedContainerHelper::class);
        $twig = $this->createMock(Environment::class);

        $delayedContainerHelper->method('isDelayedContainerRequired')->willReturn(true);
        $twig->method('render')
            ->with('@theme/delayed_container/delayed_container_footer.html.twig')
            ->willReturn('rendered_footer');

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber($delayedContainerHelper, $twig);
        $subscriber->renderTemplateFooters($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('rendered_footer', $event->getItems()[0]);
    }

    public function testRenderTemplateFootersDoesNothingWhenNotRequired(): void
    {
        $delayedContainerHelper = $this->createMock(DelayedContainerHelper::class);
        $twig = $this->createMock(Environment::class);

        $delayedContainerHelper->method('isDelayedContainerRequired')->willReturn(false);

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectDelayedContainerFallbackScriptsEventSubscriber($delayedContainerHelper, $twig);
        $subscriber->renderTemplateFooters($event);

        self::assertEmpty($event->getItems());
    }
}
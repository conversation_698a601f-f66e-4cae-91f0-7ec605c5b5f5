<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\SplitTest\SplitTestExtendedReaderInterface;
use App\Template\Event\RenderTemplateHtmlClassEvent;
use App\Template\EventSubscriber\InjectSplitTestEntriesEventSubscriber;
use PHPUnit\Framework\TestCase;

final class InjectSplitTestEntriesEventSubscriberTest extends TestCase
{
    public function testRenderTemplateHtmlClassAddsVariantWhenActive(): void
    {
        $splitTestReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestReader->method('getVariant')->willReturn('test-variant');

        $event = new RenderTemplateHtmlClassEvent();
        $subscriber = new InjectSplitTestEntriesEventSubscriber($splitTestReader);
        $subscriber->renderTemplateHtmlClass($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('variant-test-variant', $event->getItems()[0]);
    }

    public function testRenderTemplateHtmlClassDoesNothingWhenNoVariant(): void
    {
        $splitTestReader = $this->createMock(SplitTestExtendedReaderInterface::class);
        $splitTestReader->method('getVariant')->willReturn(null);

        $event = new RenderTemplateHtmlClassEvent();
        $subscriber = new InjectSplitTestEntriesEventSubscriber($splitTestReader);
        $subscriber->renderTemplateHtmlClass($event);

        self::assertEmpty($event->getItems());
    }
}
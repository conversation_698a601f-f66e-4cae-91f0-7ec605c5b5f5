<?php

declare(strict_types=1);

namespace Tests\Unit\Template\EventSubscriber;

use App\Generic\Device\Device;
use App\Template\Event\RenderTemplateHtmlClassEvent;
use App\Template\EventSubscriber\InjectTrackingEntriesEventSubscriber;
use App\Tracking\Helper\ActiveTrackingEntryHelperInterface;
use App\Tracking\Model\TrafficSource;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Tracking\Entry\TrackingEntryStubBuilder;

final class InjectTrackingEntriesEventSubscriberTest extends TestCase
{
    public function testRenderTemplateHtmlClassAddsDeviceAndTrafficSource(): void
    {
        $activeTrackingEntryHelper = $this->createMock(ActiveTrackingEntryHelperInterface::class);

        $trackingEntry = (new TrackingEntryStubBuilder())
            ->clear()
            ->setDevice(Device::MOBILE)
            ->setTrafficSource(TrafficSource::GOOGLE)
            ->setQuery('test-query')
            ->create();

        $activeTrackingEntryHelper->method('getActiveTrackingEntry')->willReturn($trackingEntry);

        $event = new RenderTemplateHtmlClassEvent();
        $subscriber = new InjectTrackingEntriesEventSubscriber($activeTrackingEntryHelper);
        $subscriber->renderTemplateHtmlClass($event);

        self::assertCount(2, $event->getItems());
        self::assertContains('device-m', $event->getItems());
        self::assertContains('traffic-source-g', $event->getItems());
    }

    public function testRenderTemplateHtmlClassAddsOnlyDeviceWhenNoTrafficSource(): void
    {
        $activeTrackingEntryHelper = $this->createMock(ActiveTrackingEntryHelperInterface::class);

        $trackingEntry = (new TrackingEntryStubBuilder())
            ->clear()
            ->setDevice(Device::DESKTOP)
            ->setTrafficSource(null)
            ->setQuery('test-query')
            ->create();

        $activeTrackingEntryHelper->method('getActiveTrackingEntry')->willReturn($trackingEntry);

        $event = new RenderTemplateHtmlClassEvent();
        $subscriber = new InjectTrackingEntriesEventSubscriber($activeTrackingEntryHelper);
        $subscriber->renderTemplateHtmlClass($event);

        self::assertCount(1, $event->getItems());
        self::assertContains('device-d', $event->getItems());
    }
}
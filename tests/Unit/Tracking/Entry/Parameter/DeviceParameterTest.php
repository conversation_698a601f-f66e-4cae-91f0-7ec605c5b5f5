<?php

declare(strict_types=1);

namespace Tests\Unit\Tracking\Entry\Parameter;

use App\Generic\Device\Detection\AdBotDeviceDetection;
use App\Generic\Device\Detection\MobileDeviceDetection;
use App\Generic\Device\Device;
use App\Tracking\Entry\Parameter\DeviceParameter;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Tracking\Request\SeaRequestStub;

class DeviceParameterTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function deviceDataProvider(): array
    {
        return [
            'default'                     => [
                'seaRequestDevice'      => null,
                'adBotDevice'           => null,
                'mobileDetectionDevice' => null,
                'expectedDevice'        => Device::DESKTOP,
            ],
            'sea device'                  => [
                'seaRequestDevice'      => Device::MOBILE,
                'adBotDevice'           => null,
                'mobileDetectionDevice' => null,
                'expectedDevice'        => Device::MOBILE,
            ],
            'sea device with fallbacks'   => [
                'seaRequestDevice'      => Device::MOBILE,
                'adBotDevice'           => Device::TABLET,
                'mobileDetectionDevice' => Device::TABLET,
                'expectedDevice'        => Device::MOBILE,
            ],
            'ad bot device'               => [
                'seaRequestDevice'      => null,
                'adBotDevice'           => Device::TABLET,
                'mobileDetectionDevice' => null,
                'expectedDevice'        => Device::TABLET,
            ],
            'ad bot device with fallback' => [
                'seaRequestDevice'      => null,
                'adBotDevice'           => Device::TABLET,
                'mobileDetectionDevice' => Device::MOBILE,
                'expectedDevice'        => Device::TABLET,
            ],
            'detection device'            => [
                'seaRequestDevice'      => null,
                'adBotDevice'           => null,
                'mobileDetectionDevice' => Device::MOBILE,
                'expectedDevice'        => Device::MOBILE,
            ],
        ];
    }

    #[DataProvider('deviceDataProvider')]
    public function testDevice(
        ?Device $seaRequestDevice,
        ?Device $adBotDevice,
        ?Device $mobileDetectionDevice,
        ?Device $expectedDevice
    ): void
    {
        $seaRequestStub = (new SeaRequestStub())
            ->setDevice($seaRequestDevice);

        /** @var AdBotDeviceDetection|MockObject $adBotDeviceDetectionMock */
        $adBotDeviceDetectionMock = $this->createConfiguredMock(
            AdBotDeviceDetection::class,
            [
                'getDevice' => $adBotDevice,
            ],
        );

        /** @var MobileDeviceDetection|MockObject $mobileDeviceDetectionMock */
        $mobileDeviceDetectionMock = $this->createConfiguredMock(
            MobileDeviceDetection::class,
            [
                'getDevice' => $mobileDetectionDevice,
            ],
        );

        $deviceParameter = new DeviceParameter(
            $seaRequestStub,
            $adBotDeviceDetectionMock,
            $mobileDeviceDetectionMock,
        );

        self::assertSame($expectedDevice, $deviceParameter->getDevice());
    }
}

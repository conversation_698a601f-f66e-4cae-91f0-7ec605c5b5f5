<?php

declare(strict_types=1);

namespace Tests\Unit\Tracking\Entry\Parameter;

use App\Account\Service\AccountService;
use App\Account\Settings\AccountSettingsRepository;
use App\AdBot\Bot\AdBot;
use App\Tracking\Entry\Parameter\TrafficSourceParameter;
use App\Tracking\Model\ClickId\ClickId;
use App\Tracking\Model\ClickId\ClickIdSource;
use App\Tracking\Model\TrafficSource;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Account\Request\AccountRequestStub;
use Tests\Stub\Account\Settings\AccountSettingsStubBuilder;
use Tests\Stub\AdBot\Request\AdBotRequestStub;
use Tests\Stub\Tracking\Request\SeaRequestStub;

class TrafficSourceParameterTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function trafficSourceDataProvider(): array
    {
        $data = [
            'none'              => [
                'accountId'             => null,
                'service'               => null,
                'seaRequestCallback'    => null,
                'route'                 => null,
                'adBot'                 => null,
                'expectedTrafficSource' => null,
            ],
            'account id'        => [
                'accountId'             => 1,
                'service'               => null,
                'seaRequestCallback'    => null,
                'route'                 => null,
                'adBot'                 => null,
                'expectedTrafficSource' => null,
            ],
            'google service'    => [
                'accountId'             => 1,
                'service'               => AccountService::GOOGLE_ADS,
                'seaRequestCallback'    => null,
                'route'                 => null,
                'adBot'                 => null,
                'expectedTrafficSource' => TrafficSource::GOOGLE,
            ],
            'microsoft service' => [
                'accountId'             => 1,
                'service'               => AccountService::MICROSOFT_ADVERTISING,
                'seaRequestCallback'    => null,
                'route'                 => null,
                'adBot'                 => null,
                'expectedTrafficSource' => TrafficSource::MICROSOFT,
            ],
            'none for route'    => [
                'accountId'             => null,
                'service'               => null,
                'seaRequestCallback'    => null,
                'route'                 => 'route_display_search_related',
                'adBot'                 => null,
                'expectedTrafficSource' => null,
            ],
            'google ad bot'     => [
                'accountId'             => null,
                'service'               => null,
                'seaRequestCallback'    => null,
                'route'                 => null,
                'adBot'                 => AdBot::GOOGLE,
                'expectedTrafficSource' => TrafficSource::GOOGLE,
            ],
            'microsoft ad bot'  => [
                'accountId'             => null,
                'service'               => null,
                'seaRequestCallback'    => null,
                'route'                 => null,
                'adBot'                 => AdBot::MICROSOFT,
                'expectedTrafficSource' => TrafficSource::MICROSOFT,
            ],
        ];

        $clickIds = [
            'fbclid-123'  => 'facebook',
            'gclid-123'   => 'google',
            'msclkid-123' => 'microsoft',
            'ScCid-123'   => 'snapchat',
            'twclid-123'  => 'twitter',
            'vsmclid-123' => 'visymo',
            'yclid-123'   => 'yahoo',
            'qclid-123'   => 'quora',
            'ttclid-123'  => 'tiktok',
        ];

        foreach ($clickIds as $clickValue => $trafficSource) {
            [$source, $value] = explode('-', $clickValue);

            $data[sprintf('%s traffic source', $trafficSource)] = [
                'accountId'             => null,
                'service'               => null,
                'seaRequestCallback'    => static function (SeaRequestStub $seaRequestStub) use ($value, $source): void {
                    $clickIdSource = ClickIdSource::tryFrom($source);

                    if ($clickIdSource === null) {
                        return;
                    }

                    $seaRequestStub->setClickId(new ClickId($value, $clickIdSource));
                },
                'route'                 => null,
                'adBot'                 => null,
                'expectedTrafficSource' => TrafficSource::tryFrom($trafficSource),
            ];
        }

        return $data;
    }

    #[DataProvider('trafficSourceDataProvider')]
    public function testGetTrafficSource(
        ?int $accountId,
        ?AccountService $service,
        ?callable $seaRequestCallback,
        ?string $route,
        ?AdBot $adBot,
        ?TrafficSource $expectedTrafficSource
    ): void
    {
        $adBotRequestStub = (new AdBotRequestStub())
            ->setIsAdBot($adBot !== null)
            ->setAdBot($adBot);

        $seaRequestStub = (new SeaRequestStub())
            ->clearClickIds();

        if ($seaRequestCallback !== null) {
            $seaRequestCallback($seaRequestStub);
        }

        $accountRequestStub = (new AccountRequestStub())
            ->setAccountId($accountId);

        $accountSettings = $service !== null ? (new AccountSettingsStubBuilder())->setService($service)->create() : null;

        $accountSettingsRepositoryMock = $this->createMock(AccountSettingsRepository::class);
        $accountSettingsRepositoryMock
            ->method('getByAccountId')
            ->willReturn($accountSettings);

        $trafficSourceHelper = new TrafficSourceParameter(
            $adBotRequestStub,
            $seaRequestStub,
            $accountRequestStub,
            $accountSettingsRepositoryMock,
        );

        self::assertSame($expectedTrafficSource, $trafficSourceHelper->getTrafficSource());
    }
}

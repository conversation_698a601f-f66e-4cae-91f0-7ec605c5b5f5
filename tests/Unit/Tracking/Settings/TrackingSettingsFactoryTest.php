<?php

declare(strict_types=1);

namespace Tests\Unit\Tracking\Settings;

use App\Tracking\Settings\TrackingSettingsFactory;
use Tests\Unit\Module\AbstractModuleSettingsFactoryTestCase;

final class TrackingSettingsFactoryTest extends AbstractModuleSettingsFactoryTestCase
{
    private TrackingSettingsFactory $trackingSettingsFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->trackingSettingsFactory = new TrackingSettingsFactory(
            $this->websiteConfigurationHelperMock,
        );
    }

    public function testCampaignNameValidationDisabled(): void
    {
        $this->setBrandConfig(
            [
                'tracking' => [],
            ],
        );

        $trackingSettings = $this->trackingSettingsFactory->create();

        self::assertFalse($trackingSettings->campaignNameValidationEnabled);
    }

    public function testCampaignNameValidationEnabled(): void
    {
        $this->setBrandConfig(
            [
                'tracking' => [
                    'campaign_name_validation_enabled' => true,
                ],
            ],
        );

        $trackingSettings = $this->trackingSettingsFactory->create();

        self::assertTrue($trackingSettings->campaignNameValidationEnabled);
    }
}

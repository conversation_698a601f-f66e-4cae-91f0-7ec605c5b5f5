<?php

declare(strict_types=1);

namespace Tests\Unit\WebsiteSettings\DomainToBrandMap;

use App\Brand\Settings\BrandSettings;
use App\WebsiteSettings\Configuration\Cache\CachedWebsiteConfigurationFileReader;
use App\WebsiteSettings\Configuration\WebsiteConfiguration;
use App\WebsiteSettings\Configuration\WebsiteConfigurationFileRepository;
use App\WebsiteSettings\Configuration\WebsiteConfigurationRepository;
use App\WebsiteSettings\DomainToBrandMap\DomainToBrandMapGenerator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

class DomainToBrandMapGeneratorTest extends TestCase
{
    private WebsiteConfigurationRepository & MockObject $websiteConfigurationRepositoryMock;

    private WebsiteConfigurationFileRepository & MockObject $websiteConfigurationFileRepositoryMock;

    private SerializedFileInterface & MockObject $serializedFileMock;

    private DomainToBrandMapGenerator $domainToBrandMapGenerator;

    protected function setUp(): void
    {
        $cachedWebsiteConfigurationFileReaderMock = $this->createMock(CachedWebsiteConfigurationFileReader::class);
        $cachedWebsiteConfigurationFileReaderMock
            ->method('read')
            ->willReturnCallback(
                static fn (
                    SerializedFileInterface $serializedFile,
                    ?int $accountId,
                    ?string $campaignName
                ) => $serializedFile->getContents(),
            );

        $this->websiteConfigurationRepositoryMock = $this->createMock(WebsiteConfigurationRepository::class);
        $this->websiteConfigurationFileRepositoryMock = $this->createMock(WebsiteConfigurationFileRepository::class);
        $this->serializedFileMock = $this->createMock(SerializedFileInterface::class);
        $this->domainToBrandMapGenerator = new DomainToBrandMapGenerator(
            $this->websiteConfigurationRepositoryMock,
            $this->websiteConfigurationFileRepositoryMock,
            $this->serializedFileMock,
        );
    }

    /**
     * @return mixed[]
     */
    public static function domainToBrandMapGeneratorDataProvider(): array
    {
        return [
            'single brand and domain' => [
                'websiteConfigurations' => [
                    [
                        'example',
                        [],
                        [
                            'nl.example.com',
                        ],
                    ],
                ],
                'expectedMap'           => [
                    'nl.example.com' => 'example',
                ],
            ],
            'multiple domains'        => [
                'websiteConfigurations' => [
                    [
                        'example2',
                        [],
                        [
                            'nl.example2.com',
                            'nl.example-two.com',
                            'another-domain.de',
                        ],
                    ],
                ],
                'expectedMap'           => [
                    'nl.example2.com'    => 'example2',
                    'nl.example-two.com' => 'example2',
                    'another-domain.de'  => 'example2',
                ],
            ],
            'multiple brands'         => [
                'websiteConfigurations' => [
                    [
                        'brandweb',
                        [],
                        [
                            'de.brandweb.com',
                            'ru.brandweb.com',
                            'ru-int.brandweb.com',
                        ],
                    ],
                    [
                        'brandley',
                        [],
                        [
                            'fr.brandley.com',
                            'us.brandley.com',
                            'us-int.brandley.com',
                        ],
                    ],
                    [
                        'brandmixer',
                        [],
                        [
                            'www.brandmixer.com',
                        ],
                    ],
                ],
                'expectedMap'           => [
                    'de.brandweb.com'     => 'brandweb',
                    'ru.brandweb.com'     => 'brandweb',
                    'ru-int.brandweb.com' => 'brandweb',
                    'fr.brandley.com'     => 'brandley',
                    'us.brandley.com'     => 'brandley',
                    'us-int.brandley.com' => 'brandley',
                    'www.brandmixer.com'  => 'brandmixer',
                ],
            ],
            'redirect domain'         => [
                'websiteConfigurations' => [
                    [
                        'redirectly',
                        [
                            'www.example.com',
                        ],
                        [],
                    ],
                ],
                'expectedMap'           => [
                    'www.example.com' => 'redirectly',
                ],
            ],
        ];
    }

    /**
     * @param mixed[]               $websiteConfigurations
     * @param array<string, string> $expectedMap
     */
    #[DataProvider('domainToBrandMapGeneratorDataProvider')]
    public function testDomainToBrandMapGenerator(array $websiteConfigurations, array $expectedMap): void
    {
        $brandSlugs = [];

        foreach ($websiteConfigurations as $websiteConfiguration) {
            $websiteConfiguration = $this->createWebsiteConfiguration(...$websiteConfiguration);
            $brandSlugs[$websiteConfiguration->getBrandSlug()] = $websiteConfiguration;
        }

        $this->websiteConfigurationFileRepositoryMock
            ->expects($this->once())
            ->method('getAvailableBrands')
            ->willReturn(array_keys($brandSlugs));

        $this->websiteConfigurationRepositoryMock
            ->expects($this->exactly(count($brandSlugs)))
            ->method('getForBrandAndAccount')
            ->willReturnCallback(
                static fn (string $brandSlug) => match ($brandSlug) {
                    $brandSlug => $brandSlugs[$brandSlug],
                },
            );

        $this->serializedFileMock->expects($this->once())->method('writeContent')->with($expectedMap);

        $this->domainToBrandMapGenerator->generate();
    }

    /**
     * @param string[] $availableRedirectDomains
     * @param string[] $availableDomains
     */
    private function createWebsiteConfiguration(
        string $brandSlug,
        array $availableRedirectDomains,
        array $availableDomains
    ): WebsiteConfiguration
    {
        return new WebsiteConfiguration(
            [
                WebsiteConfiguration::KEY_BRAND            => [
                    BrandSettings::KEY_SLUG => $brandSlug,
                ],
                WebsiteConfiguration::KEY_REDIRECT_DOMAINS => array_flip($availableRedirectDomains),
                WebsiteConfiguration::KEY_DOMAINS          => array_flip($availableDomains),
            ],
        );
    }
}

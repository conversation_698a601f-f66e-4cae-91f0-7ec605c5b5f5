<?xml version="1.0"?>
<ruleset name="PHP_CodeSniffer">
    <!-- Load company standards ruleset -->
    <rule ref="./vendor/visymo/coding-standards/dist/phpcs.xml">
        <exclude name="Visymo.DateTime.DateTime.InvalidTimezoneArgument"/>
        <exclude name="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn.UselessIfCondition"/>
    </rule>

    <!-- Folders to check are hardcoded because it makes phpcs much faster.
         If all folders are loaded, and later excluded with an exclude-pattern, then it seems
         that all files/folders are still scanned, just not analysed.
    -->
    <file>src</file>
    <file>tests</file>
    <file>bundles/develop-bundle/src</file>
    <file>bundles/develop-bundle/tests</file>
    <file>bundles/prototype-bundle/src</file>
    <file>bundles/prototype-bundle/tests</file>
    <file>bundles/visymo-label-bundle/src</file>
    <file>bundles/visymo-label-bundle/tests</file>

    <exclude-pattern>config/config-api/artemis/brand-config/*.php</exclude-pattern>
    <exclude-pattern>config/config-api/artemis/brand-assets/*.php</exclude-pattern>
    <exclude-pattern>config/config-api/artemis/project/*.php</exclude-pattern>
    <exclude-pattern>config/bundles.php</exclude-pattern>
    <exclude-pattern>public/index.php</exclude-pattern>
    <exclude-pattern>**/phpunit-bootstrap.php</exclude-pattern>
</ruleset>

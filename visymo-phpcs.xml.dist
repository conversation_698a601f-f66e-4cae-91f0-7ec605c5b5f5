<?xml version="1.0"?>
<ruleset name="PHP_CodeSniffer">
    <!-- Load company standards ruleset -->
    <rule ref="./vendor/visymo/coding-standards/dist/phpcs.xml"/>

    <!-- Files to check -->
    <file>./src</file>
    <file>./tests</file>

    <!-- Exclude specific folders -->
    <exclude-pattern>*/.idea/*</exclude-pattern>
    <exclude-pattern>*/vendor/*</exclude-pattern>
    <exclude-pattern>*/config/*</exclude-pattern>
    <exclude-pattern>*/public/index.php</exclude-pattern>
    <exclude-pattern>*/public/check.php</exclude-pattern>
    <exclude-pattern>*/var/cache/*</exclude-pattern>
</ruleset>

parameters:
    level: 9
    paths:
        - src
    bootstrapFiles:
        - visymo-phpstan-bootstrap.php
    symfony:
        console_application_loader: visymo-phpstan-console.php
    reportUnmatchedIgnoredErrors: false
    ignoreErrors:
        # This rules finds false positives in Symfony ParameterBag objects
        - "#Casting to string something that's already string.#"
includes:
    - vendor/phpstan/phpstan-deprecation-rules/rules.neon
    - vendor/phpstan/phpstan-phpunit/extension.neon
    - vendor/phpstan/phpstan-phpunit/rules.neon
    - vendor/phpstan/phpstan-strict-rules/rules.neon
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-symfony/rules.neon
